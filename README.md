# AI-Care

## 基础架构

​	采用 Flutter + Getx 作为基础架构，在此基础上封装了日志管理、状态管理、网络请求、国际化、内存管理、环境管理、基础Controller、View、Widget的封装、样式管理、标题栏管理。



## 运行

指定环境文件运行：

```cmd
flutter run -t lib/main_dev.dart -d edge
```





## 目录结构

```cmd
├─app
│  ├─bingdings					初始化页面绑定
│  ├─core								核心配置
│  │  ├─base						封装的基础架构
│  │  │  ├─controller			控制器
│  │  │  ├─remote				网络请求
│  │  │  ├─view						页面
│  │  │  └─widget					组件
│  │  ├─model						数据
│  │  ├─utils							工具类
│  │  ├─values						全局常量
│  │  └─widget						通用组件
│  ├─data							封装的页面绑定
│  │  ├─local
│  │  │  └─preference			内存管理
│  │  ├─model						数据模型
│  │  └─repository				仓储管理
│  ├─modules						页面目录
│  ├─network							网络请求处理
│  │  ├─api								接口地址
│  │  └─exceptions				自定义错误请求
│  └─routes							路由管理
├─flavors								环境配置
└─l10n									国际化
```



## 插件配置

```dart
name: rpmtemplate
version: 1.0.0+1
publish_to: none
description: rpmtemplate.
environment:
  sdk: '>=3.3.4 <4.0.0'

dependencies:
  cupertino_icons: ^1.0.6
  flutter_screenutil: ^5.9.0  #屏幕适配
  shared_preferences: ^2.2.3  #内存管理
  logger: ^2.3.0              #日志管理
  get: 4.6.6
  dio: 5.3.3                  #网络管理
  flutter_localizations:      #国际化
    sdk: flutter
  fluttertoast: 8.2.2         #消息显示
  flutter_svg: 2.0.8
  pretty_dio_logger: 1.3.1    #日志格式管理
  intl: 0.18.1                #国际化
  flutter:
    sdk: flutter

dev_dependencies:
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter

flutter:
  generate: true              #国际化生成
  uses-material-design: true
flutter_intl:
  enabled: true
```





## 全局配置

lib/main.prod.dart 和 lib/main_dev.dart 分别配置了开发环境和发布环境两套运行配置，以及一些基础配置；

```dart
void main() {
  EnvConfig prodConfig = EnvConfig(
    appName: "Flutter GetX Template Prod",
    baseUrl: "https://api.github.com",
    shouldCollectCrashLog: true,
  );

  BuildConfig.instantiate(
    envType: Environment.DEVELOPMENT,
    envConfig: prodConfig,
  );
  //配置透明状态栏
  SystemUiOverlayStyle systemUiOverlayStyle =
      const SystemUiOverlayStyle(statusBarColor: Colors.transparent);
  SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  runApp(const MyApp());
}
```



## 基础架构

base_controller.dart：

基础控制器，包含日志，国际化，基础通用方法，页面状态，加载提示，显示信息配置等。

```dart
/**
 * 封装基础控制器
 * 1、日志
 * 2、国际化
 * 3、刷新页面
 * 4、页面状态管理
 * 5、加载提示
 * 6、显示消息框
 */
abstract class BaseController extends GetxController {
  //获取logger单例
  final Logger logger = BuildConfig.instance.config.logger;

  AppLocalizations get appLocalization => AppLocalizations.of(Get.context!)!;

  final logoutController = false.obs;

  //Reload the page
  final _refreshController = false.obs;

  refreshPage(bool refresh) => _refreshController(refresh);

  //Controls page state
  final _pageSateController = PageState.DEFAULT.obs;

  PageState get pageState => _pageSateController.value;

  updatePageState(PageState state) => _pageSateController(state);

  resetPageState() => _pageSateController(PageState.DEFAULT);

  showLoading() => updatePageState(PageState.LOADING);

  hideLoading() => resetPageState();

  final _messageController = ''.obs;

  String get message => _messageController.value;

  showMessage(String msg) => _messageController(msg);

  final _errorMessageController = ''.obs;

  String get errorMessage => _errorMessageController.value;

  showErrorMessage(String msg) {
    _errorMessageController(msg);
  }

  final _successMessageController = ''.obs;

  String get successMessage => _messageController.value;

  showSuccessMessage(String msg) => _successMessageController(msg);

  /**
   * 一个通用的数据服务调用函数 callDataService，
   * 它接受一个异步操作 future，
   * 并提供了一些回调函数来处理成功、失败和完成时的事件。
   */
  // ignore: long-parameter-list
  dynamic callDataService<T>(
    Future<T> future, {
    Function(Exception exception)? onError,
    Function(T response)? onSuccess,
    Function? onStart,
    Function? onComplete,
  }) async {
    Exception? _exception;

    onStart == null ? showLoading() : onStart();

    try {
      final T response = await future;

      if (onSuccess != null) onSuccess(response);

      onComplete == null ? hideLoading() : onComplete();

      return response;
    } // 按照不同的异常类型进行捕获和处理
      on ServiceUnavailableException catch (exception) {
      _exception = exception;
      showErrorMessage(exception.message);
    } on UnauthorizedException catch (exception) {
      _exception = exception;
      showErrorMessage(exception.message);
    } on TimeoutException catch (exception) {
      _exception = exception;
      showErrorMessage(exception.message ?? 'Timeout exception');
    } on NetworkException catch (exception) {
      _exception = exception;
      showErrorMessage(exception.message);
    } on JsonFormatException catch (exception) {
      _exception = exception;
      showErrorMessage(exception.message);
    } on NotFoundException catch (exception) {
      _exception = exception;
      showErrorMessage(exception.message);
    } on ApiException catch (exception) {
      _exception = exception;
    } on AppException catch (exception) {
      _exception = exception;
      showErrorMessage(exception.message);
    } catch (error) {
      _exception = AppException(message: "$error");
      logger.e("Controller>>>>>> error $error");
    }

    if (onError != null) onError(_exception);

    onComplete == null ? hideLoading() : onComplete();
  }

  @override
  void onClose() {
    _messageController.close();
    _refreshController.close();
    _pageSateController.close();
    super.onClose();
  }
}
```



base_remote_source.dart

​	基础请求封装，处理请求状态码不成功的时候，以及异常报错；

	import 'package:dio/dio.dart';
	import 'package:get/get_connect/http/src/status/http_status.dart';
	
	import '/app/network/dio_provider.dart';
	import '/app/network/error_handlers.dart';
	import '/app/network/exceptions/base_exception.dart';
	import '/flavors/build_config.dart';
	
	abstract class BaseRemoteSource {
	  Dio get dioClient => DioProvider.dioWithHeaderToken;
	
	  final logger = BuildConfig.instance.config.logger;
	
	  Future<Response<T>> callApiWithErrorParser<T>(Future<Response<T>> api) async {
	    try {
	      Response<T> response = await api;
	
	      if (response.statusCode != HttpStatus.ok ||
	          (response.data as Map<String, dynamic>)['statusCode'] !=
	              HttpStatus.ok) {
	        // TODO
	      }
	
	      return response;
	    } on DioError catch (dioError) {
	      Exception exception = handleDioError(dioError);
	      logger.e(
	          "Throwing error from repository: >>>>>>> $exception : ${(exception as BaseException).message}");
	      throw exception;
	    } catch (error) {
	      logger.e("Generic error: >>>>>>> $error");
	
	      if (error is BaseException) {
	        rethrow;
	      }
	
	      throw handleError("$error");
	    }
	  }
	}

 


## 网络请求

​	dio_provicer.dart：提供了一个dio的实例，设置连接时长，接受时长，并通过自定义的PrettyDioLogger注册了日志记录的请求/响应/错误拦截器配置，包括日志打印的输出配置，错误处理等。

​	RequestHeaderInterceptor：请求头拦截器，可以在请求前捕获请求头头部的信息，自定义处理。

​	pretty_dio_logger.dart：日志拦截器，自定义日志配置；

```dart
import 'package:dio/dio.dart';
import 'package:rpmtemplate/app/network/pretty_dio_logger.dart';
import 'package:rpmtemplate/app/network/request_headers.dart';
import 'package:rpmtemplate/flavors/environment.dart';

import '/flavors/build_config.dart';

class DioProvider {
  static final String baseUrl = BuildConfig.instance.config.baseUrl;

  static Dio? _instance;

  static const int _maxLineWidth = 90;
  //用于日志记录的拦截器配置
  static final _prettyDioLogger = PrettyDioLogger(
      requestHeader: true,
      requestBody: true,
      responseBody: BuildConfig.instance.environment == Environment.DEVELOPMENT,
      responseHeader: false,
      error: true,
      compact: true,
      maxWidth: _maxLineWidth);

  static final BaseOptions _options = BaseOptions(
    baseUrl: baseUrl,
    connectTimeout: const Duration(seconds: 5),
    receiveTimeout: const Duration(seconds: 5),
  );

  //返回一个带有日志记录拦截器的 Dio 实例。
  static Dio get httpDio {
    if (_instance == null) {
      _instance = Dio(_options);

      _instance!.interceptors.add(_prettyDioLogger);


      return _instance!;
    } else {
      _instance!.interceptors.clear();
      _instance!.interceptors.add(_prettyDioLogger);

      return _instance!;
    }
  }

  ///returns a Dio client with Access token in header
  ///返回一个带有请求头拦截器（包括访问令牌）的 Dio 实例。
  static Dio get tokenClient {
    _addInterceptors();

    return _instance!;
  }

  ///returns a Dio client with Access token in header
  ///Also adds a token refresh interceptor which retry the request when it's unauthorized
  ///返回一个带有请求头拦截器（包括访问令牌）和日志记录拦截器的 Dio 实例。
  static Dio get dioWithHeaderToken {
    _addInterceptors();

    return _instance!;
  }

  //添加请求头拦截器和日志记录拦截器。
  static _addInterceptors() {
    _instance ??= httpDio;
    _instance!.interceptors.clear();
    _instance!.interceptors.add(RequestHeaderInterceptor());
    _instance!.interceptors.add(_prettyDioLogger);
  }

  //构建自定义内容类型字符串。
  static String _buildContentType(String version) {
    return "user_defined_content_type+$version";
  }


  DioProvider.setContentType(String version) {
    _instance?.options.contentType = _buildContentType(version);
  }

  DioProvider.setContentTypeApplicationJson() {
    _instance?.options.contentType = "application/json";
  }
}
```

flutter run -d d33ac056f4719248c9bdc4f084761b72df3ddf6e    


dart run build_runner build

get create page:bluetooth_info

zhangshipeng@zhangshipengdeMacBook-Pro RPM-APP % flutter --version
Flutter 3.22.2 • channel stable • https://github.com/flutter/flutter.git
Framework • revision 761747bfc5 (1 year, 2 months ago) • 2024-06-05 22:15:13 +0200
Engine • revision edd8546116
Tools • Dart 3.4.3 • DevTools 2.34.3
zhangshipeng@zhangshipengdeMacBook-Pro RPM-APP % dart --version
Dart SDK version: 3.4.3 (stable) (Tue Jun 4 19:51:39 2024 +0000) on "macos_arm64"
zhangshipeng@zhangshipengdeMacBook-Pro RPM-APP % flutter --version
Flutter 3.32.8 • channel stable • https://github.com/flutter/flutter.git
Framework • revision edada7c56e (3 days ago) • 2025-07-25 14:08:03 +0000
Engine • revision ef0cd00091 (3 days ago) • 2025-07-24 12:23:50 -0700
Tools • Dart 3.8.1 • DevTools 2.45.1
zhangshipeng@zhangshipengdeMacBook-Pro RPM-APP % dart --version   
Dart SDK version: 3.8.1 (stable) (Wed May 28 00:47:25 2025 -0700) on "macos_arm64"
zhangshipeng@zhangshipengdeMacBook-Pro RPM-APP % 

http://msub.lyf520.xyz/api/v1/client/subscribe?token=b0e32d776b4261cc8249e0b52c2edd57