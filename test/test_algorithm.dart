/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-16 16:24:36
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-20 01:26:11
 * @FilePath: /rpmappmaster/test/test_algorithm.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:collection';

// import 'package:get/get.dart';

void main() {
  LinkedList list = LinkedList();
  list.add(1);
  list.add(2);
  list.add(3);
  list.add(4);

  print("原始链表:");
  list.printList();

  list.swapPairs2();

  print("交换后的链表:");
  list.printList();

  LinkedStringList list2 = LinkedStringList();
  list2.fromString("hello");
  print(list2.toString());
  list2.reverse();
  print(list2.toString());

}

//如何判断单链表是否有坏
//方法一：循环遍历节点，遍历一个便标记一个，遍历过程判断是否被标记，若已被标记则表示有坏
//方法说明：头指针移动，若到达之前到达过的位置则表示有坏，若无坏则会走到链表末端
isLinkListBroken1(Node head) {
  Set<Node> set = new HashSet();

  Node? current = head;
  while (current != null) {
    if (set.contains(current)) {
      return true;
    } else {
      set.add(current);
      current = current.next!;
    }
  }
  return false;
}

//方法二：声明两个指针，一个指针头一次经过两个节点（快指针quick），另一个走一次经过一个节点（慢指针slow）
//方法说明：快指针走的比较快，若链表有坏，则一定会追上慢指针，若无坏，则会走到链表末端
isLinkListBroken2(Node head) {
  //  声明两个节点从头开始遍历节点
  Node? quick = head;
  Node? slow = head;
  while (quick != null && quick.next != null) {
    quick = quick.next?.next;
    slow = slow?.next;
    if (quick == slow) {
      return true;
    }
  }
  return false;
}

//节点类
class Node {
  int data; //数据部分
  Node? next; //指向下一个节点的指针

  Node(this.data, {this.next});
}

//单链表类
class LinkedList {
  Node? head; //链表的头节点

  //构造函数
  LinkedList();

  //向链表添加一个节点
  void add(int value) {
    Node newNode = Node(value);
    if (head == null) {
      head = newNode;
    } else {
      Node current = head!;
      while (current.next != null) {
        current = current.next!;
      }
      current.next = newNode;
    }
  }

  //打印链表中的所有元素
  void printList() {
    if (head == null) {
      print("链表为空");
      return;
    }
    Node? current = head; // current 允许为 null
    while (current != null) {
      print(current.data); // 打印当前节点数据
      current = current.next; // 移动到下一个节点
    }
  }

  //删除链表中的指定值
  void delete(int value) {
    if (head == null) {
      // print(current)
      print("链表为空");
      return;
    }
    if (head!.data == value) {
      head = head!.next;
    }
    Node current = head!;
    while (current.next != null && current.next!.data != value) {
      current = current.next!;
    }

    //如果找到要删除的节点
    if (current.next != null) {
      current.next = current.next!.next;
    } else {
      print("没有找到值为 $value 的节点");
    }
  }

  //查找链表中的指定值
  bool contains(int value) {
    if (head == null) {
      print("链表为空");
    }

    Node current = head!;
    while (current.next != null) {
      if (current.data == value) return true;
      current = current.next!;
    }
    return false;
  }

  //获取链表的长度
  int length() {
    int count = 0;
    if (head == null) return count;
    Node current = head!;
    while (current.next != null) {
      count++;
      current = current.next!;
    }
    return count;
  }

  // 相邻节点交换
  void swapPairs1() {
    if (head == null || head!.next == null) return;

    Node? dumpy = Node(0, next: head);
    Node? current = dumpy;

    while (current?.next != null && current?.next!.next != null) {
      Node? first = current?.next;
      Node? second = current?.next!.next;

      first!.next = second!.next;
      second!.next = first;
      current?.next = second;

      //移动到下一个节点
      current = first;
    }

    head = dumpy.next;
  }

  // 使用值交换相邻节点
  void swapPairs2() {
    Node? current = head;
    while (current != null && current.next != null) {
      // 交换当前节点和下一个节点的值
      int temp = current.data;
      current.data = current.next!.data;
      current.next!.data = temp;

      // 移动到下一对
      current = current.next!.next;
    }
  }
}

//利用栈进行字符串反转
String reverseWithStack(String input) {
  List<String> stack = []; // 用 List 作为栈
  for (int i = 0; i < input.length; i++) {
    stack.add(input[i]); // 将每个字符压入栈
  }

  StringBuffer reversed = StringBuffer(); // 用 StringBuffer 构建结果
  while (stack.isNotEmpty) {
    reversed.write(stack.removeLast()); // 从栈中弹出字符
  }

  return reversed.toString();
}

//利用chartAt实现字符串反转
String reverseWithCharAt(String input) {
  StringBuffer reversed = StringBuffer();
  for (int i = input.length - 1; i >= 0; i--) {
    reversed.write(input[i]); // 从后向前逐个字符添加到结果中
  }
  return reversed.toString();
}

class NodeString {
  String data;
  NodeString? next;

  NodeString(this.data, {this.next});
}

class LinkedStringList {
  NodeString? head;

  // 从字符串创建链表
  void fromString(String input) {
    head = null;  //hello
    for (int i = input.length - 1; i >= 0; i--) {
      NodeString newNode = NodeString(input[i], next: head);
      head = newNode;
    }
  }

  // 将链表转换回字符串
  String toString() {
    StringBuffer result = StringBuffer();
    NodeString? current = head;
    while (current != null) {
      result.write(current.data);
      current = current.next;
    }
    return result.toString();
  }

  // 反转链表
  void reverse() {
    NodeString? prev = null;
    NodeString? current = head;
    while (current != null) {
      NodeString? nextNode = current.next; // 暂存下一节点
      current.next = prev; // 反转指针
      prev = current; // 移动 prev
      current = nextNode; // 移动 current
    }
    head = prev; // 更新头节点
  }
}

int parseInt(String s, [int radix = 10]) {
  if (radix < 2 || radix > 36) {
    throw FormatException("Radix must be between 2 and 36");
  }

  int result = 0;
  int i = 0;
  int digit;
  bool negative = false;
  int length = s.length;

  if (length > 0) {
    // 获取第一个字符
    var firstChar = s[0];

    // 处理正负号
    if (firstChar == '-') {
      negative = true;
      i++;
    } else if (firstChar == '+') {
      i++;
    }

    // 确保符号后有数字
    if (length == i) {
      throw FormatException("Invalid input: $s");
    }

    while (i < length) {
      // 将字符转换为对应的数字
      digit = int.tryParse(s[i]) ?? -1;

      // 检查 digit 是否在指定的进制范围内
      if (digit < 0 || digit >= radix) {
        throw FormatException("Invalid digit '${s[i]}' for radix $radix");
      }

      // 更新结果值
      result *= radix;
      result += digit;
      i++;
    }
  } else {
    throw FormatException("Invalid input: $s");
  }

  return negative ? -result : result;
}





