<svg width="168" height="172" viewBox="0 0 168 172" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_iii_581_688)">
<circle cx="67" cy="105" r="67" fill="url(#paint0_linear_581_688)" fill-opacity="0.01"/>
</g>
<g opacity="0.65" filter="url(#filter1_f_581_688)">
<circle cx="60.5" cy="143.5" r="3.5" fill="white"/>
</g>
<g opacity="0.65" filter="url(#filter2_f_581_688)">
<circle cx="72" cy="159" r="5" fill="white"/>
</g>
<g opacity="0.65" filter="url(#filter3_f_581_688)">
<circle cx="73" cy="135" r="2" fill="white"/>
</g>
<g opacity="0.25" filter="url(#filter4_if_581_688)">
<circle cx="35.5" cy="35.5" r="35.5" transform="matrix(-1 0 0 1 136 32)" fill="white"/>
<circle cx="35.5" cy="35.5" r="35" transform="matrix(-1 0 0 1 136 32)" stroke="url(#paint1_linear_581_688)"/>
</g>
<defs>
<filter id="filter0_iii_581_688" x="0" y="38" width="142" height="142" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_581_688"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="9"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.554577 0 0 0 0 1 0 0 0 0 0.302995 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_581_688" result="effect2_innerShadow_581_688"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.69661 0 0 0 0 1 0 0 0 0 0.532389 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_581_688" result="effect3_innerShadow_581_688"/>
</filter>
<filter id="filter1_f_581_688" x="56.5" y="139.5" width="8" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.25" result="effect1_foregroundBlur_581_688"/>
</filter>
<filter id="filter2_f_581_688" x="66.5" y="153.5" width="11" height="11" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.25" result="effect1_foregroundBlur_581_688"/>
</filter>
<filter id="filter3_f_581_688" x="70.5" y="132.5" width="5" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.25" result="effect1_foregroundBlur_581_688"/>
</filter>
<filter id="filter4_if_581_688" x="33" y="0" width="135" height="135" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_581_688"/>
<feGaussianBlur stdDeviation="16" result="effect2_foregroundBlur_581_688"/>
</filter>
<linearGradient id="paint0_linear_581_688" x1="67.4891" y1="41.4311" x2="67.4891" y2="172.302" gradientUnits="userSpaceOnUse">
<stop stop-color="#30FF1E"/>
<stop offset="1" stop-color="#1ED90D"/>
</linearGradient>
<linearGradient id="paint1_linear_581_688" x1="35.7351" y1="25.1556" x2="35.7351" y2="94.9801" gradientUnits="userSpaceOnUse">
<stop stop-color="#49F23B"/>
<stop offset="1" stop-color="#85ED00"/>
</linearGradient>
</defs>
</svg>
