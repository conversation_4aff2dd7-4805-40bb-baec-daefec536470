#!/bin/bash

# 上传所有 dSYM 文件到 Firebase Crashlytics
# 使用方法: ./upload_dsyms.sh

set -e

UPLOAD_SCRIPT="ios/Pods/FirebaseCrashlytics/upload-symbols"
GSP_FILE="ios/Runner/GoogleService-Info.plist"
PROJECT_PATH="ios"

echo "开始上传 dSYM 文件到 Firebase Crashlytics..."

# 上传构建目录中的 dSYM 文件
echo "=== 上传 Release 构建的 dSYM 文件 ==="
if [ -d "build/ios/Release-iphoneos" ]; then
    find "build/ios/Release-iphoneos" -name "*.dSYM" -type d | while read dsym_path; do
        echo "正在上传: $dsym_path"
        if "$UPLOAD_SCRIPT" -gsp "$GSP_FILE" -p "$PROJECT_PATH" "$dsym_path"; then
            echo "✅ 成功上传: $dsym_path"
        else
            echo "❌ 上传失败: $dsym_path"
        fi
        echo "---"
    done
fi

# 上传 Debug 构建的 dSYM 文件
echo "=== 上传 Debug 构建的 dSYM 文件 ==="
if [ -d "build/ios/Debug-iphoneos" ]; then
    find "build/ios/Debug-iphoneos" -name "*.dSYM" -type d | while read dsym_path; do
        echo "正在上传: $dsym_path"
        if "$UPLOAD_SCRIPT" -gsp "$GSP_FILE" -p "$PROJECT_PATH" "$dsym_path"; then
            echo "✅ 成功上传: $dsym_path"
        else
            echo "❌ 上传失败: $dsym_path"
        fi
        echo "---"
    done
fi

# 上传归档文件中的 dSYM 文件
echo "=== 上传归档文件中的 dSYM 文件 ==="
if [ -d "ios/Runner.xcarchive/dSYMs" ]; then
    find "ios/Runner.xcarchive/dSYMs" -name "*.dSYM" -type d | while read dsym_path; do
        echo "正在上传: $dsym_path"
        if "$UPLOAD_SCRIPT" -gsp "$GSP_FILE" -p "$PROJECT_PATH" "$dsym_path"; then
            echo "✅ 成功上传: $dsym_path"
        else
            echo "❌ 上传失败: $dsym_path"
        fi
        echo "---"
    done
fi

echo "所有 dSYM 文件上传完成！"
echo ""
echo "注意：如果 Firebase 控制台仍显示缺失的 dSYM 文件，这些可能是："
echo "1. 来自之前版本的构建"
echo "2. 来自 App Store Connect/TestFlight 的构建"
echo "3. 标记为'可选'的文件，不影响崩溃报告的符号化"
