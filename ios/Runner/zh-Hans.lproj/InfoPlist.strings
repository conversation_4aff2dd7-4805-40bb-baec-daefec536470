/* 
  InfoPlist.strings
  Runner

  Created by 张仕鹏 on 2025/2/26.
  
*/
NSCameraUsageDescription = "We need access to your camera to take photos.";
NSLocationWhenInUseUsageDescription = "We need to access your location to scan and connect nearby Bluetooth devices (such as smart health devices). This information is only used for device pairing and health data synchronization, and will not be used to track user location.";
NSLocalNetworkUsageDescription = "We need to access your local network in order to use the full application functionality.";

