import Foundation
import Flutter
import UIKit
import AHDevicePlugin

class AojStreamHandler: NSObject, FlutterStreamHandler {
    // 用 static 方便全局访问
    static let shared = AojStreamHandler()
    private override init() {}

    // eventSink 是一个回调，用于向 Flutter 发送数据
    var eventSink: FlutterEventSink?

    // onListen: Flutter 开始监听时，只保存 eventSink，不自动开始扫描
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.eventSink = events
        return nil
    }

    // onCancel: Flutter 取消监听时，清理 eventSink
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        self.eventSink = nil
        return nil
    }

    // 由外部调用，推送扫描到的设备
    func pushDevice(_ device: BTDeviceInfo) {
        guard let sink = self.eventSink else { return }
//        print("deviceasdasdas:"+device.toString())
        print(device.deviceId)
        print("deviceType:"+"\(device.deviceType.rawValue)")
        var name = "";
        switch(device.deviceType.rawValue){
        case 3:
            name = "AOJ-30B"
        default:
            name = ""
        }
        let deviceData: [String: String] = [
            "name": name ?? "",
            "macAddress": device.macAddress ?? "",
            "broadcastID": device.broadcastId ?? "",
            "deviceType": "\(device.deviceType.rawValue ?? 0)",
            "rssi": "\(device.rssi ?? 0)"
        ]
        sink(deviceData)
    }
}
