//
//  MyConvert.swift
//  Runner
//
//  Created by 张仕鹏 on 2024/11/5.
//

import Foundation

class MyConvert{
    
    static func convertMeasureResultToDictionary(_ measureResult: BTMeasureResult) -> [String: Any] {
        return [
            "measurementTime": measureResult.measurementTime,
            "measurementResult": measureResult.measurementResult,
            "measureType": measureResult.measureType.rawValue // 如果是枚举，需要转换为原始值
        ]
    }
}
