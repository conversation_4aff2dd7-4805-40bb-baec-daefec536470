import Flutter
import UIKit

class InstantMeasurementStreamHandler: NSObject, FlutterStreamHandler {
    private var eventSink: FlutterEventSink?
    
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.eventSink = events
        
        // 开始测量的逻辑
        if let args = arguments as? [String: Any],
           let type = args["type"] as? Int,
           let operation = args["operation"] as? Int {
            
            var cmd: DeviceCommand
            switch type {
            case 1:
                cmd = .measureHr
            case 2:
                cmd = .measureSpo2
            case 6:
                cmd = .measureTemp
            default:
                return FlutterError(code: "INVALID_ARGUMENT", message: "Invalid measurement type", details: nil)
            }
            
            // 检查设备是否已连接
            let isconn = BTService.shared().isConnected
                        if !BTService.shared().isConnected {
                            events(FlutterError(code: "DEVICE_NOT_CONNECTED", message: "Device is not connected. Please connect the device first.", details: nil))
                                            return nil
                                        
                        }
            
            startMeasurement(cmd: cmd)
        } else {
            events(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid arguments", details: nil))
                        return nil
        }
        
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        // 停止测量并清除 eventSink
        eventSink = nil
        return nil
    }
    
    private func startMeasurement(cmd: DeviceCommand) {
        guard let type = cmd.toMeasurementType() else {
            return
        }
        
        // 使用设备服务开始测量
        BTService.shared().measureReq.startMeasurement(with: type) { [weak self] isStarted in
            if let eventSink = self?.eventSink {
                if isStarted {
                    eventSink(["event": "started", "message": "\(cmd.rawValue) measurement started..."])
                } else {
                    eventSink(FlutterError(code: "MEASUREMENT_FAILED", message: "Measurement could not be started. Please try again.", details: nil))
                }
            }
        } acked: { [weak self] isMeasuredSuccess in
            if let eventSink = self?.eventSink {
                if isMeasuredSuccess {
                    eventSink(["event": "inProgress", "message": "\(cmd.rawValue) measurement in progress..."])
                } else {
                    eventSink(FlutterError(code: "MEASUREMENT_FAILED", message: "Measurement failed. Please try again.", details: nil))
                }
            }
        } resultReceived: { [weak self] data in
            if let eventSink = self?.eventSink {
                if data.measurementResult == 0 || data.measurementResult == -1 {
                    eventSink(FlutterError(code: "MEASUREMENT_FAILED", message: "Measurement failed. Please try again.", details: nil))
                } else {
                    // 将数据转换为 JSON 字符串或字典发送到 Flutter
                    let measureDict = MyConvert.convertMeasureResultToDictionary(data)
                    eventSink(["event": "result", "data": measureDict])
                }
            }
        }
    }
}
