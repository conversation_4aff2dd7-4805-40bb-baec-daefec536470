import Foundation

class BatteryStatusStream: NSObject, FlutterStreamHandler {
    private var eventSink: FlutterEventSink?
    // 缓存戒指的电池状态（可选）
    var rings: [Ring] = []
    
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.eventSink = events
        
        // 设置 BTService 的电池状态回调
        BTService.shared().chargeHandler = { [weak self] powerModel in
            guard let eventSink = self?.eventSink else { return }
            
            // 根据不同的充电状态，设置相应的状态
            let chargingState: String
            
            print("ios查看电池状态")
            print(powerModel.chargingState)
            
            switch powerModel.chargingState {
            case 0:
                chargingState = "NOT_CHARGING"
            case 1:
                chargingState = "CHARGING"
            case 2:
                chargingState = "FULLY_CHARGED"
            default:
                chargingState = "UNKNOWN"
            }
            
            // 直接返回包含电量和充电状态的列表
            eventSink([
                powerModel.batteryLevel,  // 电量
                chargingState            // 充电状态
            ])
        }
        
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        // 停止监听，清除 eventSink
        eventSink = nil
        return nil
    }
}
