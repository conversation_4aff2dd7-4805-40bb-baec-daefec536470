//
//  BatteryStatusStream.swift
//  Runner
//
//  Created by 张仕鹏 on 2024/11/1.
//

import Foundation

class ScanningStatusStream: NSObject, FlutterStreamHandler {
    private var eventSink: FlutterEventSink?
    // Cached rings from search
    // 搜索到的戒指对象缓存
    var rings: [Ring] = []
    
    // 搜索结果更新回调
    var onSearchUpdated: (([Ring]) -> Void)?
    
    // 戒指状态更新回调
    var onRingStatusChanged: ((String) -> Void)?
    
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.eventSink = events
        
        
        
        // 开始扫描并配置设备发现回调
        startScanning()
        
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        // 停止扫描和清理 eventSink
        BTService.shared().stopScan()
        eventSink = nil
        return nil
    }
    
    private func startScanning() {
        BTService.shared().startScan()
        
        // 假设 `BTService.shared().onDeviceFound` 是一个用于设备发现的回调
        BTService.shared().scanningDeviceUpdated  { [weak self] rings in
            self?.sendDeviceToFlutter(rings: rings)
        }
    }
    
    // 将搜索到的设备发送给 Flutter
    private func sendDeviceToFlutter(rings: [Ring]) {
//        if let eventSink = eventSink {
//            let devicesData = rings.map { ring in
//                return [
//                    "name": ring.name,
//                    "uuidString": ring.uuidString,
//                    "macAddress": ring.macAddress ?? "N/A",
//                    "rssi": ring.rssi
//                ]
//            }
//            eventSink(["event": "aizoDeviceFound", "data": devicesData])
//        }
        if let eventSink = eventSink {
            let devicesData = rings.map { ring in
//                return [
//                    "name": ring.name,
//                    "uuidString": ring.uuidString,
//                    "macAddress": ring.macAddress ?? "N/A",
//                    "rssi": ring.rssi
//                ]
                return ring.toDictionary()
            }
            eventSink(["event": "aizoDeviceFound", "data": devicesData])
        }
    }
}
