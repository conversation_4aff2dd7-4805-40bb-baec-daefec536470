//
//  RingStatusStream.swift
//  Runner
//
//  Created by 张仕鹏 on 2024/11/4.
//

import Foundation

class RingStatusStream: NSObject, FlutterStreamHandler {
    private var eventSink: FlutterEventSink?
    // Cached rings from search
    // 搜索到的戒指对象缓存
    var rings: [Ring] = []
    
    // 搜索结果更新回调
    var onSearchUpdated: (([Ring]) -> Void)?
    
    // 戒指状态更新回调
    var onRingStatusChanged: ((String) -> Void)?
    
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.eventSink = events
        
        // 开始扫描并配置设备发现回调
        startRingMonitior()
        
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        // 停止扫描和清理 eventSink
        eventSink = nil
        return nil
    }
    
    private func startRingMonitior() {
        
        BTService.shared().ringStatusUpdated { [weak self] status in
            self?.updateRingStatus(status)
        }
    }
    
    // 将搜索到的设备发送给 Flutter
    private func updateRingStatus(_ status: BTDeviceStatus) {
        if let eventSink = eventSink {
            let statusDesc = getStatusDesc(status)
            eventSink(["event": "aizoRingStatus", "data": statusDesc])
        }
    }
    
    func getStatusDesc(_ status: BTDeviceStatus) -> String {
        switch status {
        case .disconnected:     return "DISCONNECTED"
        case .connecting:       return "CONNECTING"
        case .connected:        return "CONNECTED"
        case .connectFailed:    return "CONNECT_FAILED"
        case .authRefused:      return "AUTH_REFUSED"
        case .authSuccess:      return "AUTH_SUCCESS"
        case .authBound:        return "AUTH_BOUND"
        case .authIllegal:      return "AUTH_ILLEGAL"
        case .vendorIdDifferent:return "VENDOR_ID_DIFFERENT"
        case .appIdDifferent:   return "APP_ID_DIFFERENT"
        case .off:              return "OFF"
        case .noPair:           return "NO_PAIR"
        case .pairingRemoved:   return "PAIRING_REMOVED"
        @unknown default:       return "UNKNOWN"
        }
    }
    
}
