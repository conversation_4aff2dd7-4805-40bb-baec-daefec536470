//
//  AHDevicePlugin.h
//  AHDevicePlugin
//
//  Created by caichixiang on 2020/6/30.
//  Copyright © 2020 sky. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AHDevicePlugin.h"
#import "AHDevicePlugin+Push.h"
#import "AHDevicePlugin+Sync.h"
#import "AHDevicePlugin+Pair.h"

//! Project version number for LSBluetoothPlugin.
FOUNDATION_EXPORT double AHDevicePluginVersionNumber;

//! Project version string for LSBluetoothPlugin.
FOUNDATION_EXPORT const unsigned char AHDevicePluginVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <AHDevicePlugin/PublicHeader.h>


#pragma mark - Current Verion 1.0.0

/**
 * SDK 支持的系统架构
 *  armv7,
 *  armv7s,
 *  arm64,
 *  arm64e,
 */
