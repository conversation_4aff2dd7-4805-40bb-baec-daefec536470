/// RingDef.h
/// RingSDK
//
/// Created by <PERSON><PERSON><PERSON> on 2024/05/13.

#pragma mark - Pairing & Connection 配对连接
/// 定义戒指配对各状态：连接与鉴权。Defines states of ring pairing: connection and authentication.
typedef NS_ENUM(NSInteger, BTDeviceStatus) {
    /// 已断开。disconnected
    BTDeviceStatusDisconnected = -7,
    /// 终端移除配对信息。Terminal removed pairing info.
    BTDeviceStatusPairingRemoved = -6,
    /// 设备关闭。Device off.
    BTDeviceStatusOff = -5,
    /// 未绑定设备。Unbound device.
    BTDeviceStatusNoPair = -4,
    /// 连接中。Connecting.
    BTDeviceStatusConnecting = -3,
    /// 已连接。Connected.
    BTDeviceStatusConnected = -2,
    /// 连接失败。Connection failed.
    BTDeviceStatusConnectFailed = -1,
    /// 戒指端点击拒绝。Watch side refused.
    BTDeviceStatusAuthRefused = 0x00,
    /// 鉴权成功。Authentication successful.
    BTDeviceStatusAuthSuccess = 0x01,
    /// 失败已被其他手机绑定。Failed, already bound to another phone.
    BTDeviceStatusAuthBound = 0x02,
    /// 非法厂商。Illegal manufacturer.
    BTDeviceStatusAuthIllegal = 0x03,
    /// 厂商ID不一致。Vendor ID mismatch.
    BTDeviceStatusVendorIdDifferent = 0x13,
    /// App的ID不一致。App ID mismatch.
    BTDeviceStatusAppIdDifferent = 0x23
};

#pragma mark - Device Info & Control 设备信息和控制
/// 充电状态。Charging statuses.
typedef NS_ENUM(NSInteger, BTChargeStatus) {
    /// 充电中。Charging.
    BTChargeStatusCharging,
    /// 未充电。Not charging.
    BTChargeStatusNotCharging
};

/// 设备操作类型。Device operation types.
typedef NS_ENUM(NSUInteger, BTOperationType) {
    /// 重置。Reset.
    BTOperationTypeReset = 0x01,
    /// 解绑。Unbind.
    BTOperationTypeUnbind = 0x02,
    /// 重启。Restart.
    BTOperationTypeRestart = 0x04
};

/// 设备配置类型。Device configuration types.
typedef NS_ENUM(Byte, BTConfigType) {
    /// 设置。Settings.
    BTConfigTypeSettings = 0x01,
    /// 状态。Status.
    BTConfigTypeStatus = 0x02,
    /// 信息。Information.
    BTConfigTypeInformation = 0x03,
    /// 功能列表。Features List.
    BTConfigTypeFeatures = 0x04
};

#pragma mark - OTA Firmware Upgrade 固件升级
/// OTA更新状态。OTA update statuses.
typedef NS_ENUM(NSInteger, BTOTAUpdateStatus) {
    /// 准备中。Preparing.
    BTOTAUpdateStatusPreparing,
    /// 解析文件。Parsing file.
    BTOTAUpdateStatusParsingFile,
    /// 开始升级。OTA started.
    BTOTAUpdateStatusStarted,
    /// 更新中。In progress.
    BTOTAUpdateStatusProgress,
    /// 成功。Success.
    BTOTAUpdateStatusSuccess,
    /// 失败。Failed.
    BTOTAUpdateStatusFailed,
    /// 未连接。Not connected.
    BTOTAUpdateStatusNotConnected,
    /// 文件无效。Invalid file.
    BTOTAUpdateStatusFileNotValid,
    /// 下载中。Downloading.
    BTOTAUpdateStatusDownloading
};

#pragma mark - Touch 触控
/// 触控模式。Touch modes.
typedef NS_ENUM(UInt8, BTTouchMode) {
    /// 音乐模式。Music Mode.
    BTTouchModeMusic = 0x01,
    /// 拍照模式。Photo Mode.
    BTTouchModePhoto = 0x02,
    /// 视频模式。Video Mode.
    BTTouchModeVideo = 0x03,
    /// 阅读模式(左右翻页)。Reading Mode (Left-Right Page Turning).
    BTTouchModeReadingLR = 0x04,
    /// 阅读模式(上下滑动)。Reading Mode (Up-Down Scrolling).
    BTTouchModeReadingUD = 0x05,
    /// 幻灯片模式。Slide Show Mode.
    BTTouchModeSlideShow = 0x06,
    /// 电视遥控模式。TV Remote Control Mode.
    BTTouchModeTVRemote = 0x07,
    /// 网页浏览模式。Web Browsing Mode.
    BTTouchModeWebBrowsing = 0x08,
    /// 魔方模式。Magic Cube Mode.
    BTTouchModeMagicCube = 0x09,
    /// 传情模式。Sentimental Mode.
    BTTouchModeSentimental = 0x11,
    /// 自定义模式。Custom Mode.
    BTTouchModeCustom = 0xA1
};

//// 触控状态。Touch state.
typedef NS_ENUM(UInt8, BTTouchState) {
    /// 关闭。Off.
    BTTouchStateOff = 0x00,
    /// 打开。On.
    BTTouchStateOn = 0x01
};

#pragma mark - Measure 测量
/// 测量类型。Measurement types.
typedef NS_ENUM(UInt8, BTMeasureType) {
    /// 心率。Heart Rate.
    BTMeasureTypeHr = 0x01,
    /// 血氧。Blood Oxygen.
    BTMeasureTypeSpo2 = 0x02,
    /// 血压(不支持)。Blood Pressure (unsupported).
    BTMeasureTypeBp = 0x03,
    /// 心电。ECG.
    BTMeasureTypeEcg = 0x04,
    /// 呼吸(不支持)。Respiration (unsupported).
    BTMeasureTypeResp = 0x05,
    /// 体温。Body Temperature.
    BTMeasureTypeTemp = 0x06,
    /// 身体成份。Body Composition.
    BTMeasureTypeBodyComp = 0x07
};

/// 睡眠类型。Sleep modes.
typedef NS_ENUM(NSUInteger, SleepMode) {
    /// 浅睡。Light sleep.
    SleepModeLight = 0x01,
    /// 深睡。Deep sleep.
    SleepModeDeep = 0x02,
    /// 清醒。Awake.
    SleepModeAwake = 0x03,
    /// 未配戴。Not worn.
    SleepModeNotWorn = 0x04,
    /// 快速眼动。Rapid Eye Movement.
    SleepModeREM = 0x05,
    /// 关机。Shutdown.
    SleepModeShutdown = 0x07
};

/// 运动类型。Types of activities.
typedef NS_ENUM(Byte, BTSportType) {
    /// 室内步行。Indoor walking.
    BTSportTypeIndoorWalk = 0x05,

    /// 室内单车。Indoor biking.
    BTSportTypeIndoorBike = 0x07,

    /// 室内跑步。Indoor running.
    BTSportTypeIndoorRun = 0x08,

    /// 室外步行。Outdoor walking.
    BTSportTypeOutdoorWalk = 0x09,

    /// 室外跑步。Outdoor running.
    BTSportTypeOutdoorRun = 0x0A,

    /// 室外骑行。Outdoor biking.
    BTSportTypeOutdoorBike = 0x0B
};

/// 运动操作类型。Types of activities.
typedef NS_ENUM(UInt16, BTSportOperationType) {
    /// 开始 - Start
    BTSportOperationTypeStart = 0x9611,

    /// 暂停 - Pause
    BTSportOperationTypePause = 0x9612,

    /// 恢复 - Resume
    BTSportOperationTypeResume = 0x9613,

    /// 停止 - Stop
    BTSportOperationTypeStop = 0x9614,
};

/// 运动的不同状态。Enumeration for different sports statuses.
typedef NS_ENUM(NSUInteger, BTSportStatusType) {
    /// 已开始 - Started
    BTSportStatusTypeStarted = 0,
    
    /// 开始失败 - Start Failed
    BTSportStatusTypeStartFailed = 1,
    
    /// 已暂停 - Paused
    BTSportStatusTypePaused = 2,
    
    /// 暂停失败 - Pause Failed
    BTSportStatusTypePauseFailed = 3,
    
    /// 已恢复 - Resumed
    BTSportStatusTypeResumed = 4,
    
    /// 恢复失败 - Resume Failed
    BTSportStatusTypeResumeFailed = 5,
    
    /// 已停止 - Stopped
    BTSportStatusTypeStopped = 6,
    
    /// 停止失败 - Stop Failed
    BTSportStatusTypeStopFailed = 7,
    
    /// 运动中 - Active. 若运动中，需先停再开始新的运动。If active, stop before starting a new activity.
    BTSportStatusTypeActive = 8,
    
    /// 数据未上传 - 如果数据未上传，需要先同步数据再开始新的运动。Data Not Uploaded. Sync data before starting a new activity.
    BTSportStatusTypeUnsynced = 9,

    /// 没有运动 - No Sport. 表示没有正在进行的运动。Indicates no sport is ongoing.
    BTSportStatusTypeNoSport = 10
};
