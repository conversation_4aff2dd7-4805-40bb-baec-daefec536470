//
//  BTMeasure.h
//  RingSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2024/5/27.
//

#import <Foundation/Foundation.h>
#import <RingSDK/BTMeasureResult.h>
#import <RingSDK/BTECGPacket.h>

NS_ASSUME_NONNULL_BEGIN

// 测量完成回调
// Measurement completion handler
typedef void (^BTMeasureCompletionHandler)(BTMeasureResult *result);

/// 测量控制类，用于启动和停止测量
/// Measurement control class for starting and stopping measurements
@interface BTMeasure : NSObject

/// 开始测量指定类型的数据
/// Start measuring specified type of data
/// @param type 测量类型。Type of measurement.
/// @param sentCompletion 指令发送回调。Callback when the command is sent.
/// @param ackCompletion 确认回调。Callback when the command is acknowledged.
/// @param resultCompletion 结果回调。Callback when the result is received.
/// @discussion YES表示成功发送指令，NO表示重复发送或发送失败。YES indicates the command was sent successfully, NO indicates a duplicate or failure.
- (BOOL)startMeasurementWithType:(BTMeasureType)type
                    commandSent:(void (^)(BOOL sent))sentCompletion
                        acked:(void (^)(BOOL acked))ackCompletion
                  resultReceived:(void (^)(BTMeasureResult * result))resultCompletion;

/// 停止指定类型的测量
/// Stop measuring a specified type of data
/// @param type 测量类型。Type of measurement.
/// @param completion 确认回调。Callback when the command is acknowledged.
/// @discussion YES表示成功发送指令，NO表示重复发送或发送失败。YES indicates the command was sent successfully, NO indicates a duplicate or failure.
- (BOOL)stopMeasurementWithType:(BTMeasureType)type completion:(void (^)(BOOL ackReceived))completion;


/// 切换到ECG延迟模式
/// Switch to ECG latency mode
/// @param completion 确认回调。Callback when the command is acknowledged.
/// @discussion YES表示成功发送指令，NO表示发送失败。YES indicates the command was sent successfully, NO indicates a failure.
- (BOOL)sendECGLatencyModeWithStatus:(BOOL)isEnable Completion:(void (^)(BOOL success))completion;

/// 开始ECG测量
/// Start ECG measurement
/// @param completion 确认回调。Callback when the command is acknowledged.
/// @param dataBlock 数据回调。Callback when a batch of data packets is received.
/// @param dataEndBlock 数据结束回调。Callback when the data collection is complete.
/// @discussion YES表示成功发送指令并接收到ACK，NO表示发送失败或ACK失败。YES indicates the command was sent successfully and ACK received, NO indicates a failure.
- (BOOL)startECGWithCompletion:(void (^)(BOOL success))completion
                         data:(void (^)(NSArray<BTECGPacket *> *pkts))dataBlock
                      dataEnd:(void (^)(NSArray<BTECGPacket *> *allPkts))dataEndBlock;

@end

NS_ASSUME_NONNULL_END
