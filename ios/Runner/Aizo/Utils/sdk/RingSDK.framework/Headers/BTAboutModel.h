#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// `BTAboutModel` 类封装了蓝牙设备配置的详细信息。
/// `BTAboutModel` class encapsulates detailed information about a Bluetooth device's configuration.
@interface BTAboutModel : NSObject

/// 蓝牙及其版本，基于数据的整数值。
/// Bluetooth and its version based on integer value from data.
@property (nonatomic, assign) NSInteger bluetoothVersion;

/// 表盘形状类型。
/// Dial shape type derived from data.
@property (nonatomic, assign) NSInteger dialShape;

/// WiFi支持能力。
/// Support for WIFI capabilities.
@property (nonatomic, assign) NSInteger supportWIFI;

/// 手表类型。
/// Type of watch as defined by the data.
@property (nonatomic, assign) NSInteger watchType;

/// 定位类型支持。
/// Type of positioning supported by the device.
@property (nonatomic, assign) NSInteger positionType;

/// 传感器类型。
/// Sensor types supported by the device.
@property (nonatomic, assign) NSInteger sensorType;

/// 是否支持气压传感器。
/// Support for air pressure sensor.
@property (nonatomic, assign) NSInteger isSupportAirPressure;

/// 是否支持湿度传感器。
/// Support for humidity sensor.
@property (nonatomic, assign) NSInteger isSupportHumidity;

/// 是否支持天气状况。
/// Support for weather conditions.
@property (nonatomic, assign) NSInteger isSupportWeather;

/// 是否支持触摸屏。
/// Support for touchscreen functionality.
@property (nonatomic, assign) NSInteger isSupportTouchScreen;

/// 存储容量，可能以MB为单位。
/// Storage capacity, possibly in MB, derived from the data.
@property (nonatomic, assign) NSInteger storageCapacity;

/// 是否支持闹钟功能。
/// Support for alarm clock functionality.
@property (nonatomic, assign) NSInteger isSupportAlarmClock;

/// 定时开关机支持。
/// Support for timed power on/off.
@property (nonatomic, assign) NSInteger timingBoot;

/// 是否支持语音提醒。
/// Support for voice reminders.
@property (nonatomic, assign) NSInteger isSupportVoiceRemind;

/// 是否支持定位模式设置。
/// Support for different positioning modes.
@property (nonatomic, assign) NSInteger isSupportPositionMode;

/// 目标市场。
/// Target market for the device.
@property (nonatomic, strong) NSString *targetMarket;

/// 终端平台。
/// Terminal platform used by the device.
@property (nonatomic, strong) NSString *terminalPlatform;

/// 显示屏特性，包括类型和分辨率。
/// Display characteristics of the screen including type and resolution.
@property (nonatomic, strong) NSString *displayScreen;

/// 软件版本。
/// Software version of the device.
@property (nonatomic, strong) NSString *softwareVersion;

/// 硬件版本。
/// Hardware version of the device.
@property (nonatomic, strong) NSString *hardwareVersion;

/// 项目整机编码。
/// Project code representing the entire machine or system.
@property (nonatomic, strong) NSString *projectCode;

/// 资源包版本号。
/// Resource version number, typically representing firmware or software resources.
@property (nonatomic, strong) NSString *resourceVersion;

/// 预留信息。
/// Reserved information for future use or device-specific data.
@property (nonatomic, strong) NSString *reservedInfo;

/// UI系统版本及详细信息。
/// UI system version and details.
@property (nonatomic, strong) NSString *uiSystem;

/// 客户编码。
/// Customer code or identifier, often used for customization or tracking.
@property (nonatomic, strong) NSString *customerCode;

/// 电池型号编码。
/// Battery model code, indicating the type of battery used.
@property (nonatomic, strong) NSString *batteryModelCode;

/// 设备序列号。
/// Serial number of the device.
@property (nonatomic, strong) NSString *serialNumber;

/// 是否支持双模蓝牙。
/// Indicates if the device supports dual-mode Bluetooth.
@property (nonatomic, strong) NSString *dualModeBluetooth;

/// 是否具有双蓝牙芯片。
/// Indicates if the device has dual Bluetooth chips.
@property (nonatomic, strong) NSString *dualChipBluetooth;

/// 经典蓝牙MAC地址。
/// MAC address of the classic Bluetooth interface.
@property (nonatomic, strong) NSString *classicBluetoothMAC;

/// 表盘缩略图分辨率。
/// Resolution of the dial thumbnail, if applicable.
@property (nonatomic, strong) NSString *dialThumbnailResolution;

/// 使用二进制数据初始化模型的构造函数。
/// Initializes a new model instance with binary data.
/// @param data Raw binary data received from the device.
- (instancetype)initWithData:(NSData *)data;

/// 将模型属性转换为JSON字符串的方法。
/// Converts model properties to a JSON string.
- (NSString *)toJSONString;

/// 使用从 `toJSONString` 方法得到的 JSON 字符串初始化模型的构造函数。
/// Initializer to create an instance from a JSON string, which can be generated by the `toJSONString` method.
- (instancetype)initWithJSONString:(NSString *)jsonString;

@end

NS_ASSUME_NONNULL_END
