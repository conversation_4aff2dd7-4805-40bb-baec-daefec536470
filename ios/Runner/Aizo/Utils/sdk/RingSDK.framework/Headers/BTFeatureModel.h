//
//  BTFeatureModel.h
//  RingSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2024/5/23.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// BTFeatureModel 类用于封装蓝牙设备支持的功能。
/// The BTFeatureModel class encapsulates the features supported by a Bluetooth device.
@interface BTFeatureModel : NSObject

/// 是否支持触控开关。
/// Indicates if touch switch is supported.
@property (nonatomic, assign) NSInteger isSupportTouchSwitch;

/// 是否支持心率测量。
/// Indicates if heart rate measurement is supported.
@property (nonatomic, assign) NSInteger hrMeasurement;

/// 是否支持睡眠测量。
/// Indicates if sleep measurement is supported.
@property (nonatomic, assign) NSInteger sleepMeasurement;

/// 是否支持血氧测试。
/// Indicates if blood oxygen measurement is supported.
@property (nonatomic, assign) NSInteger bloodOxygenMeasurement;

/// 是否支持体温测量。
/// Indicates if body temperature measurement is supported.
@property (nonatomic, assign) NSInteger bodyTempMeasurement;

/// 是否支持心电测量。
/// Indicates if ECG (electrocardiogram) measurement is supported.
@property (nonatomic, assign) NSInteger ecgMeasurement;

/// 是否支持SOS功能。
/// Indicates if SOS functionality is supported.
@property (nonatomic, assign) NSInteger isSupportSOS;

/// 是否支持手势唤醒触控功能。
/// Indicates if gesture wakeup control is supported.
@property (nonatomic, assign) NSInteger isSupportGestureWakeup;

/// SOS触发模式。
/// SOS trigger mode.
@property (nonatomic, assign) NSInteger sosTriggerMode;

/// 支持触控自动休眠时延设置。
/// Support for touch control auto-sleep delay settings.
@property (nonatomic, assign) NSInteger touchSleepDelay;

/// 支持触控视频模式设置。
/// Support for touch control video mode settings.
@property (nonatomic, assign) NSInteger touchVideMode;

/// 是否支持特别关爱提醒。
/// Indicates if special care reminders are supported.
@property (nonatomic, assign) NSInteger isSupportCareUser;

/// 是否支持网页浏览触控模式。
/// Indicates if touch control for web browsing is supported.
@property (nonatomic, assign) NSInteger touchWebMode;

/// 是否支持身体成分测量功能。
/// Indicates if body composition measurement is supported.
@property (nonatomic, assign) NSInteger isSupportBC;

/// 是否支持一键魔方。
/// Indicates if one-key magic is supported.
@property (nonatomic, assign) NSInteger isSupportonKeyMagic;

/// 使用特定的二进制数据初始化模型。
/// Initializes the model with specific binary data.
/// @param data 用于初始化模型的二进制数据。 Binary data used to initialize the model.
- (instancetype)initWithData:(NSData *)data;

/// 将模型数据转换为JSON字符串。
/// Converts model data into a JSON string.
/// @return 返回模型数据的JSON字符串表示形式。Returns a JSON string representation of the model data.
- (NSString *)toJSONString;

@end

NS_ASSUME_NONNULL_END
