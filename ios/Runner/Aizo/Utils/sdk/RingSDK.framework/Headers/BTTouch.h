//
//  BTTouch.h
//  RingSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2024/5/27.
//

#import <Foundation/Foundation.h>
#import <RingSDK/BTTouchModel.h>

NS_ASSUME_NONNULL_BEGIN

/// 设备控制器类，用于管理设备的触控模式和状态
/// Device controller class, used for managing the touch mode and state of the device
@interface BTTouch : NSObject

/// 获取设备当前触控状态
/// Fetch the current state of the touch
/// @param completion 完成回调，返回触控状态。Completion callback, returning the touch state.
/// @discussion YES 表示成功发送指令，NO 表示重复发送或发送失败。YES indicates the command was sent successfully, NO indicates a duplicate or failure.
- (BOOL)fetchTouchState:(void (^)(BTTouchModel * _Nullable state))completion;

/// 更新设备触控状态
/// Update the state of the touch
/// @param state 新的触控状态。The new touch state.
/// @param completion 完成回调，返回操作是否成功。Completion callback, returning whether the operation was successful.
/// @discussion YES 表示成功发送指令，NO 表示重复发送或发送失败。YES indicates the command was sent successfully, NO indicates a duplicate or failure.
- (BOOL)updateTouchState:(BTTouchModel *)state completion:(void (^)(BOOL success))completion;

@end

NS_ASSUME_NONNULL_END
