<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ExampleInstrumentedTest.kt</key>
		<data>
		eOGqt4z+WPnZcYeoJb22xcGlVfI=
		</data>
		<key>Headers/BTAboutModel.h</key>
		<data>
		8Up0MjjbtNmqV4ArIOeloeP2ofM=
		</data>
		<key>Headers/BTAuthModel.h</key>
		<data>
		d8jsMzBFEcBv4p4jn+w6oPMhsSI=
		</data>
		<key>Headers/BTBase.h</key>
		<data>
		LJctLjn6rnbUxIMNlh97wod2FAs=
		</data>
		<key>Headers/BTDevice.h</key>
		<data>
		6QDXlM5W0H73m53kKL93U/57s1E=
		</data>
		<key>Headers/BTECGEndPacket.h</key>
		<data>
		GnER/YHCSwAwgUocKaVFG0zyKSM=
		</data>
		<key>Headers/BTECGPacket.h</key>
		<data>
		5BhMZBLJWGURL/cyYa216kyFEMI=
		</data>
		<key>Headers/BTECGValue.h</key>
		<data>
		zo+DgcJSQ4sD9x/aTc9IywbyQW4=
		</data>
		<key>Headers/BTFeatureModel.h</key>
		<data>
		1ZeaMDHLpjTsE6X8Ay7olLJWM4o=
		</data>
		<key>Headers/BTHealth.h</key>
		<data>
		nTrU4m1sWeI7yT441iSd9RFesTA=
		</data>
		<key>Headers/BTHealthModel.h</key>
		<data>
		jNvJO3xk45onLVQIgRknrdeAlSc=
		</data>
		<key>Headers/BTHealthScore.h</key>
		<data>
		ePhhzw371+36Wj3nstUmUOKLfgw=
		</data>
		<key>Headers/BTIntervalModel.h</key>
		<data>
		TxTKj+YEt24z44QyXaf038reRc4=
		</data>
		<key>Headers/BTMeasure.h</key>
		<data>
		lsSwglNtQjQExnZxr8hxFMBRDCc=
		</data>
		<key>Headers/BTMeasureResult.h</key>
		<data>
		67NWJC51QfwOtFAesz9twF5jKS8=
		</data>
		<key>Headers/BTNapDetailModel.h</key>
		<data>
		t3G9FyzuGP6DQBRrdPLZW4QCcFM=
		</data>
		<key>Headers/BTOTAChecker.h</key>
		<data>
		mmBd3gzKR5s8IkHEswEp0qHa0x0=
		</data>
		<key>Headers/BTOTAModel.h</key>
		<data>
		CdPBxwLqOlT9rvT6bPrEKb79mEk=
		</data>
		<key>Headers/BTPowerModel.h</key>
		<data>
		y/8kWmBlzx+Gf0V4G+GuyvVG3GI=
		</data>
		<key>Headers/BTSOS.h</key>
		<data>
		AherMG7IdHvpMDhd+ZkM4VySf0k=
		</data>
		<key>Headers/BTSOSStateModel.h</key>
		<data>
		D3LR5oophmVQtwonatkgrUznjH8=
		</data>
		<key>Headers/BTScoreAlgorithm.h</key>
		<data>
		n0ZKbfDSPhQYnNXabinhsPEoRlQ=
		</data>
		<key>Headers/BTService.h</key>
		<data>
		aWGb9MBc+qU1+IsZkGtLigl9ZKo=
		</data>
		<key>Headers/BTSleep.h</key>
		<data>
		w89yVOG9tGCZ9KB8OMexENiNob0=
		</data>
		<key>Headers/BTSleepDetailModel.h</key>
		<data>
		i2S/BeQ44GhmqO5LRm4kLH023yU=
		</data>
		<key>Headers/BTSleepSummaryModel.h</key>
		<data>
		LwB3s4EIPlGgtW9D3DeVldYNV4k=
		</data>
		<key>Headers/BTSport.h</key>
		<data>
		d772BzwWV68KaMj2wSmrdp9he0w=
		</data>
		<key>Headers/BTSportDetailModel.h</key>
		<data>
		Oj/7VmB45qFQJ48Q3ep9yIErhcM=
		</data>
		<key>Headers/BTSportRealTimeModel.h</key>
		<data>
		nrraNiP9VrAdyukl5T2NNicLIJg=
		</data>
		<key>Headers/BTSportStatus.h</key>
		<data>
		EOz53pmdmlTKuBBTzX8CJFAr1fE=
		</data>
		<key>Headers/BTSportSummaryModel.h</key>
		<data>
		uo9x2B7idQRuM9FqI8DEgSIS/GQ=
		</data>
		<key>Headers/BTStatusModel.h</key>
		<data>
		uJwoAncuUeA2fdtW7VZFuSxTVSM=
		</data>
		<key>Headers/BTTouch.h</key>
		<data>
		yoDj/RwCzh98THswYF5rqbXWvWs=
		</data>
		<key>Headers/BTTouchModel.h</key>
		<data>
		g71le2h5q5/7oLulCTIwtIKTKIk=
		</data>
		<key>Headers/BTUserPrefs.h</key>
		<data>
		DUEPk/j8ebuOknsgvlaZzmsGxZ4=
		</data>
		<key>Headers/CommandsTableViewController.h</key>
		<data>
		tarNqVYYGmYA7lqTfdxSeRr70aw=
		</data>
		<key>Headers/NSData+format.h</key>
		<data>
		vPnXkhnDqtJpR7S/szsGOphljL0=
		</data>
		<key>Headers/NSString+SaveToDocuments.h</key>
		<data>
		NS9aGehgHVcj9BsMniaBqhZ2+po=
		</data>
		<key>Headers/RTKOTAUpdater.h</key>
		<data>
		/c117EvtcMHVsn7CJISomqJ8wDU=
		</data>
		<key>Headers/Ring.h</key>
		<data>
		Q6OOMa1JJ9X5aV3X/kwgxAGL1xs=
		</data>
		<key>Headers/RingDef.h</key>
		<data>
		5VZuaKujsCSFa55Kht5GP4yfr4A=
		</data>
		<key>Headers/RingSDK-Swift.h</key>
		<data>
		b44W288qPRyKIDj1fsctnwC/Dhk=
		</data>
		<key>Headers/RingSDK.h</key>
		<data>
		ZmYFEYgyRRV39YySNLolA/Sexx8=
		</data>
		<key>Info.plist</key>
		<data>
		/hLyChcTFcMHhcVwQqsiC+Cv+5w=
		</data>
		<key>Modules/RingSDK.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		szjSjXwPvvrcXzFQYGVC36K9kJc=
		</data>
		<key>Modules/RingSDK.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		FSPnLbho3G+LL9smI3XgVOqBIQ4=
		</data>
		<key>Modules/RingSDK.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		XseubiZHTSIcQA9pa60TpG6eRXU=
		</data>
		<key>Modules/RingSDK.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<data>
		tr4vcjzHFpznurQN6o/yIYu2Dao=
		</data>
		<key>PrivateHeaders/BTConfigure.h</key>
		<data>
		Kn5Q84dP5EHvXkmXoN48hugC7i4=
		</data>
		<key>PrivateHeaders/BTDataTools.h</key>
		<data>
		m0KrxhRpxrUa852IjsnYmGMT6fU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ExampleInstrumentedTest.kt</key>
		<dict>
			<key>hash2</key>
			<data>
			CQbpzT+tibZZfDIf0XrfsOGwM93NN4y2GysDM53ejds=
			</data>
		</dict>
		<key>Headers/BTAboutModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ctHQz1MGV6fyInYeRYfmMCZEhMD+9Gz1dWLf5sIM/2s=
			</data>
		</dict>
		<key>Headers/BTAuthModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hV/lew0RyaS0hUYmVJ76ofB/WoDp12w7vDoqGpbRreg=
			</data>
		</dict>
		<key>Headers/BTBase.h</key>
		<dict>
			<key>hash2</key>
			<data>
			J6k5i4L5ad1sYtOlLzxbF/IfQ4b5wBZ6p//2CjDJAAo=
			</data>
		</dict>
		<key>Headers/BTDevice.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ajzXwiOiabT033u9oWqLzGFq4bE35+e8h8/saO3azpc=
			</data>
		</dict>
		<key>Headers/BTECGEndPacket.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iFPR6YKjcBloQ51av+y4C1UqYUxX8hX15/msfFBri2k=
			</data>
		</dict>
		<key>Headers/BTECGPacket.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pd/jErlS/BkTJ7fnliK8keWzEG9mEXr6LI60WiHXVdA=
			</data>
		</dict>
		<key>Headers/BTECGValue.h</key>
		<dict>
			<key>hash2</key>
			<data>
			dXKa2jMOO8vFjn7Y3j5w5mYfbCXlD+Qhow1VXhaimZ0=
			</data>
		</dict>
		<key>Headers/BTFeatureModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			2B7dVmqfU+ticDE2eJeYGMUCYPAgEdULmYCUUif2TR8=
			</data>
		</dict>
		<key>Headers/BTHealth.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pbFATkn8E8EY1JROt8EKmXrCMsBSEJkZ0/HGmk88+0g=
			</data>
		</dict>
		<key>Headers/BTHealthModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			I0OM0O+XTUuDE3HER4MARsFpa1B/Ed/W538Xyulzzn4=
			</data>
		</dict>
		<key>Headers/BTHealthScore.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Oe/oUD4yPXm2D5PAa45SSvmXR90zzWyirzZ4IUBaudg=
			</data>
		</dict>
		<key>Headers/BTIntervalModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3G4Ezp03m8ScQooOfCG1UR+hLorMSdgsAyhYnASo3tM=
			</data>
		</dict>
		<key>Headers/BTMeasure.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CSCFeWqfT5ZIcjbXo8spas6LNepAiYNhN6IjL3wCTro=
			</data>
		</dict>
		<key>Headers/BTMeasureResult.h</key>
		<dict>
			<key>hash2</key>
			<data>
			66gW+x1GY13A5J44wYMbom0vPq0GpEv+lG8SXdxYkRE=
			</data>
		</dict>
		<key>Headers/BTNapDetailModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZI8ABE6S6PhWDs5KsfR4bkPEVu61H0ynzqsRlifhuow=
			</data>
		</dict>
		<key>Headers/BTOTAChecker.h</key>
		<dict>
			<key>hash2</key>
			<data>
			2n5IGKzquutKcJU279t2wtqMZuqK3Q1c47nEOSIrt/U=
			</data>
		</dict>
		<key>Headers/BTOTAModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			PJuY0FvDqUZWh0TASgshK6gdTCuVQ+aYP9ZxcfjhAnc=
			</data>
		</dict>
		<key>Headers/BTPowerModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XtEbJDRaaIOhiEt1miZeiDyj/w7HLt0/VTJAwn9HMyw=
			</data>
		</dict>
		<key>Headers/BTSOS.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9/BTiX2QsjpgdZT13fZ/wxCK+/rCfIJyv0FelLnrlyQ=
			</data>
		</dict>
		<key>Headers/BTSOSStateModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Ij5Uc92UhyBSCshs48/EYhZ3XroXGCnjIVDWpmEHWyQ=
			</data>
		</dict>
		<key>Headers/BTScoreAlgorithm.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jYhexWfcheu1A/vBebhDtDq1Xvf7kfRXwBuXbF+KRlk=
			</data>
		</dict>
		<key>Headers/BTService.h</key>
		<dict>
			<key>hash2</key>
			<data>
			wpCweXrEPS9MAJ2IGNBi6RrIlFYcaSu71mKeaLzEvfk=
			</data>
		</dict>
		<key>Headers/BTSleep.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EGOJYY7G9v+JjUtyz0aQaBtXiAlcmYoySA6Aqd5IK+w=
			</data>
		</dict>
		<key>Headers/BTSleepDetailModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iwtvmUfHLQQbGxE5mr/96ksMMz0acXoKuFZeYEfoTFk=
			</data>
		</dict>
		<key>Headers/BTSleepSummaryModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9CGxLPm9mZXSJlO7lyj/JB648UBdQ514pHY/SE8GfNE=
			</data>
		</dict>
		<key>Headers/BTSport.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0QdBxr8v/t3JUUQr+RNwKaxNXT6b6TmHLhLri4tlDkE=
			</data>
		</dict>
		<key>Headers/BTSportDetailModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			J4wwI7eJwIWqfsRo0XkwJ5EOyafyN8i6zmN2IxQCZDw=
			</data>
		</dict>
		<key>Headers/BTSportRealTimeModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5khgPElzb6DaQpnntn7UmFAUNX+ot0TeC7IU/CnMeGI=
			</data>
		</dict>
		<key>Headers/BTSportStatus.h</key>
		<dict>
			<key>hash2</key>
			<data>
			8ikYUz5konwKVzLgsGzlpVa1oYBVzuOM6WnrCCYndWg=
			</data>
		</dict>
		<key>Headers/BTSportSummaryModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jDrN5bfJ1SjRDud4TVeFO83UZx5Rs3RY8uFQdNIqH7s=
			</data>
		</dict>
		<key>Headers/BTStatusModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			dY37vU8PzwXa4z4siPuBaK9F9fS/f61yqNPGDNC1AV0=
			</data>
		</dict>
		<key>Headers/BTTouch.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rOgzFBY8iwHm5I04oT2ZDod9cJOmLpjPz+1BFK3AYTM=
			</data>
		</dict>
		<key>Headers/BTTouchModel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gLoADAHTmiR8lC27KiRGpm7/3ECHL61Mixub/IDFZ8w=
			</data>
		</dict>
		<key>Headers/BTUserPrefs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EsC6CYAd5iohOoEGWRxXnxXMo//ix715XvC+ib/8XA0=
			</data>
		</dict>
		<key>Headers/CommandsTableViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			8fnhZ7HzuKhR2w5GqutlUYjHbRX5iIas33aphgNba0w=
			</data>
		</dict>
		<key>Headers/NSData+format.h</key>
		<dict>
			<key>hash2</key>
			<data>
			lrKpWoscVHQf8uce0Y0/sEHxrxganSJtH2lG3IFU41o=
			</data>
		</dict>
		<key>Headers/NSString+SaveToDocuments.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U5vY34WtvBUbq5NcgHI9FPtjEbMiY9MWyxWlapkXEdU=
			</data>
		</dict>
		<key>Headers/RTKOTAUpdater.h</key>
		<dict>
			<key>hash2</key>
			<data>
			lls6gaRtuYBYD+Qxrex1MwSAnW4iatT7vEzIQy93YCs=
			</data>
		</dict>
		<key>Headers/Ring.h</key>
		<dict>
			<key>hash2</key>
			<data>
			T2hIHZ8SGUT2qj1gMLqATCH9k24bhmqrA1EWpks3qJA=
			</data>
		</dict>
		<key>Headers/RingDef.h</key>
		<dict>
			<key>hash2</key>
			<data>
			IaHFmzWXbMgY7TlQFkclxEn9YRV6uMLtSwpi725bX7g=
			</data>
		</dict>
		<key>Headers/RingSDK-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ML0m2ejZtkOi8g8CapKKt95v+8V5azwrwMHZaXxxqo8=
			</data>
		</dict>
		<key>Headers/RingSDK.h</key>
		<dict>
			<key>hash2</key>
			<data>
			FkDX/yQiBZQgjqTW3n6JwMceDajmTC7E0FyqtDa/YO4=
			</data>
		</dict>
		<key>Modules/RingSDK.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			sPBGIguQ5nWgcEUN4XC0xghBe+dGkYkrpWoasOamMHg=
			</data>
		</dict>
		<key>Modules/RingSDK.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KnRdWE4y6t4QM5zi5JDptPdHFgJy1Tku+7GLkZS2aNM=
			</data>
		</dict>
		<key>Modules/RingSDK.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			ieo2eNqHbO8tL51ayDNm9jQ9HvXpeFSKqGMhOI0acEs=
			</data>
		</dict>
		<key>Modules/RingSDK.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			/nBgiwjdxMfOpfczzy604DltVLTidb3EQZZzZNFtpBk=
			</data>
		</dict>
		<key>PrivateHeaders/BTConfigure.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YZ1eRQMG9Rj5cZdeJHlBjjUSLrT1oDir0A+2sJqRHlo=
			</data>
		</dict>
		<key>PrivateHeaders/BTDataTools.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JBuOj5vZO42JCjVdi0WFUmNWtc7GLQhLYxWmGknpk80=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
