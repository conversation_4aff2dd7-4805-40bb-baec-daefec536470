//
//  KeychainHelper.swift
//  Runner
//
//  Created by 张仕鹏 on 2024/10/30.
//

import UIKit
import Foundation

class KeychainHelper {
    
    static let shared = KeychainHelper()
    private let keychain: Keychain
    private let uniqueIDKey = "com.aihnet.aicare.uniqueID"
    
    private init() {
        keychain = Keychain(service: "com.aihnet.aicare.service")
    }
    
    // 获取应用唯一识别码
    func getUniqueID() -> String {
        if let uniqueID = keychain[uniqueIDKey] {
            return uniqueID
        } else {
            let newUniqueID = UUID().uuidString
            keychain[uniqueIDKey] = newUniqueID
            return newUniqueID
        }
    }
}
