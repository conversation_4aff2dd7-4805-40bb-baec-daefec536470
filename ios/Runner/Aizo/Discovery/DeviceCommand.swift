//
//  CommandTableView.swift
//  Example
//
//  Created by <PERSON><PERSON><PERSON> on 2024/5/22.
//


import UIKit

enum DeviceCommand: String, CaseIterable {
    
    // 沙盒
    case sandbox = "文件管理(File Manager)"
   
    // 设备
    case power = "电源 (Power)"
    case reboot = "重启 (Reboot)"
    case reset = "重设 (Reset)"
    case features = "功能 (features)"
    case about = "关于 (about)"
    case HardwareLog = "硬件日志 (hardware log)"
    
    // 测量
    case measureHr = "(measure HR)"
    case measureSpo2 = "(measure spo2)"
    case measureTemp = "(measure temperature)"
    case measureECG = "[Beta](measure ECG)"
    case setInterval = " (set measure interval)"
    case getInterval = " (get measure interval)"
    
    // 模块
    case health = "健康数据(health data)"
    case sleep = "睡眠数据(sleep data)"
    case sport = "运动(sport)"
    case touch = "触摸(touch)"
    
    // 固件
    case ota = "升级(OTA)"
    case otaStressor = "升级压测(OTA Stressor)"
    case otaChecker = "升级检查(ota checker)"
    
    case userPrefs = "用户设置(userPrefs)"
    case sosToggle = "切换 SOS(SOS Toggle)"

    case score = "评分(score)"
    case toggleReconnect = "重连切换(toggle Reconnect)"

    // 获取分组信息
    var group: String {
        switch self {
        case .sandbox: return "沙盒 (Sandbox)"
        case .power, .reboot, .reset, .features, .about , .HardwareLog: return "设备 (Device)"
        case .measureHr, .measureSpo2, .measureTemp, .measureECG, .setInterval, .getInterval: return "测量 (Measurement)"
        case .score,.health, .sleep, .sport, .touch: return "模块 (Modules)"
        case .ota, .otaStressor, .otaChecker: return "固件 (Firmware)"
        case .userPrefs, .sosToggle, .toggleReconnect: return "其他 (Others)"
        }
    }
    
    func toMeasurementType() -> BTMeasureType? {
        switch self {
        case .measureHr:
            return .hr
        case .measureSpo2:
            return .spo2
        case .measureTemp:
            return .temp
        default:
            return nil
        }
    }
}
