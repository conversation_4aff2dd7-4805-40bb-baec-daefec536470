<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
<key>BGTaskSchedulerPermittedIdentifiers</key>
<array>
    <string>com.transistorsoft.fetch</string>
    <string>com.transistorsoft.customtask</string>
</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>aiCare</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>aiCare</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.aihhnet.aicare</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>aicare</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>auth</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>The application requires Bluetooth permission to connect to nearby devices.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>The application requires Bluetooth permission to access and control the device.</string>
	<key>NSCameraUsageDescription</key>
	<string>$(PRODUCT_NAME) uses the camera to take photos for uploading user avatars. This allows you to personalize your profile within the app.</string>
	<key>NSFileProtectionComplete</key>
	<string>NSFileProtectionComplete</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>$(PRODUCT_NAME) uses the local network to enhance the Bluetooth connection process. By accessing the local network, the app can more efficiently communicate with nearby Bluetooth devices, ensuring stable and fast data transfer during the pairing and usage of these devices.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Location permission is required both when the app is in use and always in the background to enable accurate Bluetooth device scanning and connection. This ensures that you can quickly and reliably pair with nearby Bluetooth - enabled devices, enhancing the overall functionality and user experience of the app.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Always access to location permissions is necessary to continuously support Bluetooth device scanning. By having continuous access to your location, the app can promptly detect and connect to nearby Bluetooth devices, providing you with seamless connectivity and uninterrupted use of Bluetooth - related features.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>When you use $(PRODUCT_NAME), the app needs your location to scan for and connect to nearby Bluetooth devices. This enables you to easily pair with Bluetooth - enabled equipment and use the associated features while actively interacting with the app.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We need to access your photo library in order to save images.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need to access your photo library in order to select and save images.</string>
	<key>NSUserNotificationUsageDescription</key>
	<string>$(PRODUCT_NAME) requires notification permissions to send you push notifications about important measurement results and timely reminders. This helps you stay informed and manage your health - related data effectively.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>processing</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
