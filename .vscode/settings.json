//  {
//     // 警告颜色（使用亮黄色避免与橙色冲突）
//     "editorWarning.foreground": "#FFD700", // 金黄色：警告波浪线
//     "editorWarning.border": "#FFD700", // 金黄色：警告边框
//     "editorWarning.background": "#3D3B2D", // 深黄色背景：警告背景色
//     // 错误颜色（保持红色）
//     "editorError.foreground": "#FF6347", // 亮红色：错误波浪线
//     "editorError.border": "#FF6347", // 亮红色：错误边框
//     "editorError.background": "#4A2A2A", // 深红色背景：错误背景色
//     // 信息提示颜色（淡蓝色）
//     "editorInfo.foreground": "#87CEFA", // 淡蓝色：提示信息波浪线
//     "editorInfo.border": "#87CEFA", // 淡蓝色：提示信息边框
//     "editorInfo.background": "#2C3E50", // 深蓝色背景：提示信息背景色
//     // Problems 面板图标颜色
//     "problemsWarningIcon.foreground": "#FFD700", // 金黄色：警告图标
//     "problemsErrorIcon.foreground": "#FF6347", // 亮红色：错误图标
//     "problemsInfoIcon.foreground": "#87CEFA", // 淡蓝色：信息图标
//     // Git 状态颜色
//     "gitDecoration.addedResourceForeground": "#4CAF50", // 绿色：新增文件
//     "gitDecoration.modifiedResourceForeground": "#FFA726", // 柔和的橙色：修改过的文件
//     "gitDecoration.deletedResourceForeground": "#FF6347", // 亮红色：已删除文件
//     "gitDecoration.conflictingResourceForeground": "#FF5722", // 深橙色：有冲突的文件
//     "gitDecoration.ignoredResourceForeground": "#757575", // 灰色：忽略的文件
//     "gitDecoration.untrackedResourceForeground": "#1E90FF", // 亮蓝色：未跟踪的文件
//     "gitDecoration.submoduleResourceForeground": "#BA68C8", // 亮紫色：子模块文件
//     // 背景色配置
//     "gitDecoration.modifiedResourceBackground": "#4E342E", // 深棕橙背景：修改过的文件背景
//     "gitDecoration.deletedResourceBackground": "#4A2A2A", // 深红色背景：已删除文件背景
//     "gitDecoration.conflictingResourceBackground": "#5D4037", // 深橙棕背景：冲突文件背景
//     "gitDecoration.ignoredResourceBackground": "#424242", // 深灰色背景：忽略文件背景
//     "gitDecoration.untrackedResourceBackground": "#1A2732", // 深蓝色背景：未跟踪文件背景
//     "gitDecoration.submoduleResourceBackground": "#4A235A" // 深紫色背景：子模块背景
// }
{
    "files.exclude": {
        "**/.CVS": true,
        "**/.Thumbs.db": true,
        "**/CVS": false,
        "**/Thumbs.db": false
    },
    "commentTranslate.hover.enabled": false,
    "commentTranslate.browse.mode": "contrast"
}