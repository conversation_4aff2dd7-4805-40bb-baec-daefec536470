name: aiCare
version: 1.3.6+56
publish_to: none
description: applacation of aiHCare.
environment:
  sdk: '>=3.3.4 <4.0.0'

dependencies:
  cupertino_icons: ^1.0.6
  flutter_screenutil: ^5.9.0  #屏幕适配
  # shared_preferences: ^2.2.3  #内存管理
  mmkv: ^2.2.2                #本地存储
  # flutter_secure_storage: ^9.2.2  #安全内存管理
  logger: ^2.3.0              #日志管理
  get: 4.6.6
  dio: 5.3.3                  #网络管理
  cherry_toast: 1.11.0       #消息提示
  fluttertoast: 8.2.10         #消息显示
  flutter_svg: 2.0.8          #svg图片显示
  pretty_dio_logger: 1.3.1    #日志格式管理
  flutter:
    sdk: flutter
  super_tooltip: ^2.0.7
  string_validator: ^1.1.0    #检验工具
  percent_indicator: ^4.2.3   #图表插件
  auth0_flutter: ^1.7.2       #auth0国外
  authing_sdk: ^1.1.12        #authing国内
  fl_chart: ^0.68.0           #图表插件
  dropdown_button2: ^2.3.9    #下拉筛选框
  sign_in_with_apple: ^6.1.1  #苹果登录
  dart_jsonwebtoken: ^2.14.0  #jwt解析token，获取id
  flutter_echarts: ^2.5.0     #echarts图表
  calendar_date_picker2: ^1.1.5 #日期选择器
  flutter_launcher_icons: "^0.13.1" #更换全局图标
  flutter_local_notifications: ^18.0.1  #本地消息推送
  permission_handler: ^11.3.1 #动态申请权限
  flutter_blue_plus: ^1.32.7  #蓝牙插件
  flutter_displaymode: ^0.6.0 #刷新率插件
  gap: ^3.0.1                 #row and column gap
  flutter_styled_toast: ^2.2.1  #消息弹窗
  image_picker: ^1.0.1        #图片上传
  cached_network_image: ^3.4.1  #网络图片缓存
  image_cropper: ^8.0.2      #图片裁剪
  url_launcher: ^6.3.1        #url解析转换   
  path_provider: ^2.1.5       #本地存储文件
  # background_fetch: ^1.3.6   #后台任务
  firebase_core: ^3.15.2     #火狐debug
  firebase_crashlytics: ^4.3.10  #火狐崩溃记录
  firebase_analytics: ^11.6.0 # 日志分析（可选）

  

  




dev_dependencies:
  flutter_gen_runner: ^5.3.1  #管理资源文件
  flutter_gen_core: 5.3.1
  build_runner: ^2.4.6       #管理资源文件
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter
  dart_style: 2.3.2


flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/


flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/icon.jpg"
  min_sdk_android: 21 # android min sdk min:16, default 21
