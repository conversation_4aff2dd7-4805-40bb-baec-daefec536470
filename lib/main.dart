/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-30 16:45:54
 * @FilePath: /RPM-APP-MASTER/lib/main.dart
 * @Description: 应用程序入口文件
 */

import 'dart:ui';

import 'package:aiCare/app/bingdings/initial_binding.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/base/controller/global_controller.dart';
import 'package:aiCare/app/core/base/controller/language_controller.dart';
import 'package:aiCare/app/core/enum/region_unit.dart';
import 'package:aiCare/app/core/translations/app_translations.dart';
import 'package:aiCare/app/core/utils/backend_util.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/firebase_options.dart';
import 'package:aiCare/flavors/build_config.dart';
import 'package:aiCare/flavors/env_config.dart';
import 'package:aiCare/flavors/environment.dart';
import 'package:authing_sdk/authing.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mmkv/mmkv.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';

/// 应用入口函数
///
/// 负责初始化应用所需的各种配置和服务
/// 包括：
/// 1. Flutter绑定初始化 - 确保Flutter引擎和框架正确初始化
/// 2. 应用设置初始化 - 包括存储、屏幕适配、API区域等基础配置
/// 3. 控制器初始化 - 初始化全局控制器和语言控制器
/// 4. 路由初始化 - 设置应用的路由系统
/// 5. 屏幕方向设置 - 限制应用只能竖屏显示
/// 6. 启动应用 - 运行主应用组件
void main() async {
  // 确保 Flutter 绑定初始化，这是运行Flutter应用的必要步骤
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化应用设置，包括存储、屏幕适配、API区域等
  await _initSetting();

  // 初始化控制器
  Get.put(GlobalController());
  Get.put(LanguageController());

  
  // 初始化应用路由系统
  await AppPages.initialize();

  // 初始化后台任务
  // BackgroundFetch.registerHeadlessTask(backgroundFetchHeadlessTask);

  // 设置屏幕方向，限制只能竖屏显示
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp, // 只允许竖屏
  ]);

  // 启动应用
  runApp(const MyApp());
}

// 必需：Headless Task 入口点必须是顶层函数
// @pragma('vm:entry-point')
// void backgroundFetchHeadlessTask(HeadlessTask task) async {
//   print("backgroundFetchHeadlessTask");
//   print("task: $task");
//   print("task.taskId: ${task.taskId}");
//   // 获取任务ID
//   String taskId = task.taskId;
  
//   // 检查是否超时
//   bool isTimeout = task.timeout;
  
//   if (isTimeout) {
//     print("[BackgroundFetch] Headless task timed-out: $taskId");
//     BackgroundFetch.finish(taskId);
//     return;
//   }
  
//   print('[BackgroundFetch] Headless event received: $taskId');
  
//   // // 调用 BackendUtil 的处理逻辑
//   await BackendUtil.handleHeadlessTask(taskId);
  
//   BackgroundFetch.finish(taskId);
// }


/// 初始化应用设置
///
/// 负责初始化应用的基础配置，包括：
/// 1. MMKV本地存储初始化 - 用于高效的数据存储
/// 2. 屏幕适配初始化 - 确保应用在不同设备上正确显示
/// 3. 默认API区域设置 - 设置应用的默认服务器区域
/// 4. 环境配置初始化 - 设置开发环境相关配置
/// 5. Authing初始化 - 初始化第三方认证服务
Future<void> _initSetting() async {
  // 初始化MMKV并获取根目录，MMKV是一个高性能的key-value存储框架
  final rootDir = await MMKV.initialize();
  print('MMKV for flutter with rootDir = $rootDir');

  // 初始化屏幕适配，确保应用在不同设备上正确显示
  await ScreenUtil.ensureScreenSize();

  // 配置开发环境参数
  final devConfig = EnvConfig(
    appName: "aiCare", // 应用名称
    baseUrlCN: "https://aispine.aihnet.cn", // 中国区服务器地址
    baseUrlOUT: "https://myaih.net", // 国际区服务器地址
    auth0Domain: "https://ineck.auth0.com", // Auth0认证域名
    clientID: "n3MTVDRdyq9xl2riKm5cWW2wxWcGhnLA", // Auth0客户端ID
    clientSercet: "****************************************************************", // Auth0客户端密钥
    userPoolId: "617f57a540eb446b2603de64", // 用户池ID
    appId: "628b26330995e701146bc591", // 应用ID
    shouldCollectCrashLog: true, // 是否收集崩溃日志
  );

  // 实例化构建配置
  BuildConfig.instantiate(
    envType: Environment.DEVELOPMENT, // 设置环境类型为开发环境
    envConfig: devConfig, // 设置环境配置
  );

  // 设置默认API区域
  final storage = SecureStorageService.instance;
  //设置为前两天
  // storage.setString(AppValues.aizoHealthLastUpdateTime, DateTime.now().subtract(const Duration(days: 2)).toString());
  // storage.setString(AppValues.aizoSleepLastUpdateTime, DateTime.now().subtract(const Duration(days: 2)).toString());
  // storage.deleteAll();
  // await storage.delete(AppValues.apiRegionKey);
  final regionValue = storage.getRegion();
  print("regionValue: $regionValue");
  if (regionValue == null) {
    // 如果没有设置过API区域，尝试通过IP判断用户所在地区
    try {
      final response = await http.get(Uri.parse('http://ip-api.com/json'));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final countryCode = data['countryCode'] as String;
        // 如果用户在中国，设置为中文环境，否则设置为英文环境
        SecureStorageService.instance.setRegion(
          countryCode == 'CN' ? RegionUnit.zh : RegionUnit.en
        );
        // print("countryCode: $countryCode");
      } else {
        // 如果API调用失败，默认设置为英文环境
        SecureStorageService.instance.setRegion(RegionUnit.en);
        // print("response.statusCode: ${response.statusCode}");
      }
    } catch (e) {
      // print('Failed to get IP location: $e');
      // 如果发生错误，默认设置为英文环境
       SecureStorageService.instance.setRegion(RegionUnit.en);
    }
  }

  // 初始化Authing认证服务
  Authing.init(devConfig.userPoolId, devConfig.appId);

  // 初始化 Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // 设置 Crashlytics
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  // 捕获所有未处理的异常
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };


}

/// 应用程序主组件
///
/// 负责构建应用的主界面和配置
/// 包括：
/// 1. 屏幕适配配置
/// 2. 主题设置
/// 3. 路由配置
/// 4. 国际化配置
class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  // 环境配置实例
  final EnvConfig _envConfig = BuildConfig.instance.config;
  // 语言控制器实例
  final languageController = Get.find<LanguageController>();

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      // 设置设计稿尺寸，用于屏幕适配
      designSize: const Size(375, 812),
      // 允许文字大小自适应
      minTextAdapt: true,
      // 支持分屏模式
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
          // 设置应用主题
          theme: ThemeData(
            colorScheme: const ColorScheme.light(
              onPrimary: Colors.white,
              surfaceTint: Colors.transparent,
            ),
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
          ),
          // 关闭调试标签
          debugShowCheckedModeBanner: false,
          // 设置应用名称
          title: _envConfig.appName,
          // 设置初始路由
          initialRoute: AppPages.INITIAL,
          // 设置初始绑定
          initialBinding: InitialBinding(),
          // 设置默认页面切换动画
          defaultTransition: Transition.rightToLeft,
          // 设置页面切换动画时长
          transitionDuration: const Duration(milliseconds: 200),
          // 设置路由表
          getPages: AppPages.routes,
          // 设置国际化翻译
          translations: AppTranslations(),
          // 设置当前语言
          locale: languageController.currentLocale,
          // 设置备用语言为英文
          fallbackLocale: const Locale('en', 'US'),
        );
      },
    );
  }
}
