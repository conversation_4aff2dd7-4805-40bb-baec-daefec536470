/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/icon.jpg
  AssetGenImage get icon => const AssetGenImage('assets/icons/icon.jpg');

  /// List of all assets
  List<AssetGenImage> get values => [icon];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/account_security_icon.png
  AssetGenImage get accountSecurityIcon =>
      const AssetGenImage('assets/images/account_security_icon.png');

  /// File path: assets/images/airing_function.png
  AssetGenImage get airingFunction =>
      const AssetGenImage('assets/images/airing_function.png');

  /// File path: assets/images/aizo_ring.png
  AssetGenImage get aizoRing =>
      const AssetGenImage('assets/images/aizo_ring.png');

  /// File path: assets/images/apple.svg
  String get apple => 'assets/images/apple.svg';

  /// File path: assets/images/array_right_green.svg
  String get arrayRightGreen => 'assets/images/array_right_green.svg';

  /// File path: assets/images/avatar_default.png
  AssetGenImage get avatarDefault =>
      const AssetGenImage('assets/images/avatar_default.png');

  /// File path: assets/images/back_blue_blur.png
  AssetGenImage get backBlueBlur =>
      const AssetGenImage('assets/images/back_blue_blur.png');

  /// File path: assets/images/back_icon.svg
  String get backIcon => 'assets/images/back_icon.svg';

  /// File path: assets/images/back_icon_white.png
  AssetGenImage get backIconWhite =>
      const AssetGenImage('assets/images/back_icon_white.png');

  /// File path: assets/images/bluetooth_back_circle1.png
  AssetGenImage get bluetoothBackCircle1 =>
      const AssetGenImage('assets/images/bluetooth_back_circle1.png');

  /// File path: assets/images/bluetooth_circle.png
  AssetGenImage get bluetoothCircle =>
      const AssetGenImage('assets/images/bluetooth_circle.png');

  /// File path: assets/images/bluetooth_found_open.png
  AssetGenImage get bluetoothFoundOpen =>
      const AssetGenImage('assets/images/bluetooth_found_open.png');

  /// File path: assets/images/bluetooth_function.png
  AssetGenImage get bluetoothFunction =>
      const AssetGenImage('assets/images/bluetooth_function.png');

  /// File path: assets/images/bluetooth_menu.png
  AssetGenImage get bluetoothMenu =>
      const AssetGenImage('assets/images/bluetooth_menu.png');

  /// File path: assets/images/bluetooth_no.svg
  String get bluetoothNo => 'assets/images/bluetooth_no.svg';

  /// File path: assets/images/bluetooth_on.svg
  String get bluetoothOn => 'assets/images/bluetooth_on.svg';

  /// File path: assets/images/bluetooth_search1.png
  AssetGenImage get bluetoothSearch1 =>
      const AssetGenImage('assets/images/bluetooth_search1.png');

  /// File path: assets/images/bluetooth_search2.png
  AssetGenImage get bluetoothSearch2 =>
      const AssetGenImage('assets/images/bluetooth_search2.png');

  /// File path: assets/images/body_blue_icon.png
  AssetGenImage get bodyBlueIcon =>
      const AssetGenImage('assets/images/body_blue_icon.png');

  /// File path: assets/images/body_no_icon.png
  AssetGenImage get bodyNoIcon =>
      const AssetGenImage('assets/images/body_no_icon.png');

  /// File path: assets/images/brain.png
  AssetGenImage get brain => const AssetGenImage('assets/images/brain.png');

  /// File path: assets/images/checkMark.png
  AssetGenImage get checkMark =>
      const AssetGenImage('assets/images/checkMark.png');

  /// File path: assets/images/check_blue.svg
  String get checkBlue => 'assets/images/check_blue.svg';

  /// File path: assets/images/check_circle.png
  AssetGenImage get checkCircle =>
      const AssetGenImage('assets/images/check_circle.png');

  /// File path: assets/images/delete_icon.png
  AssetGenImage get deleteIcon =>
      const AssetGenImage('assets/images/delete_icon.png');

  /// File path: assets/images/edit.png
  AssetGenImage get edit => const AssetGenImage('assets/images/edit.png');

  /// File path: assets/images/error_info.svg
  String get errorInfo => 'assets/images/error_info.svg';

  /// File path: assets/images/facebook.png
  AssetGenImage get facebook =>
      const AssetGenImage('assets/images/facebook.png');

  /// File path: assets/images/failed_icon.png
  AssetGenImage get failedIcon =>
      const AssetGenImage('assets/images/failed_icon.png');

  /// File path: assets/images/filter.svg
  String get filter => 'assets/images/filter.svg';

  /// File path: assets/images/fitness_consum_fire.png
  AssetGenImage get fitnessConsumFire =>
      const AssetGenImage('assets/images/fitness_consum_fire.png');

  /// File path: assets/images/fitness_distance.png
  AssetGenImage get fitnessDistance =>
      const AssetGenImage('assets/images/fitness_distance.png');

  /// File path: assets/images/fitness_holistic_people_no.png
  AssetGenImage get fitnessHolisticPeopleNo =>
      const AssetGenImage('assets/images/fitness_holistic_people_no.png');

  /// File path: assets/images/fitness_holistic_people_yellow.png
  AssetGenImage get fitnessHolisticPeopleYellow =>
      const AssetGenImage('assets/images/fitness_holistic_people_yellow.png');

  /// File path: assets/images/fitness_pie.png
  AssetGenImage get fitnessPie =>
      const AssetGenImage('assets/images/fitness_pie.png');

  /// File path: assets/images/fitness_step.png
  AssetGenImage get fitnessStep =>
      const AssetGenImage('assets/images/fitness_step.png');

  /// File path: assets/images/google.png
  AssetGenImage get google => const AssetGenImage('assets/images/google.png');

  /// File path: assets/images/green_circle.png
  AssetGenImage get greenCirclePng =>
      const AssetGenImage('assets/images/green_circle.png');

  /// File path: assets/images/green_circle.svg
  String get greenCircleSvg => 'assets/images/green_circle.svg';

  /// File path: assets/images/health_activity_blue.png
  AssetGenImage get healthActivityBlue =>
      const AssetGenImage('assets/images/health_activity_blue.png');

  /// File path: assets/images/health_activity_no.png
  AssetGenImage get healthActivityNo =>
      const AssetGenImage('assets/images/health_activity_no.png');

  /// File path: assets/images/health_bg.png
  AssetGenImage get healthBg =>
      const AssetGenImage('assets/images/health_bg.png');

  /// File path: assets/images/health_goal_icon.png
  AssetGenImage get healthGoalIcon =>
      const AssetGenImage('assets/images/health_goal_icon.png');

  /// File path: assets/images/health_heart_no.png
  AssetGenImage get healthHeartNo =>
      const AssetGenImage('assets/images/health_heart_no.png');

  /// File path: assets/images/health_heart_range1.png
  AssetGenImage get healthHeartRange1 =>
      const AssetGenImage('assets/images/health_heart_range1.png');

  /// File path: assets/images/health_heart_range2.png
  AssetGenImage get healthHeartRange2 =>
      const AssetGenImage('assets/images/health_heart_range2.png');

  /// File path: assets/images/health_heart_range3.png
  AssetGenImage get healthHeartRange3 =>
      const AssetGenImage('assets/images/health_heart_range3.png');

  /// File path: assets/images/health_heart_red.png
  AssetGenImage get healthHeartRed =>
      const AssetGenImage('assets/images/health_heart_red.png');

  /// File path: assets/images/health_info_banner.png
  AssetGenImage get healthInfoBanner =>
      const AssetGenImage('assets/images/health_info_banner.png');

  /// File path: assets/images/health_message_icon.png
  AssetGenImage get healthMessageIcon =>
      const AssetGenImage('assets/images/health_message_icon.png');

  /// File path: assets/images/health_not_click.svg
  String get healthNotClick => 'assets/images/health_not_click.svg';

  /// File path: assets/images/health_on_click.svg
  String get healthOnClick => 'assets/images/health_on_click.svg';

  /// File path: assets/images/health_oxygen.png
  AssetGenImage get healthOxygen =>
      const AssetGenImage('assets/images/health_oxygen.png');

  /// File path: assets/images/health_picture_1.png
  AssetGenImage get healthPicture1 =>
      const AssetGenImage('assets/images/health_picture_1.png');

  /// File path: assets/images/health_picture_2.png
  AssetGenImage get healthPicture2 =>
      const AssetGenImage('assets/images/health_picture_2.png');

  /// File path: assets/images/health_picture_3.png
  AssetGenImage get healthPicture3 =>
      const AssetGenImage('assets/images/health_picture_3.png');

  /// File path: assets/images/health_recovery_icon.png
  AssetGenImage get healthRecoveryIcon =>
      const AssetGenImage('assets/images/health_recovery_icon.png');

  /// File path: assets/images/health_sleep_range1.png
  AssetGenImage get healthSleepRange1 =>
      const AssetGenImage('assets/images/health_sleep_range1.png');

  /// File path: assets/images/health_sleep_range2.png
  AssetGenImage get healthSleepRange2 =>
      const AssetGenImage('assets/images/health_sleep_range2.png');

  /// File path: assets/images/health_sleep_range3.png
  AssetGenImage get healthSleepRange3 =>
      const AssetGenImage('assets/images/health_sleep_range3.png');

  /// File path: assets/images/health_temperature.png
  AssetGenImage get healthTemperature =>
      const AssetGenImage('assets/images/health_temperature.png');

  /// File path: assets/images/heart_icon.png
  AssetGenImage get heartIcon =>
      const AssetGenImage('assets/images/heart_icon.png');

  /// File path: assets/images/heart_rate_icon.png
  AssetGenImage get heartRateIcon =>
      const AssetGenImage('assets/images/heart_rate_icon.png');

  /// File path: assets/images/home_not_click.svg
  String get homeNotClick => 'assets/images/home_not_click.svg';

  /// File path: assets/images/home_on_click.svg
  String get homeOnClick => 'assets/images/home_on_click.svg';

  /// File path: assets/images/home_over_none.png
  AssetGenImage get homeOverNone =>
      const AssetGenImage('assets/images/home_over_none.png');

  /// File path: assets/images/knowledge.png
  AssetGenImage get knowledge =>
      const AssetGenImage('assets/images/knowledge.png');

  /// File path: assets/images/measure_button.png
  AssetGenImage get measureButton =>
      const AssetGenImage('assets/images/measure_button.png');

  /// File path: assets/images/my_not_click.svg
  String get myNotClick => 'assets/images/my_not_click.svg';

  /// File path: assets/images/my_on_click.svg
  String get myOnClick => 'assets/images/my_on_click.svg';

  /// File path: assets/images/navigation_right.png
  AssetGenImage get navigationRight =>
      const AssetGenImage('assets/images/navigation_right.png');

  /// File path: assets/images/none_circle_151.png
  AssetGenImage get noneCircle151 =>
      const AssetGenImage('assets/images/none_circle_151.png');

  /// File path: assets/images/notion.svg
  String get notion => 'assets/images/notion.svg';

  /// File path: assets/images/notion_my.png
  AssetGenImage get notionMy =>
      const AssetGenImage('assets/images/notion_my.png');

  /// File path: assets/images/notions_icon.svg
  String get notionsIcon => 'assets/images/notions_icon.svg';

  /// File path: assets/images/orange_circle.svg
  String get orangeCircle => 'assets/images/orange_circle.svg';

  /// File path: assets/images/oxygen_detail_banner.png
  AssetGenImage get oxygenDetailBanner =>
      const AssetGenImage('assets/images/oxygen_detail_banner.png');

  /// File path: assets/images/oxygen_icon.png
  AssetGenImage get oxygenIcon =>
      const AssetGenImage('assets/images/oxygen_icon.png');

  /// File path: assets/images/pen.png
  AssetGenImage get pen => const AssetGenImage('assets/images/pen.png');

  /// File path: assets/images/plus_outer_circle.svg
  String get plusOuterCircle => 'assets/images/plus_outer_circle.svg';

  /// File path: assets/images/pointer.svg
  String get pointer => 'assets/images/pointer.svg';

  /// File path: assets/images/pressure_dashed.png
  AssetGenImage get pressureDashed =>
      const AssetGenImage('assets/images/pressure_dashed.png');

  /// File path: assets/images/pressure_icon.png
  AssetGenImage get pressureIcon =>
      const AssetGenImage('assets/images/pressure_icon.png');

  /// File path: assets/images/pressure_inner_low.png
  AssetGenImage get pressureInnerLow =>
      const AssetGenImage('assets/images/pressure_inner_low.png');

  /// File path: assets/images/pressure_inner_mild.png
  AssetGenImage get pressureInnerMild =>
      const AssetGenImage('assets/images/pressure_inner_mild.png');

  /// File path: assets/images/pressure_inner_moderate.png
  AssetGenImage get pressureInnerModerate =>
      const AssetGenImage('assets/images/pressure_inner_moderate.png');

  /// File path: assets/images/pressure_inner_normal.png
  AssetGenImage get pressureInnerNormal =>
      const AssetGenImage('assets/images/pressure_inner_normal.png');

  /// File path: assets/images/pressure_inner_nothing.png
  AssetGenImage get pressureInnerNothing =>
      const AssetGenImage('assets/images/pressure_inner_nothing.png');

  /// File path: assets/images/pressure_inner_serious.png
  AssetGenImage get pressureInnerSerious =>
      const AssetGenImage('assets/images/pressure_inner_serious.png');

  /// File path: assets/images/pressure_outer_low.png
  AssetGenImage get pressureOuterLow =>
      const AssetGenImage('assets/images/pressure_outer_low.png');

  /// File path: assets/images/pressure_outer_mild.png
  AssetGenImage get pressureOuterMild =>
      const AssetGenImage('assets/images/pressure_outer_mild.png');

  /// File path: assets/images/pressure_outer_moderate.png
  AssetGenImage get pressureOuterModerate =>
      const AssetGenImage('assets/images/pressure_outer_moderate.png');

  /// File path: assets/images/pressure_outer_normal.png
  AssetGenImage get pressureOuterNormal =>
      const AssetGenImage('assets/images/pressure_outer_normal.png');

  /// File path: assets/images/pressure_outer_nothing.png
  AssetGenImage get pressureOuterNothing =>
      const AssetGenImage('assets/images/pressure_outer_nothing.png');

  /// File path: assets/images/pressure_outer_serious.png
  AssetGenImage get pressureOuterSerious =>
      const AssetGenImage('assets/images/pressure_outer_serious.png');

  /// File path: assets/images/pressure_reminder_noting.svg
  String get pressureReminderNoting =>
      'assets/images/pressure_reminder_noting.svg';

  /// File path: assets/images/rate_pie.png
  AssetGenImage get ratePie =>
      const AssetGenImage('assets/images/rate_pie.png');

  /// File path: assets/images/rate_pie_none.png
  AssetGenImage get ratePieNone =>
      const AssetGenImage('assets/images/rate_pie_none.png');

  /// File path: assets/images/record.svg
  String get record => 'assets/images/record.svg';

  /// File path: assets/images/red_circle.svg
  String get redCircle => 'assets/images/red_circle.svg';

  /// File path: assets/images/related_infor.png
  AssetGenImage get relatedInfor =>
      const AssetGenImage('assets/images/related_infor.png');

  /// File path: assets/images/right_arrow_48.svg
  String get rightArrow48 => 'assets/images/right_arrow_48.svg';

  /// File path: assets/images/search.svg
  String get search => 'assets/images/search.svg';

  /// File path: assets/images/selected_icon.png
  AssetGenImage get selectedIcon =>
      const AssetGenImage('assets/images/selected_icon.png');

  /// File path: assets/images/setting.svg
  String get setting => 'assets/images/setting.svg';

  /// File path: assets/images/setting_button_no.png
  AssetGenImage get settingButtonNo =>
      const AssetGenImage('assets/images/setting_button_no.png');

  /// File path: assets/images/setting_button_on.png
  AssetGenImage get settingButtonOn =>
      const AssetGenImage('assets/images/setting_button_on.png');

  /// File path: assets/images/setting_my.png
  AssetGenImage get settingMy =>
      const AssetGenImage('assets/images/setting_my.png');

  /// File path: assets/images/sleep_awake.png
  AssetGenImage get sleepAwake =>
      const AssetGenImage('assets/images/sleep_awake.png');

  /// File path: assets/images/sleep_breath.png
  AssetGenImage get sleepBreath =>
      const AssetGenImage('assets/images/sleep_breath.png');

  /// File path: assets/images/sleep_deep.png
  AssetGenImage get sleepDeep =>
      const AssetGenImage('assets/images/sleep_deep.png');

  /// File path: assets/images/sleep_deep_none.png
  AssetGenImage get sleepDeepNone =>
      const AssetGenImage('assets/images/sleep_deep_none.png');

  /// File path: assets/images/sleep_icon.png
  AssetGenImage get sleepIcon =>
      const AssetGenImage('assets/images/sleep_icon.png');

  /// File path: assets/images/sleep_image1.png
  AssetGenImage get sleepImage1 =>
      const AssetGenImage('assets/images/sleep_image1.png');

  /// File path: assets/images/sleep_image2.png
  AssetGenImage get sleepImage2 =>
      const AssetGenImage('assets/images/sleep_image2.png');

  /// File path: assets/images/sleep_light.png
  AssetGenImage get sleepLight =>
      const AssetGenImage('assets/images/sleep_light.png');

  /// File path: assets/images/sleep_moon.png
  AssetGenImage get sleepMoon =>
      const AssetGenImage('assets/images/sleep_moon.png');

  /// File path: assets/images/sleep_nap.png
  AssetGenImage get sleepNap =>
      const AssetGenImage('assets/images/sleep_nap.png');

  /// File path: assets/images/sleep_pie_inner.png
  AssetGenImage get sleepPieInner =>
      const AssetGenImage('assets/images/sleep_pie_inner.png');

  /// File path: assets/images/sleep_rem.png
  AssetGenImage get sleepRem =>
      const AssetGenImage('assets/images/sleep_rem.png');

  /// File path: assets/images/sleep_rem_none.png
  AssetGenImage get sleepRemNone =>
      const AssetGenImage('assets/images/sleep_rem_none.png');

  /// File path: assets/images/sleep_sun.png
  AssetGenImage get sleepSun =>
      const AssetGenImage('assets/images/sleep_sun.png');

  /// File path: assets/images/step_calories.png
  AssetGenImage get stepCalories =>
      const AssetGenImage('assets/images/step_calories.png');

  /// File path: assets/images/step_distance.png
  AssetGenImage get stepDistance =>
      const AssetGenImage('assets/images/step_distance.png');

  /// File path: assets/images/step_pie_blue.png
  AssetGenImage get stepPieBlue =>
      const AssetGenImage('assets/images/step_pie_blue.png');

  /// File path: assets/images/step_pie_no.png
  AssetGenImage get stepPieNo =>
      const AssetGenImage('assets/images/step_pie_no.png');

  /// File path: assets/images/successfule_icon.png
  AssetGenImage get successfuleIcon =>
      const AssetGenImage('assets/images/successfule_icon.png');

  /// File path: assets/images/sugar_pie_bg.png
  AssetGenImage get sugarPieBg =>
      const AssetGenImage('assets/images/sugar_pie_bg.png');

  /// File path: assets/images/target_bg.png
  AssetGenImage get targetBg =>
      const AssetGenImage('assets/images/target_bg.png');

  /// File path: assets/images/target_button.png
  AssetGenImage get targetButton =>
      const AssetGenImage('assets/images/target_button.png');

  /// File path: assets/images/target_pie_low.svg
  String get targetPieLow => 'assets/images/target_pie_low.svg';

  /// File path: assets/images/target_pie_mild.svg
  String get targetPieMild => 'assets/images/target_pie_mild.svg';

  /// File path: assets/images/target_pie_moderate.svg
  String get targetPieModerate => 'assets/images/target_pie_moderate.svg';

  /// File path: assets/images/target_pie_normal.svg
  String get targetPieNormal => 'assets/images/target_pie_normal.svg';

  /// File path: assets/images/target_pie_serious.svg
  String get targetPieSerious => 'assets/images/target_pie_serious.svg';

  /// File path: assets/images/target_title_bg.png
  AssetGenImage get targetTitleBg =>
      const AssetGenImage('assets/images/target_title_bg.png');

  /// File path: assets/images/temperature_cen_pie.png
  AssetGenImage get temperatureCenPie =>
      const AssetGenImage('assets/images/temperature_cen_pie.png');

  /// File path: assets/images/temperature_cen_pie_none.png
  AssetGenImage get temperatureCenPieNone =>
      const AssetGenImage('assets/images/temperature_cen_pie_none.png');

  /// File path: assets/images/temperature_fah_pie.png
  AssetGenImage get temperatureFahPie =>
      const AssetGenImage('assets/images/temperature_fah_pie.png');

  /// File path: assets/images/temperature_fah_pie_none.png
  AssetGenImage get temperatureFahPieNone =>
      const AssetGenImage('assets/images/temperature_fah_pie_none.png');

  /// File path: assets/images/temperature_icon.png
  AssetGenImage get temperatureIcon =>
      const AssetGenImage('assets/images/temperature_icon.png');

  /// File path: assets/images/trend_icon.png
  AssetGenImage get trendIcon =>
      const AssetGenImage('assets/images/trend_icon.png');

  /// File path: assets/images/unit_setting_icon.png
  AssetGenImage get unitSettingIcon =>
      const AssetGenImage('assets/images/unit_setting_icon.png');

  /// File path: assets/images/unit_switch.png
  AssetGenImage get unitSwitch =>
      const AssetGenImage('assets/images/unit_switch.png');

  /// File path: assets/images/vector_bottom.png
  AssetGenImage get vectorBottom =>
      const AssetGenImage('assets/images/vector_bottom.png');

  /// File path: assets/images/vector_bottom_grey.png
  AssetGenImage get vectorBottomGrey =>
      const AssetGenImage('assets/images/vector_bottom_grey.png');

  /// File path: assets/images/vector_left.png
  AssetGenImage get vectorLeft =>
      const AssetGenImage('assets/images/vector_left.png');

  /// File path: assets/images/vector_top_blue.png
  AssetGenImage get vectorTopBlue =>
      const AssetGenImage('assets/images/vector_top_blue.png');

  /// File path: assets/images/wechat.png
  AssetGenImage get wechat => const AssetGenImage('assets/images/wechat.png');

  /// File path: assets/images/wechat_icon.svg
  String get wechatIcon => 'assets/images/wechat_icon.svg';

  /// File path: assets/images/weight_pie.png
  AssetGenImage get weightPie =>
      const AssetGenImage('assets/images/weight_pie.png');

  /// File path: assets/images/weight_pie_center.png
  AssetGenImage get weightPieCenter =>
      const AssetGenImage('assets/images/weight_pie_center.png');

  /// File path: assets/images/weight_point.png
  AssetGenImage get weightPoint =>
      const AssetGenImage('assets/images/weight_point.png');

  /// File path: assets/images/weight_target_pie1.png
  AssetGenImage get weightTargetPie1 =>
      const AssetGenImage('assets/images/weight_target_pie1.png');

  /// File path: assets/images/weight_target_pie2.png
  AssetGenImage get weightTargetPie2 =>
      const AssetGenImage('assets/images/weight_target_pie2.png');

  /// File path: assets/images/weight_target_pie3.png
  AssetGenImage get weightTargetPie3 =>
      const AssetGenImage('assets/images/weight_target_pie3.png');

  /// File path: assets/images/weight_target_pie4.png
  AssetGenImage get weightTargetPie4 =>
      const AssetGenImage('assets/images/weight_target_pie4.png');

  /// List of all assets
  List<dynamic> get values => [
        accountSecurityIcon,
        airingFunction,
        aizoRing,
        apple,
        arrayRightGreen,
        avatarDefault,
        backBlueBlur,
        backIcon,
        backIconWhite,
        bluetoothBackCircle1,
        bluetoothCircle,
        bluetoothFoundOpen,
        bluetoothFunction,
        bluetoothMenu,
        bluetoothNo,
        bluetoothOn,
        bluetoothSearch1,
        bluetoothSearch2,
        bodyBlueIcon,
        bodyNoIcon,
        brain,
        checkMark,
        checkBlue,
        checkCircle,
        deleteIcon,
        edit,
        errorInfo,
        facebook,
        failedIcon,
        filter,
        fitnessConsumFire,
        fitnessDistance,
        fitnessHolisticPeopleNo,
        fitnessHolisticPeopleYellow,
        fitnessPie,
        fitnessStep,
        google,
        greenCirclePng,
        greenCircleSvg,
        healthActivityBlue,
        healthActivityNo,
        healthBg,
        healthGoalIcon,
        healthHeartNo,
        healthHeartRange1,
        healthHeartRange2,
        healthHeartRange3,
        healthHeartRed,
        healthInfoBanner,
        healthMessageIcon,
        healthNotClick,
        healthOnClick,
        healthOxygen,
        healthPicture1,
        healthPicture2,
        healthPicture3,
        healthRecoveryIcon,
        healthSleepRange1,
        healthSleepRange2,
        healthSleepRange3,
        healthTemperature,
        heartIcon,
        heartRateIcon,
        homeNotClick,
        homeOnClick,
        homeOverNone,
        knowledge,
        measureButton,
        myNotClick,
        myOnClick,
        navigationRight,
        noneCircle151,
        notion,
        notionMy,
        notionsIcon,
        orangeCircle,
        oxygenDetailBanner,
        oxygenIcon,
        pen,
        plusOuterCircle,
        pointer,
        pressureDashed,
        pressureIcon,
        pressureInnerLow,
        pressureInnerMild,
        pressureInnerModerate,
        pressureInnerNormal,
        pressureInnerNothing,
        pressureInnerSerious,
        pressureOuterLow,
        pressureOuterMild,
        pressureOuterModerate,
        pressureOuterNormal,
        pressureOuterNothing,
        pressureOuterSerious,
        pressureReminderNoting,
        ratePie,
        ratePieNone,
        record,
        redCircle,
        relatedInfor,
        rightArrow48,
        search,
        selectedIcon,
        setting,
        settingButtonNo,
        settingButtonOn,
        settingMy,
        sleepAwake,
        sleepBreath,
        sleepDeep,
        sleepDeepNone,
        sleepIcon,
        sleepImage1,
        sleepImage2,
        sleepLight,
        sleepMoon,
        sleepNap,
        sleepPieInner,
        sleepRem,
        sleepRemNone,
        sleepSun,
        stepCalories,
        stepDistance,
        stepPieBlue,
        stepPieNo,
        successfuleIcon,
        sugarPieBg,
        targetBg,
        targetButton,
        targetPieLow,
        targetPieMild,
        targetPieModerate,
        targetPieNormal,
        targetPieSerious,
        targetTitleBg,
        temperatureCenPie,
        temperatureCenPieNone,
        temperatureFahPie,
        temperatureFahPieNone,
        temperatureIcon,
        trendIcon,
        unitSettingIcon,
        unitSwitch,
        vectorBottom,
        vectorBottomGrey,
        vectorLeft,
        vectorTopBlue,
        wechat,
        wechatIcon,
        weightPie,
        weightPieCenter,
        weightPoint,
        weightTargetPie1,
        weightTargetPie2,
        weightTargetPie3,
        weightTargetPie4
      ];
}

class Assets {
  Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName);

  final String _assetName;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
