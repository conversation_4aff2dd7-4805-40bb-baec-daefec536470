/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-15 14:22:18
 * @FilePath: /rpmappmaster/lib/flavors/env_config.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:logger/logger.dart';

import '/app/core/values/app_values.dart';

/**
 * 用于在 Flutter 应用中配置不同环境下的参数，并初始化日志记录器。
 * 通过使用构造方法设置必要的环境参数，并在初始化时配置 Logger 实例，用于在应用中打印和管理日志。
 */
class EnvConfig {
  final String appName;
  final String baseUrlCN;
  final String baseUrlOUT;
  final String clientID;
  final String clientSercet;
  final String auth0Domain;
  final String userPoolId;
  final String appId;
  final bool shouldCollectCrashLog;


  EnvConfig({
    required this.appName,
    required this.baseUrlCN,
    required this.baseUrlOUT,
    this.shouldCollectCrashLog = false,
    required this.clientID,
    required this.clientSercet, 
    required this.auth0Domain,
    required this.userPoolId,
    required this.appId,
  });
}