// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-12-13 16:59:53
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-12-13 17:01:57
//  * @FilePath: /rpmappmaster/lib/app/middleware/token_middleware.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/values/app_values.dart';
// import 'package:aiCare/app/services/secureStorageService.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../routes/app_pages.dart';

// class TokenMiddleware extends GetMiddleware {
//   @override
//   int? get priority => 1; // 优先级，越小越先执行

//   @override
//   Future<RouteSettings?> redirect(String? route) async {
//     // 检查是否有有效的 Token
//     final idToken = await SecureStorageService.instance.getData(AppValues.idToken);

//     if (idToken != null) {
//       // 有 Token，设置初始页面为 TABS
//       AppPages.INITIAL = Routes.TABS;
//       return null; // 不做重定向
//     } else {
//       // 没有 Token，设置初始页面为 LOGIN
//       AppPages.INITIAL = Routes.LOGIN;
//       return const RouteSettings(name: Routes.LOGIN); // 重定向到登录页面
//     }
//   }
// }
