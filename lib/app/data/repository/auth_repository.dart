abstract class AuthRepository {
  // Future<GithubProjectSearchResponse> searchProject(
  //     GithubSearchQueryParam queryParam);

  Future<String> signUp(String username, String password);

  Future<String> loginWithEmailAndPassword(String username, String password);

  Future<bool> sendSms(String phoneNumber);

  Future<bool> loginWithPhoneAndCode(String username, String password);

  Future<bool> signInWithWechat();

  Future<bool> signInWithApple();

  Future<bool> signInWithFacebook();

  Future<bool> signInWithGoogle();

  Future<bool> forgetPassword();

  Future<Map<String, dynamic>> getUserDetails();

  Future<bool> managementAPIToken();

  Future<bool> updateUser(
      String userId, String accessToken, Map<String, dynamic> updates);

  Future<void> logout();

  Future<String> deleteAccount();
}
