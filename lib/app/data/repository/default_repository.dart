/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-15 16:02:27
 * @FilePath: /rpmappmaster/lib/app/data/repository/default_repository.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */


import 'dart:io';

import 'package:aiCare/app/data/model/aizo_auto_data.dart';
import 'package:aiCare/app/data/model/aizo_sleep_data.dart';
import 'package:aiCare/app/data/model/my_goal.dart';
import 'package:aiCare/app/data/model/user_model.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxyge_page_data.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/fitness/model/fitness_daily_activity.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_days.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_page_data.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_page_data.dart';
import 'package:dio/dio.dart';

abstract class DefaultRepository {
  Future<UserModel?> getInfo();

  Future<void> postInfo([UserModel? user]);

  Future<void> patchInfo(Map<String,Object> map);

  Future<String?> uploadImage(File file);

  Future<OxygenPageData> getOxygenPage(String fromDate, String toDate, int page,{int pageSize = 10,int dataSource = 0,CancelToken? cancelToken = null,int? type});

  Future<List<OxygenData>> getOxygen(String fromDate, String toDate,{String types = "history"});

    Future<List<dynamic>>getOxygenDays(DateTime date);

  Future<List<dynamic>>getOxygenWeeks(DateTime date);

  Future<List<dynamic>>getOxygenMonths(DateTime date);


  Future<OxygenData?> getOxygenLast();

  Future<void> postOxygen({required OxygenData data});

  Future<void> deleteOxygen(DateTime date);

  Future<TemperaturePageData> getTemperaturePage(String fromDate, String toDate, int page,{int pageSize = 10,int dataSource = 0,CancelToken? cancelToken = null,int? type});

  Future<List<TemperatureData>> getTemperature(String fromDate, String toDate,{String types = "history"});

      Future<List<dynamic>>getTemperatureDays(DateTime date);

  Future<List<dynamic>>getTemperatureWeeks(DateTime date);

  Future<List<dynamic>>getTemperatureMonths(DateTime date);

  Future<TemperatureData?> getTemperatureLast();

  Future<void> postTemperature({required TemperatureData data});

  Future<void> deleteTemperature(DateTime date);

  Future<List<HeartRateData>> getHeartRateList(String fromDate, String toDate,{String types = "history"});

  Future<HeartRatePageData> getHeartRatePage(String fromDate, String toDate, int page,{int pageSize = 10,int dataSource = 0,CancelToken? cancelToken = null,int? type});
  Future<HeartRateData> getHeartRate();
  Future<HeartRateData?> getHeartRateLast();

  Future<void> postHeartRate({required HeartRateData data});

  Future<void> deleteHeartRate(DateTime date);

  Future<List<dynamic>>getHeartRateDays(DateTime date);

  Future<List<dynamic>>getHeartRateWeeks(DateTime date);

  Future<List<dynamic>>getHeartRateMonths(DateTime date);

  Future<void> aizoAutoUpload(List<AizoAutoData> list);

  Future<void> postGoalSetting(MyGoalClass info);

  Future<List<AizoSleepData>> getSleepDataList(String fromDate, String toDate,{String types = "history"});

  Future<void> postSleepData({required AizoSleepData data});

  Future<FitnessDailyActivity?> getBodyFitnessLast();

  // Future<void> getFailyActivities();

  Future<AizoSleepData?> getAizoSleepDataLast();

  Future<List<AizoAutoData>> getAutoData();

}
