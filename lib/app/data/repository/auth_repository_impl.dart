/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-06-18 16:19:34
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-13 16:00:36
 * @FilePath: /RPM-APP/lib/app/data/repository/auth_repository_impl.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-02-28 16:57:04
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-13 15:33:41
 * @FilePath: /RPM-APP/lib/app/data/repository/auth_repository_impl.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:io';

import 'package:aiCare/app/core/enum/region_unit.dart';
import 'package:aiCare/app/network/api/api.dart';
// import 'package:aiCare/app/services/l10nService.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:auth0_flutter/auth0_flutter.dart';

import 'package:authing_sdk/result.dart';
import 'package:authing_sdk/user.dart';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:aiCare/app/core/base/remote/base_remote_source.dart';
import 'package:aiCare/app/core/values/app_values.dart';

import 'package:aiCare/app/data/repository/auth_repository.dart';

import 'package:aiCare/app/network/dio_provider.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/auth0Service.dart';
import 'package:authing_sdk/client.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class AuthRepositoryImpl extends BaseRemoteSource implements AuthRepository {
  Auth0 auth0 = Auth0Service.instance.auth0;

  @override
  Future<bool> forgetPassword() {
    // TODO: implement forgetPassword
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> getUserDetails() async {
    throw UnimplementedError();
  }

  @override
  Future<String> loginWithEmailAndPassword(
      String username, String password) async {
    try {
      var result;
      User user;
      if (await storage.getRegion() == RegionUnit.zh) {
        result = await AuthClient.loginByAccount(username, password);

        user = result.user; // user info
        
        storage.setString(AppValues.accessToken, result.accessToken);
        storage.setString(AppValues.idToken, result.idToken);
        storage.setString(AppValues.id, result.user.sub);
      } else {
        result = await auth0.api.login(
          usernameOrEmail: username,
          password: password,
          connectionOrRealm: "Username-Password-Authentication",
        );
        logger.d("查看账号密码登录的日志");
        logger.d(result.toString());
        storage.setString(AppValues.accessToken, result.accessToken);
        storage.setString(AppValues.idToken, result.idToken);
        storage.setString(AppValues.id, result.user.sub);
      }

      // 处理成功登录的情况
      print("Login successful: $result");
      print(
          "----------------------------login successful --------------------");
      print(result.toString());

      print(
          "----------------------------login successful --------------------");
      return "true";

      // 在这里可以进一步处理返回的结果，比如存储认证令牌等
    } catch (e) {
      print("----------------------------signup error --------------------");
      print('login error: $e - stack: ');
      print("----------------------------signup error --------------------");
      return e.toString();
    }
  }

  @override
  Future<bool> signInWithWechat() async {
    try {
      var result;
      var code;
      if (await storage.getString(AppValues.domestic) == "true") {
        AuthResult result = await AuthClient.loginByWechat(
            "628b26330995e701146bc591", "1cfc8944b9f7fe34ca76b3cb4aec68ef");
        ;
        code = result.code;
        logger.d("这个这个");
        logger.d(code);
        logger.d(result.message);

        // storage.setString(AppValues.phoneCode, code);
      } else {}

      // 处理成功登录的情况
      print("Login successful: $result");
      print(
          "----------------------------login successful --------------------");
      print(result.toString());

      print(
          "----------------------------login successful --------------------");
      return true;

      // 在这里可以进一步处理返回的结果，比如存储认证令牌等
    } catch (e) {
      print("----------------------------signup error --------------------");
      print('login error: $e - stack:');
      print("----------------------------signup error --------------------");
      return false;
    }
  }

  @override
  Future<bool> loginWithPhoneAndCode(String username, String password) async {
    try {
      var result;
      User user;
      final region = storage.getRegion();
      if (region == RegionUnit.zh) {
        result = await AuthClient.loginByPhoneCode(username, password,
            phoneCountryCode: "+86");
        user = result.user; // get user info

        if (user.accessToken != null)
          storage.setString(AppValues.accessToken, user.accessToken!);
        storage.setString(AppValues.idToken, user.token);
        storage.setString(AppValues.id, user.id);
        logger.d(user.token);
        if (result.code != 200) {
          return false;
        }
        print("asdasdasdasd");
        print(user.id); //66977b2e33934325d3ff5250
        print(user.token); //
        print(user.accessToken); //null
      } else {}

      // 处理成功登录的情况
      print("Login successful: $result");
      print(
          "----------------------------login successful --------------------");
      print(result.toString());

      print(
          "----------------------------login successful --------------------");
      return true;

      // 在这里可以进一步处理返回的结果，比如存储认证令牌等
    } catch (e) {
      print("----------------------------signup error --------------------");
      print('login error: $e - stack:');
      print("----------------------------signup error --------------------");
      return false;
    }
  }

  @override
  Future<bool> sendSms(String phoneNumber) async {
    try {
      var result;
      var code;
      final region = storage.getRegion();
      if (region == RegionUnit.zh) {
        AuthResult result = await AuthClient.sendSms(phoneNumber, "+86");
        code = result.code;
        logger.d("获得验证码$code");

        // storage.setString(AppValues.phoneCode, code);
      } else {}

      // 处理成功登录的情况
      print("Login successful: $result");
      print(
          "----------------------------login successful --------------------");
      print(result.toString());

      print(
          "----------------------------login successful --------------------");
      return true;

      // 在这里可以进一步处理返回的结果，比如存储认证令牌等
    } catch (e) {
      print("----------------------------signup error --------------------");
      print('login error: $e - stack:');
      print("----------------------------signup error --------------------");
      return false;
    }
  }

  @override
  Future<void> logout() async {
    try {
      if (await storage.getRegion() == RegionUnit.zh) {
        AuthResult result = await AuthClient.logout();
        storage.deleteAll();
      } else {
        String scheme;
        if (Platform.isAndroid) {
          scheme = "com.aihhnet.aicare";
        } else {
          scheme = "com.aihhnet.aicare";
        }
        await auth0.webAuthentication(scheme: scheme).logout(
              returnTo: "",
              useHTTPS: true,
            );
        storage.deleteAll();
      }

      // Get.offAllNamed(Routes.LOGIN,predicate: (route) => route.settings.name != Routes.TABS, // 过滤条件

      // );
      Get.offAllNamed(Routes.LOGIN);
    } catch (e) {
      print("----------------------------signup error --------------------");
      print('login error: $e - stack: ');
      print("----------------------------signup error --------------------");
    }
  }

  @override
  Future<String> deleteAccount() async {
    var uri = DioProvider.baseUrl + Api.deleteAccount;
    var dioCall = dioClient.delete(uri);

    try {
      return callApiWithErrorParser(dioCall).then((response) async {
        if (response.data["code"] != 1) {
          logger.d("Delete Account Failed");
          return "Delete Account Failed";
          // return TemperatureData();
        } else {
          logger.d("删除账号成功");
          storage.deleteAll();
          Get.offAllNamed(Routes.LOGIN);
          // TabsController tabsController  = Get.find();
          // tabsController.currentIndex.value = 0;
          return "Delete Account Success";
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> managementAPIToken() {
    // TODO: implement managementAPIToken
    throw UnimplementedError();
  }

  @override
  Future<bool> signInWithApple() async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
      logger.d("获得的东西");
      logger.d(credential.authorizationCode);
      logger.d(credential.email);

      var uri = DioProvider.authUrl + "/oauth/token";

      var dioCall = dioClient.post(uri,
          data: {
            "client_id": DioProvider.clientID,
            "grant_type": 'urn:ietf:params:oauth:grant-type:token-exchange',
            "subject_token": credential.authorizationCode,
            "subject_token_type":
                'http://auth0.com/oauth/token-type/apple-authz-code',
            "scope": 'openid profile offline_access',
          },
          options: Options(
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          ));

      return callApiWithErrorParser(dioCall).then((response) {
        logger.d("调用给auth0的接口");
        logger.d(response);
        var result = response.data;
        // Map<String, dynamic> result = jsonDecode(response.data);
        logger.d(result);
        logger.d(result["access_token"]);

        storage.setString(AppValues.accessToken, result["access_token"]);
        storage.setString(AppValues.idToken, result["id_token"]);

        // 解码 idToken 获取用户 ID
        final jwt = JWT.decode(result["id_token"]);
        final userId = jwt.payload['sub'];

        // // 存储用户 ID
        storage.setString(AppValues.id, userId);
        logger.d("用户id:${userId}");
        return true;
      });

      // 在这里可以进一步处理返回的结果，比如存储认证令牌等
    } catch (e) {
      print("----------------------------signup error --------------------");
      print('login error: $e - stack:');
      print("----------------------------signup error --------------------");
      return false;
    }
  }

  @override
  Future<bool> signInWithFacebook() {
    // TODO: implement signInWithFacebook
    throw UnimplementedError();
  }

  @override
  Future<bool> signInWithGoogle() async {
    // TODO: implement signInWithGoogle
    try {
      // Credentials result =
      //     await auth0.webAuthentication(scheme: Platform.isAndroid?"com.aihhnet.aicare":"com.aihnet.aihcare").login(
      //           audience: '${DioProvider.authUrl}/api/v2/', // 可选，如果需要访问特定的API
      //           scopes: {'openid', 'profile', 'email'}, // 需要的权限
      //           // redirectUrl:
      //           //     'com.aihhnet.aicare://ineck.auth0.com/android/com.aihhnet.aicare/callback', // 自定义的回调 URL
      //                               redirectUrl: Platform.isAndroid? 'com.aihhnet.aicare://ineck.auth0.com/android/com.aihhnet.aicare/callback, ':'com.aihnet.aihcare://ineck.auth0.com/ios/com.aihnet.aihcare/callback', // 自定义的回调 URL
      //           useHTTPS: true, // 确保使用 HTTPS
      //           useEphemeralSession: true, // 设置为 true 则在会话结束时不保存会话数据
      //           parameters: {
      //             'connection': 'google-oauth2', // 指定 Google 连接
      //           },
      //         );
      String redirectUrl;
      String scheme;
      if (Platform.isAndroid) {
        redirectUrl =
            'com.aihhnet.aicare://ineck.auth0.com/android/com.aihhnet.aicare/callback';
        scheme = "com.aihhnet.aicare";
      } else {
        redirectUrl =
            'com.aihnet.aihcare://ineck.auth0.com/ios/com.aihnet.aihcare/callback';
        scheme = "com.aihhnet.aicare";
      }
      Credentials result = await auth0.webAuthentication(scheme: scheme).login(
            audience: '${DioProvider.authUrl}/api/v2/', // 可选，如果需要访问特定的API
            scopes: {'openid', 'profile', 'email'}, // 需要的权限
            // redirectUrl:
            //     'com.aihhnet.aicare://ineck.auth0.com/android/com.aihhnet.aicare/callback', // 自定义的回调 URL
            redirectUrl: redirectUrl,
            useHTTPS: true, // 确保使用 HTTPS
            useEphemeralSession: true, // 设置为 true 则在会话结束时不保存会话数据
            parameters: {
              'connection': 'google-oauth2', // 指定 Google 连接
              'prompt': 'select_account', // 强制用户选择账户
            },
          );

      // 处理成功登录的情况
      print("signup successful: $result");
      print(
          "----------------------------signup successful --------------------");
      print(result.user.sub);
      storage.setString(AppValues.accessToken, result.accessToken);
      storage.setString(AppValues.idToken, result.idToken);
      storage.setString(AppValues.id, result.user.sub);
      print(
          "----------------------------signup successful --------------------");
      return true;

      // 在这里可以进一步处理返回的结果，比如存储认证令牌等
    } catch (e) {
      print("----------------------------signup error --------------------");
      print('login error: $e - stack:');
      print("----------------------------signup error --------------------");
      return false;
    }
  }

  @override
  Future<String> signUp(String username, String password) async {
    try {
      var result;
      User user;
      //print('App is domestic: ' + await storage.getString(AppValues.domestic) == "true".toString());
      if (await storage.getString(AppValues.domestic) == "true") {
        result = await AuthClient.registerByEmail(username, password);
        // logger.d(result.code == 2026);
        if (result.code == 2026) {
          // return false;
          logger.d("这是什么");
          return result.toString();
        }
        // user = result.user;
        //print(user);
        // logger.d(result.);
      } else {
        result = await auth0.api.signup(
          email: username,
          password: password,
          connection: "Username-Password-Authentication",
        );
        //print(result.username);
      }

      // result.

      // 处理成功登录的情况
      print("signup successful: $result");
      print(
          "----------------------------signup successful --------------------");

      print(
          "----------------------------signup successful --------------------");
      // return true;
      return result.toString();

      // 在这里可以进一步处理返回的结果，比如存储认证令牌等
    } catch (e) {
      print("----------------------------signup error --------------------");
      print('login error: $e - stack: ');
      print("----------------------------signup error --------------------");
      // return false;
      return e.toString();
    }
  }

  @override
  Future<bool> updateUser(
      String userId, String accessToken, Map<String, dynamic> updates) {
    // TODO: implement updateUser
    throw UnimplementedError();
  }
}
