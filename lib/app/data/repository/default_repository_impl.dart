/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-30 14:29:25
 * @FilePath: /rpmappmaster/lib/app/data/repository/default_repository_impl.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:io';
import 'dart:math';

import 'package:aiCare/app/data/model/aizo_auto_data.dart';
import 'package:aiCare/app/data/model/aizo_sleep_data.dart';
import 'package:aiCare/app/data/model/my_goal.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxyge_page_data.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/fitness/model/fitness_daily_activity.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_days.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_page_data.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_page_data.dart';
import 'package:dio/dio.dart' as dio;
import 'package:aiCare/app/core/base/remote/base_remote_source.dart';
import 'package:aiCare/app/data/model/user_model.dart';
import 'package:aiCare/app/data/repository/default_repository.dart';
import 'package:aiCare/app/network/api/Api.dart';
import 'package:aiCare/app/network/dio_provider.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart';
import 'package:aiCare/app/core/utils/logger_singleton.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/network/exceptions/api_exception.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';

class DefaultRepositoryImpl extends BaseRemoteSource
    implements DefaultRepository {
  @override
  Future<UserModel?> getInfo() {
    var uri = DioProvider.baseUrl + Api.infoApi;
    var dioCall = dioClient.get(uri);

    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseInfoResponse(response));
    } catch (e) {
      logger.e("获取用户信息失败: $e");
      if (e is ApiException) {
        rethrow;
      } else {
        // 处理数据转换错误
        ToastUtil.showError(Get.context!, T.dataParseFailedRetry.tr);
        throw ApiException(
          httpCode: -1,
          status: "Parse Error",
          message: "数据解析失败",
        );
      }
    }
  }

  @override
  Future<void> postInfo([UserModel? user]) {
    var uri = DioProvider.baseUrl + Api.infoApi;
    var dioCall;
    var myData = {
        "name": "defualtName",
        "age": 18,
        "gender": "U",
        "height": 0.0,
        "weight": 0.0,
        "photo": "",
        "birthdate": "2025-01-01",
        "phone": "",
        "email": "",
        "location": "",
        "followers_number": 0,
        "following_number": 0,
        "article_views": 0,
        "device_status": false,
        "disease_type": [],
        "certification_status": false,
        "doctor_title": "",
        "hospital_level": "",
        "hospital": "",
        "membership_id": -1,
        "membership_name": "free",
        "vip_active_time": 0,
        "vip_end_date": 0,
        "vip_start_date": 0,
        "member_trial": false,
        "vip_status": false,
        "isPersonalizeRecommendation": false,
        "preferences": {
          "height_unit": "I",
          "weight_unit": "I",
          "tempurate_unit": "I",
          "blood_sugar_unit": "I"
        },
        "tags": [],
        "target": MyGoalClass(
            step: 6000,
            bloodPressure: [80, 100],
            bloodGlucose: BloodGlucoseSub(
              wakeUp: GlucoseSub(timeRange: ["06AM-07AM"], values: [0]),
              breakfast: GlucoseSub(timeRange: [
                "07AM-09AM",
                "09AM-11AM",
              ], values: [
                0,
                0
              ]),
              lunch: GlucoseSub(timeRange: [
                "11AM-01PM",
                "01PM-05PM",
              ], values: [
                0,
                0
              ]),
              dinner: GlucoseSub(timeRange: [
                "05PM-07PM",
                "07PM-08PM",
              ], values: [
                0,
                0
              ]),
              bedtime: GlucoseSub(timeRange: ["08PM-10PM"], values: [0]),
              midnight: GlucoseSub(timeRange: ["10PM-06AM"], values: [0]),
            ),
            reminders: [120, 60])
      };
    if (user == null) {
      logger.d("创建用户");
      logger.d(myData.toString());
      logger.d("target");
      logger.d(myData["target"].toString());
      dioCall = dioClient.post(uri, queryParameters: {
        'type': 0,
      }, data:myData);
    
    } else {
      dioCall = dioClient.post(uri, queryParameters: {
        'type': 0,
      }, data: {
        "name": user.name,
        "age": user.age,
        "gender": user.gender,
        "height": user.height,
        "weight": user.weight,
        "photo": user.photo,
        "birthdate": user.birthdate,
        "phone": user.phone,
        "email": user.email,
        "location": user.location,
        "followers_number": user.followersNumber,
        "following_number": user.followingNumber,
        "article_views": user.articleViews,
        "device_status": user.deviceStatus,
        "disease_type": user.diseaseType,
        "certification_status": user.certificationStatus,
        "doctor_title": user.doctorTitle,
        "hospital_level": user.hospitalLevel,
        "hospital": user.hospital,
        "membership_id": user.membershipId,
        "membership_name": user.membershipName,
        "vip_active_time": user.vipActiveTime,
        "vip_end_date": user.vipEndDate,
        "vip_start_date": user.vipStartDate,
        "member_trial": user.memberTrial,
        "vip_status": user.vipStatus,
        "isPersonalizeRecommendation": user.isPersonalizeRecommendation,
        "preferences": user.preferences,
        "tags": user.tags
      });
    }
    logger.d(myData.toString());


    try {
      return callApiWithErrorParser(dioCall)
          .then((response) => _parseInfoResponse(response));
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> patchInfo(Map<String, Object> map) {
    var uri = DioProvider.baseUrl + Api.infoApi;

    var dioCall = dioClient.patch(uri, queryParameters: {"type": 0}, data: map);

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (response.data["code"] == 1) {
          logger.d("修改成功");
          return true;
        } else {
          logger.d("修改失败");
          return false;
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  UserModel? _parseInfoResponse(dio.Response<dynamic> response) {
    if (response.data["code"] == 0) {
      logger.d("查看response.dataUserinfo");
      logger.d(response.data["msg"]);
      logger.d(response.data["data"]);
      return null;
    }
    return UserModel.fromJson(response.data["data"]);
  }

  @override
  Future<String?> uploadImage(File file) async {
    var uri = DioProvider.baseUrl + Api.uploadImageApi;
    // 创建 FormData
    var formData = dio.FormData.fromMap({
      "file": await dio.MultipartFile.fromFile(
        file.path,
        filename: file.path.split('/').last, // 自动获取文件名
      ),
    });
    var dioCall = dioClient.post(uri, data: formData);

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        logger.d("查看是否上传成功");
        logger.d(response);
        if (response.data["code"] == 1) {
          return response.data["data"];
        }
        return null;
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<OxygenData?> getOxygenLast() {
    logger.d("获取最新值");

    var uri = DioProvider.baseUrl + Api.bloodOxygenLast;

    var dioCall = dioClient.get(uri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (response.data["code"] != 1) {
          logger.d("无数据血氧");
          return null;
        } else {
          logger.d("查看response.data");
          // logger.d(response.data);
          return OxygenData(
              dataSource: response.data["data"]["type"],
              percentage: response.data["data"]["saturation"],
              date: DateTime.parse(response.data["data"]["date_time"]));
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<OxygenPageData> getOxygenPage(String fromDate, String toDate, int page,
      {int pageSize = 10,
      int dataSource = 0,
      dio.CancelToken? cancelToken = null,
      int? type}) {
    logger.d("开始获取血氧分页数据");
    var uri = DioProvider.baseUrl + Api.bloodOxygenPage;

    var params = {"0": "0"};
    if (type != null) {
      params = {
        'start_time': fromDate,
        'end_time': toDate,
        'data_sources': dataSource.toString(),
        'test': '1',
        'page': page.toString(),
        "page_size": pageSize.toString(),
        "type": type.toString()
      };
    } else {
      params = {
        'start_time': fromDate,
        'end_time': toDate,
        'data_sources': dataSource.toString(),
        'test': '1',
        'page': page.toString(),
        "page_size": pageSize.toString(),
      };
    }
    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);
    logger.d("查看url：${fullUri.toString()}");

    var dioCall = dioClient.get(
      fullUri.toString(), cancelToken: cancelToken, // 确保这里传递了 token
    );
    try {
      return callApiWithErrorParser(dioCall).then((response) {
        // logger.w("获取page数据");
        // logger.w(response.data);
        if (response.data["data"].length == 0) {
          // logger.w("无数据");
          return OxygenPageData.withDefaults();
        } else {
          // logger.w("有数据");
          // logger.d(response.data["data"]["data"]);

          List<OxygenData> oxygenDataList = [];
          for (var data in response.data["data"]["data"]) {
            oxygenDataList.add(OxygenData.fromJsonToAPI(data));
          }
          return OxygenPageData(
              data: oxygenDataList,
              page: response.data["data"]["page"],
              pageSize: response.data["data"]["page_size"],
              total: response.data["data"]["total"]);
        }
      });
    } on dio.DioException catch (e) {
      // 明确捕获 Dio 异常
      if (e.type == dio.DioExceptionType.cancel) {
        logger.d("请求已被取消: ${e.message}");
        throw e; // 或返回特定结果
      }
      logger.e("请求失败: ${e.message}");
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<OxygenData>> getOxygen(String fromDate, String toDate,
      {String types = "history"}) {
    var uri = DioProvider.baseUrl + Api.bloodOxygen;

    var params = {
      'from_date': fromDate,
      'to_date': toDate,
      if (types == "history") 'types': 'history',
      'test': '1'
    };
    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (types != "history") {
          List<OxygenData> oxygenDataList = [
            OxygenData(percentage: response.data["data"]["saturation"])
          ];
          return oxygenDataList;
        }
        if (response.data["data"] is! List) {
          logger.d("无数据");
          return [];
        } else {
          logger.d("有数据");
          logger.d(response.data["data"]);
          logger.d(DateTime.parse(response.data["data"][0]["date_time"]));

          List<OxygenData> oxygenDataList = [];
          for (var data in response.data["data"]) {
            oxygenDataList.add(OxygenData.fromJsonToAPI(data));
          }
          return oxygenDataList;
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> postOxygen({required OxygenData data}) {
    var uri = DioProvider.baseUrl + Api.bloodOxygen;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(data.date!.toUtc());

    logger.d("查看添加日期");
    logger.d(formattedDate);

    var dioCall = dioClient.post(uri, queryParameters: {
      "test": 1,
      "date_time": formattedDate,
      "data_sources": data.dataSource
    }, data: {
      "saturation": data.percentage
    });
    try {
      return callApiWithErrorParser(dioCall).then(
          (response) => {logger.d("postOxygen获得的result："), logger.d(response)});
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> deleteOxygen(DateTime date) {
    var uri = DioProvider.baseUrl + Api.bloodOxygen;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
    var dioCall = dioClient.delete(
      uri,
      queryParameters: {"test": 1, "date_time": formattedDate},
    );
    try {
      return callApiWithErrorParser(dioCall).then((response) =>
          {logger.d("deleteOxygenn获得的result："), logger.d(response)});
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> deleteTemperature(DateTime date) {
    var uri = DioProvider.baseUrl + Api.bodyTemperature;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
    var dioCall = dioClient.delete(
      uri,
      queryParameters: {"test": 1, "date_time": formattedDate},
    );
    try {
      return callApiWithErrorParser(dioCall).then((response) =>
          {logger.d("deleteTemperature获得的result："), logger.d(response)});
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<TemperaturePageData> getTemperaturePage(
      String fromDate, String toDate, int page,
      {int pageSize = 10,
      int dataSource = 0,
      dio.CancelToken? cancelToken = null,
      int? type}) {
    logger.d("开始获取体温分页数据");
    var uri = DioProvider.baseUrl + Api.bodyTemperaturePage;

    var params = {"0": "0"};
    if (type != null) {
      params = {
        'start_time': fromDate,
        'end_time': toDate,
        'data_sources': dataSource.toString(),
        'test': '1',
        'page': page.toString(),
        "page_size": pageSize.toString(),
        "type": type.toString()
      };
    } else {
      params = {
        'start_time': fromDate,
        'end_time': toDate,
        'data_sources': dataSource.toString(),
        'test': '1',
        'page': page.toString(),
        "page_size": pageSize.toString(),
      };
    }
    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);
    logger.d("查看url：${fullUri.toString()}");

    var dioCall = dioClient.get(
      fullUri.toString(), cancelToken: cancelToken, // 确保这里传递了 token
    );
    try {
      return callApiWithErrorParser(dioCall).then((response) {
        // logger.w("获取page数据");
        // logger.w(response.data);
        if (response.data["data"].length == 0) {
          // logger.w("无数据");
          return TemperaturePageData.withDefaults();
        } else {
          // logger.w("有数据");
          // logger.d(response.data["data"]["data"]);

          List<TemperatureData> temperatureDataList = [];
          for (var data in response.data["data"]["data"]) {
            temperatureDataList.add(TemperatureData.fromJsonToAPI(data));
          }
          return TemperaturePageData(
              data: temperatureDataList,
              page: response.data["data"]["page"],
              pageSize: response.data["data"]["page_size"],
              total: response.data["data"]["total"]);
        }
      });
    } on dio.DioException catch (e) {
      // 明确捕获 Dio 异常
      if (e.type == dio.DioExceptionType.cancel) {
        logger.d("请求已被取消: ${e.message}");
        throw e; // 或返回特定结果
      }
      logger.e("请求失败: ${e.message}");
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<TemperatureData?> getTemperatureLast() {
    logger.d("获取最新值");

    var uri = DioProvider.baseUrl + Api.bodyTemperatureLast;

    var dioCall = dioClient.get(uri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (response.data["code"] != 1) {
          logger.d("无数据体温");
          return null;
        } else {
          // logger.d("查看response.data");
          // logger.d(response.data);
          return TemperatureData(
              dataSource: response.data["data"]["type"],
              data: response.data["data"]["temperature"],
              date: DateTime.parse(response.data["data"]["date_time"]));
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<TemperatureData>> getTemperature(String fromDate, String toDate,
      {String types = "history"}) {
    var uri = DioProvider.baseUrl + Api.bodyTemperature;

    var params = {
      'from_date': fromDate,
      'to_date': toDate,
      if (types == "history") 'types': 'history',
      'test': '1',
      // 'data_sources':'1'
    };
    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (types != "history") {
          logger.d(
              "response.data['data']['temperature]：${response.data["data"]}");
          List<TemperatureData> temperatureDataList = [
            TemperatureData(
                data: response.data["code"] != -1
                    ? response.data["data"]["temperature"]
                    : 0.0)
          ];
          return temperatureDataList;
        }

        if (response.data["data"] is! List) {
          logger.d("无数据");
          return [];
        } else {
          logger.d("有数据");
          logger.d(response.data["data"]);
          logger.d(DateTime.parse(response.data["data"][0]["date_time"]));

          List<TemperatureData> temperatureDataList = [];
          for (var data in response.data["data"]) {
            temperatureDataList.add(TemperatureData.fromJsonToAPI(data));
          }
          return temperatureDataList;
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> postTemperature({required TemperatureData data}) {
    var uri = DioProvider.baseUrl + Api.bodyTemperature;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(data.date!.toUtc());

    var dioCall = dioClient.post(uri, queryParameters: {
      "test": 1,
      "date_time": formattedDate,
      "data_sources": data.dataSource
    }, data: {
      "temperature": data.data
    });
    try {
      return callApiWithErrorParser(dioCall).then((response) =>
          {logger.d("postTemperature获得的result："), logger.d(response)});
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> deleteHeartRate(DateTime date) {
    var uri = DioProvider.baseUrl + Api.heartRateV2;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
    var dioCall = dioClient.delete(
      uri,
      queryParameters: {"test": 1, "date_time": formattedDate},
    );
    try {
      return callApiWithErrorParser(dioCall).then((response) =>
          {logger.d("deleteHeartRateData获得的result："), logger.d(response)});
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<HeartRatePageData> getHeartRatePage(
      String fromDate, String toDate, int page,
      {int pageSize = 10,
      int dataSource = 0,
      dio.CancelToken? cancelToken = null,
      int? type}) {
    logger.d("开始获取体温分页数据");
    var uri = DioProvider.baseUrl + Api.heartRatePage;

    var params = {"0": "0"};
    if (type != null) {
      params = {
        'start_time': fromDate,
        'end_time': toDate,
        'data_sources': dataSource.toString(),
        'test': '1',
        'page': page.toString(),
        "page_size": pageSize.toString(),
        "type": type.toString()
      };
    } else {
      params = {
        'start_time': fromDate,
        'end_time': toDate,
        'data_sources': dataSource.toString(),
        'test': '1',
        'page': page.toString(),
        "page_size": pageSize.toString(),
      };
    }
    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);
    logger.d("查看url：${fullUri.toString()}");

    var dioCall = dioClient.get(
      fullUri.toString(), cancelToken: cancelToken, // 确保这里传递了 token
    );
    try {
      return callApiWithErrorParser(dioCall).then((response) {
        // logger.w("获取page数据");
        // logger.w(response.data);
        if (response.data["data"].length == 0) {
          // logger.w("无数据");
          return HeartRatePageData.withDefaults();
        } else {
          // logger.w("有数据");
          // logger.d(response.data["data"]["data"]);

          List<HeartRateData> heartRateDataList = [];
          for (var data in response.data["data"]["data"]) {
            heartRateDataList.add(HeartRateData.fromJsonToAPI(data));
          }
          return HeartRatePageData(
              data: heartRateDataList,
              page: response.data["data"]["page"],
              pageSize: response.data["data"]["page_size"],
              total: response.data["data"]["total"]);
        }
      });
    } on dio.DioException catch (e) {
      // 明确捕获 Dio 异常
      if (e.type == dio.DioExceptionType.cancel) {
        logger.d("请求已被取消: ${e.message}");
        throw e; // 或返回特定结果
      }
      logger.e("请求失败: ${e.message}");
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<HeartRateData?> getHeartRateLast() {
    logger.d("获取最新值");

    var uri = DioProvider.baseUrl + Api.heartRateLast;

    var dioCall = dioClient.get(uri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (response.data["code"] != 1) {
          logger.d("无数据HeartRateData");
          return null;
        } else {
          // logger.d("查看response.data");
          // logger.d(response.data);
          return HeartRateData(
              dataSource: response.data["data"]["type"],
              data: response.data["data"]["value"],
              date: DateTime.parse(response.data["data"]["date_time"]));
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<HeartRateData>> getHeartRateList(String fromDate, String toDate,
      {String types = "history"}) {
    var uri = DioProvider.baseUrl + Api.heartRateV2;

    var params = {
      'from_date': fromDate,
      'to_date': toDate,
      if (types == types) 'types': 'history',
      'test': '1',
      // 'data_sources':'1'
    };
    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (response.data["data"] is! List) {
          logger.d("无数据");
          return [];
        } else {
          logger.d("有数据");
          logger.d(response.data["data"]);
          logger.d(DateTime.parse(response.data["data"][0]["date_time"]));

          List<HeartRateData> heartRateDataList = [];
          for (var data in response.data["data"]) {
            heartRateDataList.add(HeartRateData.fromJsonToAPI(data));
          }
          return heartRateDataList;
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<HeartRateData> getHeartRate() {
    var uri = DioProvider.baseUrl + Api.heartRateV3;
    var params = {
      'test': '1',
      // 'data_sources':'1'
    };

    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        logger.d("getHeartRate获得的最近一次的heartRate");
        logger.d(response);
        if (response.data["code"] != 1) {
          //当前用户无心率数据
          return HeartRateData();
        } else {
          return HeartRateData.fromJsonToAPI(response.data["data"]);
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<dynamic>> getHeartRateDays(DateTime date) {
    var uri = DioProvider.baseUrl + Api.heartRateLineDays;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
    var params = {'test': '1', 'date_time': formattedDate};

    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        // logger.d("getHeartRateDays");
        // logger.d("response.data");
        // logger.d(response.data is List<dynamic>);
        // logger.d(response.data[0] is List<dynamic>);
        // logger.d(response.data[0][0] is List<dynamic>);

        return response.data;
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<dynamic>> getHeartRateWeeks(DateTime date) {
    var uri = DioProvider.baseUrl + Api.heartRateLineWeek;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
    var params = {'test': '1', 'date_time': formattedDate};

    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        // logger.d("getHeartRateDays");
        // logger.d("response.data");
        // logger.d(response.data is List<dynamic>);
        // logger.d(response.data[0] is List<dynamic>);
        // logger.d(response.data[0][0] is List<dynamic>);

        return response.data;
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<dynamic>> getHeartRateMonths(DateTime date) {
    var uri = DioProvider.baseUrl + Api.heartRateLineMonth;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
    var params = {'test': '1', 'date_time': formattedDate};

    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        // logger.d("getHeartRateDays");
        // logger.d("response.data");
        // logger.d(response.data is List<dynamic>);
        // logger.d(response.data[0] is List<dynamic>);
        // logger.d(response.data[0][0] is List<dynamic>);

        return response.data;
      });
    } catch (e) {
      rethrow;
    }
  }



@override
Future<List<dynamic>> getOxygenDays(DateTime date) {
  var uri = DioProvider.baseUrl + Api.bloodOxygenLineDays;
  String formattedDate =
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
  var params = {'test': '1', 'date_time': formattedDate};

  Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

  var dioCall = dioClient.get(fullUri.toString());

  try {
    return callApiWithErrorParser(dioCall).then((response) {
      return response.data;
    });
  } catch (e) {
    rethrow;
  }
}

@override
Future<List<dynamic>> getOxygenWeeks(DateTime date) {
  var uri = DioProvider.baseUrl + Api.bloodOxygenLineWeek;
  String formattedDate =
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
  var params = {'test': '1', 'date_time': formattedDate};

  Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

  var dioCall = dioClient.get(fullUri.toString());

  try {
    return callApiWithErrorParser(dioCall).then((response) {
      return response.data;
    });
  } catch (e) {
    rethrow;
  }
}

@override
Future<List<dynamic>> getOxygenMonths(DateTime date) {
  var uri = DioProvider.baseUrl + Api.bloodOxygenLineMonth;
  String formattedDate =
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
  var params = {'test': '1', 'date_time': formattedDate};

  Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

  var dioCall = dioClient.get(fullUri.toString());

  try {
    return callApiWithErrorParser(dioCall).then((response) {
      return response.data;
    });
  } catch (e) {
    rethrow;
  }
}



@override
Future<List<dynamic>> getTemperatureDays(DateTime date) {
  var uri = DioProvider.baseUrl + Api.bodyTemperatureLineDays;
  String formattedDate =
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
  var params = {'test': '1', 'date_time': formattedDate};

  Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

  var dioCall = dioClient.get(fullUri.toString());

  try {
    return callApiWithErrorParser(dioCall).then((response) {
      return response.data;
    });
  } catch (e) {
    rethrow;
  }
}

@override
Future<List<dynamic>> getTemperatureWeeks(DateTime date) {
  var uri = DioProvider.baseUrl + Api.bodyTemperatureLineWeek;
  String formattedDate =
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
  var params = {'test': '1', 'date_time': formattedDate};

  Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

  var dioCall = dioClient.get(fullUri.toString());

  try {
    return callApiWithErrorParser(dioCall).then((response) {
      return response.data;
    });
  } catch (e) {
    rethrow;
  }
}

@override
Future<List<dynamic>> getTemperatureMonths(DateTime date) {
  var uri = DioProvider.baseUrl + Api.bodyTemperatureLineMonth;
  String formattedDate =
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(date.toUtc());
  var params = {'test': '1', 'date_time': formattedDate};

  Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

  var dioCall = dioClient.get(fullUri.toString());

  try {
    return callApiWithErrorParser(dioCall).then((response) {
      return response.data;
    });
  } catch (e) {
    rethrow;
  }
}


  @override
  Future<void> postHeartRate({required HeartRateData data}) {
    var uri = DioProvider.baseUrl + Api.heartRateV2;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(data.date!.toUtc());

    var dioCall = dioClient.post(uri, queryParameters: {
      "test": 1,
      "date_time": formattedDate,
      "data_sources": data.dataSource
    }, data: {
      "value": data.data
    });
    try {
      return callApiWithErrorParser(dioCall).then((response) =>
          {logger.d("postHeartRate获得的result："), logger.d(response)});
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> aizoAutoUpload(List<AizoAutoData> list) {
    var uri = DioProvider.baseUrl + Api.aizoAutoUpload;

    var dioCall =
        dioClient.post(uri, data: list.map((data) => data.toJson()).toList());
    try {
      return callApiWithErrorParser(dioCall).then((response) =>
          {logger.d("aizoAutoUpload获得的result："), logger.d(response)});
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> postGoalSetting(MyGoalClass info) {
    var uri = DioProvider.baseUrl + Api.goalSetting;

    var dioCall = dioClient.post(uri, data: {"target": info});
    try {
      return callApiWithErrorParser(dioCall).then((response) =>
          {logger.d("postGoalSetting获得的result："), logger.d(response)});
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<AizoSleepData>> getSleepDataList(String fromDate, String toDate,
      {String types = "history"}) {
    var uri = DioProvider.baseUrl + Api.sleepData;

    var params = {
      'from_date': fromDate,
      'to_date': toDate,
      if (types == "history") 'types': 'history',
      'test': '1'
    };
    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        if (response.data["data"] is! List) {
          logger.d("无数据");
          return [];
        } else {
          logger.d("有数据");
          logger.d(response.data["data"]);
          // logger.d(DateTime.parse(response.data["data"][0]["date_time"]));

          List<AizoSleepData> sleepDataList = [];
          for (var data in response.data["data"]) {
            sleepDataList.add(AizoSleepData.fromJson(data));
          }
          return sleepDataList;
        }
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> postSleepData({required AizoSleepData data}) {
    var uri = DioProvider.baseUrl + Api.sleepData;
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(data.date!.toUtc());

    logger.d("查看添加日期");
    logger.d(formattedDate);

    var dioCall = dioClient.post(uri, queryParameters: {
      "test": 1,
      "date_time": formattedDate,
      "data_sources": 2
    }, data: {
      "date": data.date.toUtc().toIso8601String(),
      "startTime": data.startTime.toUtc().toIso8601String(),
      "endTime": data.endTime.toUtc().toIso8601String(),
      "totalDuration": data.totalDuration,
      "awakeDuration": data.awakeDuration,
      "lightDuration": data.lightDuration,
      "deepDuration": data.deepDuration,
      "remDuration": data.remDuration,
      "details": data.details.map((e) => e.toJson()).toList()
    });
    logger.d("查看postSleepData的data");
    logger.d(data.toJson());
    try {
      return callApiWithErrorParser(dioCall).then((response) =>
          {logger.d("postSleepData获得的result："), logger.d(response)});
    } catch (e) {
      rethrow;
    }
  }

    @override
  Future<FitnessDailyActivity?> getBodyFitnessLast() {
    var uri = DioProvider.baseUrl + Api.bodyActivityData;
    var params = {
      'test': '1',
      // 'data_sources':'1'
    };

    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri).replace(queryParameters: params);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        // logger.d("getBodyFitnessLast获得的最近一次的heartRate");
        // logger.d(response);
        if (response.data["code"] != 1) {
          //当前用户无身体活动数据
          logger.d("无数据运动");
          return null;
        } else {
          // logger.d('查看获取步数运动的水');
          // logger.d(response.data["data"]);
          return FitnessDailyActivity.fromJson(response.data["data"]);
        }
      });
    } catch (e) {
      rethrow;
    }
  }


      @override
  Future<AizoSleepData?> getAizoSleepDataLast() {
    var uri = DioProvider.baseUrl + Api.sleepDataLast;

    // 使用 Uri.https 拼接查询参数
    Uri fullUri = Uri.parse(uri);

    var dioCall = dioClient.get(fullUri.toString());

    try {
      return callApiWithErrorParser(dioCall).then((response) {
        // logger.d("getAizoSleepDataLast获得的最近一次的heartRate");
        // logger.d(response);
        if (response.data["code"] != 1) {
          //当前用户无身体活动数据
          logger.d("无数据睡眠");
          return null;
        } else {

            return AizoSleepData.fromJson(response.data["data"]);

        }
      });
    } catch (e) {
      rethrow;
    }
  }

}
