/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-10-16 14:39:24
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-30 17:20:42
 * @FilePath: /RPM-APP-MASTER/lib/app/data/repository/bluetooth_repository.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:async';
import 'dart:ffi';

import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/data/model/aizo_auto_data.dart';
import 'package:aiCare/app/data/model/aizo_auto_finial_data.dart';
import 'package:aiCare/app/data/model/aizo_headrt_rate.dart';
import 'package:aiCare/app/data/model/aizo_measure_result.dart';
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/data/model/aizo_sleep_data.dart';
import 'package:aiCare/app/data/model/aizo_userinfo.dart';
import 'package:aiCare/app/data/model/bluetooth_response.dart';
import 'package:aiCare/app/modules/bluetooth/model/aizo_dev_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

abstract class BluetoothRepository {
  //设备初始化
  Future<bool> init();

  //aizo连接请求
  Future<bool> aizoConnect(String deviceName, String deviceMacs);

  //aizo电池状态与信息
  Future<void> aizoBatteryStatus({
    required setBatteryStatus(List<String> value),
  });

  //aizo获取用户信息
  Future<AizoUserInfo> aizoGetUserInfo();

  //aizo设置用户信息
  Future<AizoUserInfo> aizoSetUserInfo(AizoUserInfo userInfo);

  //aizo获取心率间隔
  Future<AizoHeartRate?> aizoGetMeasureInterval();

  //aizo设置心率间隔 默认分钟
  Future<bool> aizoSetMeasureInterval(int time);

  //aizo测量数据
  Future<void> aizoInstantMeasurementAndroid(
    int type,
    int operation, {
    required setMeasurement(String value),
  });

  //aizo测量数据
  Future<void> aizoInstantMeasurementIOS(
    int type,
    int operation, {
    required setMeasurement(String value),
  });

  //aizo获取健康数值
  Future<List<AizoAutoFinialData>> aizoGetHealthData(DateTime time);

  //aizo获取蓝牙配置清单
  Future<AizoDevConfig?> aizoConfigurationList();

  //aizo获取硬件信息
  Future<String> aizoGetHardwareData();

  //aizo获取睡眠数据
  Future<AizoSleepData?> aizoGetSleepData(DateTime time);

  //aizo解绑
  Future<bool> aizoUnbind();

  //设备销毁
  Future<bool> aizoDestory();

  //aizo获取当前活动目标
  Future<List<int>?> aizoGetCurActGoal();

  Future<void> aizoRingStatus(
      {required onConnected,
      required onDisconnected,
      // required getIndex(),
      required setRingStatus(String value),
      required updateConnectionStatus(bool value)});

  Future<AizoRing> getAizoConnect();

  // StreamSubscription<dynamic>? startIOSScan(
  //     Function(List<MyBluetoothDevice>) onDevicesFound);

  // Future<bool> startAojScan();

  // // 停止扫描 AOJ 设备
  // Future<bool> stopAojScan();

  // // 添加 AOJ 设备
  // // 0:血压计 1:电子体温计 2:血氧仪 3：体温计 4：未知类型
  // Future<bool> addAojDevice(String macAddress, int deviceType);

  // // 开始数据同步
  // Future<bool> startAojSync();

  // // 停止数据同步
  // Future<bool> stopAojSync();

  // // 设置温度计模式
  // Future<bool> setTempMode(String deviceId, int mode, int unit);

  // // 设置血压计用户
  // Future<bool> setBpmUser(String deviceId, int user);

  // // 监听扫描结果
  // Stream<Map<dynamic, dynamic>> get aojScanResults;

  // // 监听同步数据
  // Stream<Map<dynamic, dynamic>> get aojSyncData;

  // /// 监听 AOJ 同步流
  // Stream<Map<dynamic, dynamic>> get aojSyncStream;

  // 血压计测量
  // Future<bool> aojMeasureBpm(MyBluetoothDevice device);
}
