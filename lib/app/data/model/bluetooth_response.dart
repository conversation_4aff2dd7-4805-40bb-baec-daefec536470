/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-10-17 13:02:20
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-06 10:00:54
 * @FilePath: /rpmappmaster/lib/app/data/model/bluetooth_response.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

class BluetoothResponse {
  bool status; // 蓝牙状态，true 表示成功，false 表示失败
  List<AizoRing> list; // 扫描结果列表

  // 构造函数
  BluetoothResponse({required this.status, required this.list});

  // 工厂构造函数，允许从扫描结果和状态生成 BluetoothResponse
  factory BluetoothResponse.fromScanResults(bool status, List<AizoRing> results) {
    return BluetoothResponse(status: status, list: results);
  }

  // 获取设备数量
  int get deviceCount => list.length;

  // 获取特定设备
  AizoRing? getDeviceById(String deviceId) {
    try {
      return list.firstWhere((result) => result.macAddress == deviceId);
    } catch (e) {
      return null; // 找不到设备返回 null
    }
  }

  // 检查是否有设备
  bool hasDevices() {
    return list.isNotEmpty;
  }

 
}