/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-06 08:55:58
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-06 08:56:37
 * @FilePath: /rpmappmaster/lib/app/data/model/remind_data.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class RemindData {
  String date;
  String label;
  bool switchValue;
  List<bool> repeatList;

  RemindData({
    required this.date,
    required this.label,
    required this.switchValue,
    required this.repeatList,
  });

  // 将对象转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'label': label,
      'switchValue': switchValue,
      'repeatList': repeatList,
    };
  }

  // 从 JSON 创建对象
  factory RemindData.fromJson(Map<String, dynamic> json) {
    return RemindData(
      date: json['date'] as String,
      label: json['label'] as String,
      switchValue: json['switchValue'] as bool,
      repeatList: List<bool>.from(json['repeatList']),
    );
  }
}