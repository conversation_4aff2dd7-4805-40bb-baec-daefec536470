/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-04-09 14:55:44
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-30 14:21:24
 * @FilePath: /RPM-APP/lib/app/data/model/aizo_sleep_data.dart
 * @Description: 睡眠数据模型（含 toString 方法）
 */

import 'dart:io';

import 'package:intl/intl.dart';

// 睡眠阶段枚举（与原生端 mode 严格对应）
enum SleepStage {
  light, // 浅睡 (mode=1)
  deep, // 深睡 (mode=2)
  awake, // 清醒 (mode=3)
  notWorn, // 未佩戴 (mode=4)
  rem, // 快速眼动 (mode=5)
  unknown, // 其他未知状态
}

// 睡眠阶段类型转换（修正后的逻辑，覆盖所有 mode 值）
SleepStage _parseSleepStage(int mode) {
  switch (mode) {
    case 1:
      return SleepStage.light;
    case 2:
      return SleepStage.deep;
    case 3:
      return SleepStage.awake;
    case 4:
      return SleepStage.notWorn;
    case 5:
      return SleepStage.rem;
    default:
      return SleepStage.unknown;
  }
}

class AizoSleepData {
  DateTime date; // 睡眠日期（UTC时间，不带时分秒）
  DateTime startTime; // 睡眠开始时间
  DateTime endTime; // 睡眠结束时间
  int totalDuration; // 总睡眠时长（秒）
  int awakeDuration; // 清醒时长（秒）
  int lightDuration; // 浅睡时长（秒）
  int deepDuration; // 深睡时长（秒）
  int remDuration; // REM时长（秒）
  List<SleepDetail> details; // 睡眠阶段详情

  AizoSleepData({
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.totalDuration,
    required this.awakeDuration,
    required this.lightDuration,
    required this.deepDuration,
    required this.remDuration,
    required this.details,
  });

  // 添加默认构造方法
  factory AizoSleepData.withDefault() {
    return AizoSleepData(
      date: DateTime(2000, 1, 1),
      startTime: DateTime(2000, 1, 1),
      endTime: DateTime(2000, 1, 1),
      totalDuration: 0,
      awakeDuration: 0,
      lightDuration: 0,
      deepDuration: 0,
      remDuration: 0,
      details: [],
    );
  }
    factory AizoSleepData.fromJsonToAPI(Map<String, dynamic> json) {
    print("sleepjson: $json");
    // 处理时间转换
    DateTime parseTime(dynamic timeValue) {
      if (timeValue is int) {
        // print("timeValue is int: $timeValue");
        // print("timeValue is int: ${DateTime.fromMillisecondsSinceEpoch(timeValue)}");
        return DateTime.fromMillisecondsSinceEpoch(timeValue);
      } else if (timeValue is String) {
        // print("timeValue is String: $timeValue");
        // print("timeValue is String: ${DateTime.parse(timeValue)}");
        // return DateTime.parse(timeValue);
        return DateTime.parse(timeValue).toLocal();
      }
      return DateTime.now().toUtc();
    }
    print("json['details']: ${json['details']}");
    List<SleepDetail> details = [];
    if (Platform.isIOS) {
      details = (json['details'] as List<dynamic>?)
              ?.map((e) => SleepDetail.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [];
    }else{
      details = (json['sleepDetails'] as List<dynamic>?)
              ?.map((e) => SleepDetail.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [];
    }

    return AizoSleepData(
      date: parseTime(json['date']),
      startTime: parseTime(json['startTime']),
      endTime: parseTime(json['endTime']),
      totalDuration: json['totalDuration'] as int,
      awakeDuration: json['awakeDuration'] as int,
      lightDuration: json['lightDuration'] as int,
      deepDuration: json['deepDuration'] as int,
      remDuration: json['remDuration'] as int,
      details: details,
    );
  }


  factory AizoSleepData.fromJson(Map<String, dynamic> json) {
    // print("sleepjson: $json");
    // 处理时间转换
    DateTime parseTime(dynamic timeValue) {
      if (timeValue is int) {
        // print("timeValue is int: $timeValue");
        // print("timeValue is int: ${DateTime.fromMillisecondsSinceEpoch(timeValue)}");
        return DateTime.fromMillisecondsSinceEpoch(timeValue);
      } else if (timeValue is String) {
        // print("timeValue is String: $timeValue");
        // print("timeValue is String: ${DateTime.parse(timeValue)}");
        // return DateTime.parse(timeValue);
        return DateTime.parse(timeValue).toLocal();
      }
      return DateTime.now().toUtc();
    }
    // print("fromjson['details']: ${json['details']}");
    List<SleepDetail> details = [];
      details = (json['details'] as List<dynamic>?)
              ?.map((e) => SleepDetail.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [];
    print(details);

    return AizoSleepData(
      date: parseTime(json['date']),
      startTime: parseTime(json['startTime']),
      endTime: parseTime(json['endTime']),
      totalDuration: json['totalDuration'] as int,
      awakeDuration: json['awakeDuration'] as int,
      lightDuration: json['lightDuration'] as int,
      deepDuration: json['deepDuration'] as int,
      remDuration: json['remDuration'] as int,
      details: details,
    );
  }

  // 添加 toString 方法（核心）
@override
String toString() {
  final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
  return '''
=== AizoSleepData ===
日期: ${dateFormat.format(date)}
开始时间格式传：${startTime}
开始时间: ${dateFormat.format(startTime)}
结束时间: ${dateFormat.format(endTime)}
总睡眠时长: $totalDuration 分
清醒时长: $awakeDuration 分
浅睡时长: $lightDuration 分
深睡时长: $deepDuration 分
REM时长: $remDuration 分
睡眠阶段详情（${details.length} 条）:
${details.map((d) => '  ${d.toString()}').join('\n')}
''';
}

  Map<String, dynamic> toJson() {
    return {
      'date': date.millisecondsSinceEpoch,
      'startTime': startTime.millisecondsSinceEpoch,
      'endTime': endTime.millisecondsSinceEpoch,
      'totalDuration': totalDuration,
      'awakeDuration': awakeDuration,
      'lightDuration': lightDuration,
      'deepDuration': deepDuration,
      'remDuration': remDuration,
      'sleepDetails': details.map((d) => d.toJson()).toList(),
    };
  }

}

class SleepDetail {
  final SleepStage stage; // 睡眠阶段类型
  final DateTime time; // 阶段发生时间

  SleepDetail({
    required this.stage,
    required this.time,
  });

  // factory SleepDetail.fromJson(Map<String, dynamic> json) {
  //   return SleepDetail(
  //     stage: _parseSleepStage(json['mode'] as int),
  //     time: DateTime.fromMillisecondsSinceEpoch(json['time'] as int),
  //   );
  // }
  factory SleepDetail.fromJson(Map<String, dynamic> json) {
    // print("json12312: $json");
    // 兼容两种格式
    int mode;
    if (json.containsKey('mode')) {
      mode = json['mode'] as int;
    } else if (json.containsKey('stage')) {
      mode = json['stage'] as int;
    } else {
      mode = -1;
    }

    DateTime time;
    if (json['time'] is int) {
      // print("json['time'] is int: ${json['time']}");
      time = DateTime.fromMillisecondsSinceEpoch(json['time'] as int);
      // print("time213: ${time}");
    } else if (json['time'] is String) {
      // print("json['time'] is String: ${json['time']}");
      time = DateTime.parse(json['time'] as String);
      // print("time213: ${time}");
    } else {
      time = DateTime.now();
    }

    return SleepDetail(
      stage: _parseSleepStage(mode),
      time: time,
    );
  }

  Map<String, dynamic> toJson() {
    return {"time": time.toIso8601String(), "stage": getModeFromStage(stage)};
  }

  // // 添加 toString 方法
  // @override
  // String toString() {
  //   final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
  //   return '${dateFormat.format(time)} - ${stage.toString().split('.').last}（mode: ${getModeFromStage(stage)}）';
  // }
  @override
String toString() {
  final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
  return '${dateFormat.format(time.toLocal())} - ${stage.toString().split('.').last}（mode: ${getModeFromStage(stage)}）';
}
} // 辅助方法：从枚举反查 mode（用于日志显示）

int getModeFromStage(SleepStage stage) {
  switch (stage) {
    case SleepStage.light:
      return 1;
    case SleepStage.deep:
      return 2;
    case SleepStage.awake:
      return 3;
    case SleepStage.notWorn:
      return 4;
    case SleepStage.rem:
      return 5;
    case SleepStage.unknown:
      return -1;
  }
}
