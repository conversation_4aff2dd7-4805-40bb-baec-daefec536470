/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-10 16:05:57
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-01-02 12:13:31
 * @FilePath: /rpmappmaster/lib/app/data/model/aizo_auto_finial_data.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class AizoAutoFinialData {
  int spo2;
  double environmentTemperature;
  int dailyHeartRate;
  int calories;
  int timestamp;
  bool sosAlert;
  int date;
  String timeStr;
  int distance;
  int hrv;
  double bodyTemperature;
  int steps;

  // 构造函数
  AizoAutoFinialData({
    required this.spo2,
    required this.environmentTemperature,
    required this.dailyHeartRate,
    required this.calories,
    required this.timestamp,
    required this.sosAlert,
    required this.date,
    required this.timeStr,
    required this.distance,
    required this.hrv,
    required this.bodyTemperature,
    required this.steps,
  });

  // 从JSON数据中创建AizoAutoFinialData对象
  factory AizoAutoFinialData.fromJson(Map<Object?, Object?> json) {
    return AizoAutoFinialData(
      spo2: json['spo2'] as int,
      environmentTemperature: json['environmentTemperature'] as double,
      dailyHeartRate: json['dailyHeartRate'] as int,
      calories: json['calories'] as int,
      timestamp: json['timestamp'] as int,
      sosAlert: json['sosAlert'] as bool,
      date: json['date'] as int,
      timeStr: json['timeStr'] as String,
      distance: json['distance'] as int,
      hrv: json['hrv'] as int,
      bodyTemperature: json['bodyTemperature'] as double,
      steps: json['steps'] as int,
    );
  }

    factory AizoAutoFinialData.fromAdJson(Map<Object?, Object?> json) {
    return AizoAutoFinialData(
      spo2: json['bo'] as int,
      environmentTemperature: json['temp'] as double,
      dailyHeartRate: json['hr'] as int,
      calories: json['calorie'] as int,
      timestamp: json['time'] as int,
      sosAlert: json['sos'] == 0 ? false : true,
      date: json['date'] as int,
      timeStr: DateTime.fromMillisecondsSinceEpoch((json['time'] as int)).toString() ,
      distance: json['distance'] as int,
      hrv: json['hrv'] as int,
      bodyTemperature: json['temp'] as double,
      steps: json['step'] as int,
    );
  }

  // 将AizoAutoFinialData对象转换为JSON数据
  Map<String, dynamic> toJson() {
    return {
      'spo2': spo2,
      'environmentTemperature': environmentTemperature,
      'dailyHeartRate': dailyHeartRate,
      'calories': calories,
      'timestamp': timestamp,
      'sosAlert': sosAlert,
      'date': date,
      'timeStr': timeStr,
      'distance': distance,
      'hrv': hrv,
      'bodyTemperature': bodyTemperature,
      'steps': steps,
    };
  }

  @override
  String toString() {
    return "spo2: $spo2, environmentTemperature: $environmentTemperature, dailyHeartRate: $dailyHeartRate, calories: $calories, timestamp: $timestamp, sosAlert: $sosAlert, date: $date, timeStr: $timeStr, distance: $distance, hrv: $hrv, bodyTemperature: $bodyTemperature, steps: $steps";
  }
}
