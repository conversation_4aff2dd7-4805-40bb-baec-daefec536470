/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-06 09:26:48
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-04-08 08:32:12
 * @FilePath: /rpmappmaster/lib/app/data/model/aizo_ring.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class AizoRing {
  String name;
  String uuidString;
  String macAddress;
  int rssi;
  bool isSystemConnected;

  AizoRing({
    this.name = "",
    this.uuidString = "",
    this.macAddress = "",
    this.rssi = 0,
    this.isSystemConnected = false,
  });

  // 从 JSON (Map) 创建 AizoRing 实例
// 从 JSON (Map) 创建 AizoRing 实例
  factory AizoRing.fromJson(Map<Object?, Object?> json) {
    return AizoRing(
      // name: (json['name'] as String?) ?? '', // 如果不存在或为 null，默认值为空字符串
      name: "aiRing",
      uuidString: (json['uuidString'] as String?) ?? '', // 默认值为空字符串
      macAddress: (json['macAddress'] as String?) ?? '', // 默认值为空字符串
      rssi: (json['rssi'] as int?) ?? 0, // 如果不存在或为 null，默认值为 0
      isSystemConnected:
          (json['isSystemConnected'] as bool?) ?? false, // 默认值为 false
    );
  }

  // 将 AizoRing 实例转换为 JSON
  Map<Object?, Object?> toJson() {
    return {
      'name': name,
      'uuidString': uuidString,
      'macAddress': macAddress,
      'rssi': rssi,
      'isSystemConnected': isSystemConnected,
    };
  }

  bool myIsNull() {
    if (macAddress == null)
      return true;
    else
      return false;
  }

  // AizoRing copyWith({int? rssi}) {}
  AizoRing copyWith({
    String? name,
    String? uuidString,
    String? macAddress,
    int? rssi,
    bool? isSystemConnected,
  }) {
    return AizoRing(
      name: name ?? this.name,
      uuidString: uuidString ?? this.uuidString,
      macAddress: macAddress ?? this.macAddress,
      rssi: rssi ?? this.rssi,
      isSystemConnected: isSystemConnected ?? this.isSystemConnected,
    );
  }

}
