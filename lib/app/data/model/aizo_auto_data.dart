// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-10 15:55:53
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-08 14:50:53
 * @FilePath: /rpmappmaster/lib/app/data/model/aizo_auto_data.dart
 * @Description: 自动监测数据模型（新增step字段）
 */
class AizoAutoData {
  // 类的字段
  int saturation;      // 血氧饱和度
  int dailyHeartRate;  // 日常心率
  double temperature;  // 体温
  String dateTime;     // 日期时间
  int step;            // 新增：步数
  int distance;        //距离
  int calorie;         //卡路里

  // 构造函数
  AizoAutoData({
    required this.saturation,
    required this.dailyHeartRate,
    required this.temperature,
    required this.dateTime,
    required this.step,
    required this.distance,
    required this.calorie,
  });

  // 从JSON数据中创建AizoAutoData对象
  factory AizoAutoData.fromJson(Map<String, dynamic> json) {
    return AizoAutoData(
      saturation: json['saturation'] as int,
      dailyHeartRate: json['dailyHeartRate'] as int,
      temperature: (json['temperature'] as num).toDouble(), // 兼容int/double
      dateTime: json['date_time'] as String,
      step: json['step'] as int? ?? 0, // 处理可能缺失的字段
      distance:json['distance'] as int ?? 0,
      calorie:json['calorie'] as int ?? 0,
    );
  }

  // 将AizoAutoData对象转换为JSON数据
  Map<String, dynamic> toJson() {
    return {
      'saturation': saturation,
      'dailyHeartRate': dailyHeartRate,
      'temperature': temperature,
      'date_time': dateTime,
      'step': step,  // 新增字段,
      'distance': distance,  // 新增字段,
      'calorie': calorie,  // 新增字段,

    };
  }

  // 可选：重写toString方法便于调试
  @override
  String toString() {
    return 'AizoAutoData(saturation: $saturation, dailyHeartRate: $dailyHeartRate, temperature: $temperature, dateTime: $dateTime, step: $step, distance: $distance, calorie: $calorie)';
  }

  AizoAutoData copyWith({
    int? saturation,
    int? dailyHeartRate,
    double? temperature,
    String? dateTime,
    int? step,
    int? distance,
    int? calorie,
  }) {
    return AizoAutoData(
      saturation: saturation ?? this.saturation,
      dailyHeartRate: dailyHeartRate ?? this.dailyHeartRate,
      temperature: temperature ?? this.temperature,
      dateTime: dateTime ?? this.dateTime,
      step: step ?? this.step,
      distance: distance ?? this.distance,
      calorie: calorie ?? this.calorie,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'saturation': saturation,
      'dailyHeartRate': dailyHeartRate,
      'temperature': temperature,
      'dateTime': dateTime,
      'step': step,
      'distance': distance,
      'calorie': calorie,
    };
  }

  factory AizoAutoData.fromMap(Map<String, dynamic> map) {
    return AizoAutoData(
      saturation: map['saturation'] as int,
      dailyHeartRate: map['dailyHeartRate'] as int,
      temperature: map['temperature'] as double,
      dateTime: map['dateTime'] as String,
      step: map['step'] as int,
      distance: map['distance'] as int,
      calorie: map['calorie'] as int,
    );
  }

  // String toJson() => json.encode(toMap());

  // factory AizoAutoData.fromJson(String source) => AizoAutoData.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool operator ==(covariant AizoAutoData other) {
    if (identical(this, other)) return true;
  
    return 
      other.saturation == saturation &&
      other.dailyHeartRate == dailyHeartRate &&
      other.temperature == temperature &&
      other.dateTime == dateTime &&
      other.step == step &&
      other.distance == distance &&
      other.calorie == calorie;
  }

  @override
  int get hashCode {
    return saturation.hashCode ^
      dailyHeartRate.hashCode ^
      temperature.hashCode ^
      dateTime.hashCode ^
      step.hashCode ^
      distance.hashCode ^
      calorie.hashCode;
  }
}
