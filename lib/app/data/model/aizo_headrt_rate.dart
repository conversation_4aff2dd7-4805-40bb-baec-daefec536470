class AizoHeartRate {
  int currentInterval;
  int defaultInterval;
  List<int> intervalList;

  // 构造函数
  AizoHeartRate({
    required this.currentInterval,
    required this.defaultInterval,
    required this.intervalList,
  });

  // `copy` 方法
  AizoHeartRate copyWith({
    int? currentInterval,
    int? defaultInterval,
    List<int>? intervalList,
  }) {
    return AizoHeartRate(
      currentInterval: currentInterval ?? this.currentInterval,
      defaultInterval: defaultInterval ?? this.defaultInterval,
      intervalList: intervalList ?? this.intervalList,
    );
  }

  // `toString` 方法
  @override
  String toString() {
    return 'AizoHeartRate(currentInterval: $currentInterval, defaultInterval: $defaultInterval, intervalList: $intervalList)';
  }

  // `hashCode` 方法
  @override
  int get hashCode {
    return currentInterval.hashCode ^ defaultInterval.hashCode ^ intervalList.hashCode;
  }

  // `==` 方法
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AizoHeartRate) return false;
    return other.currentInterval == currentInterval &&
        other.defaultInterval == defaultInterval &&
        other.intervalList == intervalList;
  }
}