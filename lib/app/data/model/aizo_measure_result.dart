class AizoMeasureResult {
  bool result;
  int type;
  int time;
  int heartrate;
  int bloodoxygen;
  double bodytemp;
  double envtemp;

  AizoMeasureResult({
    this.result = false,
    this.type = -1,
    int? time,  // Remove dynamic default value
    this.heartrate = -1,
    this.bloodoxygen = -1,
    this.bodytemp = 0.0,
    this.envtemp = 0.0,
  }) : time = time ?? DateTime.now().millisecondsSinceEpoch;  // Set dynamic value here
}