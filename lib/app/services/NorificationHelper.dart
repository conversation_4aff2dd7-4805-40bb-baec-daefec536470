import 'dart:async';
import 'dart:io';

import 'package:aiCare/app/services/toastHelper.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

@pragma('vm:entry-point')
/**
 * @description: 处理后台通知点击
 * @pragma('vm:entry-point')  标记此函数为Dart VM的入口点，确保Flutter引擎在后台能调用它（必须保留，否则后台点击无效）。
 * @parents notificationResponse  id: 通知的唯一ID。 actionId: 用户点击的操作按钮ID（如"回复"按钮）。  payload: 发送通知时传递的自定义数据（如跳转路由、消息ID）  input: 用户输入的文本（适用于带输入框的通知）。
 * @return {*}
 */
void notificationTapBackground(NotificationResponse notificationResponse) {
  // ignore: avoid_print
  //打印通知基本信息
  print('notification(${notificationResponse.id}) action tapped: '
      '${notificationResponse.actionId} with'
      ' payload: ${notificationResponse.payload}');

  //检测是否有用户输入（如通知中的文本输入框）
  if (notificationResponse.input?.isNotEmpty ?? false) {
    // ignore: avoid_print
    print(
        'notification action tapped with input: ${notificationResponse.input}');
  }
}

class NotificationHelper {
  static NotificationHelper? _instance;
  static NotificationHelper getInstance() {
    _instance ??= NotificationHelper._initial();
    return _instance!;
  }

  factory NotificationHelper() => _instance ??= NotificationHelper._initial();

  //创建命名构造函数
  NotificationHelper._initial() {
    initialize();
  }

  // FlutterLocalNotificationsPlugin实例
  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final StreamController<NotificationResponse> selectNotificationStream =
      StreamController<NotificationResponse>.broadcast();
  // 常量定义
  static const String _channelId = 'message_notifications';
  static const String _channelName = 'message notification';
  static const String _channelDescription =
      'Notifications for receiving new messages';
  static const String _ticker = 'ticker';
  static const String _darwinNotificationCategoryPlain = 'plainCategory';
  static const String darwinNotificationCategoryText = 'textCategory';
  int id = 0;
  bool _notificationsEnabled = false;

  // 初始化通知插件
  /**
   * @description: 初始化步骤：

配置Android和iOS的初始化参数。

调用initialize()方法初始化插件，设置前台和后台通知点击回调。

异常捕获打印日志，但未处理重试逻辑。
   * @return {*}
   */
  Future<void> initialize() async {
    try {
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/launcher_icon');
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
              requestSoundPermission: false,
              requestBadgePermission: false,
              requestAlertPermission: false);

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _notificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: selectNotificationStream.add, // 前台点击
        onDidReceiveBackgroundNotificationResponse:
            notificationTapBackground, // 后台点击
      );
    } catch (e) {
      print('初始化通知插件失败: $e');
    }
  }

  initPermission() {
    _isAndroidPermissionGranted();
    _requestPermissions();
    _configureSelectNotificationSubject();
  }

  closeSubject() {
    selectNotificationStream.close();
  }

  Future<void> _isAndroidPermissionGranted() async {
    if (Platform.isAndroid) {
      final bool granted = await _notificationsPlugin
              .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin>()
              ?.areNotificationsEnabled() ??
          false;
      _notificationsEnabled = granted;
    }
  }

  /**
   * @description: Android：检查并请求通知权限。

iOS：请求通知权限（alert、badge、sound）。

结果处理：将权限状态保存到_notificationsEnabled，但未在显示通知前校验。
   * @return {*}
   */
  Future<void> _requestPermissions() async {
    if (Platform.isIOS) {
      await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );

    } else if (Platform.isAndroid) {
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      final bool? grantedNotificationPermission =
          await androidImplementation?.requestNotificationsPermission();
      _notificationsEnabled = grantedNotificationPermission ?? false;

      // if (!_notificationsEnabled) {
      //   // 权限未授予时
      //   ToastHelper.Failed("通知权限申请被拒绝");
      // }

    }
  }

  

  /**
   * @description: StreamController：selectNotificationStream广播点击事件。

监听回调：点击通知时打印日志，示例中导航逻辑被注释，需根据实际需求实现。
   * @return {*}
   */
  void _configureSelectNotificationSubject() {
    selectNotificationStream.stream
        .listen((NotificationResponse? response) async {
      // await Navigator.of(context).push(MaterialPageRoute<void>(
      //   builder: (BuildContext context) =>
      //       SecondPage(response?.payload, data: response?.data),
      // ));
      print("点击消息携带的数据$response");
    });
  }

  // 显示通知
  /**
   * @description: 参数配置：

Android：渠道ID、名称、优先级等（渠道在首次显示通知时自动创建）。

iOS：通知类别标识符。

显示逻辑：使用递增的id确保通知唯一性，携带payload传递数据。
   * @return {*}
   */
  Future<void> showNotification(
      {required String title,
      required String body,
      String payload = ""}) async {
    const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: _channelDescription,
      importance: Importance.max,
      priority: Priority.high,
      ticker: _ticker,
    );
    const DarwinNotificationDetails iosNotificationDetails =
        DarwinNotificationDetails(
            categoryIdentifier: _darwinNotificationCategoryPlain);
    const NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails, iOS: iosNotificationDetails);
    await _notificationsPlugin.show(
      id++,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

//   6. 通知取消
// 取消所有通知：cancelAll()

// 取消指定通知：cancelId(id)

  /// 取消全部通知
  cancelAll() {
    _notificationsPlugin.cancelAll();
  }

  /// 取消对应ID的通知
  cancelId(int id) {
    _notificationsPlugin.cancel(id);
  }
}
