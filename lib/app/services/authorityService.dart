/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-09-02 16:02:33
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-07 16:00:56
 * @FilePath: /RPM-APP-MASTER/lib/app/services/authorityService.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:io';

import 'package:aiCare/app/core/utils/logger_singleton.dart';
import 'package:aiCare/flavors/build_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

class AuthorityService {
  static final AuthorityService instance = AuthorityService._internal();
  final logger = LoggerSingleton.getInstance();
  factory AuthorityService() => instance;

  AuthorityService._internal();

  /// 获取存储权限
  Future<bool> getStoragePermission() async {
    PermissionStatus myPermission;

    if (defaultTargetPlatform == TargetPlatform.iOS) {
      logger.d("Requesting photos permission for iOS");
      myPermission = await Permission.bluetooth.request();
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      logger.d("Requesting storage permission for Android");
      myPermission = await Permission.storage.request();
    } else {
      myPermission = PermissionStatus.denied;
    }

    logger.d("Permission status: $myPermission");

    if (myPermission.isGranted) {
      return true;
    } else if (myPermission.isPermanentlyDenied) {
      logger.d("Permission permanently denied, opening settings...");
      openAppSettings();
      return false;
    } else {
      return false;
    }
  }

  void checkPermission() async {
    final permissionState = await getStoragePermission();
    permissionState
        ? logger.d("Permission granted.")
        : logger.d("Permission denied.");
  }

  Future<bool> bluetoothPermission(BuildContext context) async {
    logger.d("开始获取蓝牙权限");

    if (Platform.isAndroid) {
      return await _requestBluetoothPermissionsAndroid(context);
    } else if (Platform.isIOS) {
      return await _requestBluetoothPermissionsIOS(context);
    } else {
      return false;
    }

    // logger.d("开始获取蓝牙权限");
    // if (Platform.isAndroid) {
    //   Map<Permission, PermissionStatus> statuses = await [
    //     Permission.bluetoothScan,
    //     Permission.bluetoothConnect,
    //     Permission.locationWhenInUse,
    //   ].request();
    //   bool allGranted =
    //       await statuses.values.every((status) => status.isGranted);
    //   if (allGranted) {
    //     logger.d("完全授予");
    //     // print("权限已授予");
    //     return true;
    //   } else {
    //     logger.d("未完全授予");
    //     // print("需要蓝牙和位置权限");

    //     // 提示用户需要权限
    //     showDialog(
    //       context: context,
    //       builder: (BuildContext context) {
    //         return AlertDialog(
    //           title: Text("权限请求"),
    //           content: Text("应用需要蓝牙和位置权限才能正常工作。"),
    //           actions: <Widget>[
    //             TextButton(
    //               child: Text("确定"),
    //               onPressed: () {
    //                 Navigator.of(context).pop();
    //               },
    //             ),
    //           ],
    //         );
    //       },
    //     );
    //   }
    //   return false;
    // } else if (Platform.isIOS) {
    //   return true;
    // } else {
    //   return false;
    // }
  }

  /// Android蓝牙权限申请
  Future<bool> _requestBluetoothPermissionsAndroid(BuildContext context) async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.locationWhenInUse,
    ].request();

    bool allGranted = statuses.values.every((status) => status.isGranted);

    if (!allGranted) {
      _showPermissionDialog(context, "应用需要蓝牙和位置权限才能正常工作。");
      logger.d("未完全授予");
      return false;
    }

    logger.d("完全授予");
    return true;
  }

  /// iOS蓝牙权限申请
  Future<bool> _requestBluetoothPermissionsIOS(BuildContext context) async {
    PermissionStatus bluetoothPermission = await Permission.bluetooth.request();
    PermissionStatus locationPermission =
        await Permission.locationWhenInUse.request();

    if (!bluetoothPermission.isGranted || !locationPermission.isGranted) {
      _showPermissionDialog(context, "应用需要蓝牙和位置权限才能正常工作。");
      return false;
    }

    logger.d("iOS 蓝牙和位置权限已授予");
    return true;
  }

  /// 显示权限请求对话框
  // void _showPermissionDialog(BuildContext context, String message) {
  //   showDialog(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return AlertDialog(
  //         title: Text("权限请求"),
  //         content: Text(message),
  //         actions: <Widget>[
  //           TextButton(
  //             child: Text("确定"),
  //             onPressed: () {
  //               Navigator.of(context).pop();
  //             },
  //           ),
  //         ],
  //       );
  //     },
  //   );
  // }

void _showPermissionDialog(
  BuildContext context,
  String message, {
  VoidCallback? onConfirm,
}) {
  showDialog(
    context: context,
    builder: (BuildContext context) => AlertDialog(
      title: Text("权限请求"),
      content: Text(message),
      actions: [
        TextButton(
          child: Text("取消"),
          onPressed: () => Navigator.pop(context),
        ),
        TextButton(
          child: Text("去设置", style: TextStyle(color: Colors.blue)),
          onPressed: () {
            Navigator.pop(context);
            onConfirm?.call();
          },
        ),
      ],
    ),
  );
}

/// 获取相机权限（优化版）
Future<bool> getCameraPermission(BuildContext context) async {
  try {
    // 检查当前权限状态
    PermissionStatus status = await Permission.camera.status;

    // 已授予权限
    if (status.isGranted) {
      logger.d("相机权限已授予");
      return true;
    }

    // 处理永久拒绝的情况
    if (status.isPermanentlyDenied) {
      logger.d("相机权限被永久拒绝，引导至设置页");
      _showPermissionDialog(
        context,
        "相机权限已被永久拒绝，请前往设置页面手动开启",
        onConfirm: () => openAppSettings(),
      );
      return false;
    }

    // 首次请求或普通拒绝后再次请求
    PermissionStatus requestStatus = await Permission.camera.request();
    if (requestStatus.isGranted) {
      return true;
    }

    // 普通拒绝时提示必要性
    if (requestStatus.isDenied) {
      _showPermissionDialog(
        context,
        "需要相机权限才能拍摄照片，请授权",
        onConfirm: () => getCameraPermission(context), // 递归请求
      );
    }

    return false;
  } catch (e) {
    logger.e("相机权限请求异常: $e");
    return false;
  }
}

}
