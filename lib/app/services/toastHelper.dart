/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-02-28 16:57:04
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-14 15:14:58
 * @FilePath: /RPM-APP/lib/app/services/toastHelper.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

class ToastHelper {
  static void Successful(String text) {
    showToastWidget(
      _buildIconToast(
        text: text,
        type:true,
        backgroundColor: AppColors.topBarSuccessful,
        textColor: AppColors.topBarTextSuccessful,
      ),
      context: Get.context,
      position: StyledToastPosition.top,
      animDuration: const Duration(milliseconds: 1000),
      duration: const Duration(seconds: 4),
      alignment: Alignment.topCenter,
      animation: StyledToastAnimation.slideFromTop,
      reverseAnimation: StyledToastAnimation.slideToTop,
      
    );
  }

    static void Failed(String text) {
    showToastWidget(
      _buildIconToast(
        text: text,
        type:false,
        backgroundColor: AppColors.topBarFailed,
        textColor: AppColors.topBarTextFailed,
      ),
      context: Get.context,
      position: StyledToastPosition.top,
      animDuration: const Duration(milliseconds: 1000),
      duration: const Duration(seconds: 4),
      alignment: Alignment.topCenter,
      animation: StyledToastAnimation.slideFromTop,
      reverseAnimation: StyledToastAnimation.slideToTop,
      
    );
  }

  // static MyToast(String text) {
  //   showToast(
  //     text,
  //     position: StyledToastPosition.top,
  //     animation: StyledToastAnimation.slideFromTop,
  //     reverseAnimation: StyledToastAnimation.slideToTop,
  //     animDuration: Duration(milliseconds: 300),
  //     duration: Duration(seconds: 2),
  //     backgroundColor: Colors.black.withOpacity(0.8),
  //     textStyle: TextStyle(color: Colors.white, fontSize: 16),
  //     borderRadius: BorderRadius.circular(8.0),
  //     alignment: Alignment.topCenter,
  //   );
  // }

  static Widget _buildIconToast({
    required String text,
    required Color backgroundColor,
    required Color textColor, required bool type,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(40)),
      margin: EdgeInsets.only(top: ScreenAdapter.height(40)),
      alignment: Alignment.topCenter,
      child: Container(
      // height: ScreenAdapter.height(32),
      padding: EdgeInsets.symmetric(
        horizontal: ScreenAdapter.width(8),
        vertical: ScreenAdapter.height(8)
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: type? AppColors.topBarTextSuccessful :AppColors.topBarTextFailed
        )
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
                      SizedBox(
              height: ScreenAdapter.height(20),
              child: AspectRatio(
                aspectRatio: 1,
                child: Image.asset(
                  // Assets.images.healthGoalIcon.path,
                  type ?Assets.images.successfuleIcon.path : Assets.images.failedIcon.path,
                  // iconList(i),
                ),
              ),
            ),
          Gap( ScreenAdapter.width(8)),
          Flexible(
            child: Text(
              // textAlign: TextAlign.,
              text,
              style: TextStyle(
                color: textColor,
                fontSize: ScreenAdapter.fontSize(12),
                height: 14 / 12.0,
              ),
              // overflow: TextOverflow.ellipsis, // 文本溢出时显示省略号
              // maxLines: 3, // 最多显示一行
            ),
          ),
        ],
      ),
    ),
    );
  }
}