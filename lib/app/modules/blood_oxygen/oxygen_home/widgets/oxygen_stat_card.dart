/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-31 10:54:14
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-05 14:19:54
 * @FilePath: /rpmappmaster/lib/app/modules/blood_oxygen/oxygen_home/widgets/oxygen_stat_card.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

class OxygenStatCard extends StatelessWidget {
  // 新增的 bool 参数，默认值为 false
  final bool isHighlighted; 
  final String text;
  final int value;

  OxygenStatCard({
    required this.text,
    required this.value,
    this.isHighlighted = false, // 默认值为 false
  });

  @override
  Widget build(BuildContext context) {
    // 根据 isHighlighted 的值设置颜色
    final textColor = isHighlighted ? Color(0xFFC293FF) : AppColors.Color999;

    return SizedBox(
      height: ScreenAdapter.height(50),
      child: Column(
        children: <Widget>[
          Text(
            text,
            style: TextStyle(
              fontSize: ScreenAdapter.fontSize(12),
              color: AppColors.Color666,
              height: ScreenAdapter.fontSize(16.8 / 12),
            ),
          ),
          value == 0
          ?Text(
            "--",
            style: TextStyle(
              fontSize: ScreenAdapter.fontSize(14),
              color: AppColors.Color999, // 使用动态颜色
              height: ScreenAdapter.fontSize(17 / 10),
            ),
          ):
          Text(
            value.toStringAsFixed(0) + '%',
            style: TextStyle(
              fontSize: ScreenAdapter.fontSize(14),
              color: textColor, // 使用动态颜色
              height: ScreenAdapter.fontSize(17 / 10),
            ),
          ),
        ],
      ),
    );
  }
}
