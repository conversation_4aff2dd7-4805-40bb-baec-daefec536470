import 'dart:convert';
import 'dart:math';

import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/bluetooth/custom_bluetooth_icon.dart';
import 'package:aiCare/app/core/widget/custom_liquid.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/controllers/blood_oxygen_controller.dart';

import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/widgets/oxygen_level_card.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/widgets/oxygen_stat_card.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_echarts/flutter_echarts.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class OxygenPieChart extends StatefulWidget {
  const OxygenPieChart({super.key});

  @override
  State<StatefulWidget> createState() => OxygenPieChartState();
}

class OxygenPieChartState extends State<OxygenPieChart> {
  final BloodOxygenController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
      width: ScreenAdapter.width(343),
      height: ScreenAdapter.height(288 + 25 + 16 + 16),
      decoration: BoxDecoration(
        borderRadius:
            BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
        color: AppColors.colorWhite,
      ),
      child: Stack(
        children: [
          //状态条
          Positioned(
            top: ScreenAdapter.height(12),
            left: ScreenAdapter.width(16),
            child: Container(
              // margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(15)),
              // width: ScreenAdapter.width(103),
              height: ScreenAdapter.height(90),
              // height: ScreenAdapter.height(50),
              child: Column(
  mainAxisSize: MainAxisSize.min,
  crossAxisAlignment: CrossAxisAlignment.start,
  mainAxisAlignment: MainAxisAlignment.center,
  children: <Widget>[
    OxygenLevelCard(
      levelColor: AppColors.normalColor,
      text: T.bloodOxygenNormalLevel.tr,
    ),
    SizedBox(height: ScreenAdapter.height(4)), // 替代 Expanded
    OxygenLevelCard(
      levelColor: AppColors.mildColor,
      text: T.bloodOxygenMildLevel.tr,
    ),
    SizedBox(height: ScreenAdapter.height(4)), // 替代 Expanded
    OxygenLevelCard(
      levelColor: AppColors.moderateColor,
      text: T.bloodOxygenModerateLevel.tr,
    ),
    SizedBox(height: ScreenAdapter.height(4)), // 替代 Expanded
    OxygenLevelCard(
      levelColor: AppColors.minuentColor,
      text: T.bloodOxygenSeriousLevel.tr,
    ),
  ],
),
            ),
          ),

          CustomBluetoothIcon(),

          //水滴图
          Positioned(
              top: ScreenAdapter.height(64),
              left: ScreenAdapter.width(112),
              child: Column(
                children: [
                  Obx(() => CustomLiquid(
                      amplitude:
                          !controller.homeController.oxygenData.value.isNull()
                              ? controller
                                  .homeController.oxygenData.value.percentage!
                                  .toDouble()
                              : 0,
                      // amplitude: controller.lastValue.value.percentage!=null? 90 :0,
                      parentWidth: 151,
                      childWidth: 137,
                      dataStyle: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: ScreenAdapter.fontSize(36),
                          height: 42.19 / 36,
                          color: AppColors.colorWhite),
                      parentBdWidth: 2)),
                  // Container(
                  //   // margin: EdgeInsets.only(top: ScreenAdapter.height(59)),
                  //   width: ScreenAdapter.width(151),
                  //   height: ScreenAdapter.height(151),
                  //   // margin: EdgeInsets.symmetric(vertical: ScreenAdapter.height(20)),
                  //   child: Echarts(
                  //     extensions: [liquidScript],
                  //     option: controller.option.value,
                  //   ),
                  // ),

                  Container(
                      width: ScreenAdapter.width(110),
                      height: ScreenAdapter.height(17),
                      margin: EdgeInsets.only(top: ScreenAdapter.height(6)),
                      child: Row(
                        children: [
                          Obx(() {
                            if (controller
                                    .homeController.oxygenData.value.date ==
                                null) return SizedBox();
                            return Text(
                              DateFormat('yyyy-MM-dd').format(controller
                                  .homeController.oxygenData.value.date!
                                  .toLocal()),
                              style: TextStyle(
                                fontSize: ScreenAdapter.fontSize(12),
                                height: ScreenAdapter.height(17 / 10),
                                fontWeight: FontWeight.w400,
                                color: AppColors.Color666,
                              ),
                            );
                          }),
                          Expanded(child: Container()),
                          Obx(() {
                            print("查看血氧最近的时间");
                            print(controller.homeController.oxygenData.value);

                            // 先检查是否为空
                            if (controller
                                    .homeController.oxygenData.value.date ==
                                null) {
                              print("血氧数据日期为空");
                              return SizedBox();
                            }

                            // 安全访问
                            print(controller
                                .homeController.oxygenData.value.date!
                                .toLocal());
                            return Text(
                              DateFormat('HH:mm').format(controller
                                  .homeController.oxygenData.value.date!
                                  .toLocal()),
                              style: TextStyle(
                                fontSize: ScreenAdapter.fontSize(12),
                                height: ScreenAdapter.height(17 / 10),
                                fontWeight: FontWeight.w400,
                                color: AppColors.Color666,
                              ),
                            );
                          })
                        ],
                      )),
                ],
              )),

          //测量按钮
          Positioned(
            top: ScreenAdapter.height(243 + 16),
            left: ScreenAdapter.width(112 + 16 + 8),
            // right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  onTap: () {
                    controller.measure();
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreenAdapter.width(16)),
                    height: ScreenAdapter.height(25),
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(Assets.images.measureButton.path),
                        fit: BoxFit.cover,
                      ),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      T.measure.tr,
                      style: normalF12H17C999.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          //状态值
          Positioned(
              top: ScreenAdapter.height(243 + 25 + 16 + 16),
              left: ScreenAdapter.width(66),
              width: ScreenAdapter.width(240),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(
                    () => OxygenStatCard(
                      text: T.wordsAverage.tr,
                      value: controller.dayDataList.value[0],
                    ),
                  ),
                  Expanded(child: Container()),
                  Obx(
                    () => OxygenStatCard(
                      text: T.wordsMaximum.tr,
                      value: controller.dayDataList.value[1],
                      isHighlighted: true,
                    ),
                  ),
                  Expanded(child: Container()),
                  Obx(
                    () => OxygenStatCard(
                      text: T.wordsMinimum.tr,
                      value: controller.dayDataList.value[2],
                    ),
                  )
                ],
              )),

        ],
      ),
    );
  }
}
