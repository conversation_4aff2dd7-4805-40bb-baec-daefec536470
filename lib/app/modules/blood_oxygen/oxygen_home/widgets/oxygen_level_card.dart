/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-31 10:54:14
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-31 16:36:09
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_home/widgets/oxygen_level_card.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

class OxygenLevelCard extends StatelessWidget with BaseWidgetMixin {
  OxygenLevelCard({required this.levelColor, required this.text});
  final Color levelColor;
  final String text;

  @override
  Widget body(BuildContext context) {
    return Container(
      // width: ScreenAdapter.width(103),
      height: ScreenAdapter.height(18),
      // color: Colors.green,
      //margin: EdgeInsets.symmetric(vertical: ScreenAdapter.height(2)),
      child: Row(
        //mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Container(
            width: ScreenAdapter.width(5),
            height: ScreenAdapter.height(5),
            margin: EdgeInsets.only(right: ScreenAdapter.width(4)),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: levelColor,
            ),
          ),
          Container(
            // width: ScreenAdapter.width(150),
            //height: ScreenAdapter.height(150),
            child: Text(
              text,
              style: normalF12H17C666,
            ),
          ),
        ],
      ),
    );
  }
}
