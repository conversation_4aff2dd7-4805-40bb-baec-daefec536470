/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-31 10:54:14
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-19 13:17:30
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_home/widgets/oxygen_line_chart.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_line.dart';
import 'package:aiCare/app/core/widget/fl_chart/scatter_plot.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_echarts/flutter_echarts.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import "dart:convert";

import '../../../../core/values/app_colors.dart';
import '../controllers/blood_oxygen_controller.dart';


class OxygenLineChart extends StatefulWidget {
  const OxygenLineChart({Key? key}) : super(key: key);

  @override
  State<OxygenLineChart> createState() => _LineChartState();
}

class _LineChartState extends State<OxygenLineChart> {
  final BloodOxygenController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
        padding: EdgeInsets.only(
            // top: ScreenAdapter.height(12),
            left: ScreenAdapter.width(12),
            right: ScreenAdapter.height(12),bottom: ScreenAdapter.height(12)),
        width: ScreenAdapter.width(343),
        // height: ScreenAdapter.height(299),
        decoration: BoxDecoration(
          borderRadius:
              BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
          color: AppColors.colorWhite,
        ),
        child: Column(
          children: [

            Container(
              // width: ScreenAdapter.width(343),
              // margin: EdgeInsets.only(bottom: ScreenAdapter.height(12)),
              height: ScreenAdapter.height(48),
              // height: ScreenAdapter.height(88),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: <Widget>[
                  Container(
                    padding: EdgeInsets.only(
                        top: ScreenAdapter.height(16),
                        bottom: ScreenAdapter.height(12)),
                    width: ScreenAdapter.width(16),
                    height: ScreenAdapter.height(44),
                    child: Image.asset(Assets.images.trendIcon.path),
                  ),
                  Container(
                      padding: EdgeInsets.only(
                          top: ScreenAdapter.height(16),
                          bottom: ScreenAdapter.height(12)),
                      margin: EdgeInsets.symmetric(
                          horizontal: ScreenAdapter.width(4)),
                      child: Text(T.bloodOxygen.tr,
                          style: normalF12H17C999.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppColors.Color333))),
                  // Expanded(child: SizedBox()),
                  Spacer(),
                  // TextButton(onPressed: () {  }, child: Text("Nihao"),),
                  _rightButton(),
                ],
              ),
            ),
            Obx(() => MyScatterPlot(
              key: controller.oxygenLineKey,
                index:1,
                requestParamList:controller.requestParamList,
                timeFrame: controller.timeFrame.value,
                width: ScreenAdapter.width(324),
                height: ScreenAdapter.height(250),
                headerStyle: ScatterHeaderStyle(
                  margin: EdgeInsets.only(bottom: ScreenAdapter.height(8)),
                  // margin: EdgeInsets.only(bottom: ScreenAdapter.height(40)),
                  mainTitleStyle: normalF16H22C666.copyWith(
                    color: AppColors.Color333,
                    fontWeight: FontWeight.w600,
                  ),
                  unitStyle: normalF12H17C999,
                  timeStyle:
                      normalF12H17C999.copyWith(fontWeight: FontWeight.w500),
                ), 
                chartStyle: ScatterChartStyle(
                  dotColor:AppColors.lightBlue, 
                  verticalTH: ScreenAdapter.height(20), 
                  horizontalTW: ScreenAdapter.width(22),
                  // horizontalTW: 0, 
                  backgroundColor: AppColors.lightBlue.withOpacity(0.03), 
                  padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(4)), 
                  verticalTextStyle: normalF12H17C999, 
                  horizontalTextStyle: normalF12H17C999, 
                  ),
                  requestParamListChange: controller.requestParamListChange,
                  unit:T.wordsPercentage.tr,
                ),
                
                ),
                Gap(ScreenAdapter.height(12)),

          
          
          ],
        ));
  }

  _rightButton() {
    return Container(
      // width: ScreenAdapter.width(105),
      // height: ScreenAdapter.height(32),
      decoration: BoxDecoration(
        color: AppColors.Coloreee,
        borderRadius: BorderRadius.circular(5),
      ),
      padding: EdgeInsets.symmetric(
          vertical: ScreenAdapter.height(4),
          horizontal: ScreenAdapter.width(6)),
      child: Row(children: List.generate(3, (i) => _rightButtonItem(i))),
    );
  }

  _rightButtonItem(int i) {
    return InkWell(
        onTap: () {
          controller.setTimeFrame(i);
        },
        child: Obx(
          () => Container(
            decoration: BoxDecoration(
                color: controller.timeFrame.value == i
                    ? AppColors.colorWhite
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(3),
                boxShadow: controller.timeFrame.value == i
                    ? [
                        BoxShadow(
                          color: Color(0x14000000),
                          blurRadius: 2,
                          offset: Offset(-ScreenAdapter.width(1), 0),
                          spreadRadius: 0,
                        ),
                        BoxShadow(
                          color: Color(0x14000000),
                          blurRadius: 4,
                          offset: Offset(0, ScreenAdapter.height(4)),
                          spreadRadius: 0,
                        )
                      ]
                    : []),
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(
                vertical: ScreenAdapter.height(4),
                horizontal: ScreenAdapter.width(16)),
            height: ScreenAdapter.height(28),
            child: Text(
              _rightGetText(i),
              style: normalF12H17C666,
            ),
          ),
        ));
  }

  _rightGetText(int i) {
    switch (i) {
      case 0:
        return T.wordsDay.tr;
      case 1:
        return T.wordsWeek.tr;
      case 2:
        return T.wordsMonth.tr;
    }
  }
}


// class OxygenLineChart extends StatefulWidget {
//   const OxygenLineChart({Key? key}) : super(key: key);

//   @override
//   State<OxygenLineChart> createState() => _LineChartState();
// }

// class _LineChartState extends State<OxygenLineChart> {
//   final BloodOxygenController controller = Get.find();

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//         margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
//         padding: EdgeInsets.only(top: ScreenAdapter.height(12)),
//         width: ScreenAdapter.width(343),
//         height: ScreenAdapter.height(219),
//         decoration: BoxDecoration(
//           borderRadius:
//               BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
//           color: AppColors.colorWhite,
//         ),
//         child: Column(
//           children: [
//             Container(
//               // width: ScreenAdapter.width(343),
//               margin: EdgeInsets.only(
//                   left: ScreenAdapter.width(12),
//                   bottom: ScreenAdapter.height(12)),

//               height: ScreenAdapter.height(17),
//               child: Row(
//                 children: <Widget>[
//                   SizedBox(
//                     width: ScreenAdapter.width(16),
//                     height: ScreenAdapter.height(16),
//                     child: Image.asset(Assets.images.trendIcon.path),
//                   ),
//                   Container(
//                       margin: EdgeInsets.symmetric(
//                           horizontal: ScreenAdapter.width(4)),
//                       child: Text(
//                         T.bloodOxygenTrend.tr,
//                         style: normalF12H17C999.copyWith(
//                           fontWeight: FontWeight.w500,
//                           color: AppColors.Color333
//                         )))
//                 ],
//               ),
//             ),
//             Container(
//               width: ScreenAdapter.width(328),
//               height: ScreenAdapter.height(176),
//               margin: EdgeInsets.only(left: ScreenAdapter.width(8),right: ScreenAdapter.width(8)),
//               child: Obx(()=>CustomLine(
//                     key: UniqueKey(),
//                     lineTouchData: controller.lineTouchData,
//                       spots: controller.spots.value,
//                       leftMin: controller.leftMin,
//                       leftMax: controller.leftMax,
//                       leftInterval: controller.leftInterval,
//                       leftStyle: controller.leftStyle,
//                       bgColor: controller.bgColor,
//                       spotsColor: controller.spotsColor,
//                       spotsWidth: ScreenAdapter.width(1),
//                       dotStyle: controller.dotStyle,
//                       bottomStyle: controller.bottomStyle,
//                       listData: [
//                         "01",
//                         "02"
//                       ],),
//             )
//             )
//           ],
//         ));
//   }
// }
