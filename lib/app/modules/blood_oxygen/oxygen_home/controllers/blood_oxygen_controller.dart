import 'dart:convert';
import 'dart:ffi';

import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/model/domains.dart';
import 'package:aiCare/app/core/render/fl_chart/image_dot_painter.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/fl_chart/scatter_plot.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:aiCare/app/network/exceptions/network_exception.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:intl/intl.dart';

class BloodOxygenController extends BaseController {
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  HomeController homeController = Get.find();
  BluetoothController bluetoothController =  Get.find();
  //My domain list
  List<Domains> domainList = [];

  //line ECharts configuration JSON string
  RxString lineOption = "".obs;

  //Line chart x-axis coordinate
  List<String> listDate = [
    '06/01',
    '06/02',
    '06/03',
    '06/04',
    '06/05',
    '06/06',
    '06/07'
  ];

  //Line chart y-axis coordinate
  List<double> value = [88, 94, 99, 92, 96, 82, 90];

  //Is Bluetooth connected
  RxBool bluetoothConnect = true.obs;

  RxList<int> dayDataList = [0, 0, 0].obs;

  RxList<List<FlSpot>> spots = [
    [20]
        .asMap()
        .entries
        .map((entry) =>
            FlSpot((entry.key + 1).toDouble(), entry.value.toDouble()))
        .toList(),
  ].obs;

  double leftMax = 110;
  double leftMin = 60;
  double leftInterval = 10;
  TextStyle leftStyle = normalF12H17C666;
  TextStyle bottomStyle = normalF12H17C666;
  Color bgColor = AppColors.searchButton;

  List<Color> spotsColor = [AppColors.lightBlue];

  List<FlDotData> dotStyle = [
    FlDotData(
        show: true,
        getDotPainter: (spot, perent, barData, index) {
          if (spot.y < 95) {
            return GlowDotCirclePainter(
                radius: ScreenAdapter.width(3), //点的半径
                color: AppColors.errorPoint, //点的颜色
                strokeWidth: ScreenAdapter.width(1), //点的边框宽度
                strokeColor: AppColors.colorWhite, //点边框的颜色
                glowColor: AppColors.errorPoint,
                glowRadius: ScreenAdapter.width(5));
          }
          return FlDotCirclePainter(
            radius: ScreenAdapter.width(3), //点的半径
            color: AppColors.lightBlue, //点的颜色
            strokeWidth: ScreenAdapter.width(1), //点的边框宽度
            strokeColor: Colors.white, //点边框的颜色
          );
        }),
  ];

  Color getDynamicColor(double yValue) {
    return (yValue < 95)
        ? AppColors.errorPoint // 红色
        : AppColors.lightBlue; // 蓝色
  }

  late LineTouchData lineTouchData; // 使用 late 初始化
  
  RxList<OxygenData> valueList = <OxygenData>[].obs;

  // 新增请求控制参数
  RxBool requestParam = true.obs;

  List<RxBool> requestParamList = [false.obs, false.obs, false.obs];

  RxInt timeFrame = 0.obs;

  GlobalKey<MyScatterPlotState> oxygenLineKey = GlobalKey();


  @override
  void onInit() {
    super.onInit();

    everFunction();
    initDomainList();
    initRequestParamList();
  }

    setTimeFrame(int value) {
    // logger.e("message");
    timeFrame.value = value;
    // refresh();
    // logger.d("重新设置了timeFrame");
    // logger.d(timeFrame.value);
  }

  initRequestParamList() {
    requestParamList[0].value =
        storage.getBool(AppValues.bloodOxygenLineDaysRefresh) ?? true;
    requestParamList[1].value =
        storage.getBool(AppValues.bloodOxygenLineWeeksRefresh) ?? true;
    requestParamList[2].value =
        storage.getBool(AppValues.bloodOxygenLineMonthsRefresh) ?? true;
  }

  requestParamListChange(int index, bool value) {
    requestParamList[index].value = value;
    storage.setBool(
        AppValues.bloodOxygenLineDaysRefresh, requestParamList[0].value);
    storage.setBool(
        AppValues.bloodOxygenLineWeeksRefresh, requestParamList[1].value);
    storage.setBool(
        AppValues.bloodOxygenLineMonthsRefresh, requestParamList[2].value);
  }

    everFunction() {
    // 监听 requestParam 的变化
    ever(requestParam, (value) {
      if (value) {
        //刷新
      }
      // 这里可以做你需要的处理
    });

    ever(bluetoothController.isConnected, (connected) {
      if (!connected) {
        hideLoading();
      }
    });
  }

  @override
  void onReady() {
    logger.d("切换后执行函数");
    // TODO: implement onReady
    getData();
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void refreshData() {
    requestParam.value = false;
    requestParam.value = true;
  }

  /**
   * @description: Update Line chart
   * @return null
   */
  void updateLineOption() async {

    // 从 mylist 中提取 percentage 字段并转换为 List<double>
    List<int> percentages =
        valueList.value.map((data) => data.percentage!).toList();
    logger.d("修改三个参数");
    dayDataList.value[2] = 110;
    valueList.value.forEach((data) {
      if (data.percentage! > dayDataList.value[1])
        dayDataList.value[1] = data.percentage!;
      if (data.percentage! < dayDataList.value[2])
        dayDataList.value[2] = data.percentage!;
    });
    if(dayDataList.value[2] ==110) dayDataList.value[2] =0;

    dayDataList.refresh();
    // logger.d(percentages);
    // 将 percentages 转换为 List<FlSpot>
    List<FlSpot> newSpots = percentages
        .sublist(
            percentages.length > 7 ? percentages.length - 7 : 0) // 获取最后 7 个数值
        .asMap()
        .entries
        .map((entry) =>
            FlSpot((entry.key + 1).toDouble(), entry.value.toDouble()))
        .toList();

    // 更新 spots 的值
    spots.value = [newSpots];
    spots.refresh();

    hideLoading();
  }

  /**
   * @description: Initialize my functional area
   * @return null
   */
  void initDomainList() {
    domainList = [
      // Domains(
      //   images: Assets.images.setting,

      //   text: appLocalization.measurementSettings,
      //   tap: () {
      //     logger.d("onclick");
      //   },
      // ),
      // Domains(
      //   images: Assets.images.notion,
      //   text: T.measurementRemind.tr,
      //   tap: () {
      //     logger.d("onclick");
      //     Get.toNamed(Routes.OXYGEN_REMIND_HOME);
      //   },
      // ),
      Domains(
        images: Assets.images.record,
        text: T.measurementRecord.tr,
        tap: () {
          logger.d("onclick");
          Get.toNamed(Routes.OXYGEN_RECORD_HOME);
        },
      ),
    ];
  }

  // void getData({DateTime? fromDate, DateTime? toDate}) async {
  //   // showToast("hello styled toast",context:Get.context);
  //   logger.d("我草");
  //   print(Get.context);
  //   // showLoading();
  //   //默认获取近一天的数据
  //   fromDate ??= DateTime.now().subtract(Duration(days: 1)).toUtc();
  //   toDate ??= DateTime.now().toUtc();
  //   // fromDate.

  //   // String formattedFromDate = formatToIso8601WithNanos(fromDate);
  //   // String formattedToDate = formatToIso8601WithNanos(toDate);
  //   String formattedFromDate =
  //       DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(fromDate.toUtc());
  //   String formattedToDate =
  //       DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(toDate.toUtc());

  //   logger.d("起始日期78：$formattedFromDate");
  //   logger.d("结束日期：$formattedToDate");

  //   // defaultRepositoryImpl.postOxygen(90, DateTime.now().toUtc());
  //   //1天的数据列表，平均
  //   final results = await Future.wait([
  //     //数据列表
  //     defaultRepositoryImpl.getOxygen(formattedFromDate, formattedToDate),
  //     //平均值
  //     defaultRepositoryImpl.getOxygen(formattedFromDate, formattedToDate,
  //         types: "null"),
  //   ]);
  //   // OxygenData
  //   valueList.value = results[0].reversed.toList();
  //   // valueList.value = results[0];
  //   if( results[1][0].percentage != null && results[1][0].percentage != -1){
  //       dayDataList.value[0] = results[1][0].percentage! ;
  //   }
    
  //   // dayDataList.refresh();
  //   logger.d("查看获取最新值");
  //   logger.d(results);
  //   updateLineOption();
  // }

void getData({DateTime? fromDate, DateTime? toDate}) async {
  logger.d("获取数据...");
  
  // 默认获取近一天的数据
  fromDate ??= DateTime.now().subtract(Duration(days: 1)).toUtc();
  toDate ??= DateTime.now().toUtc();

  // 格式化日期为 ISO8601 格式
  String formattedFromDate =
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(fromDate.toUtc());
  String formattedToDate =
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(toDate.toUtc());

  logger.d("起始日期：$formattedFromDate");
  logger.d("结束日期：$formattedToDate");

  // 获取存储的数据
  var storedOxygenList = await storage.getString(AppValues.bloodOxygenList);
  var storedOxygenAverage = await storage.getString(AppValues.bloodOxygenAverage);

  // 如果存储中存在数据，则直接使用存储的数据，不重新请求
  if (storedOxygenList != null && storedOxygenAverage != null && requestParam != true) {
    // 转换存储的字符串数据为实际的对象
    valueList.value = (jsonDecode(storedOxygenList) as List)
        .map((item) => OxygenData.fromJson(item)) // 假设 OxygenData 是一个模型类
        .toList();

    dayDataList.value[0] = int.tryParse(storedOxygenAverage) ?? 0;
    logger.d("使用存储的血氧数据");
  } else {
    // 否则进行数据请求
    final results = await Future.wait([
      // 数据列表
      defaultRepositoryImpl.getOxygen(formattedFromDate, formattedToDate),
      // 平均值
      defaultRepositoryImpl.getOxygen(formattedFromDate, formattedToDate, types: "null"),
    ]);

    // 存储获取的数据
    valueList.value = results[0].reversed.toList();
    if (results[1][0].percentage != null && results[1][0].percentage != -1) {
      dayDataList.value[0] = results[1][0].percentage!;
    }

    // 将数据存入本地存储
     storage.setString(AppValues.bloodOxygenList, jsonEncode(valueList.value));
     storage.setString(AppValues.bloodOxygenAverage, dayDataList.value[0].toString());

    logger.d("获取新数据并存储");
  }

  logger.d("查看获取的数据");
  logger.d(valueList.value);

  updateLineOption();
}




  void measure() async{
    logger.d("查看isConnected:");
    logger.d(bluetoothController.isConnected.value);
    if (bluetoothController.isConnected.value) {
      showLoading();
      // bluetoothController.bluetoothRepository.aizoGetAllData();
      logger.d("bloodOxygen开始测量");
      bluetoothController.aizoMeasureSpO2(
          setMeasurement: setMeasurement);
      // logger.d("查看打印的result");

      // hideLoading();
    } else {
      ToastUtil.showError(Get.context!, T.bluetoothNotConnected.tr);
    }
  }

  void setMeasurement(String value) async{
    logger.d("查看setMeasurement: $value");
    if (value.contains("in progress")) {
      // measurementStatus.value = T.measurementInProgress.tr;
      // return;
    }else
    if (value.contains("started")) {
      // measurementStatus.value = T.measurementStarted.tr;
      ToastUtil.showSuccess(Get.context!, T.measurementStarted.tr);
    } else if (value.contains("successful")) {
      logger.d("查看值");
      logger.d(value);
      //获取最新值
      HomeController homeController = Get.find();
      await homeController.fetchAllData();
      ToastUtil.showSuccess(Get.context!, T.measurementSuccess.tr);
                  logger.d("设置为全 true");
      requestParamList[0].value = true;
      requestParamList[1].value = true;
      requestParamList[2].value = true;
      storage.setBool(AppValues.heartRateLineDaysRefresh, true);
      storage.setBool(AppValues.heartRateLineWeeksRefresh, true);
      storage.setBool(AppValues.heartRateLineMonthsRefresh, true);
      // hideLoading();
      getData();
    } else if (value.contains("error")) {
      // measurementStatus.value = T.measurementError.tr;
      ToastUtil.showError(Get.context!, T.measurementError.tr);
      hideLoading();
    }
  }



}
