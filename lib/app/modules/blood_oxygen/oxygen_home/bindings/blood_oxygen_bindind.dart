/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-31 10:54:14
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-26 16:23:00
 * @FilePath: /rpmappmaster/lib/app/modules/blood_oxygen/oxygen_home/bindings/blood_oxygen_bindind.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/controllers/blood_oxygen_controller.dart';
import 'package:get/get.dart';

class BloodOxygenBinding extends Bindings {
  @override
  void dependencies() {
     Get.lazyPut<BloodOxygenController>(
      () => BloodOxygenController(),
    );
  }
}
