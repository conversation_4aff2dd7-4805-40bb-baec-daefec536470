/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-31 10:54:14
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-23 15:27:13
 * @FilePath: /rpmappmaster/lib/app/modules/blood_oxygen/oxygen_home/views/blood_oxygen_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/widget/custom_domain.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/controllers/blood_oxygen_controller.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/widgets/oxygen_pie_chart.dart';
import 'package:aiCare/app/core/widget/custom_home_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/widgets/oxygen_line_chart.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:get/get.dart';

class BloodOxygenView extends BaseView<BloodOxygenController> {
  BloodOxygenView({
    super.key,
  }) : super(
          bgColor: AppColors.homeBgColor,
          statusBarColor: Colors.white,
        );

  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: ScreenAdapter.height(812-44), // Set the minimum height
        ),
        child: Column(
          children: [
            CustomHomeBar(),
            OxygenPieChart(),
            OxygenLineChart(),
            CustomDomain(list: controller.domainList),
            // OxygenDomain(),
          ],
        ),
      ),
    );
  }
}
