/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-07 10:21:15
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-15 17:07:14
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_record/record_detail/controllers/oxygen_detail_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:convert';

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/controllers/blood_oxygen_controller.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_record/record_home/controllers/record_home_controller.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:get/get.dart';

class OxygenDetailController extends BaseController {
  // BloodOxygenController controller = Get.find();
  final recordHomeController = Get.find<RecordHomeController>();
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  HomeController homeController = Get.find();
  late OxygenData model;

  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    logger.d("获得的model:$model");
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void toDelete() async {
    showLoading();
    logger.d("删除的元素");
    logger.d(model.date);
    await callDataService(defaultRepositoryImpl.deleteOxygen(model.date!));
    if (model.date == homeController.oxygenData.value.date) {
      var result = await defaultRepositoryImpl.getOxygenLast();
      if (result != null) {
        homeController.oxygenData.value = result;
      } else {
        homeController.oxygenData.value = OxygenData();
      }
    }
    hideLoading();

    // Remove the item using the new method
    recordHomeController.removeItemByDate(model.date!);

    Get.back();
  }
}
