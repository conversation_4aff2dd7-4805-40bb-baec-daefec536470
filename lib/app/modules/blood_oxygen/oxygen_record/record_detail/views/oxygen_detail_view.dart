/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-07 10:21:15
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-29 14:56:02
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_detail/views/oxygen_detail_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/model/page_background.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_decoration.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_shadow.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/core/widget/custom_liquid.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_record/record_detail/widgets/oxygen_detail_banner.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_record/record_detail/widgets/oxygen_detail_button.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_record/record_detail/widgets/oxygen_detail_sub.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_record/record_detail/widgets/oxygen_detail_title.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';

import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import '../controllers/oxygen_detail_controller.dart';

class OxygenDetailView extends BaseView<OxygenDetailController> {
  OxygenDetailView({
    super.key,
  }) : super(
          bgColor: Colors.transparent,
          bgImage: PageBackground(
            imagePath: Assets.images.backBlueBlur.path,
            width: ScreenAdapter.width(375), // 宽度375
            height: ScreenAdapter.height(320), // 高度320
            fit: BoxFit.fitHeight, // 填充方式
            left: 0,
            // top: -MediaQuery.of(Get.context!).padding.top,
            top: 0,
          ),
          statusBarColor: AppColors.colorWhite,
        );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: "",
      backgroundColor: Colors.transparent,
    );
  }

  @override
  Widget body(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      width: double.infinity,
      child: Column(
        children: [
          Container(
            // top: ScreenAdapter.height(10),
            // left: ScreenAdapter.width(92),
            child: CustomLiquid(
                amplitude: controller.model.percentage!.toDouble(),
                parentWidth: 182,
                childWidth: 166,
                dataStyle: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: ScreenAdapter.fontSize(40),
                    height: 46.88 / 40,
                    color: AppColors.colorWhite),
                parentBdWidth: 2),
          ),
          SizedBox(
            height: ScreenAdapter.height(36),
          ),
          OxygenDetailTitle(),
          SizedBox(
            height: ScreenAdapter.height(12),
          ),
          OxygenDetailSub(),
          Expanded(child: SizedBox()),
          OxygenDetailButton(),
          SizedBox(
            height: ScreenAdapter.height(20),
          )
        ],
      ),
    );
  }
}
