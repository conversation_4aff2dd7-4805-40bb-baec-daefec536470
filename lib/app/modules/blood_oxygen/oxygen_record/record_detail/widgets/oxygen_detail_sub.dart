/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-07 17:42:55
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-05 16:11:59
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_detail/widgets/oxygen_detail_sub.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_decoration.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_shadow.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;

class OxygenDetailSub extends StatelessWidget with BaseWidgetMixin{
   OxygenDetailSub({super.key});

  
  @override
  Widget body(BuildContext context) {
        return Stack(
      clipBehavior: Clip.none, // 使得子组件可以超出父组件边界
      children: [
        Container(
          padding: EdgeInsets.symmetric(
              vertical: ScreenAdapter.height(10),
              horizontal: ScreenAdapter.width(12)),
          decoration: BoxDecoration(
            color: AppColors.searchButton,
            borderRadius: BorderRadius.circular(ScreenAdapter.width(8)),
          ),
          child: Text(
            T.measurementResultNormal.tr,
            style: TextStyle(
                color: AppColors.Color999,
                fontWeight: FontWeight.w500,
                fontSize: ScreenAdapter.fontSize(12),
                height: ScreenAdapter.fontSize(17 / 12)),
          ),
        ),
        Positioned(
          top: -ScreenAdapter.height(10), // 控制三角形位置
          left: ScreenAdapter.width(162), // 控制三角形位置
          child: CustomPaint(
            size: Size(
                ScreenAdapter.width(18), ScreenAdapter.height(10)), // 不需要设置大小
            painter: TrianglePainter(),
          ),
        ),
      ],
    );
  
  }
}

class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.searchButton
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(size.width / 2, 0) // 顶点在上方，水平居中
      ..lineTo(0, size.height) // 左下角
      ..lineTo(size.width, size.height) // 右下角
      ..close(); // 关闭路径，形成三角形

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
