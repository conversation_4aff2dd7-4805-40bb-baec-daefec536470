/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-07 10:26:39
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-08-26 15:01:35
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_detail/widgets/oxygen_detail_banner.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';

class OxygenDetailBanner extends StatelessWidget {
  const OxygenDetailBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: ScreenAdapter.height(-50),
      left: ScreenAdapter.width(48),
      right: 0,
      child: IgnorePointer(
          ignoring: true,
          child: <PERSON><PERSON>B<PERSON>(
            width: ScreenAdapter.width(327),
            height: ScreenAdapter.height(220),
            child: Image.asset(
              Assets.images.oxygenDetailBanner.path,
              fit: BoxFit.fill,
            ),
          )),
    );
  }
}
