/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-07 17:43:49
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-04 13:16:33
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_detail/widgets/oxygen_detail_button.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_decoration.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_shadow.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_record/record_detail/controllers/oxygen_detail_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class OxygenDetailButton extends StatelessWidget with BaseWidgetMixin {
  OxygenDetailController controller = Get.find();
  OxygenDetailButton({super.key});

  @override
  Widget body(BuildContext context) {
    return InkWell(
      onTap: (){
        controller.toDelete();
      },
      child: Container(
        width: ScreenAdapter.width(208),
        height: ScreenAdapter.height(38),
        decoration: BoxDecoration(
            color: AppColors.searchButton,
            border: Border.all(
                color: AppColors.lightBlue, width: ScreenAdapter.width(1)),
            borderRadius: BorderRadius.circular(ScreenAdapter.width(24))),
        child: Center(
          child: Text(
            T.commonDeleteRecord.tr,
            style: TextStyle(
                color: AppColors.lightBlue,
                fontSize: ScreenAdapter.fontSize(16),
                fontWeight: FontWeight.w500,
                height: ScreenAdapter.fontSize(22.4 / 16)),
          ),
        )),
    );
  
  }
}
