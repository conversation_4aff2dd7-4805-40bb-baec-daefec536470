/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-31 11:23:25
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-09-03 14:59:53
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_record/record_home/bindings/oxygen_record_binding.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:get/get.dart';

import '../controllers/record_home_controller.dart';

class RecordHomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<RecordHomeController>(
      () => RecordHomeController(),
    );
  }
}
