/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-04 01:31:43
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-07 13:39:27
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_record/record_home/views/record_home_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/core/widget/record_card.dart';
import 'package:aiCare/app/core/widget/record_search.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/record_home_controller.dart';

class RecordHomeView extends BaseView<RecordHomeController> {
  RecordHomeView({super.key})
      : super(
          bgColor: AppColors.colorWhite,
          statusBarColor: AppColors.colorWhite,
        );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: T.measurementRecord.tr,
    );
  }

  @override
  Widget body(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            SizedBox(
              height: ScreenAdapter.height(64),
            ),
            Expanded(
              child: Obx(() => RefreshIndicator(
                    onRefresh: controller.refreshData,
                    color: AppColors.lightBlue,
                    child: controller.valueList.isEmpty 
                      ? ListView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          children: [
                            SizedBox(
                              height: MediaQuery.of(context).size.height * 0.3,
                            ),
                            Center(
                              child: Text(
                                T.recordNoMore.tr,
                                style: normalF12H17C999,
                              ),
                            ),
                          ],
                        )
                      : ListView.builder(
                          controller: controller.scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          itemCount: controller.valueList.length + 1,
                          itemBuilder: (context, index) {
                            if (index == controller.valueList.length) {
                              return _buildLoadingIndicator(controller);
                            }
                            return RecordCard(
                              index: 0,
                              data: controller.valueList[index],
                            );
                          },
                        ),
                  )),
            )
          ],
        ),
        Obx(() => controller.searchBool.value
            ? Positioned.fill(
                child: InkWell(
                onTap: controller.doClose,
                child: Container(
                  color: Colors.black.withOpacity(0.6), // 半透明的阴影色
                ),
              ))
            : SizedBox()),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: RecordSearch(
            controller: controller,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingIndicator(RecordHomeController controller) {
    return Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: controller.isLoading.value
            ? SizedBox()
            : Text(controller.currentPage.value < controller.totalPages
                ? T.recordDropMore.tr
                : T.recordNoMore.tr,
                style: normalF12H17C999,),
      ),
    );
  }
}
