import 'dart:math';

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/base/controller/record_search_controller.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxyge_page_data.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/controllers/blood_oxygen_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:intl/intl.dart';

class RecordHomeController extends RecordSearchController<OxygenData> {
  final CancelToken _cancelToken = CancelToken(); // 定义 CancelToken
  final ScrollController scrollController = ScrollController();
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();

  // 定义状态变量
  var isRefreshing = false.obs; // 下拉刷新状态
  var isLoadingMore = false.obs; // 上拉加载状态

  // 分页相关状态
  RxInt currentPage = 1.obs;
  RxInt total = 0.obs; // 总数据量
  RxBool isLoading = false.obs;
  int pageSize = 10;

  // 计算总页数
  int get totalPages => (total.value + pageSize - 1) ~/ pageSize;

  //默认时间筛选参数
  DateTime startTime = DateTime.now().subtract(Duration(days: 365)).toUtc();
  DateTime endTime = DateTime.now().toUtc();

  //保存数据的列表
  RxList<OxygenData> valueList = RxList<OxygenData>([]);
  RxList<OxygenData> initialList = RxList<OxygenData>([]);

  @override
  void onInit() {
    super.onInit();
    initList();
    _setupScrollListener();
    loadInitialData();
    // getPageData();
    // update();
  }

  @override
  void onClose() {
    scrollController.dispose();
    _cancelToken.cancel("页面销毁"); // 页面退出时取消所有关联请求
    super.onClose();
  }

  void _setupScrollListener() {
    scrollController.addListener(() {
      final position = scrollController.position;
      final scrollDirection = position.userScrollDirection;
      
      // 检查是否可以滚动
      if (!position.hasContentDimensions) return;
      
      if (position.pixels >= position.maxScrollExtent - 200 && // 接近底部
          position.pixels <= position.maxScrollExtent && // 防止弹性滚动
          scrollDirection == ScrollDirection.reverse && // 滚动方向向上
          // !isRefreshing.value && // 未在刷新
          !isLoadingMore.value && // 未在加载
          currentPage.value < totalPages) { // 有更多数据
        loadMoreData();
      }
    });
  }

  

  // 初始化加载数据
  Future<void> loadInitialData() async {
    isRefreshing.value = true;
    currentPage.value = 1;
    super.doClose();
    valueList.clear();
    await fetchData(
      page: currentPage.value,
    );
    isRefreshing.value = false;
  }

  // 通用数据获取方法
  Future<void> fetchData({required int page, int? type}) async {
    try {
      showLoading();
      // logger.w("修改LoadingValue 为 true");

      isLoading.value = true;

      // 格式化筛选参数
      final formattedFrom =
          DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(startTime);
      final formattedTo =
          DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(endTime);

      // 获取分页数据
      OxygenPageData response;
      if (type == null) {
        response = await defaultRepositoryImpl.getOxygenPage(
            formattedFrom, formattedTo, page,
            pageSize: pageSize, cancelToken: _cancelToken);
      } else {
        response = await defaultRepositoryImpl.getOxygenPage(
            formattedFrom, formattedTo, page,
            pageSize: pageSize, cancelToken: _cancelToken, type: type);
      }

      //如果 page 是 第一页 并且 type 为空，则表示当前是初始数据，存储在 initialList
      if (page == 1 && type == null) {
        initialList.value = response.data;
      }

      if (page == 1) {
        valueList.clear();
        total.value = response.total;
      }
      valueList.addAll(response.data);
      currentPage.value = response.page;
    } catch (e) {
      if (e is DioException && e.type == DioExceptionType.cancel) {
        logger.e("请求被主动取消"); // 可忽略或记录日志
      }
    } finally {
      // logger.w("修改LoadingValue 为 false");
      isLoading.value = false;
      hideLoading();
    }
  }

  // 加载更多数据
  Future<void> loadMoreData() async {
    isLoadingMore.value = true;
    if (currentPage.value >= totalPages || isLoading.value) return;
    await fetchData(page: currentPage.value + 1,type: selectSeverity.value);
    isLoadingMore.value = false;
  }

  // 上拉刷新
  Future<void> refreshData() async {
    if (isRefreshing.value) return;
    await loadInitialData();
  }

  /**
   * @description: 
   * 1.收缩筛选框
   * 2.selectSeverity，判断程度划分
   * 3.获取selectFuzzyTime，判断时间划分 设置 startTime 和 endTime
   * 4.设置 currentPage = 1
   * 5.fetchData()，传入对应的参数
   * @return null
   */
  @override
  doFinish() {
    logger.d("执行了oxygen的dofinish");
    searchBool.value = false;

    //0 1 2 3
    logger.d("查看selectSeverity：${selectSeverity.value}");
    //0 1 2 3 99：精确时间筛选
    logger.d("查看selectFuzzyTime：${selectFuzzyTime.value}");
    switch (selectFuzzyTime.value) {
      case 0:
        startTime = DateTime.now().subtract(Duration(days: 1)).toUtc();
        endTime = DateTime.now().toUtc();
        break;
      case 1:
        startTime = DateTime.now().subtract(Duration(days: 7)).toUtc();
        endTime = DateTime.now().toUtc();
        break;
      case 2:
        startTime = DateTime.now().subtract(Duration(days: 31)).toUtc();
        endTime = DateTime.now().toUtc();
        break;
      case 3:
        startTime = DateTime.now().subtract(Duration(days: 365)).toUtc();
        endTime = DateTime.now().toUtc();
        break;
      case 99:
        logger.d("进入精确时间筛选");
        logger.d(firstData);
        // 精确时间筛选
        if (firstData.value
                .any((element) => element != "0000" && element != "00") &&
            lateData.value
                .any((element) => element != "0000" && element != "00")) {
          DateTime firstDate = DateTime(
            int.parse(firstData.value[0]), // 假设年份前面是20
            int.parse(firstData.value[1]),
            int.parse(firstData.value[2]),
          );
          DateTime lateDate = DateTime(
            int.parse(lateData.value[0]), // 假设年份前面是20
            int.parse(lateData.value[1]),
            int.parse(lateData.value[2]),
          );
          startTime = firstDate.toUtc();
          endTime = lateDate.toUtc();
          break;
        }

      default:
        // 其他情况
        logger.d("时间筛选未匹配");
        break;
    }

    currentPage.value = 1;
    fetchData(page: currentPage.value, type: selectSeverity.value);
  }

  @override
  doClose() {
    // loadInitialData();
    searchBool.value = false;
    final temp = initialList.value;
    valueList.value = temp;
    valueList.refresh();
    return super.doClose();
  }

  /**
   * @description: init text List
   * @return null
   */
  void initList() {
    severityList.value = [
      T.wordsNormal.tr,
      T.wordsMild.tr,
      T.wordsModerate.tr,
      T.wordsSerious.tr,
    ];
    dateList.value = [
      T.dateToday.tr,
      T.dateLastWeek.tr,
      T.dateLastMonth.tr,
      T.dateLastYear.tr,
    ];
  }

  /// Removes an item from both valueList and initialList by date
  /// Updates the total count and refreshes the lists
  void removeItemByDate(DateTime date) {
    valueList.removeWhere((item) => item.date?.toIso8601String() == date.toIso8601String());
    initialList.removeWhere((item) => item.date?.toIso8601String() == date.toIso8601String());
    total.value--;
    
    // Reset pagination state if current page is empty
    if (valueList.isEmpty && currentPage.value > 1) {
      currentPage.value--;
      fetchData(page: currentPage.value, type: selectSeverity.value);
    } else {
      valueList.refresh();
      initialList.refresh();
    }

    // Reset scroll controller state
    if (scrollController.hasClients) {
      scrollController.position.jumpTo(0);
    }
  }
}
