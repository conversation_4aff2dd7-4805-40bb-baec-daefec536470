/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-16 17:22:33
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-02-11 15:05:52
 * @FilePath: /rpmappmaster/lib/app/modules/blood_oxygen/model/oxygen_data.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class OxygenData {
  int? percentage; // 百分比
  DateTime? date; // 时间
  int? dataSource; // 数据来源

  // 构造函数
  OxygenData({
    this.dataSource,
    this.percentage,
    this.date,
  });

  // 将对象转换为 JSON 格式的 Map
  Map<String, dynamic> toJson() {
    return {
      'percentage': percentage,
      'date': date?.toIso8601String(), // 将 DateTime 转换为字符串，处理 null 值
      'dataSource': dataSource,
    };
  }

  // 从 JSON 创建对象
  factory OxygenData.fromJson(Map<String, dynamic> json) {
    return OxygenData(
      dataSource: json['dataSource'] as int?,
      percentage: (json['percentage'] as int?)?.toInt(),
      date:
          json['date_time'] != null ? DateTime.parse(json['date_time']) : null,
    );
  }

  // 从 JSON 创建对象
  factory OxygenData.fromJsonToAPI(Map<String, dynamic> json) {
    return OxygenData(
      dataSource: json['type'] as int? ?? 1, // 默认值为 1
      percentage:
          (json['saturation'] as num?)?.toInt(), // saturation 对应 percentage
      date: json['date_time'] != null
          ? DateTime.parse(json['date_time'])
          : null, // 解析时间字符串
    );
  }

  @override
  String toString() {
    return 'OxygenData(percentage: $percentage, date: $date, dataSource: $dataSource)';
  }

  int getTotalLevel() {
    if (percentage == null) {
      return 3; // 默认返回 3，如果 percentage 为空
    } else if (percentage! >= 95) {
      return 0;
    } else if (percentage! >= 90) {
      return 1;
    } else if (percentage! >= 80) {
      return 2;
    } else {
      return 3;
    }
  }

  bool isNull(){
    if(percentage == null) return true;
    return false;
  }
}
