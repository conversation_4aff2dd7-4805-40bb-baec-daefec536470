// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2025-01-08 13:52:34
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-01-10 16:57:16
//  * @FilePath: /rpmappmaster/lib/app/modules/test_component/widgets/avatar_upload.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:io';
// import 'package:aiCare/app/data/repository/default_repository_impl.dart';
// import 'package:aiCare/app/modules/user/user_home/controllers/user_controller.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:get/get.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:flutter/material.dart';

// class AvatarUploadPage extends StatefulWidget {
//   @override
//   _AvatarUploadPageState createState() => _AvatarUploadPageState();
// }

// class _AvatarUploadPageState extends State<AvatarUploadPage> {
//   File? _image;
//   final ImagePicker _picker = ImagePicker();
//   UserHomeController userHomeController = Get.find();
//   DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();

//   // 选择图片
//   Future<void> _pickImage() async {
//     final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
//     if (pickedFile != null) {
//       File? croppedFile = await _cropImage(File(pickedFile.path)); // 裁剪图片
//       if (croppedFile != null) {
//         setState(() {
//           _image = croppedFile;
//         });
//       }
//     }
//   }

//   // 裁剪图片
//   Future<File?> _cropImage(File imageFile) async {
//     // final croppedFile = await ImageCropper().cropImage(
//     //   sourcePath: imageFile.path,
//     //   aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1), // 裁剪为正方形
//     //   compressQuality: 90, // 压缩图片质量
//     //   uiSettings: [
//     //     AndroidUiSettings(
//     //       toolbarTitle: 'Crop Image',
//     //       toolbarColor: Colors.blue,
//     //       toolbarWidgetColor: Colors.white,
//     //       initAspectRatio: CropAspectRatioPreset.original,
//     //       lockAspectRatio: true,
//     //     ),
//     //     IOSUiSettings(
//     //       title: 'Crop Image',
//     //       doneButtonTitle: 'Done',
//     //       cancelButtonTitle: 'Cancel',
//     //     ),
//     //   ],
//     // );
//     // return croppedFile != null ? File(croppedFile.path) : null;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: ScreenAdapter.height(800),
//       width: double.infinity,
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           _image != null
//               ? CircleAvatar(
//                   radius: ScreenAdapter.width(336)/2,
//                   backgroundImage: FileImage(_image!),
//                 )
//               : CircleAvatar(
//                   radius: 50,
//                   child: Icon(Icons.person, size: 50),
//                 ),
//           SizedBox(height: 20),
//           ElevatedButton(
//             onPressed: _pickImage,
//             child: Text('Select and Crop Image'),
//           ),
//         ],
//       ),
//     );
//   }



// }
