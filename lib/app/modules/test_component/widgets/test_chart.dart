// import 'package:fl_chart/fl_chart.dart';
// import 'package:flutter/material.dart';

// class BloodOxygenChart extends StatefulWidget {
//   @override
//   _BloodOxygenChartState createState() => _BloodOxygenChartState();
// }

// class _BloodOxygenChartState extends State<BloodOxygenChart> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: Text("血氧饱和度")),
//       body: Column(
//         children: [
//           Expanded(
//             child: ShaderMask(
//               shaderCallback: (Rect bounds) {
//                 return LinearGradient(
//                   begin: Alignment.centerLeft,
//                   end: Alignment.centerRight,
//                   colors: [
//                     Colors.transparent,
//                     Colors.white, // 主色调，和背景色一致
//                     Colors.white,
//                     Colors.transparent,
//                   ],
//                   stops: [0.0, 0.05, 0.95, 1.0],
//                 ).createShader(bounds);
//               },
//               blendMode: BlendMode.dstIn,
//               child: SingleChildScrollView(
//                 scrollDirection: Axis.horizontal,
//                 child: Container(
//                   width: 1000, // 控制水平长度
//                   padding: EdgeInsets.symmetric(vertical: 20),
//                   child: LineChart(
//                     LineChartData(
//                       minX: 0,
//                       maxX: 24, // 代表 24 小时
//                       minY: 84,
//                       maxY: 100,
//                       titlesData: FlTitlesData(
//                         leftTitles: AxisTitles(
//                           sideTitles: SideTitles(
//                             showTitles: true,
//                             interval: 2,
//                           ),
//                         ),
//                         bottomTitles: AxisTitles(
//                           sideTitles: SideTitles(
//                             showTitles: true,
//                             interval: 4,
//                             getTitlesWidget: (value, meta) {
//                               // 时间标注，显示如 00:00, 04:00 等
//                               return Text("${value.toInt()}:00");
//                             },
//                           ),
//                         ),
//                       ),
//                       lineBarsData: [
//                         LineChartBarData(
//                           spots: [
//                             FlSpot(0, 98),
//                             FlSpot(2, 97),
//                             FlSpot(4, 96),
//                             FlSpot(6, 98),
//                             FlSpot(8, 98),
//                             FlSpot(10, 87), // 模拟数据
//                             FlSpot(12, 95),
//                             FlSpot(14, 96),
//                             FlSpot(16, 98),
//                             FlSpot(18, 97),
//                             FlSpot(20, 98),
//                             FlSpot(22, 96),
//                             FlSpot(24, 98),
//                           ],
//                           isCurved: true,
//                           // colors: [Colors.green],
//                           barWidth: 3,
//                           belowBarData: BarAreaData(
//                             show: true,
//                             // colors: [Colors.green.withOpacity(0.3)],
//                           ),
//                         ),
//                       ],
//                       gridData: FlGridData(show: false),
//                       borderData: FlBorderData(show: false),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//           // 添加类似下方的标注和信息
//           Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   "平均血氧 98%",
//                   style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
//                 ),
//                 Text(
//                   "最新值 96%   血氧范围 87% ~ 99%",
//                   style: TextStyle(fontSize: 16),
//                 ),
//                 // 颜色标注
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Icon(Icons.circle, color: Colors.red, size: 10),
//                     Text("< 70%", style: TextStyle(color: Colors.red)),
//                     Icon(Icons.circle, color: Colors.orange, size: 10),
//                     Text("70 - 89%", style: TextStyle(color: Colors.orange)),
//                     Icon(Icons.circle, color: Colors.green, size: 10),
//                     Text(">= 90%", style: TextStyle(color: Colors.green)),
//                   ],
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
