// import 'package:aiCare/app/services/screenAdapter.dart';

// class CustomNavbarData {
//   double width;
//   double height;

//   // 构造函数，默认为宽度200，高度200
//   CustomNavbarData({double? width, double? height})
//       : this.width = width ?? ScreenAdapter.width(200),
//         this.height = height ?? ScreenAdapter.width(40);

//   @override
//   String toString() {
//     return 'CustomNavbarData(width: $width, height: $height)';
//   }
// }
