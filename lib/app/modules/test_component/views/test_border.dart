// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';

// class CircularContainerWithGradientBorder extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       width: ScreenAdapter.width(151),
//       height: ScreenAdapter.height(151),
//       decoration: const BoxDecoration(
//         color: Colors.transparent, // 圆形内部的背景颜色
//         shape: BoxShape.circle, // 圆形
//       ),
//       child: CustomPaint(
//         painter: GradientBorderPainter(),
//         child: Container(),
//       ),
//     );
//   }
// }

// class GradientBorderPainter extends CustomPainter {
//   @override
//   void paint(Canvas canvas, Size size) {
//     final Paint paint = Paint()
//       ..shader = const LinearGradient(
//         begin: Alignment.topCenter,
//         end: Alignment.bottomCenter,
//         colors: [
//           Color(0xFF49F23B), // 渐变开始的颜色
//           Color(0xFF85ED00), // 渐变结束的颜色
//         ],
//       ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = ScreenAdapter.width(2); // 边框宽度

//     final double radius = size.width / 2;
//     canvas.drawCircle(Offset(radius, radius), radius - 2, paint); // 绘制圆形边框
//   }

//   @override
//   bool shouldRepaint(CustomPainter oldDelegate) {
//     return false;
//   }
// }
