// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-10-25 15:07:36
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-01 13:22:02
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/test_component/views/custom_navbar.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:ffi';

// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/modules/test_component/controllers/test_component_controller.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:get/get.dart';
// import 'package:logger/logger.dart';

// class CustomNavbar extends StatefulWidget {
//   Rx<bool> isCenterDropdownOpen = false.obs; // 控制下拉菜单的显示
//   Rx<bool> isRightDropdownOpen = false.obs; // 控制下拉菜单的显示

//   CustomNavbar({super.key});

//   @override
//   State<CustomNavbar> createState() => _CustomNavbarState();
// }

// class _CustomNavbarState extends State<CustomNavbar> {
//   TestComponentController controller = Get.find();

//   @override
//   Widget build(BuildContext context) {
//     final overlay = Overlay.of(context);

//     return Container(
//         // color: Colors.red,
//         width: double.infinity,
//         height: ScreenAdapter.height(28),
//         // margin: EdgeInsets.only(bottom: 50),
//         child: Stack(
//           clipBehavior: Clip.none,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               // crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 InkWell(
//                   onTap: () {
//                     Get.back();
//                   },
//                   child: Container(
//                     width: ScreenAdapter.width(24),
//                     height: ScreenAdapter.height(24),
//                     child: Image.asset(
//                       Assets.images.vectorLeft.path,
//                       fit: BoxFit.cover,
//                     ),
//                   ),
//                 ),

//                 GestureDetector(
//                   onTap: () {
//                     print("开始showCenterLay");
//                     showCenterLay(context, overlay); // 这里调用 showOverlayx
//                     // widget.isCenterDropdownOpen.value =
//                     //     !widget.isCenterDropdownOpen.value;
//                   },
//                   child: Container(
//                       padding: EdgeInsets.symmetric(
//                           horizontal: ScreenAdapter.width(16)),
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         // borderRadius: BorderRadius.circular(8),
//                         // border: Border.all(color: Colors.grey),
//                       ),
//                       child: Obx(
//                         () => Row(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             Text(
//                               controller.dropdownValue.value, // 当前选中的值
//                               style: TextStyle(
//                                   color: AppColors.Color444,
//                                   fontWeight: FontWeight.w500,
//                                   fontSize: ScreenAdapter.fontSize(20),
//                                   height: 28 / 20),
//                             ),
//                             SizedBox(
//                               width: ScreenAdapter.width(6),
//                             ),
//                             Container(
//                               width: ScreenAdapter.width(12),
//                               height: ScreenAdapter.height(24),
//                               // color: Colors.green,
//                               child: Image.asset(
//                                 Assets.images.vectorBottom.path,
//                                 fit: BoxFit.contain,
//                               ),
//                             )
//                           ],
//                         ),
//                       )),
//                 ),

//                 // Column(
//                 //   crossAxisAlignment: CrossAxisAlignment.center,
//                 //   children: [
//                 //     // 顶部的按钮，用于触发下拉菜单

//                 //     // 下拉菜单
//                 //   ],
//                 // ),
//                 GestureDetector(
//                     onTap: () {
//                       print("开始showRightLay");
//                       showRightLay(context,overlay); // 这里调用 showOverlayx
//                       // widget.isRightDropdownOpen.value =
//                       //     !widget.isRightDropdownOpen.value;
//                     },
//                     child: Container(
//                       margin: EdgeInsets.only(
//                           left: ScreenAdapter.width(16),
//                           top: ScreenAdapter.height(2),
//                           bottom: ScreenAdapter.height(2)),
//                       width: ScreenAdapter.width(20),
//                       height: ScreenAdapter.height(20),
//                       child: Image.asset(
//                         Assets.images.navigationRight.path,
//                         fit: BoxFit.cover,
//                       ),
//                     )),
//               ],
//             ),

//             //测试
//             // Positioned(
//             //   top: ScreenAdapter.height(100),
//             //   left: 0,
//             //   right: 0,
//             //   child: Center(
//             //     child: InkWell(
//             //       onTap: (){
//             //         print("点击");
//             //       },
//             //       child: Container(
//             //         color: Colors.red,
//             //         width: ScreenAdapter.width(100),
//             //         height: ScreenAdapter.height(20),

//             //       ),
//             //     ),
//             //   )),

//             Positioned(
//                 top: ScreenAdapter.height(100),
//                 // left: 0,
//                 right: 0,
//                 // left: 40,
//                 child: Center(
//                   child: Obx(
//                     () => Visibility(
//                       visible: widget.isRightDropdownOpen.value,
//                       child: IntrinsicWidth(
//                         child: Container(
//                           // color: Colors.blue,
//                           padding: EdgeInsets.symmetric(
//                             horizontal: ScreenAdapter.width(8),
//                             vertical: ScreenAdapter.height(8),
//                           ),
//                           decoration: BoxDecoration(
//                             color: Colors.blue,
//                             borderRadius:
//                                 BorderRadius.circular(ScreenAdapter.width(4)),
//                             boxShadow: [
//                               BoxShadow(
//                                 offset: Offset(0, ScreenAdapter.height(2)),
//                                 color: Colors.black12,
//                                 blurRadius: ScreenAdapter.width(4),
//                                 spreadRadius: 0,
//                               ),
//                             ],
//                           ),
//                           child: Column(
//                             mainAxisSize: MainAxisSize.min, // 确保宽度根据子组件大小调整
//                             crossAxisAlignment:
//                                 CrossAxisAlignment.center, // 保证文本左对齐
//                             children: List.generate(controller.sheetList.length,
//                                 (index) {
//                               String value = controller.sheetList[index];
//                               return Column(
//                                 children: [
//                                   InkWell(
//                                     onTap: () {
//                                       print("点击了");
//                                       widget.isRightDropdownOpen.value = false;
//                                       controller.dropdownValue.value = value;
//                                     },
//                                     child: Container(
//                                       color: Colors.red,
//                                       width: double.infinity, // 占满整个父容器宽度
//                                       padding: EdgeInsets.symmetric(
//                                           horizontal: ScreenAdapter.height(8)),
//                                       margin: EdgeInsets.only(
//                                         top: index == 0
//                                             ? 0
//                                             : ScreenAdapter.height(6),
//                                         bottom: index ==
//                                                 controller.sheetList.length - 1
//                                             ? 0
//                                             : ScreenAdapter.height(6),
//                                         left: ScreenAdapter.width(12),
//                                         right: ScreenAdapter.width(12),
//                                       ),
//                                       child: Center(
//                                           child: Row(
//                                         mainAxisAlignment:
//                                             MainAxisAlignment.start,
//                                         children: [
//                                           SizedBox(
//                                             width: ScreenAdapter.width(20),
//                                             height: ScreenAdapter.height(20),
//                                             child: Image.asset(
//                                               Assets.images.pen.path,
//                                             ),
//                                           ),
//                                           SizedBox(
//                                             width: ScreenAdapter.width(4),
//                                           ),
//                                           Text(
//                                             value,
//                                             style: TextStyle(
//                                               fontSize:
//                                                   ScreenAdapter.fontSize(16),
//                                               height: 22.4 / 16,
//                                               color: AppColors.Color666,
//                                             ),
//                                           )
//                                         ],
//                                       )
//                                       ),
//                                     ),
//                                   ),
//                                   // 添加分割线，但排除最后一个子项
//                                   if (index < controller.sheetList.length - 1)
//                                     Divider(
//                                       height: ScreenAdapter.height(1),
//                                       color: AppColors.homeBgColor, // 设置分割线颜色
//                                     ),
//                                 ],
//                               );
//                             }),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                 )),

//             Positioned(
//                 top: ScreenAdapter.height(40),
//                 left: 0,
//                 right: 0,
//                 // left: 40,
//                 child: Center(
//                   child: Obx(
//                     () => Visibility(
//                       visible: widget.isCenterDropdownOpen.value,
//                       child: IntrinsicWidth(
//                         child: Container(
//                           padding: EdgeInsets.symmetric(
//                             horizontal: ScreenAdapter.width(8),
//                             vertical: ScreenAdapter.height(8),
//                           ),
//                           decoration: BoxDecoration(
//                             color: Colors.white,
//                             borderRadius:
//                                 BorderRadius.circular(ScreenAdapter.width(4)),
//                             boxShadow: [
//                               BoxShadow(
//                                 offset: Offset(0, ScreenAdapter.height(2)),
//                                 color: Colors.black12,
//                                 blurRadius: ScreenAdapter.width(4),
//                                 spreadRadius: 0,
//                               ),
//                             ],
//                           ),
//                           child: Column(
//                             mainAxisSize: MainAxisSize.min, // 确保宽度根据子组件大小调整
//                             crossAxisAlignment:
//                                 CrossAxisAlignment.center, // 保证文本左对齐
//                             children: List.generate(controller.sheetList.length,
//                                 (index) {
//                               String value = controller.sheetList[index];
//                               return Column(
//                                 children: [
//                                   GestureDetector(
//                                     onTap: () {
//                                       widget.isCenterDropdownOpen.value = false;
//                                       controller.dropdownValue.value = value;
//                                     },
//                                     child: Container(
//                                       color: Colors.red,
//                                       width: double.infinity, // 占满整个父容器宽度
//                                       padding: EdgeInsets.symmetric(
//                                           horizontal: ScreenAdapter.height(8)),
//                                       margin: EdgeInsets.only(
//                                         top: index == 0
//                                             ? 0
//                                             : ScreenAdapter.height(6),
//                                         bottom: index ==
//                                                 controller.sheetList.length - 1
//                                             ? 0
//                                             : ScreenAdapter.height(6),
//                                         left: ScreenAdapter.width(12),
//                                         right: ScreenAdapter.width(12),
//                                       ),
//                                       child: Center(
//                                           child: Text(
//                                         value,
//                                         style: TextStyle(
//                                           fontSize: ScreenAdapter.fontSize(16),
//                                           height: 22.4 / 16,
//                                           color: AppColors.Color666,
//                                         ),
//                                       )),
//                                     ),
//                                   ),
//                                   // 添加分割线，但排除最后一个子项
//                                   if (index < controller.sheetList.length - 1)
//                                     Divider(
//                                       height: ScreenAdapter.height(1),
//                                       color: AppColors.homeBgColor, // 设置分割线颜色
//                                     ),
//                                 ],
//                               );
//                             }),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                 ))
//           ],
//         ));
//   }

//   void showCenterLay(BuildContext context, OverlayState overlay) {
//     // 提前声明 overlayEntry 变量，使用 `late` 延迟初始化
//     late OverlayEntry overlayEntry;

//     // 创建 OverlayEntry 并赋值给 overlayEntry
//     overlayEntry = OverlayEntry(
//       builder: (context) => Positioned(
//         top: ScreenAdapter.height(124),
//         left: 0,
//         right: 0,
//         child: Center(
//           child: Material(
//             // 添加 Material 以支持 InkWell 和 Text 等 Material 组件
//             // color: Colors.transparent,
//             child: Obx(
//               () => IntrinsicWidth(
//                 child: Container(
//                   padding: EdgeInsets.symmetric(
//                     horizontal: ScreenAdapter.width(8),
//                     vertical: ScreenAdapter.height(8),
//                   ),
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     borderRadius: BorderRadius.circular(ScreenAdapter.width(4)),
//                     boxShadow: [
//                       BoxShadow(
//                         offset: Offset(0, ScreenAdapter.height(2)),
//                         color: Colors.black12,
//                         blurRadius: ScreenAdapter.width(4),
//                         spreadRadius: 0,
//                       ),
//                     ],
//                   ),
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min, // 确保宽度根据子组件大小调整
//                     crossAxisAlignment: CrossAxisAlignment.center, // 保证文本左对齐
//                     children:
//                         List.generate(controller.sheetList.length, (index) {
//                       String value = controller.sheetList[index];
//                       return Column(
//                         children: [
//                           InkWell(
//                             onTap: () {
//                               print("点击了");
//                               overlayEntry.remove(); // 点击后移除 Overlay
//                               widget.isRightDropdownOpen.value = false;
//                               controller.dropdownValue.value = value;
//                             },
//                             child: Container(
//                               // color: Colors.red,
//                               width: double.infinity, // 占满整个父容器宽度
//                               padding: EdgeInsets.symmetric(
//                                   horizontal: ScreenAdapter.height(8)),
//                               margin: EdgeInsets.only(
//                                 top: index == 0 ? 0 : ScreenAdapter.height(6),
//                                 bottom: index == controller.sheetList.length - 1
//                                     ? 0
//                                     : ScreenAdapter.height(6),
//                                 left: ScreenAdapter.width(12),
//                                 right: ScreenAdapter.width(12),
//                               ),
//                               child: Center(
//                                 child:Text(
//                                       value,
//                                       style: TextStyle(
//                                         fontSize: ScreenAdapter.fontSize(16),
//                                         height: 22.4 / 16,
//                                         color: AppColors.Color666,
//                                       ),
//                                     ),
//                               ),
//                             ),
//                           ),
//                           // 添加分割线，但排除最后一个子项
//                           if (index < controller.sheetList.length - 1)
//                             Divider(
//                               height: ScreenAdapter.height(1),
//                               color: AppColors.homeBgColor, // 设置分割线颜色
//                             ),
//                         ],
//                       );
//                     }),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );

//     // 将 OverlayEntry 插入到 Overlay 中
//     overlay.insert(overlayEntry);
//   }

//   void showRightLay(BuildContext context, OverlayState overlay) {
//     // 提前声明 overlayEntry 变量，使用 `late` 延迟初始化
//     late OverlayEntry overlayEntry;

//     // 创建 OverlayEntry 并赋值给 overlayEntry
//     overlayEntry = OverlayEntry(
//       builder: (context) => Positioned(
//         top: ScreenAdapter.height(124),
//         // left: 0,
//         right: ScreenAdapter.width(16),
//         child: Center(
//           child: Material(
//             // 添加 Material 以支持 InkWell 和 Text 等 Material 组件
//             // color: Colors.transparent,
//             child: Obx(
//               () => IntrinsicWidth(
//                 child: Container(
//                   padding: EdgeInsets.symmetric(
//                     horizontal: ScreenAdapter.width(8),
//                     vertical: ScreenAdapter.height(8),
//                   ),
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     borderRadius: BorderRadius.circular(ScreenAdapter.width(4)),
//                     boxShadow: [
//                       BoxShadow(
//                         offset: Offset(0, ScreenAdapter.height(2)),
//                         color: Colors.black12,
//                         blurRadius: ScreenAdapter.width(4),
//                         spreadRadius: 0,
//                       ),
//                     ],
//                   ),
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min, // 确保宽度根据子组件大小调整
//                     crossAxisAlignment: CrossAxisAlignment.center, // 保证文本左对齐
//                     children:
//                         List.generate(controller.sheetList.length, (index) {
//                       String value = controller.sheetList[index];
//                       return Column(
//                         children: [
//                           InkWell(
//                             onTap: () {
//                               print("点击了");
//                               overlayEntry.remove(); // 点击后移除 Overlay
//                               widget.isRightDropdownOpen.value = false;
//                               controller.dropdownValue.value = value;
//                             },
//                             child: Container(
//                               // color: Colors.red,
//                               width: double.infinity, // 占满整个父容器宽度
//                               margin: EdgeInsets.only(
//                                 top: index == 0 ? 0 : ScreenAdapter.height(6),
//                                 bottom: index == controller.sheetList.length - 1
//                                     ? 0
//                                     : ScreenAdapter.height(6),
//                               ),
//                               child: Center(
//                                 child:Row(
//                                         mainAxisAlignment:
//                                             MainAxisAlignment.start,
//                                         children: [
//                                           SizedBox(
//                                             width: ScreenAdapter.width(20),
//                                             height: ScreenAdapter.height(20),
//                                             child: Image.asset(
//                                               Assets.images.pen.path,
//                                             ),
//                                           ),
//                                           SizedBox(
//                                             width: ScreenAdapter.width(4),
//                                           ),
//                                           Text(
//                                             value,
//                                             style: TextStyle(
//                                               fontSize:
//                                                   ScreenAdapter.fontSize(16),
//                                               height: 22.4 / 16,
//                                               color: AppColors.Color666,
//                                             ),
//                                           )
//                                         ],
//                                       ),
//                               ),
//                             ),
//                           ),
//                           // 添加分割线，但排除最后一个子项
//                           if (index < controller.sheetList.length - 1)
//                             Divider(
//                               height: ScreenAdapter.height(1),
//                               color: AppColors.homeBgColor, // 设置分割线颜色
//                             ),
//                         ],
//                       );
//                     }),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );

//     // 将 OverlayEntry 插入到 Overlay 中
//     overlay.insert(overlayEntry);
//   }

// }
