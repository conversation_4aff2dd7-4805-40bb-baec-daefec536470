// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-12-11 10:55:41
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-12-11 15:56:46
//  * @FilePath: /rpmappmaster/lib/app/modules/test_component/views/test.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
// import 'package:aiCare/app/core/widget/fl_chart/custom_dot_painter.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:flutter/material.dart';

// class CustomBarChart extends StatelessWidget {
// // 示例数据
//   List<HeartRateData> heartRateData = [
//     HeartRateData(date: DateTime(2024, 12, 10, 0, 0), data: 53),
//     HeartRateData(date: DateTime(2024, 12, 10, 6, 0), data: 75),
//     HeartRateData(date: DateTime(2024, 12, 10, 12, 0), data: 100),
//     HeartRateData(date: DateTime(2024, 12, 10, 18, 0), data: 125),
//   ];

//   @override
//   Widget build(BuildContext context) {
//     return Column(children: [
//       AspectRatio(
//         aspectRatio: 1.5,
//         child: ScatterChart(
//           ScatterChartData(
//             scatterSpots: heartRateData
//                 .map((e) => ScatterSpot(
//                       e.date!.hour.toDouble(), // 使用小时作为X轴
//                       e.data!.toDouble(), // 心率值作为Y轴
//                       dotPainter: CustomDotPainter(
//                           color: Colors.red,
//                           width: ScreenAdapter.width(10),
//                           fromY: 10,
//                           toY: 40),
//                     ))
//                 .toList(),
//             minX: 0,
//             maxX: 24,
//             minY: 50, // Y轴最小值（心率下限）
//             maxY: 150, // Y轴最大值（心率上限）
//             gridData: FlGridData(show: true), // 显示网格线
//             borderData: FlBorderData(
//               show: true,
//               border: Border.all(color: Colors.black),
//             ),
//             titlesData: FlTitlesData(),
//           ),
//         ),
//       )
//     ]);
//   }
// }
