import 'dart:io';
import 'dart:math';

import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/utils/permission_util.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/core/widget/custom_line.dart';
import 'package:aiCare/app/core/widget/sample5.dart';
import 'package:aiCare/app/modules/test_component/views/custom_canvos.dart';
import 'package:aiCare/app/modules/test_component/views/custom_navbar.dart';
import 'package:aiCare/app/modules/test_component/views/test_border.dart';
import 'package:aiCare/app/modules/test_component/views/test_fl.dart';
import 'package:aiCare/app/modules/test_component/views/test_pie.dart';
import 'package:aiCare/app/modules/test_component/widgets/avatar_upload.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/app/services/toastHelper.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
// import 'package:wave/config.dart';
// import 'package:wave/wave.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/test_component_controller.dart';
import 'test.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';

class TestComponentView extends BaseView<TestComponentController> {
  TestComponentView({
    super.key,
  }) : super(bgColor: Colors.white);

  static const _backgroundColor = Color(0xFFF15BB5);

  static const _colors = [
    Color(0xFFFEE440),
    Color(0xFF00BBF9),
  ];

  static const _durations = [
    5000,
    4000,
  ];

  static const _heightPercentages = [
    0.65,
    0.66,
  ];





  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(title: T.bluetoothFunction.tr);
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(ScreenAdapter.width(20)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 设备状态显示区域
            Container(
              padding: EdgeInsets.all(ScreenAdapter.width(15)),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(T.deviceStatus.tr, style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  SizedBox(height: 10),
                  // AIZO状态
                  Obx(() => Text("AIZO "+T.connectionStatus.tr+": ${controller.bluetoothController.aizoConnectStatus.toString().split('.').last}")),
                  Obx(() => Text("AIZO "+T.batteryStatus.tr+": ${controller.bluetoothController.aizoBatteryStatus.toString().split('.').last}")),
                  Obx(() => Text("AIZO "+T.batteryLevel.tr+": ${controller.bluetoothController.aizoBatteryLevel}%")),
                  Obx(() => Text("${T.currentMeasurementInterval.tr}: ${controller.currentMeasurementTime.value != null ? controller.currentMeasurementTime.value.toString() : '20'+T.wordsMinute.tr}")),
                  Obx(() => Text("${T.currentMeasurementStatus.tr}: ${controller.currentMeasurementStatus.value}")),
                  Obx(() => Text("${T.measurementResult.tr}: ${controller.currentMeasurementResult.value}")),
                  SizedBox(height: 10),
                  // AOJ温度计状态
                  Text(T.aojThermometer.tr, style: TextStyle(fontWeight: FontWeight.bold)),
                  Obx(() => Text("${T.connectionStatus.tr}: ${controller.bluetoothController.aojConnectStatus.toString().split('.').last}")),
                  Obx(() => Text("${T.batteryStatus.tr}: ${controller.bluetoothController.aojBatteryStatus.toString().split('.').last}")),
                  Obx(() => Text("${T.batteryLevel.tr}: ${controller.bluetoothController.aojBatteryLevel}%")),
                  Obx(() => Text("${T.currentTemperature.tr}: ${controller.bluetoothController.aojTemperature.toStringAsFixed(1)}°C")),
                  Obx(() => Text("${T.lastMeasurementTime.tr}: ${controller.bluetoothController.aojLastMeasurementTime}")),
                ],
              ),
            ),
            SizedBox(height: 20),

            // AOJ温度计调试区域
            // _buildSectionTitle(T.aojThermometer.tr),
            _buildSectionTitle("aoj devices"),
            _buildButton(T.scanAndClassifyDevices.tr, () => controller.scanAndClassifyDevices()),
            Obx(() => controller.aojDevicesList.isEmpty
                ? Text(T.noDevicesFound.tr)
                : Column(
                    children: controller.aojDevicesList.map((device) {
                      return Card(
                        margin: EdgeInsets.symmetric(vertical: 4),
                        child: Padding(
                          padding: EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 设备信息区域
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          device.advName.isNotEmpty ? device.advName : T.unknownDevice.tr,
                                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                        ),
                                        SizedBox(height: 4),
                                        Text(
                                          '${T.macAddress.tr}: ${device.remoteId.str}',
                                          style: TextStyle(color: Colors.grey[600], fontSize: 12),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 12),
                              // 操作按钮区域
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: device.advName.contains('30B')
                                      ? [
                                          ElevatedButton(
                                            onPressed: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? null : () => controller.connectAojDevice(device),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? Colors.grey : Colors.blue,
                                            ),
                                            child: Text('连接'),
                                          ),
                                          SizedBox(width: 8),
                                          // ElevatedButton(
                                          //   onPressed: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? () => controller.aojDisconnect() : null,
                                          //   style: ElevatedButton.styleFrom(
                                          //     backgroundColor: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? Colors.blue : Colors.grey,
                                          //   ),
                                          //   child: Text('断开'),
                                          // ),
                                          SizedBox(width: 8),
                                          ElevatedButton(
                                            onPressed: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? () => controller.measureAojBpm(device) : null,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? Colors.blue : Colors.grey,
                                            ),
                                            child: Text('测量'),
                                          ),
                                        ]
                                      : [
                                          ElevatedButton(
                                            onPressed: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? null : () => controller.connectAojDevice(device),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? Colors.grey : Colors.blue,
                                            ),
                                            child: Text(T.connectAojThermometer.tr),
                                          ),
                                          SizedBox(width: 8),
                                          ElevatedButton(
                                            onPressed: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? () => controller.disconnectAojThermometerDevice() : null,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? Colors.blue : Colors.grey,
                                            ),
                                            child: Text(T.disconnectAojThermometer.tr),
                                          ),
                                          SizedBox(width: 8),
                                          // ElevatedButton(
                                          //   onPressed: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? () => controller.measureAojThermometerTemperature() : null,
                                          //   style: ElevatedButton.styleFrom(
                                          //     backgroundColor: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? Colors.blue : Colors.grey,
                                          //   ),
                                          //   child: Text(T.measureTemperature.tr),
                                          // ),
                                          SizedBox(width: 8),
                                          ElevatedButton(
                                            onPressed: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? () => controller.getAojThermometerBatteryLevel() : null,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? Colors.blue : Colors.grey,
                                            ),
                                            child: Text(T.getBatteryLevel.tr),
                                          ),
                                          SizedBox(width: 8),
                                          // ElevatedButton(
                                          //   onPressed: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? () => controller.getAojThermometerLastTemperature() : null,
                                          //   style: ElevatedButton.styleFrom(
                                          //     backgroundColor: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? Colors.blue : Colors.grey,
                                          //   ),
                                          //   child: Text("获取最后温度"),
                                          // ),
                                          SizedBox(width: 8),
                                          // ElevatedButton(
                                          //   onPressed: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? () => controller.setAojThermometerTime() : null,
                                          //   style: ElevatedButton.styleFrom(
                                          //     backgroundColor: controller.selectedAojDevice.value?.remoteId.str == device.remoteId.str ? Colors.blue : Colors.grey,
                                          //   ),
                                          //   child: Text("设置时间"),
                                          // ),
                                        ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  )
            ),
            SizedBox(height: 20),

            // 所有AOJ设备调试区域
            _buildSectionTitle("所有AOJ设备"),
            Obx(() => controller.aojOtherDevicesList.isEmpty
                ? Text("未发现其他AOJ设备")
                : Column(
                    children: controller.aojOtherDevicesList.map((device) => Card(
                      margin: EdgeInsets.symmetric(vertical: 4),
                      child: Padding(
                        padding: EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 设备信息区域
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        device.advName.isNotEmpty ? device.advName : T.unknownDevice.tr,
                                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                      ),
                                      SizedBox(height: 4),
                                      Text(
                                        '${T.macAddress.tr}: ${device.remoteId.str}',
                                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 12),
                            // 操作按钮区域
                            SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  ElevatedButton(
                                    onPressed: controller.bluetoothController.isAojConnected
                                        ? null
                                        : () => controller.connectAojDevice(device),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: controller.bluetoothController.isAojConnected ? Colors.grey : Colors.blue,
                                    ),
                                    child: Text("连接"),
                                  ),
                                  SizedBox(width: 8),
                                  ElevatedButton(
                                    onPressed: controller.bluetoothController.isAojConnected
                                        ? () => controller.disconnectAojThermometerDevice()
                                        : null,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: controller.bluetoothController.isAojConnected ? Colors.blue : Colors.grey,
                                    ),
                                    child: Text("断开"),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )).toList(),
                  )
            ),
            SizedBox(height: 20),

            // 连接控制区域
            _buildSectionTitle(T.connectionControl.tr),
            _buildButton(
              T.connectDevice.tr,
              () => controller.blueInit(context),
            ),
            _buildButton(
              T.disconnectDevice.tr,
              () => controller.aizoUnbind(context),
            ),
            SizedBox(height: 20),

            // 测量间隔控制区域
            _buildSectionTitle(T.measurementInterval.tr),
            _buildButton(
              T.getCurrentInterval.tr,
              () => controller.aizoGetHeartRateInterval(),
            ),
            _buildButton(
              T.set10MinutesInterval.tr,
              () => controller.aizoSetHeartRateInterval(0),
            ),
            _buildButton(
              T.set20MinutesInterval.tr,
              () => controller.aizoSetHeartRateInterval(1),
            ),
            _buildButton(
              T.set30MinutesInterval.tr,
              () => controller.aizoSetHeartRateInterval(2),
            ),
            SizedBox(height: 20),

            // 测量控制区域
            _buildSectionTitle(T.measurementControl.tr),
            _buildButton(
              T.measureHeartRate.tr,
              () => controller.aizoInstantMeasurement(1, 1),
            ),
            _buildButton(
              T.measureBloodOxygen.tr,
              () => controller.aizoInstantMeasurement(2, 1),
            ),
            _buildButton(
              T.measureTemperature.tr,
              () => controller.aizoInstantMeasurement(6, 1),
            ),
            SizedBox(height: 20),

            // 数据获取区域
            _buildSectionTitle(T.dataRetrieval.tr),
            _buildButton(
              T.getHealthData.tr,
              () => controller.aizoGetHealthData(DateTime.now()),
            ),
            _buildButton(
              T.getSleepData.tr,
              () => controller.aizoGetSleepData(DateTime.now()),
            ),
            _buildButton(
              T.getHardwareInfo.tr,
              () => controller.aizoGetHardwareData(),
            ),
            // 新增：AIZO数据展示
            Obx(() => controller.aizoHealthDataList.isEmpty
                ? Text(T.commonNoData.tr)
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: controller.aizoHealthDataList.map((data) => Text(data.toString())).toList(),
                  )),
            Obx(() => controller.aizoSleepData.value == null
                ? SizedBox()
                : Text("${T.getSleepData.tr}: ${controller.aizoSleepData.value.toString()}")),
            Obx(() => controller.aizoHardwareInfo.value.isEmpty
                ? SizedBox()
                : Text("${T.getHardwareInfo.tr}: ${controller.aizoHardwareInfo.value}")),
            SizedBox(height: 20),

            // 权限控制区域
            _buildSectionTitle(T.permissions.tr),
            _buildButton(
              T.requestBluetoothPermission.tr,
              () => PermissionUtil.requestBluetooth(),
            ),
            _buildButton(
              T.requestLocationPermission.tr,
              () => PermissionUtil.requestLocation(),
            ),
            _buildButton(
              T.requestNotificationPermission.tr,
              () => PermissionUtil.requestNotification(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildButton(String text, VoidCallback onTap) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: 10),
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 12),
          backgroundColor: Colors.black87,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
