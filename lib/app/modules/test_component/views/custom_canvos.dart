// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-30 12:18:12
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-10-09 14:04:39
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/test_component/views/custom_canvos.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:math';

// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/widgets.dart';

// class CustomCanvos extends CustomPainter {
  
//   late double radius;
//   late double centerX;
//   late double centerY;
//   late Canvas canvas;
//   late Size size;

//   @override
//   void paint(Canvas canvas, Size size) {
//     this.canvas = canvas;
//     this.size = size;
//     // 确定圆心坐标
//     radius = size.width / 2;
//     centerX = size.width / 2;
//     // 向上调整圆心，避免圆形端点超出
//     centerY = size.height - ScreenAdapter.width(16) / 2;

//     // drawOutBdCircle();
//     // drawInnerBdCircle();
//     drawInnerCircle();
//     // drawScaleLine();
//     // drawPointer();

//     //绘制中心圆
//     // canvas.drawArc(rect, startAngle, sweepAngle, useCenter, paint)
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) {
//     return true; // 每次传入的新 rotation 值会触发重绘
//   }

//   drawOutBdCircle() {
//     Paint backgroundPaint = Paint()
//       ..color = Color(0xffF6F6F6)
//       // ..color = Colors.red
//       ..style = PaintingStyle.stroke // 使用 stroke 模式来绘制弧线
//       // ..strokeWidth = ScreenAdapter.width(16) // 设置线宽
//       ..strokeCap = StrokeCap.round; // 设置圆形端点

//     backgroundPaint.strokeWidth = ScreenAdapter.width(1);

//     canvas.drawArc(
//       Rect.fromCircle(center: Offset(centerX, centerY), radius: radius),
//       pi,
//       pi,
//       false,
//       backgroundPaint,
//     );
//   }

//   drawInnerBdCircle() {
//     Paint backgroundPaint = Paint()
//       ..color = Color(0xffF6F6F6)
//       // ..color = Colors.red
//       ..style = PaintingStyle.stroke // 使用 stroke 模式来绘制弧线
//       ..strokeWidth = ScreenAdapter.width(16) // 设置线宽
//       ..strokeCap = StrokeCap.round; // 设置圆形端点

//     canvas.drawArc(
//       Rect.fromCircle(
//           center: Offset(centerX, centerY),
//           radius:
//               radius - ScreenAdapter.width(16) / 2 - ScreenAdapter.width(5.6)),
//       pi, // Start angle
//       pi, // Sweep angle
//       // true, // Use center
//       false,
//       backgroundPaint,
//     );
//   }

//   drawInnerCircle() {
//     // 创建线性渐变
//     final gradient = LinearGradient(
//       colors: [
//         Color(0xff3C63FF),
//         Color(0xff3C63FF),
//         Color(0xFF92FF5B),
//         Color(0xFFFFD966),
//         Color(0xFFFD9C68),
//         Color(0xFFFB69699),
//       ], // 从蓝色到橙色的渐变
//     );

//     // 定义渐变的 Shader
//     final Rect arcRect =
//         Rect.fromCircle(center: Offset(centerX, centerY), radius: radius);

//     Paint testPaint = Paint()
//       // ..color = Color(0xff28AFF3)
//       ..shader = gradient.createShader(arcRect) // 使用渐变 shader
//       ..style = PaintingStyle.stroke // 使用 stroke 模式来绘制弧线
//       ..strokeWidth = ScreenAdapter.width(16); // 设置圆形端点

//     // backgroundPaint.color = Color(0xff28AFF3);
//     canvas.drawArc(
//       Rect.fromCircle(
//           center: Offset(centerX, centerY),
//           radius:
//               radius - ScreenAdapter.width(16) / 2 - ScreenAdapter.width(5.6)),
//       pi+pi/12, // Start angle
//       pi-pi/12-pi/12, // Sweep angle
//       // true, // Use center
//       false,
//       testPaint,
//     );

//     // 定义弧线的终点角度
//     double endAngle = pi + pi / 3; // pi 为起始角度，pi / 3 为 sweep angle，表示总弧度

//     // 使用与绘制弧线相同的半径来计算弧线末端的位置
//     double arcRadius =
//         radius - ScreenAdapter.width(16) / 2 - ScreenAdapter.width(5.6);

//     // 计算弧线末端的位置
//     double endX = centerX + arcRadius * cos(endAngle);
//     double endY = centerY + arcRadius * sin(endAngle);

//     // 绘制阴影效果的小球
//     Paint ballPaintWithShadow = Paint()
//       ..color = Colors.black.withOpacity(0.25) // 阴影颜色为黑色，透明度 25%
//       ..style = PaintingStyle.fill
//       ..maskFilter = MaskFilter.blur(BlurStyle.normal, 4); // 模糊半径为 4

//     // 阴影偏移的效果，y 方向偏移 4
//     canvas.drawCircle(Offset(endX, endY + ScreenAdapter.width(4)),
//         ScreenAdapter.width(8.315), ballPaintWithShadow); // 先绘制阴影

//     // 绘制白色的小球
//     Paint ballPaint = Paint()
//       ..color = Colors.white
//       ..style = PaintingStyle.fill;

//     canvas.drawCircle(
//         Offset(endX, endY), ScreenAdapter.width(8.315), ballPaint); // 再绘制小球
//   }

//   drawScaleLine() {
//     //绘制刻度线
//     Paint linePaint = Paint()
//       ..color = Color(0xFF575757)
//       ..strokeWidth = 1; // 默认线条宽度

//     double angleIncrement = pi / 30; // 每个刻度之间的角度增量（31 个区域）

//     for (int i = 0; i <= 30; i++) {
//       double angle = pi + angleIncrement * i; // 计算每个刻度的角度
//       if (i == 0) {
//         angle = angle + 0.02222222; // 第一个刻度微调
//       }
//       // if (i == 29) {
//       //   angle = angle - 0.02222222; // 最后一个刻度微调
//       // }

//       // 根据 i 判断是否加厚和加长刻度
//       double lineLength = ScreenAdapter.width(6); // 默认长度
//       double strokeWidth = ScreenAdapter.width(1); // 默认线宽

//       if (i == 0 || i % 5 == 0) {
//         // 第一个刻度和每隔 5 个刻度
//         lineLength = ScreenAdapter.width(11); // 加长线条
//         strokeWidth = ScreenAdapter.width(1.35); // 加粗线条
//       }

//       // 更新画笔的线宽
//       linePaint.strokeWidth = strokeWidth;

//       // 计算刻度线起始和结束的坐标

//       double startX = centerX +
//           (radius - lineLength - ScreenAdapter.width(28.27)) * cos(angle);
//       double startY = centerY +
//           (radius - lineLength - ScreenAdapter.width(28.27)) * sin(angle);
//       double endX = startX + lineLength * cos(angle); // 使用动态长度
//       double endY = startY + lineLength * sin(angle);

//       // 画线
//       canvas.drawLine(Offset(startX, startY), Offset(endX, endY), linePaint);
//     }
//   }

//   drawPointer() {
//     final Paint paint = Paint()
//       ..color = Color(0xFF575757) // 指针颜色
//       ..style = PaintingStyle.fill; // 填充模式

//     double arcRadius = radius - ScreenAdapter.width(65.24);
//     // 定义弧线的终点角度
//     double endAngle = pi + pi / 3; // pi 为起始角度，pi / 3 为 sweep angle，表示总弧度

//     double pointerLength = ScreenAdapter.width(18.66); // 指针长度
//     double pointerWidth = ScreenAdapter.width(3.96); // 指针宽度（头部）
//     // 定义y轴需要提升的位移量
//     double yOffset = ScreenAdapter.width(6.16);

//     // 计算弧线末端的位置
//     double pointerTipX = centerX + arcRadius * cos(endAngle);
//     double pointerTipY = centerY + arcRadius * sin(endAngle) - yOffset;
//     // 计算指针底部（尾部）两侧的点，形成三角形
//     double baseX1 = centerX + pointerWidth / 2 * cos(endAngle + pi / 2);
//     double baseY1 =
//         centerY + pointerWidth / 2 * sin(endAngle + pi / 2) - yOffset;
//     double baseX2 = centerX + pointerWidth / 2 * cos(endAngle - pi / 2);
//     double baseY2 =
//         centerY + pointerWidth / 2 * sin(endAngle - pi / 2) - yOffset;

//     // 绘制指针的路径
//     Path pointerPath = Path();
//     pointerPath.moveTo(pointerTipX, pointerTipY); // 尖端
//     pointerPath.lineTo(baseX1, baseY1); // 左侧底部
//     pointerPath.lineTo(baseX2, baseY2); // 右侧底部
//     pointerPath.close(); // 封闭路径

//     // // 绘制一头尖、一头圆的指针
//     // Path pointerPath = Path();

//     // // 尖端位置（指针的顶点）
//     // pointerPath.moveTo(pointerX, pointerY - pointerLength);

//     // // 左侧边缘，指针逐渐变宽
//     // pointerPath.lineTo(centerX - pointerWidth / 2, centerY);

//     // // 右侧边缘
//     // pointerPath.lineTo(centerX + pointerWidth / 2, centerY);

//     // // 关闭路径形成一个尖头
//     // pointerPath.close();

//     // 绘制指针路径
//     canvas.drawPath(pointerPath, paint);

//     // 绘制指针尾部的圆形
//     canvas.drawCircle(
//       Offset(centerX, centerY - yOffset), // 圆心位置
//       pointerWidth / 2, // 圆形的半径
//       paint,
//     );
//   }
// }
