// import 'dart:math';

// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:flutter/material.dart';

// // ignore: must_be_immutable
// class LineChartSample9 extends StatelessWidget {
//   LineChartSample9({super.key});

//   // final spots = List.generate(101, (i) => (i - 50) / 10)
//   //     .map((x) => FlSpot(x, cos(x)))
//   //     .toList();
//   // 第一条线的点数据
//   final spots1 = [168, 174, 179, 172, 176, 162, 170]
//       .asMap()
//       .entries
//       .map(
//           (entry) => FlSpot((entry.key + 1).toDouble(), entry.value.toDouble()))
//       .toList();

//   // 第二条线的点数据
//   final spots2 = [78, 85, 90, 83, 89, 75, 80]
//       .asMap()
//       .entries
//       .map(
//           (entry) => FlSpot((entry.key + 1).toDouble(), entry.value.toDouble()))
//           .toList();



//   @override
//   Widget build(BuildContext context) {
//     return Center(
//         child: Container(
//       width: ScreenAdapter.width(324),
//       height: ScreenAdapter.height(189),
//       child: LineChart(
//         LineChartData(
//           backgroundColor: AppColors.pressureRemindBg,  // 设置背景颜色
//           //触摸交互行为
//           // lineTouchData: LineTouchData(

//           //   touchTooltipData: LineTouchTooltipData(
//           //     maxContentWidth: 100,
//           //     getTooltipColor: (touchedSpot) => Colors.black,
//           //     getTooltipItems: (touchedSpots) {
//           //       return touchedSpots.map((LineBarSpot touchedSpot) {
//           //         final textStyle = TextStyle(
//           //           color: touchedSpot.bar.gradient?.colors[0] ??
//           //               touchedSpot.bar.color,
//           //           fontWeight: FontWeight.bold,
//           //           fontSize: 14,
//           //         );
//           //         return LineTooltipItem(
//           //           '${touchedSpot.x}, ${touchedSpot.y.toStringAsFixed(2)}',
//           //           textStyle,
//           //         );
//           //       }).toList();
//           //     },
//           //   ),
//           //   handleBuiltInTouches: true,
//           //   getTouchLineStart: (data, index) => 0,
//           // ),
//           //图表中显示的线条数据
//           lineBarsData: [

//             LineChartBarData(

//               spots: spots1, //图表上点的列表
//               // spots: [
//               //   FlSpot(0, 1),
//               //   FlSpot(1, -1),
//               //   FlSpot(2, 1),
//               //   FlSpot(3, -1),
//               // ],
//               show: true, //是否显示此数据线
//               color: AppColors.pressureDia, //数据线的颜色。如果未设置，且 gradient 为空，则默认为Colors.cyan。
//               //gradient://数据线的渐变颜色
//               barWidth: ScreenAdapter.width(2), //数据线的宽度
//               isCurved: true, //数据线是否为曲线
//               // curveSmoothness:0.9, //曲线的平滑度
//               // preventCurveOverShooting:true,//是否防止曲线的过度超出
//               // preventCurveOvershootingThreshold//防止曲线超出的阈值
//               isStrokeCapRound: true, //是否将线条的末端设置为圆形
//               // isStrokeJoinRound: true,//是否将线条的连接处设置为圆形
//               //数据线以下的区域填充
//               // belowBarData: BarAreaData(
//               //   show: true,
//               //   // color: AppColors.errorTextColor,
//               //   gradient: LinearGradient(
//               //     begin: Alignment.topCenter, // 渐变从顶部开始
//               //     end: Alignment.bottomCenter, // 渐变在底部结束
//               //     colors: [
//               //       Color(0xFFFF7575)
//               //           .withOpacity(0.1064), // 起始颜色，透明度为 10.64%
//               //       Color(0xFFFE473B)
//               //           .withOpacity(1), // 结束颜色，透明度为 100.31%
//               //     ],
//               //   ),
//               // ),
//               //数据线以上的内容填充
//               //                     aboveBarData: BarAreaData(
//               //   show: true,
//               //   // color: AppColors.errorTextColor,
//               //   gradient: LinearGradient(
//               //     begin: Alignment.topCenter, // 渐变从顶部开始
//               //     end: Alignment.bottomCenter, // 渐变在底部结束
//               //     colors: [
//               //       Color(0xFFFF7575)
//               //           .withOpacity(0.1064), // 起始颜色，透明度为 10.64%
//               //       Color(0xFFFE473B)
//               //           .withOpacity(1), // 结束颜色，透明度为 100.31%
//               //     ],
//               //   ),
//               // ),
//               //数据点的样式
//               dotData: FlDotData(
//                   show: true,
//                   getDotPainter: (spot, perent, barData, index) {
//                     return FlDotCirclePainter(
//                       radius: ScreenAdapter.width(3), //点的半径
//                       color: AppColors.pressureDia, //点的颜色
//                       strokeWidth: ScreenAdapter.width(1), //点的边框宽度
//                       strokeColor: Colors.white, //点边框的颜色
//                     );
//                   }),
//               // showingIndicators: [1, 2, 3], // 高亮显示索引为1，2,3的数据点，没看出来
//               // dashArray: [5,2]//设置虚线样式，看不出来
//               // shadow: //数据线的阴影模式
//               // isStepLineChart: true,//是否为阶梯线图
//               // lineChartStepData: //阶梯线图的数据
//             ),
            
//             LineChartBarData(

//               spots: spots2, //图表上点的列表
//               // spots: [
//               //   FlSpot(0, 1),
//               //   FlSpot(1, -1),
//               //   FlSpot(2, 1),
//               //   FlSpot(3, -1),
//               // ],
//               show: true, //是否显示此数据线
//               color: AppColors
//                   .pressureSys, //数据线的颜色。如果未设置，且 gradient 为空，则默认为Colors.cyan。
//               //gradient://数据线的渐变颜色
//               barWidth: ScreenAdapter.width(2), //数据线的宽度
//               isCurved: true, //数据线是否为曲线
//               // curveSmoothness:0.9, //曲线的平滑度
//               // preventCurveOverShooting:true,//是否防止曲线的过度超出
//               // preventCurveOvershootingThreshold//防止曲线超出的阈值
//               isStrokeCapRound: true, //是否将线条的末端设置为圆形
//               // isStrokeJoinRound: true,//是否将线条的连接处设置为圆形
//               //数据线以下的区域填充
//               // belowBarData: BarAreaData(
//               //   show: true,
//               //   // color: AppColors.errorTextColor,
//               //   gradient: LinearGradient(
//               //     begin: Alignment.topCenter, // 渐变从顶部开始
//               //     end: Alignment.bottomCenter, // 渐变在底部结束
//               //     colors: [
//               //       Color(0xFFFF7575)
//               //           .withOpacity(0.1064), // 起始颜色，透明度为 10.64%
//               //       Color(0xFFFE473B)
//               //           .withOpacity(1), // 结束颜色，透明度为 100.31%
//               //     ],
//               //   ),
//               // ),
//               //数据线以上的内容填充
//               //                     aboveBarData: BarAreaData(
//               //   show: true,
//               //   // color: AppColors.errorTextColor,
//               //   gradient: LinearGradient(
//               //     begin: Alignment.topCenter, // 渐变从顶部开始
//               //     end: Alignment.bottomCenter, // 渐变在底部结束
//               //     colors: [
//               //       Color(0xFFFF7575)
//               //           .withOpacity(0.1064), // 起始颜色，透明度为 10.64%
//               //       Color(0xFFFE473B)
//               //           .withOpacity(1), // 结束颜色，透明度为 100.31%
//               //     ],
//               //   ),
//               // ),
//               //数据点的样式
//               dotData: FlDotData(
//                   show: true,
//                   getDotPainter: (spot, perent, barData, index) {
//                     return FlDotCirclePainter(
//                       radius: ScreenAdapter.width(3), //点的半径
//                       color: AppColors.pressureSys, //点的颜色
//                       strokeWidth: ScreenAdapter.width(1), //点的边框宽度
//                       strokeColor: Colors.white, //点边框的颜色
//                     );
//                   }),
//               // showingIndicators: [1, 2, 3], // 高亮显示索引为1，2,3的数据点，没看出来
//               // dashArray: [5,2]//设置虚线样式，看不出来
//               // shadow: //数据线的阴影模式
//               // isStepLineChart: true,//是否为阶梯线图
//               // lineChartStepData: //阶梯线图的数据
//             ),
          
//           ],
//           //用于在不同线条之间绘制区域填充
//           // betweenBarsData
//           //Y轴的最小值
//           minY: 40,
//           //Y轴的最大值
//           maxY: 220,
//           minX: 1,
//           maxX: 7, // 根据需要设置 X 轴的范围
//           //配置图表的标题数据，包括轴标签、标题和样式
//           titlesData: FlTitlesData(
//             leftTitles: AxisTitles(
//               sideTitles: SideTitles(
//                 interval: 10,
//                 showTitles: true,
//                 getTitlesWidget: (value, meta) => leftTitleWidgets(value, meta),
//                 reservedSize: ScreenAdapter.width(40),
//               ),
//               drawBelowEverything: true,
//             ),
//             rightTitles: const AxisTitles(
//               sideTitles: SideTitles(showTitles: false),
//             ),
//             bottomTitles: AxisTitles(
//               sideTitles: SideTitles(
//                 showTitles: true,
//                 getTitlesWidget: (value, meta) =>
//                     bottomTitleWidgets(value, meta),
//                 reservedSize: 36,
//                 interval: 1,
//               ),
//               drawBelowEverything: true,
//             ),
//             topTitles: const AxisTitles(
//               sideTitles: SideTitles(showTitles: false),
//             ),
//           ),

//           gridData: FlGridData(
//             show: true,
//             drawHorizontalLine: true,
//             drawVerticalLine: false,
//             horizontalInterval: 1.5,
//             verticalInterval: 5,
//             checkToShowHorizontalLine: (value) {
//               // 只显示在 Y 轴等于 40, 70, 100, 130, 160, 190 的位置
//               return [40, 70, 100, 130, 160, 190].contains(value.toInt());
//             },
//             getDrawingHorizontalLine: (value) {
//               if (value.toInt() == 40) {
//                 return FlLine(
//                   color: AppColors.Coloreee, // 最下面的实线
//                   strokeWidth: ScreenAdapter.width(2),
//                 );
//               } else {
//                 return FlLine(
//                   color: AppColors.Coloreee, // 其他虚线
//                   strokeWidth: ScreenAdapter.width(2),
//                   dashArray: [3, 3], // 设置虚线样式
//                 );
//               }
//             },
//             // getDrawingHorizontalLine: (_) => FlLine(
//             //   color: Colors.blue.withOpacity(1),
//             //   dashArray: [8, 2],
//             //   strokeWidth: 0.8,
//             // ),
//             // getDrawingVerticalLine: (_) => FlLine(
//             //   color: Colors.yellow.withOpacity(1),
//             //   dashArray: [8, 2],
//             //   strokeWidth: 0.8,
//             // ),
//             // checkToShowVerticalLine: (value) {
//             //   return value.toInt() == 0;
//             // },
//           ),

//           borderData: FlBorderData(show: false),
//           //配置额外的线条数据，通常用于绘制基线、参考线等。
//           // extraLinesData: ExtraLinesData(
//           //   horizontalLines: [
//           //     HorizontalLine(
//           //       y: 0, // Y 轴线在 y=0 的位置
//           //       color: Colors.red, // 设置线条颜色
//           //       strokeWidth: 2, // 设置线条宽度
//           //       dashArray: [10, 5], // 设置虚线样式
//           //     ),
//           //   ],
//           // ),
//           //置顶显示工具指示指示器的位置
//           // showingTooltipIndicators:
//         ),
//         // duration: Duration(milliseconds: 10000),
//       ),
//     ));
//   }
// }
