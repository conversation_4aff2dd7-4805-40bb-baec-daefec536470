// import 'dart:async';
// import 'dart:math' as math;

// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:fl_chart/fl_chart.dart';

// import 'package:flutter/material.dart';

// class LineChartSample10 extends StatefulWidget {
//   final double amplitude; // 中心百分比数据
//   final double parentWidth; // 外层宽高
//   final double childWidth; // 内层宽高
//   final TextStyle dataStyle; // 中心百分比样式
//   final double parentBdWidth; // 外层边框宽度

//   LineChartSample10({
//     super.key,
//     required this.amplitude,
//     required this.parentWidth,
//     required this.childWidth,
//     required this.dataStyle,
//     required this.parentBdWidth,
//   });

//   final Color bdColor = AppColors.lightBlue;

//   @override
//   State<LineChartSample10> createState() => _LineChartSample10State();
// }

// class _LineChartSample10State extends State<LineChartSample10> with SingleTickerProviderStateMixin {
//   final limitCount = 100;
//   final sinPoints = <FlSpot>[];
//   double xValue = 0;
//   double step = 0.25;
//   late Timer timer;

//   List<List<Color>> waveList = [
//     [Color(0xFF30FF1E), Color(0xFF1ED90D)],
//     [Color(0xFFFFB775), Color(0xFFFEBF1E)],
//     [Color(0xFFB174FF), Color(0xFFE5D0FF)],
//     [Color(0xFFFF7575), Color(0xFFFE473B)],
//   ];

//   @override
//   void initState() {
//     super.initState();
//     for (int i = 0; i < limitCount; i++) {
//       sinPoints.add(FlSpot(xValue, widget.amplitude + 0.05 * math.sin(xValue)));
//       xValue += step;
//     }
//     timer = Timer.periodic(const Duration(milliseconds: 40), (timer) {
//       while (sinPoints.length > limitCount) {
//         sinPoints.removeAt(0);
//       }
//       setState(() {
//         sinPoints.add(FlSpot(xValue, widget.amplitude + 0.05 * math.sin(xValue)));
//       });
//       xValue += step;
//     });
//   }

//   int switchIndex(double data) {
//     if (data >= 0.95) return 0;
//     else if (data >= 0.90) return 1;
//     else if (data >= 0.80) return 2;
//     else return 3;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Center(
//       child: Stack(
//         alignment: Alignment.center, // 中心对齐
//         children: [
//           Container(
//             width: ScreenAdapter.width(151),
//             height: ScreenAdapter.width(151),
//             decoration: const BoxDecoration(
//               color: Colors.transparent,
//               shape: BoxShape.circle,
//             ),
//             child: CustomPaint(
//               painter: GradientBorderPainter(
//                 paintColor: waveList[switchIndex(widget.amplitude)],
//                 width: widget.parentBdWidth,
//               ),
//               child: Center(
//                 child: Container(
//                   width: ScreenAdapter.width(137),
//                   height: ScreenAdapter.width(137),
//                   decoration: const BoxDecoration(
//                     shape: BoxShape.circle,
//                   ),
//                   child: ClipOval(
//                     child: LineChart(
//                       LineChartData(
//                         minY: -1,
//                         maxY: 1,
//                         minX: sinPoints.first.x + 0.2,
//                         maxX: sinPoints.last.x,
//                         lineTouchData: const LineTouchData(enabled: false),
//                         clipData: const FlClipData.all(),
//                         gridData: const FlGridData(show: true, drawVerticalLine: false),
//                         borderData: FlBorderData(show: false),
//                         lineBarsData: [
//                           sinLine(sinPoints),
//                         ],
//                         titlesData: const FlTitlesData(show: false),
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//           // 百分比文本显示
//           // Positioned(
//           //   top: ScreenAdapter.width(55),
//           //   left: ScreenAdapter.width(45),
//           //   child: 
//             Text(
//             '${(widget.amplitude * 100).toInt()}%', // 动态显示百分比
//             style: widget.dataStyle,
//           )
//           // )
//         ],
//       ),
//     );
//   }

//   LineChartBarData sinLine(List<FlSpot> points) {
//     return LineChartBarData(
//       spots: points,
//       dotData: const FlDotData(show: false),
//       color: Colors.transparent,
//       barWidth: ScreenAdapter.width(1),
//       isCurved: false,
//       isStrokeCapRound: true,
//       belowBarData: BarAreaData(
//         show: true,
//         gradient: LinearGradient(
//           begin: Alignment.topCenter,
//           end: Alignment.bottomCenter,
//           colors: waveList[switchIndex(widget.amplitude)],
//         ),
//       ),
//     );
//   }

//   @override
//   void dispose() {
//     timer.cancel();
//     super.dispose();
//   }
// }

// class GradientBorderPainter extends CustomPainter {
//   List<Color> paintColor;
//   double width;

//   GradientBorderPainter({required this.paintColor, required this.width});

//   @override
//   void paint(Canvas canvas, Size size) {
//     final Paint paint = Paint()
//       ..shader = LinearGradient(
//         begin: Alignment.topCenter,
//         end: Alignment.bottomCenter,
//         colors: paintColor,
//       ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = ScreenAdapter.width(width);

//     final double radius = size.width / 2;
//     canvas.drawCircle(Offset(radius, radius), radius - 2, paint);
//   }

//   @override
//   bool shouldRepaint(CustomPainter oldDelegate) {
//     return false;
//   }
// }