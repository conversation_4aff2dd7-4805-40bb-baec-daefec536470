// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-11-11 17:21:05
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-11 17:29:28
//  * @FilePath: /rpmappmaster/lib/app/modules/test_component/views/testview.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/modules/test_component/controllers/test_component_controller.dart';
// import 'package:aiCare/app/modules/test_component/widgets/test_chart.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/src/widgets/framework.dart';
// import 'package:flutter/src/widgets/preferred_size.dart';

// class TestComponentView extends BaseView<TestComponentController> {
//   TestComponentView({
//     super.key,
//   }) : super(
//           parentPaddings: [0, 0, 0, 0],
//           bgColor: AppColors.homeBgColor,
//         );

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return null;
//   }

//   @override
//   Widget body(BuildContext context) {
//     return SingleChildScrollView(
//       child: Column(
//         children: [
//           SizedBox(
//             width: ScreenAdapter.width(375),
//             height: ScreenAdapter.height(200),
//             child: BloodOxygenChart())
//           ],
//       ),
//     );
//   }
// }
