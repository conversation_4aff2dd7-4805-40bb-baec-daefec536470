/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-23 16:46:43
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-30 17:22:45
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/test_component/controllers/test_component_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import 'dart:io';

import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/bluetooth_util.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/data/model/aizo_headrt_rate.dart';
import 'package:aiCare/app/data/model/aizo_measure_result.dart';
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/data/model/aizo_sleep_data.dart';
import 'package:aiCare/app/data/model/aizo_userinfo.dart';
import 'package:aiCare/app/data/model/bluetooth_response.dart';
import 'package:aiCare/app/data/repository/bluetooth_repository.dart';
import 'package:aiCare/app/data/repository/bluetooth_repository_impl.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/controllers/blood_oxygen_controller.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/controllers/temperature_home_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:intl/intl.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/data/model/aizo_auto_finial_data.dart';

class TestComponentController extends BaseController {
  RxList<String> sheetList = [""].obs;
  late RxString dropdownValue = sheetList.first.obs;
  BluetoothRepositoryImpl bluetoothRepositoryImpl = BluetoothRepositoryImpl();
  RxList<AizoRing> devicesList = <AizoRing>[].obs;
  // 当前测量时间
 Rx<int?> currentMeasurementTime = Rx<int?>(null);
// 当前测量结果（可以是 String，也可以是 Map 或自定义类型）
 RxString currentMeasurementResult = ''.obs;

 // 当前测量状态（比如 idle, measuring, success, fail）
 RxString currentMeasurementStatus = 'idle'.obs;

  //TODO: Implement TestComponentController

  double leftMax = 220;
  double leftMin = 40;
  double leftInterval = 30;
  TextStyle leftStyle = normalF12H17C666;
  TextStyle bottomStyle = normalF12H17C666;
  Color bgColor = AppColors.pressureRemindBg;

  late AizoRing aizoDevices;
  BluetoothController bluetoothController = Get.find();

  RxInt textValue = 0.obs;

  // AOJ设备相关变量
  RxList<MyBluetoothDevice> aojDevicesList = <MyBluetoothDevice>[].obs;
  RxList<MyBluetoothDevice> aojOtherDevicesList = <MyBluetoothDevice>[].obs;


  Rx<MyBluetoothDevice?> selectedAojDevice = Rx<MyBluetoothDevice?>(null);
  MyBluetoothDevice? selectedAizoDevice;
  RxDouble aojThermometerTemperature = 0.0.obs;
  RxString aojThermometerLastMeasurementTime = ''.obs;

  // 新增：AIZO健康数据、睡眠数据、硬件信息
  RxList<AizoAutoFinialData> aizoHealthDataList = <AizoAutoFinialData>[].obs;
  Rx<AizoSleepData?> aizoSleepData = Rx<AizoSleepData?>(null);
  RxString aizoHardwareInfo = ''.obs;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    // bluetoothRepositoryImpl.aizoUnbind();
    // bluetoothRepositoryImpl.aizoDestory();
    super.onClose();
  }

  Locale getSystemLocale() {
    // 获取系统首选语言列表
    final systemLocales = WidgetsBinding.instance.window.locales;

    // 返回第一个语言（通常是系统首选语言）
    if (systemLocales.isNotEmpty) {
      return systemLocales.first;
    }

    // 如果没有获取到系统语言，返回默认语言（如英文）
    return const Locale('en');
  }

  blueInit(BuildContext context) async {
    await bluetoothRepositoryImpl.init();
    blueScanning(context);
  }

  blueScanning(BuildContext context) async {
    devicesList.value = [];
    devicesList.refresh();

    // await for (BluetoothResponse response
    //     in bluetoothRepositoryImpl.scanning(context)) {
    //   if (response.status) {
    //     logger.d("扫描到设备：${response.list}");
    //     devicesList.value = response.list;
    //     devicesList.refresh();
    //     response.list.forEach((AizoRing i) {
    //       if (i.name == "AIZO RING") {
    //         logger.d("找到aizo了");
    //         logger.d(i);
    //         aizoDevices = i;
    //       }
    //     });
    //   } else {
    //     print("扫描失败或没有找到设备");
    //   }
    // }
  }

  // void aizoBatteryStatus() {
  //   bluetoothRepositoryImpl.aizoBatteryStatus();
  // }

  void aizoConfigurationList() {
    bluetoothRepositoryImpl.aizoConfigurationList();
  }

  void aizoSetUserInfo() {
    // 格式化日期为 yyyy-MM
    String formattedBirth = DateFormat('yyyy-MM').format(DateTime.now());

    bluetoothRepositoryImpl.aizoSetUserInfo(AizoUserInfo(
        birth: formattedBirth.isNotEmpty ? formattedBirth : '2000-01', // 设置默认值
        gender: 1,
        height: 172,
        weight: 50.5));
  }

  void aizoGetUserInfo() {
    bluetoothRepositoryImpl.aizoGetUserInfo();
  }

  void aizoSetHeartRateInterval(int i) {
   bluetoothController.aizoSetMeasureInterval((i + 1) * 10);
  }

  void aizoGetHeartRateInterval() async{
    AizoHeartRate? result = await bluetoothRepositoryImpl.aizoGetMeasureInterval();
    if (result != null) {
      currentMeasurementTime.value = result.currentInterval;
      refresh();
    }
  }

  void aizoInstantMeasurement(int type, int operation) async {
    logger.d("开始测量");

    if (Platform.isAndroid) {
      // showLoading("开始测量");
      bluetoothRepositoryImpl.aizoInstantMeasurementAndroid(type, operation,
          setMeasurement: setMeasurement);
      // hideLoading();
    } else {
      try {
        // showLoading("开始测量");
        bluetoothRepositoryImpl.aizoInstantMeasurementIOS(type, operation,
            setMeasurement: setMeasurement);
      } catch (e) {
        logger.d("result获取测量结果时出错: $e");
        Get.snackbar('Error', 'Measurement failed');
      }
    }
  }

void setMeasurement(String value) {
  currentMeasurementResult.value = value;
  // currentMeasurementStatus.value = 'success';
  refresh();
}

  void aizoGetHealthData(DateTime dateTime) async {
    List<AizoAutoFinialData> result = await bluetoothRepositoryImpl.aizoGetHealthData(dateTime);
    aizoHealthDataList.value = result;
    aizoHealthDataList.refresh();
    refresh();
  }

  void aizoUnbind(BuildContext context) {
    bluetoothController.aizoDisconnect();
  }

  void sheetInit() {
    sheetList.value = [
      T.bloodOxygen.tr,
      T.bloodPressure.tr,
      T.temperature.tr,
      T.wordsWeight.tr
    ];

    //test
    logger.d("当前sheetList${sheetList.value}");
    // selectedPageIndex.value = 4;
    // dropdownValue.value = sheetList.value[selectedPageIndex.value];
    //over
    update();
  }

  void aizoGetHardwareData() async {
    String result = await bluetoothRepositoryImpl.aizoGetHardwareData();
    aizoHardwareInfo.value = result;
    aizoHardwareInfo.refresh();
    refresh();
  }

  void aizoRingStatus() {
    // bluetoothRepositoryImpl.aizoRingStatus();
  }

  void setTimeFrame(Set<int> p1) {
    textValue.value = p1.first;
    textValue.refresh();
  }

  void aizoGetCurActGoal() {
    bluetoothRepositoryImpl.aizoGetCurActGoal();
  }

  void aizoGetSleepData(DateTime now) async {
    AizoSleepData? result = await bluetoothRepositoryImpl.aizoGetSleepData(now);
    if (result != null) {
      aizoSleepData.value = result;
      aizoSleepData.refresh();
      refresh();
    }
  }

  void bluetoothInit() async {
    if (BluetoothUtil.isInitialized) {
      logger.d("蓝牙已初始化");
      return;
    }

    var result = await BluetoothUtil.init();
    if (result) {
      logger.d("蓝牙初始化成功");
    } else {
      logger.d("蓝牙初始化失败");
    }
  }

  void bluetoothStartScan() {
    BluetoothUtil.startScan();
  }

  void aojConnectAndListen() {
    BluetoothUtil.aojConnectAndListenByMac(
      "A4:C1:38:17:9E:10",
      onConnectionState: (state) {
        logger.d("AOJ设备连接状态: $state");
      },
      onData: (data) {
        logger.d("AOJ收到数据: $data");
      },
    );
  }

  // void aojDisconnect() {
  //   bluetoothController.aojDisconnect();
  //   bluetoothRepositoryImpl.stopAojSync();
  // }

  // ==================== AOJ温度计专门方法 ====================

  /// 扫描并分类设备
  void scanAndClassifyDevices() async {
    try {
      await bluetoothController.scanDevicesTask();
      
      // 分类设备
      aojDevicesList.value = bluetoothController.getAojDevices();
      
      logger.d('aojDevicesList: ${aojDevicesList.length}');
      aojOtherDevicesList.value = bluetoothController.scanResults.where((device) {
        return bluetoothController.isAojNonThermometerDevice(device);
      }).toList();

      
      logger.d('扫描完成 - AOJ设备数量: ${aojDevicesList.length}');

      
      // 打印设备详情
      for (var device in aojOtherDevicesList) {
        logger.d('AOJ其他设备: ${device.remoteId.str} - ${device.platformName}');
      }

    } catch (e) {
      logger.e('扫描设备失败: $e');
    }
  }

  /// 连接AOJ温度计设备
  void connectAojDevice(MyBluetoothDevice device) async {
    try {
      //如果名字包含30B，则是血压计
      if (device.advName.contains('30B')) {
        logger.d('血压计设备: ${device.remoteId.str}');
        // await bluetoothRepositoryImpl.addAojDevice(device.remoteId.str, 0);
      } 
      // device.
      // else if (device.platformName.contains('30T')) {
      //   await bluetoothRepositoryImpl.addAojDevice(device.remoteId.str, 3);
      // } else if (device.platformName.contains('30S')) {
      //     await bluetoothRepositoryImpl.addAojDevice(device.remoteId.str, 2);
      // } else if (device.platformName.contains('30A')) {
      //   await bluetoothRepositoryImpl.addAojDevice(device.remoteId.str, 1);
      // }
      selectedAojDevice.value = device;
      
      // await bluetoothController.aojThermometerConnect(device);
      logger.d('AOJ温度计设备连接成功: ${device.remoteId.str}');
      logger.d('selectedAojDevice: ${selectedAojDevice.value?.remoteId.str}');
      logger.d("开始同步数据");
      // bluetoothRepositoryImpl.startAojSync();
    } catch (e) {
      logger.e('AOJ温度计设备连接失败: $e');
    }
  }

  /// 断开AOJ温度计设备
  void disconnectAojThermometerDevice() async {
    try {
      await bluetoothController.aojDisconnect();
      selectedAojDevice.value = null;
      logger.d('AOJ温度计设备断开连接');
    } catch (e) {
      logger.e('断开AOJ温度计设备失败: $e');
    }
  }



  /// 获取AOJ温度计电池状态
  void getAojThermometerBatteryLevel() async {
    try {
      int batteryLevel = await bluetoothController.aojGetBatteryLevel();
      logger.d('AOJ温度计电池电量: $batteryLevel%');
    } catch (e) {
      logger.e('获取AOJ温度计电池状态失败: $e');
    }
  }

  /// 连接AIZO设备
  void connectAizoDevice(MyBluetoothDevice device) async {
    try {
      selectedAizoDevice = device;
      await bluetoothController.aizoConnect(device);
      logger.d('AIZO设备连接成功: ${device.remoteId.str}');
    } catch (e) {
      logger.e('AIZO设备连接失败: $e');
    }
  }

  /// 断开AIZO设备
  void disconnectAizoDevice() async {
    try {
      await bluetoothController.aizoDisconnect();
      selectedAizoDevice = null;
      logger.d('AIZO设备断开连接');
    } catch (e) {
      logger.e('断开AIZO设备失败: $e');
    }
  }





  /// 血压计测量
  void measureAojBpm(MyBluetoothDevice device) async {
    logger.d('血压计测量: \\${device.remoteId.str}');
    // await bluetoothController.aojMeasureBpm(device);
    // TODO: 调用血压计测量的蓝牙方法
  }
}
