// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-26 16:51:14
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-27 16:58:36
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/weight/weight_home/views/weight_home_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/widget/custom_domain.dart';
// import 'package:aiCare/app/modules/home/<USER>/custom_home_bar.dart';
// import 'package:aiCare/app/modules/weight/weight_home/widgets/weight_line_chart.dart';
// import 'package:aiCare/app/modules/weight/weight_home/widgets/weight_pie_chart.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import '../controllers/weight_home_controller.dart';

// class WeightHomeView extends BaseView<WeightHomeController> {
//     WeightHomeView({
//     super.key,
//   }) : super(
//           parentPaddings: [0, 0, 0, 0],
//           bgColor: AppColors.homeBgColor,
//         );

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return null;
//   }



//   @override
//   Widget body(BuildContext context) {
//         return SingleChildScrollView(
//       child: ConstrainedBox(
//         constraints: BoxConstraints(
//           minHeight: ScreenAdapter.height(812-44), // Set the minimum height
//         ),
//         child: Column(
//           children: [
//             CustomHomeBar(),
//           WeightPieChart(),
//           WeightLineChart(),
//           CustomDomain(list: controller.domainList),
//         ],
//       ),
//     ));
//   }
  

// }
