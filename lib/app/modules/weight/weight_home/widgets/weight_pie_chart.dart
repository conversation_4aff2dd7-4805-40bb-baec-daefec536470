// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-26 16:57:15
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-28 13:30:23
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/weight/weight_home/widgets/weight_pie_chart.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:math';

// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/bluetooth/custom_bluetooth_icon.dart';
// import 'package:aiCare/app/modules/weight/weight_home/widgets/weight_canvos.dart';
// import 'package:aiCare/app/modules/weight/weight_home/widgets/weight_state_card.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';

// class WeightPieChart extends StatelessWidget with BaseWidgetMixin {
//   WeightPieChart({super.key});

//   @override
//   Widget body(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(8)),

//       width: ScreenAdapter.width(343),
//       height: ScreenAdapter.height(288),
//       decoration: BoxDecoration(
//         borderRadius:
//             BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
//         color: AppColors.colorWhite,
//         // color: Colors.red
//       ),
//       child: Stack(
//         children: [
//           //文字
//           Positioned(
//               top: ScreenAdapter.height(99),
//               left: ScreenAdapter.width(29),
//               child: Text(
//                 appLocalization.wordsLow,
//                 style: normalF12H17C999,
//               )),
//           Positioned(
//               top: ScreenAdapter.height(25),
//               left: ScreenAdapter.width(145),
//               child: Text(
//                 appLocalization.wordsNormal,
//                 style: normalF12H17C999,
//               )),
//           Positioned(
//               top: ScreenAdapter.height(99),
//               right: ScreenAdapter.width(35),
//               child: Text(
//                 appLocalization.wordsSerious,
//                 style: normalF12H17C999,
//               )),

//           Positioned(
//             top: ScreenAdapter.height(52.32),
//             left: ScreenAdapter.width(51.8),
//             child: Container(
//                 // color: Colors.red,
//                 width: ScreenAdapter.width(225.68), // 设置绘制区域的宽度
//                 height: ScreenAdapter.width(122.84), // 设置绘制区域的高度
//                 child: CustomPaint(
//                   painter: WeightCanvos(
//                     // data: 100.0
//                     ),
//                 ) // 使用自定义的CustomPainter
//                 ),
//           ),
//           Positioned(
//               top: ScreenAdapter.height(52.32) + ScreenAdapter.width(100.68),
//               // top: ScreenAdapter.width(144),
//               left: ScreenAdapter.width(157),
//               child: SizedBox(
//                 width: ScreenAdapter.width(15),
//                 height: ScreenAdapter.height(15),
//                 child: Image.asset(
//                   Assets.images.weightPieCenter.path,
//                   fit: BoxFit.contain,
//                 ),
//               )),

//               CustomBluetoothIcon(),

//           //时间
//           Positioned(
//               top: ScreenAdapter.height(175),
//               left: ScreenAdapter.width(131),
//               child: Text(
//                 "2024-06-01 13:24",
//                 style: normalF12H17C666,
//               )),
//           //状态值
//           Positioned(top: ScreenAdapter.height(160), child: WeightStateCard()),
//         ],
//       ),
//     );
//   }
// }
