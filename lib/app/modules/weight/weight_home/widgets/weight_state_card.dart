// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-30 11:46:13
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-28 13:29:59
//  * @FilePath: /rpmappmaster/lib/app/modules/weight/weight_home/widgets/weight_state_card.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_home/controllers/pressure_home_controller.dart';
// import 'package:aiCare/app/modules/weight/weight_home/controllers/weight_home_controller.dart';
// import 'package:aiCare/app/services/l10nService.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';

// class WeightStateCard extends StatelessWidget {
//   WeightHomeController controller = Get.find();
//   l10nService l10n = l10nService();
//   WeightStateCard({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//         width: ScreenAdapter.width(343),
//         height: ScreenAdapter.height(100),
//         margin: EdgeInsets.only(top: ScreenAdapter.height(20)),
//         padding: EdgeInsets.only(bottom: ScreenAdapter.height(12)),
//         child: Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               _WeightStateItem(
//                 title: controller.stateList[0].title,
//                 data: controller.stateList[0].data,
//                 buttonText: controller.stateList[0].buttonText,
//               ),
//               SizedBox(width: ScreenAdapter.width(45)),
//               _WeightStateItem(
//                 title: controller.stateList[1].title,
//                 data: controller.stateList[1].data,
//                 buttonText: controller.stateList[1].buttonText,
//               ),
//             ]));
//   }
// }

// class _WeightStateItem extends StatelessWidget {
//   final String title;
//   final double? data; // Make data nullable
//   final String? buttonText;
//   l10nService l10n = l10nService();

//   _WeightStateItem(
//       {super.key, required this.title, required this.data, this.buttonText});

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Expanded(child: SizedBox()),
//         Text(
//           title,
//           style: TextStyle(
//               color: AppColors.Color666,
//               fontSize: l10n.isChinese()
//                   ? ScreenAdapter.fontSize(12)
//                   : ScreenAdapter.fontSize(10),
//               height: l10n.isChinese()
//                   ? 16.8 / 12
//                   : ScreenAdapter.fontSize(14 / 10),
//               fontWeight: FontWeight.w500),
//         ),
//         Text(
//           data != null ? data.toString() : "--", // Display "--" if data is null
//           style: TextStyle(
//               color: AppColors.Color333,
//               fontSize: ScreenAdapter.fontSize(20),
//               height: ScreenAdapter.fontSize(28.0 / 20),
//               fontWeight: FontWeight.w500),
//         ),
//         buttonText != null && buttonText!.isNotEmpty
//             ? Container(
//                 padding:
//                     EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
//                 decoration: BoxDecoration(
//                   color: AppColors.pressureButton,
//                   borderRadius: BorderRadius.circular(ScreenAdapter.width(16)),
//                 ),
//                 child: Center(
//                   child: Text(
//                     buttonText!,
//                     style: TextStyle(
//                       color: AppColors.lightBlue,
//                       fontWeight: FontWeight.w500,
//                       fontSize: ScreenAdapter.fontSize(10),
//                       height: ScreenAdapter.fontSize(14.0 / 10),
//                     ),
//                   ),
//                 ),
//               )
//             : Expanded(child: SizedBox())
//       ],
//     );
//   }
// }
