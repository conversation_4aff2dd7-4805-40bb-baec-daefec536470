// import 'package:flutter/widgets.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';

// class WeightPointer extends CustomPainter {
//   final int rotation; // 旋转参数，0 代表 30 度，1 代表 90 度，2 代表 150 度，3 代表 180 度
//   final double maxWidth;
//   final double minWidth;
//   final double height;

//   WeightPointer({
//     required this.rotation,
//     required this.maxWidth,
//     required this.minWidth,
//     required this.height,
//   });

//   @override
//   void paint(Canvas canvas, Size size) {
//     final Paint paint = Paint()
//       ..color = AppColors.temperaturePointColor // 设置指针颜色
//       ..style = PaintingStyle.fill; // 填充样式

//     final double topWidth = minWidth;
//     final double bottomWidth = maxWidth;
//     final double pointerHeight = height;

//     // 旋转角度的映射
//     final Map<int, double> rotationAngles = {
//       0: 30,   // rotation 0 -> 30 degrees
//       1: 90,   // rotation 1 -> 90 degrees
//       2: 150,  // rotation 2 -> 150 degrees
//       3: 180,  // rotation 3 -> 180 degrees
//     };
//     final double angle = (rotationAngles[rotation] ?? 0) * 3.1415927 / 180; // 转换为弧度

//     // 保存当前状态
//     canvas.save();

//     // 计算旋转中心点（底部中心）
//     final Offset center = Offset(maxWidth / 2, pointerHeight);
//     canvas.translate(center.dx, center.dy); // 移动到中心
//     canvas.rotate(angle); // 旋转指针
//     canvas.translate(-center.dx, -center.dy); // 将画布移回原来的位置

//     // 绘制指针路径
//     final Path path = Path()
//       ..moveTo(0, pointerHeight) // 从底部左端开始
//       // 使用 arcTo 绘制一个朝下的半圆弧
//       ..arcTo(
//         Rect.fromLTWH(0, pointerHeight - (bottomWidth / 2), bottomWidth, bottomWidth),
//         0, // 圆弧开始角度，0度从右侧开始
//         3.1415927, // 绘制的弧度范围，π 代表半圆
//         false, // 是否强制移动起点到弧线起点
//       )
//       ..lineTo((bottomWidth / 2) + (topWidth / 2), pointerHeight - pointerHeight) // 顶部右端
//       ..lineTo((bottomWidth / 2) - (topWidth / 2), pointerHeight - pointerHeight) // 顶部左端
//       ..close(); // 关闭路径

//     // 绘制路径
//     canvas.drawPath(path, paint);

//     // 恢复之前保存的状态
//     canvas.restore();
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) {
//     return true; // 每次传入的新 rotation 值会触发重绘
//   }
// }