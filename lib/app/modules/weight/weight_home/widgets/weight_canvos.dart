// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-30 12:18:12
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-27 17:29:14
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/test_component/views/custom_canvos.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:math';

// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';
// class WeightCanvos extends CustomPainter {
//   late double radius;
//   late double centerX;
//   late double centerY;
//   late Canvas canvas;
//   late Size size;

//   final double? data; // Make data nullable

//   // Constructor with nullable data, defaulting to 0 if not provided
//   WeightCanvos({this.data = 0.0});

//   @override
//   void paint(Canvas canvas, Size size) {
//     this.canvas = canvas;
//     this.size = size;
//     // 确定圆心坐标
//     radius = size.width / 2;
//     centerX = size.width / 2;
//     // 向上调整圆心，避免圆形端点超出
//     centerY = size.height - ScreenAdapter.width(16) / 2;

//     drawOutBdCircle();
//     drawInnerBdCircle();
//     drawInnerCircle();
//     drawScaleLine();
//     drawPointer();
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) {
//     return true; // 每次传入的新 rotation 值会触发重绘
//   }

//   drawOutBdCircle() {
//     Paint backgroundPaint = Paint()
//       ..color = Color(0xffF6F6F6)
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = ScreenAdapter.width(1)
//       ..strokeCap = StrokeCap.round;

//     canvas.drawArc(
//       Rect.fromCircle(center: Offset(centerX, centerY), radius: radius),
//       pi,
//       pi,
//       false,
//       backgroundPaint,
//     );
//   }

//   drawInnerBdCircle() {
//     Paint backgroundPaint = Paint()
//       ..color = Color(0xffF6F6F6)
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = ScreenAdapter.width(16)
//       ..strokeCap = StrokeCap.round;

//     canvas.drawArc(
//       Rect.fromCircle(
//         center: Offset(centerX, centerY),
//         radius: radius - ScreenAdapter.width(16) / 2 - ScreenAdapter.width(5.6),
//       ),
//       pi,
//       pi,
//       false,
//       backgroundPaint,
//     );
//   }

//   drawInnerCircle() {
//     final gradient = LinearGradient(
//       colors: [Color(0xff3F55F9), Color(0xff139BFC), Color(0xFF2D4AF9)], // 渐变颜色
//     );

//     final Rect arcRect = Rect.fromCircle(center: Offset(centerX, centerY), radius: radius);

//     Paint testPaint = Paint()
//       ..shader = gradient.createShader(arcRect) // 使用渐变 shader
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = ScreenAdapter.width(16)
//       ..strokeCap = StrokeCap.round;

//     double sweepAngle = pi / 150 * (data ?? 0); // Use 0 if data is null
//     canvas.drawArc(
//       Rect.fromCircle(
//         center: Offset(centerX, centerY),
//         radius: radius - ScreenAdapter.width(16) / 2 - ScreenAdapter.width(5.6),
//       ),
//       pi,
//       sweepAngle, // Use dynamic sweep angle based on data
//       false,
//       testPaint,
//     );

//     double endAngle = pi + sweepAngle;
//     double arcRadius = radius - ScreenAdapter.width(16) / 2 - ScreenAdapter.width(5.6);

//     double endX = centerX + arcRadius * cos(endAngle);
//     double endY = centerY + arcRadius * sin(endAngle);

//     Paint ballPaintWithShadow = Paint()
//       ..color = Colors.black.withOpacity(0.25)
//       ..style = PaintingStyle.fill
//       ..maskFilter = MaskFilter.blur(BlurStyle.normal, 4);

//     canvas.drawCircle(Offset(endX, endY + ScreenAdapter.width(4)), ScreenAdapter.width(8.315), ballPaintWithShadow);

//     Paint ballPaint = Paint()
//       ..color = Colors.white
//       ..style = PaintingStyle.fill;

//     canvas.drawCircle(Offset(endX, endY), ScreenAdapter.width(8.315), ballPaint);
//   }

//   drawScaleLine() {
//     Paint linePaint = Paint()
//       ..color = Color(0xFF575757)
//       ..strokeWidth = 1;

//     double angleIncrement = pi / 30;

//     for (int i = 0; i <= 30; i++) {
//       double angle = pi + angleIncrement * i;
//       if (i == 0) {
//         angle += 0.02222222; // 微调
//       }

//       double lineLength = ScreenAdapter.width(6);
//       double strokeWidth = ScreenAdapter.width(1);

//       if (i == 0 || i % 5 == 0) {
//         lineLength = ScreenAdapter.width(11);
//         strokeWidth = ScreenAdapter.width(1.35);
//       }

//       linePaint.strokeWidth = strokeWidth;

//       double startX = centerX + (radius - lineLength - ScreenAdapter.width(28.27)) * cos(angle);
//       double startY = centerY + (radius - lineLength - ScreenAdapter.width(28.27)) * sin(angle);
//       double endX = startX + lineLength * cos(angle);
//       double endY = startY + lineLength * sin(angle);

//       canvas.drawLine(Offset(startX, startY), Offset(endX, endY), linePaint);
//     }
//   }

//   drawPointer() {
//     final Paint paint = Paint()
//       ..color = Color(0xFF575757)
//       ..style = PaintingStyle.fill;

//     double arcRadius = radius - ScreenAdapter.width(65.24);
//     double endAngle = pi + pi / 150 * (data ?? 0); // Use 0 if data is null

//     double pointerLength = ScreenAdapter.width(18.66);
//     double pointerWidth = ScreenAdapter.width(3.96);
//     double yOffset = ScreenAdapter.width(6.16);

//     double pointerTipX = centerX + arcRadius * cos(endAngle);
//     double pointerTipY = centerY + arcRadius * sin(endAngle) - yOffset;

//     double baseX1 = centerX + pointerWidth / 2 * cos(endAngle + pi / 2);
//     double baseY1 = centerY + pointerWidth / 2 * sin(endAngle + pi / 2) - yOffset;
//     double baseX2 = centerX + pointerWidth / 2 * cos(endAngle - pi / 2);
//     double baseY2 = centerY + pointerWidth / 2 * sin(endAngle - pi / 2) - yOffset;

//     Path pointerPath = Path();
//     pointerPath.moveTo(pointerTipX, pointerTipY);
//     pointerPath.lineTo(baseX1, baseY1);
//     pointerPath.lineTo(baseX2, baseY2);
//     pointerPath.close();

//     canvas.drawPath(pointerPath, paint);

//     canvas.drawCircle(Offset(centerX, centerY - yOffset), pointerWidth / 2, paint);
//   }
// }
