// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-07-31 10:54:14
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-12-04 13:29:06
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_oxygen/oxygen_home/widgets/oxygen_line_chart.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/custom_line.dart';
// import 'package:aiCare/app/modules/weight/weight_home/controllers/weight_home_controller.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_echarts/flutter_echarts.dart';
// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';

// import "dart:convert";

// import '../../../../core/values/app_colors.dart';

// class WeightLineChart extends StatefulWidget {
//   const WeightLineChart({Key? key}) : super(key: key);

//   @override
//   State<WeightLineChart> createState() => _LineChartState();
// }

// class _LineChartState extends State<WeightLineChart> {
//   final WeightHomeController controller = Get.find();

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//         margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
//         padding: EdgeInsets.only(top: ScreenAdapter.height(12)),
//         width: ScreenAdapter.width(343),
//         height: ScreenAdapter.height(299),
//         decoration: BoxDecoration(
//           borderRadius:
//               BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
//           color: AppColors.colorWhite,
//         ),
//         child: Column(
//           children: [
//             Container(
//               // width: ScreenAdapter.width(343),
//               margin: EdgeInsets.only(
//                   left: ScreenAdapter.width(12),
//                   bottom: ScreenAdapter.height(12)),

//               height: ScreenAdapter.height(17),
//               child: Row(
//                 children: <Widget>[
//                   SizedBox(
//                     width: ScreenAdapter.width(16),
//                     height: ScreenAdapter.height(16),
//                     child: Image.asset(Assets.images.trendIcon.path),
//                   ),
//                   Container(
//                       margin: EdgeInsets.symmetric(
//                           horizontal: ScreenAdapter.width(4)),
//                       child: Text(controller.appLocalization.weightTrend,
//                           style: normalF12H17C999.copyWith(
//                               fontWeight: FontWeight.w500,
//                               color: AppColors.Color333))),
//                   Expanded(
//                       child: Container(
//                         margin: EdgeInsets.only(right: ScreenAdapter.width(14)),
//                     // alignment: Alignment.centerRight,
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.end,
//                       crossAxisAlignment: CrossAxisAlignment.end,
//                       children: [
//                         Text(
//                           controller.appLocalization.wordsKg,
//                           style: normalF12H17C999.copyWith(
//                               color: AppColors.lightBlue,
//                               fontWeight: FontWeight.w500),
//                         ),
//                         SizedBox(
//                           width: ScreenAdapter.width(1),
//                         ),
//                         Text(
//                           "/",
//                           style: normalF12H17C999.copyWith(
//                               color: AppColors.greyLineColor,
//                               fontWeight: FontWeight.w500),
//                         ),
//                         SizedBox(
//                           width: ScreenAdapter.width(1),
//                         ),
//                         Text(
//                           controller.appLocalization.wordsLb,
//                           style: normalF12H17C999.copyWith(
//                               // color: AppColors.lightBlue,
//                               fontWeight: FontWeight.w500),
//                         ),
//                       ],
//                     ),
//                   ))
//                 ],
//               ),
//             ),
//             Container(
//               width: ScreenAdapter.width(324),
//               height: ScreenAdapter.height(244),
//               margin: EdgeInsets.only(
//                   left: ScreenAdapter.width(8), right: ScreenAdapter.width(8)),
//               child: CustomLine(
//                   key: UniqueKey(),
//                   lineTouchData: controller.lineTouchData,
//                   spots: controller.spots,
//                   leftMin: controller.leftMin,
//                   leftMax: controller.leftMax,
//                   leftInterval: controller.leftInterval,
//                   leftStyle: controller.leftStyle,
//                   bgColor: controller.bgColor,
//                   spotsColor: controller.spotsColor,
//                   spotsWidth: ScreenAdapter.width(1),
//                   dotStyle: controller.dotStyle,
//                   bottomStyle: controller.bottomStyle,
//                   listData: [
//                     "01",
//                     "02"
//                   ],),
//             )
//           ],
//         ));
//   }
// }
