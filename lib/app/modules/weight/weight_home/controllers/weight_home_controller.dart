// import 'dart:ui';

// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:aiCare/app/core/model/domains.dart';
// import 'package:aiCare/app/core/render/fl_chart/image_dot_painter.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/modules/weight/model/weight_state.dart';
// import 'package:aiCare/app/routes/app_pages.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// class WeightHomeController extends BaseController {
//   List<WeightState> stateList = [];

//   //My domain list
//   List<Domains> domainList = [];

//   List<List<FlSpot>> spots = [
//     [88, 94, 99, 92, 96, 82, 90]
//         .asMap()
//         .entries
//         .map((entry) =>
//             FlSpot((entry.key + 1).toDouble(), entry.value.toDouble()))
//         .toList(),
//   ];

//   double leftMax = 130;
//   double leftMin = 30;
//   double leftInterval = 10;
//   TextStyle leftStyle = normalF12H17C666;
//   TextStyle bottomStyle = normalF12H17C666;
//   Color bgColor = AppColors.searchButton;

//   List<Color> spotsColor = [AppColors.lightBlue];

//   List<FlDotData> dotStyle = [
//     FlDotData(
//         show: true,
//         getDotPainter: (spot, perent, barData, index) {
//           if (spot.y >90) {
//             return GlowDotCirclePainter(
//                 radius: ScreenAdapter.width(3), //点的半径
//                 color: AppColors.errorPoint, //点的颜色
//                 strokeWidth: ScreenAdapter.width(1), //点的边框宽度
//                 strokeColor: AppColors.colorWhite, //点边框的颜色
//                 glowColor: AppColors.errorPoint,
//                 glowRadius: ScreenAdapter.width(5));
//           }
//           if(spot.y > 85){
//             return GlowDotCirclePainter(
//                 radius: ScreenAdapter.width(3), //点的半径
//                 color: AppColors.warningPoint, //点的颜色
//                 strokeWidth: ScreenAdapter.width(1), //点的边框宽度
//                 strokeColor: AppColors.colorWhite, //点边框的颜色
//                 glowColor: AppColors.warningPoint,
//                 glowRadius: ScreenAdapter.width(5));
//           }
//           return FlDotCirclePainter(
//             radius: ScreenAdapter.width(3), //点的半径
//             color: AppColors.lightBlue, //点的颜色
//             strokeWidth: ScreenAdapter.width(1), //点的边框宽度
//             strokeColor: Colors.white, //点边框的颜色
//           );
//         }),
//   ];

//   Color getDynamicColor(double yValue) {
//     if(yValue > 90) return AppColors.errorTextColor;
//     if(yValue > 85) return AppColors.warningTextColor;
//     return AppColors.lightBlue;
//   }

//   late LineTouchData lineTouchData; // 使用 late 初始化

//   @override
//   void onInit() {
//     super.onInit();
//     initDomainList();
//     stateList = [
//       WeightState(
//         title: appLocalization.commonHeightInch,
//         data: 48.23,
//         buttonText: "Low",
//       ),
//       WeightState(
//         title: appLocalization.wordsBMI,
//         data: 17.9,
//         buttonText: "Low",
//       ),
//     ];

//     lineTouchData = LineTouchData(
//       enabled: true,
//                 handleBuiltInTouches: false,
//       touchTooltipData: LineTouchTooltipData(
//         tooltipRoundedRadius: ScreenAdapter.width(12),
//         tooltipPadding: EdgeInsets.symmetric(
//           horizontal: ScreenAdapter.width(8),
//           vertical: ScreenAdapter.height(2),
//         ),
//         maxContentWidth: 100,
//         getTooltipColor: (touchedSpot) {
//           return getDynamicColor(touchedSpot.y).withOpacity(0.15); // 蓝色
//         },
//         tooltipBorder: BorderSide(
//           color: AppColors.colorWhite, // 默认颜色
//           width: ScreenAdapter.width(0.5),
//         ),
//         getTooltipItems: (touchedSpots) {
//           return touchedSpots.map((LineBarSpot touchedSpot) {
//             return LineTooltipItem(
//               '${touchedSpot.y.toStringAsFixed(1)}',
//               normalF12H17C333.copyWith(color: getDynamicColor(touchedSpot.y)),
//             );
//           }).toList();
//         },
//       ),
//       // handleBuiltInTouches: true,
//       getTouchedSpotIndicator:
//           (LineChartBarData barData, List<int> spotIndexes) {
//         return spotIndexes.map((index) {
//           double yValue = barData.spots[index].y; // 获取 Y 值
//           FlLine indicatorLine = FlLine(
//             color: getDynamicColor(yValue).withOpacity(0.65), // 蓝色
//             strokeWidth: ScreenAdapter.width(0),
//             dashArray: [2, 2],
//           );

//           return TouchedSpotIndicatorData(
//             indicatorLine, // 返回相应颜色的虚线
//             FlDotData(show: false), // 不显示点
//           );
//         }).toList();
//       },
//       getTouchLineStart: (barData, spotIndex) {
//         return 0.0; // 返回左上角的坐标
//       },
//       getTouchLineEnd: (barData, spotIndex) {
//         return barData.spots[spotIndex].y; // 返回触摸点的 y 值
//       },
//     );
  
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   void initDomainList() {
//     domainList = [
//       Domains(
//         images: Assets.images.notion,
//         text: appLocalization.measurementRemind,
//         tap: () {
//           logger.d("onclick");
//           Get.toNamed(Routes.WEIGHT_REMIND_HOME);
//         },
//       ),
//       Domains(
//         images: Assets.images.setting,
//         text: appLocalization.measurementSettings,
//         tap: () {
//           logger.d("onclick");
//           Get.toNamed(Routes.WEIGHT_TARGET);
//         },
//       ),
//       Domains(
//         images: Assets.images.record,
//         text: appLocalization.measurementRecord,
//         tap: () {
//           logger.d("onclick");
//           Get.toNamed(Routes.OXYGEN_RECORD_HOME);
//         },
//       ),
//     ];
//   }
// }
