// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-26 16:51:14
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-27 16:56:50
//  * @FilePath: /rpmappmaster/lib/app/modules/weight/weight_home/bindings/weight_home_binding.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:get/get.dart';

// import '../controllers/weight_home_controller.dart';

// class WeightHomeBinding extends Bindings {
//   @override
//   void dependencies() {
//     Get.lazyPut<WeightHomeController>(
//       () => WeightHomeController(),
//     );
//   }
// }
