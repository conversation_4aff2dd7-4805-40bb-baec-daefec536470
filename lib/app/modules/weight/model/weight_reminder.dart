// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-10-09 15:26:35
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-10-09 15:27:04
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/weight/model/weight_reminder.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// class WeightReminder {
//   String date;
//   String label;
//   bool switchValue;
//   List<bool> repeatList;

//   WeightReminder({
//     required this.date,
//     required this.label,
//     required this.switchValue,
//     required this.repeatList,
//   });

//   // 将对象转换为 JSON
//   Map<String, dynamic> toJson() {
//     return {
//       'date': date,
//       'label': label,
//       'switchValue': switchValue,
//       'repeatList': repeatList,
//     };
//   }

//   // 从 JSON 创建对象
//   factory WeightReminder.fromJson(Map<String, dynamic> json) {
//     return WeightReminder(
//       date: json['date'] as String,
//       label: json['label'] as String,
//       switchValue: json['switchValue'] as bool,
//       repeatList: List<bool>.from(json['repeatList']),
//     );
//   }
// }