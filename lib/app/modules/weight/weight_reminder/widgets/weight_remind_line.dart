// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-08-28 13:00:52
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-10-10 12:56:33
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_pressure/pressure_reminder/widgets/pressure_remind_line.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';

// class weightReminderLine extends StatelessWidget {
//   final double margin;
//    weightReminderLine({super.key, required this.margin});

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.symmetric(vertical: ScreenAdapter.height(margin)),
//       width: double.infinity,
//       height: ScreenAdapter.height(1),
//       color: AppColors.recordBdColor,
//     );
//   }
// }