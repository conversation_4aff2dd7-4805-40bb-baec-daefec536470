// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-08-28 16:59:23
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-09-09 16:22:39
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_pressure/pressure_reminder/remind_repeat/views/remind_repeat_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/custom_left_back.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_reminder/remind_edit/controllers/remind_edit_controller.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_reminder/widgets/pressure_remind_line.dart';
// import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';

// import 'package:get/get.dart';
// import 'package:logger/logger.dart';

// import '../controllers/remind_repeat_controller.dart';

// class RemindRepeatView extends BaseView<RemindRepeatController> {
//   RemindRepeatView({
//     super.key,
//   }) : super(parentPaddings: [
//           // ScreenAdapter.width(16),
//           0,
//           0,
//           // ScreenAdapter.width(16),
//           0,
//           0
//         ], bgColor: AppColors.homeBgColor, banner: HomeBanner());

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return null;
//   }

//   @override
//   Widget body(BuildContext context) {
//     // TODO: implement body
//     return Container(
//         padding: EdgeInsets.symmetric(
//             horizontal: ScreenAdapter.width(16),
//             vertical: ScreenAdapter.height(16)),
//         decoration: BoxDecoration(
//             color: Colors.white,
//             boxShadow: [
//               BoxShadow(
//                 color: Color(0x0D000000), // 阴影颜色，0x0D 表示透明度为 13% 的黑色
//                 offset: Offset(0, -8), // x 轴偏移量为 0，y 轴偏移量为 -8px
//                 blurRadius: 24, // 模糊半径为 24px
//                 spreadRadius: 0, // 扩展半径为 0
//               ),
//             ],
//             borderRadius: BorderRadius.only(
//                 topLeft: Radius.circular(ScreenAdapter.width(16)),
//                 topRight: Radius.circular(ScreenAdapter.width(16)))),
//         child: Column(
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 CustomLeftBack(arguments: controller.checkList.value,),
//                 Text(
//                   appLocalization.wordsRepeat,
//                   style: w600F16H22C333,
//                 ),
//                 Text(
//                   appLocalization.wordsSave,
//                   style: normalF16H22C666.copyWith(color: Colors.transparent),
//                 ),
//               ],
//             ),
//             Container(
//               margin: EdgeInsets.only(top: ScreenAdapter.height(32)),
//               width: double.infinity,
//               padding: EdgeInsets.all(ScreenAdapter.width(16)),
//               decoration: BoxDecoration(
//                 color: AppColors.pressureRemindBg
//               ),
//               child: ListView.builder(
//                 physics:
//                     NeverScrollableScrollPhysics(), // 禁用ListView的滚动，避免与SingleChildScrollView的滚动冲突
//                 shrinkWrap: true, // 使ListView仅占据它的子项所需的空间
//                 itemCount: controller.dateList.length * 2 -
//                     1, // 列表的长度包括PressureRemindCard和_line
//                 itemBuilder: (context, index) {
//                   if (index.isEven) {
//                     return _card(
//                       index: index ~/ 2,
//                     );
//                   } else {
//                     // 奇数索引添加_line组件
//                     return pressureRemindLine(
//                       margin: 0,
//                     );
//                   }
//                 },
//               ),
//             )
//           ],
//         ));
//   }
// }

// class _card extends StatelessWidget {
//   RemindRepeatController controller = Get.find();
//   int index;
//   _card({super.key, required this.index});

//   @override
//   Widget build(BuildContext context) {
//     EdgeInsets margin;
//     if(index ==0){
//       margin = EdgeInsets.only(bottom: ScreenAdapter.height(12));
//     }else if(index ==  controller.dateList.length -1 ){
//       margin = EdgeInsets.only(top: ScreenAdapter.height(12));
//     }else{
//       margin = EdgeInsets.symmetric(vertical: ScreenAdapter.height(12));
//     }
//     return InkWell(
//       onTap: () {
//         controller.check(index);
        
//       },
//       child: Container(
//         margin: margin,
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Text(
//               controller.dateList[index],
//               style: w500F14H19C333,
//             ),
//             Obx(()=>SizedBox(
//               width: ScreenAdapter.height(20),
//               height: ScreenAdapter.height(20),
//               child: controller.checkList.value[index]
//                   ? SvgPicture.asset(
//                       Assets.images.checkBlue,
//                       fit: BoxFit.contain,
//                     )
//                   : SizedBox(),
//             ))
//           ],
//         ),
//       ),
//     );
//   }
// }
