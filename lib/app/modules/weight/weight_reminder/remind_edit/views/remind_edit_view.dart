// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
// import 'package:aiCare/app/modules/weight/weight_reminder/remind_edit/controllers/remind_edit_controller.dart';
// import 'package:aiCare/app/modules/weight/weight_reminder/widgets/weight_remind_line.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/widgets.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';

// class RemindEditView extends BaseView<RemindEditController> {
//   RemindEditView({
//     super.key,
//   }) : super(parentPaddings: [
//           // ScreenAdapter.width(16),
//           0,
//           0,
//           // ScreenAdapter.width(16),
//           0,
//           0
//         ], bgColor: AppColors.homeBgColor, banner: HomeBanner());

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return null;
//   }

//   @override
//   Widget body(BuildContext context) {
//     // TODO: implement body
//     return Container(
//         padding: EdgeInsets.symmetric(
//             horizontal: ScreenAdapter.width(16),
//             vertical: ScreenAdapter.height(16)),
//         decoration: BoxDecoration(
//             color: Colors.white,
//             boxShadow: [
//               BoxShadow(
//                 color: Color(0x0D000000), // 阴影颜色，0x0D 表示透明度为 13% 的黑色
//                 offset: Offset(0, -8), // x 轴偏移量为 0，y 轴偏移量为 -8px
//                 blurRadius: 24, // 模糊半径为 24px
//                 spreadRadius: 0, // 扩展半径为 0
//               ),
//             ],
//             borderRadius: BorderRadius.only(
//                 topLeft: Radius.circular(ScreenAdapter.width(16)),
//                 topRight: Radius.circular(ScreenAdapter.width(16)))),
//         child: Column(children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               InkWell(
//                 onTap: () {
//                   Get.back();
//                 },
//                 child: Text(
//                   appLocalization.wordsCancel,
//                   style: normalF16H22C666,
//                 ),
//               ),
//               Text(
//                 appLocalization.bloodPressureRemindEdit,
//                 style: w600F16H22C333,
//               ),
//               InkWell(
//                 onTap: () async {
//                   controller.submitRemind(true);
//                   // Navigator.pop(context); // 关闭底部弹窗
//                 },
//                 child: Text(
//                   appLocalization.wordsSave,
//                   style: normalF16H22C666,
//                 ),
//               )
//             ],
//           ),
//           Container(
//             margin: EdgeInsets.only(top: ScreenAdapter.height(36)),
//             height: ScreenAdapter.height(216),
//             child: Stack(
//               alignment: Alignment.center,
//               children: [
//                 IgnorePointer(
//                   ignoring: true, // 确保中间的文本不会拦截手势
//                   child: Container(
//                     height: ScreenAdapter.height(42),
//                     alignment: Alignment.center,
//                     decoration: BoxDecoration(
//                         color: AppColors.homeBgColor,
//                         borderRadius:
//                             BorderRadius.circular(ScreenAdapter.width(8))),
//                   ),
//                 ),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     SizedBox(
//                       width: ScreenAdapter.width(80),
//                     ),
//                     _picker(
//                       or: true,
//                     ),
//                     _picker(
//                       or: false,
//                     ),
//                     SizedBox(
//                       width: ScreenAdapter.width(80),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ),
//           _input(),
//           _submit()
//         ]));
//   }
// }

// class _picker extends StatelessWidget {
//   RemindEditController controller = Get.find();
//   final bool or;
//   _picker({super.key, required this.or});

//   @override
//   Widget build(BuildContext context) {
//     return Expanded(
//         child: Obx(
//       () => CupertinoPicker(
//         scrollController: or
//             ? controller.leftPickerController
//             : controller.rightPickerController,
//         selectionOverlay: Container(
//           color: Colors.transparent, // 设置中间选中行的背景颜色为 FAFAFA
//         ),
//         itemExtent: ScreenAdapter.height(34),
//         onSelectedItemChanged: (int index) {
//           // 右边选择框选中改变时的逻辑
//           or ? controller.switchLeft(index) : controller.switchRight(index);
//         },
//         children: List<Widget>.generate(or ? 24 : 60, (int index) {
//           bool isSelected = index ==
//               (or
//                   ? controller.leftSelectIndex.value
//                   : controller.rightSelectIndex.value);
//           String text = index < 10 ? "0$index" : index.toString();
//           return Center(
//               child: Text(
//             text,
//             style: TextStyle(
//                 color: isSelected ? AppColors.Color333 : AppColors.Color999,
//                 fontSize: isSelected
//                     ? ScreenAdapter.fontSize(24)
//                     : ScreenAdapter.fontSize(18),
//                 height: isSelected ? 33.6 / 24 : 25.2 / 18,
//                 fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400),
//           ));
//         }),
//       ),
//     ));
  
//   }
// }

// class _input extends StatelessWidget with BaseWidgetMixin {
//   RemindEditController controller = Get.find();
//   _input({super.key});

//   @override
//   Widget body(BuildContext context) {
//     bool add = controller.selectIndex == -1;
//     return Container(
//       width: ScreenAdapter.width(343),
//       margin: EdgeInsets.symmetric(vertical: ScreenAdapter.height(16)),
//       padding: EdgeInsets.all(ScreenAdapter.height(16)),
//       decoration: BoxDecoration(
//           color: AppColors.pressureRemindBg,
//           borderRadius: BorderRadius.circular(ScreenAdapter.width(8))),
//       child: Column(
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 appLocalization.wordsRepeat,
//                 style: normalF14H19C333,
//               ),
//               InkWell(
//                 onTap: () {
//                   controller.toRepeat();
//                 },
//                 child: Obx(() => Container(
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.end,
//                         children: [
//                           SizedBox(
//                             width: ScreenAdapter.width(140),
//                             child: Text(
//                               controller.repeatText.value,
//                               textAlign: TextAlign.end,
//                               style: normalF14H19C999,
//                               overflow: TextOverflow.ellipsis, // 设置文本溢出时显示省略号
//                               maxLines: 1, // 限制文本最多显示一行
//                             ),
//                           ),
//                           Container(
//                             width: ScreenAdapter.height(8),
//                             height: ScreenAdapter.height(8),
//                             margin:
//                                 EdgeInsets.only(left: ScreenAdapter.width(4)),
//                             child: SvgPicture.asset(
//                               Assets.images.rightArrow48,
//                               fit: BoxFit.contain,
//                             ),
//                           )
//                         ],
//                       ),
//                     )),
//               )
//             ],
//           ),
//           weightReminderLine(
//             margin: 12,
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 appLocalization.wordsLabel,
//                 style: normalF14H19C333,
//               ),
//               SizedBox(
//                 width: ScreenAdapter.width(100),
//                 child: TextField(
//                   maxLines: null,
//                   maxLength: 80,
//                   textAlign: TextAlign.end,
//                   cursorColor: AppColors.Color999, // 设置光标颜色为黑色
//                   controller: controller.lableController,
//                   decoration: InputDecoration(
//                     border: InputBorder.none,
//                   ),
//                   style: normalF14H19C999,
//                 ),
//               )
//             ],
//           ),
//           weightReminderLine(margin: 12),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 appLocalization.wordsSnooze,
//                 style: normalF14H19C333,
//               ),
//               SizedBox(
//                 width: ScreenAdapter.width(42),
//                 height: ScreenAdapter.height(26),
//                 child: Obx(() => CupertinoSwitch(
//                       value: add
//                           ? controller.addSwitch.value
//                           : controller.data.value!.switchValue,
//                       onChanged: (bool value) {
//                         controller.switchRecord(value);
//                       },
//                     )),
//               )
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }

// class _submit extends StatelessWidget {
//   RemindEditController controller = Get.find();
//   _submit({super.key});

//   @override
//   Widget build(BuildContext context) {
//     bool add = controller.selectIndex == -1;
//     return InkWell(
//       onTap: () {
//         controller.logger.d("点击了$add");
//         controller.submitRemind(false);
//       },
//       child: Container(
//         width: double.infinity,
//         height: ScreenAdapter.height(38),
//         decoration: BoxDecoration(
//             color: AppColors.homeBgColor,
//             borderRadius: BorderRadius.circular(ScreenAdapter.width(8))),
//         child: Center(
//           child: Text(
//             add
//                 ? controller.appLocalization.commonAddRemind
//                 : controller.appLocalization.commonDeleteRemind,
//             style: TextStyle(
//                 color: add ? AppColors.lightBlue80 : AppColors.deleteColor,
//                 fontSize: ScreenAdapter.fontSize(16),
//                 height: 22.4 / 16,
//                 fontWeight: FontWeight.w500),
//           ),
//         ),
//       ),
//     );
//   }
// }
