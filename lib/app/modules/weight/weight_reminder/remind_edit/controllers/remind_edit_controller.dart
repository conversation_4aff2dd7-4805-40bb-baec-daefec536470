

// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:aiCare/app/modules/weight/model/weight_reminder.dart';
// import 'package:aiCare/app/modules/weight/weight_reminder/reminder_home/controllers/reminder_home_controller.dart';
// import 'package:aiCare/app/routes/app_pages.dart';
// import 'package:flutter/widgets.dart';
// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';

// class RemindEditController extends BaseController {
//   ReminderHomeController remindHomeController = Get.find();
//   TextEditingController lableController = TextEditingController();
//   late FixedExtentScrollController leftPickerController; // 示例中间索引
//   late FixedExtentScrollController rightPickerController; // 示例中间索引
//   RxInt leftSelectIndex = 5.obs;
//   RxInt rightSelectIndex = 30.obs;

//   RxBool addSwitch = true.obs;
//   int selectIndex = -1;
//   Rxn<WeightReminder> data = Rxn<WeightReminder>();

//   RxString repeatText = "".obs;
//   RxList<bool> checkList = [
//     true,
//   ].obs;

//   @override
//   void onInit() {
//     super.onInit();
//     initAdd();
//   }

//   @override
//   void onClose() {
//     lableController.dispose();
//     super.onClose();
//   }

//   void initAdd()  {
//     // 获取传递的参数
//     final arguments =  Get.arguments;
//     selectIndex = arguments['index'] ?? -1;
//     data.value = arguments['data'] ?? null;
//     logger.d(data.toJson());
//     logger.d("initAdd");
//     if (selectIndex == -1) {
//       leftSelectIndex.value = 5;
//       rightSelectIndex.value = 30;
//       lableController.text = appLocalization.bloodPressureRemind;
//       addSwitch.value = true;
//       checkList.value = [
//         true,
//         true,
//         true,
//         true,
//         true,
//         true,
//         true,
//       ];
//     } else {
//       List<String> dateList =  data.value!.date.split(":");
//       leftSelectIndex.value = int.parse(dateList[0]);
//       rightSelectIndex.value = int.parse(dateList[1]);
//       checkList.value = data.value!.repeatList;
//       lableController.text = data.value!.label;
//       logger.d("$leftSelectIndex,$rightSelectIndex");
      
//     }
//     leftPickerController = FixedExtentScrollController(initialItem: leftSelectIndex.value);
//     rightPickerController = FixedExtentScrollController(initialItem: rightSelectIndex.value);
//     switchRepeatText();

//     update();
//   }

//   void submitRemind(bool or) {
//     if (selectIndex == -1 || or==true) {
//       String leftDate = leftSelectIndex.value < 10
//         ? "0${leftSelectIndex.value}"
//         : leftSelectIndex.value.toString();
//     String rightDate = rightSelectIndex.value < 10
//         ? "0${rightSelectIndex.value}"
//         : rightSelectIndex.value.toString();
//     logger.d("选中的闹钟值：$leftDate,$rightDate");
//     WeightReminder result = WeightReminder(
//         date: "$leftDate:$rightDate",
//         label: lableController.text,
//         switchValue:
//             selectIndex == -1 ? addSwitch.value : data.value!.switchValue,
//         repeatList: checkList.value);
//     logger.d(result.toJson());
//     Get.back(result: result);
//       // logger.d("left: $leftSelectIndex,right: $rightSelectIndex,label: ${lableController.text},snooze: $addSwitch");
//     } else {
//       remindHomeController.detelteRemind(selectIndex);
//       Get.back();
//     }
    
//     // return true;
//   }

//   void switchLeft(int index) {
//     leftSelectIndex.value = index;
//     logger.d(leftSelectIndex.value);
//   }

//   void switchRight(int index) {
//     rightSelectIndex.value = index;
//   }

//   void switchRecord(bool value) {
//     if (selectIndex == -1) {
//       addSwitch.value = value;
//     } else {
//       data.value!.switchValue = value;
//       data.refresh();
//     }
//   }

//   void toRepeat() {
//     Get.toNamed(Routes.WEIGHT_REMIND_REPEAT,
//             arguments: {"index": selectIndex, "repeat": checkList.value})
//         ?.then((result) {
//       if (result != null) {
//         print(result);
//         List<bool> myResult = result;
//         checkList.value = myResult;
//         switchRepeatText();
//       } else {
//         logger.d("pressure_reminder_repeat返回的结果报错了");
//       }
//     });
//   }

//   void switchRepeatText() {
//     // 获取所有选中的星期的索引
//     List<String> days = [
// appLocalization.dateSunday, // 星期日
// appLocalization.dateMonday, // 星期一
// appLocalization.dateTuesday, // 星期二
// appLocalization.dateWednesday, // 星期三
// appLocalization.dateThursday, // 星期四
// appLocalization.dateFriday, // 星期五
// appLocalization.dateSaturday // 星期六
//     ];

//     if (checkList.every((day) => day)) {
//       // 如果所有的值都为 true
//       repeatText.value = appLocalization.dateEveryday;
//     } else if (checkList.sublist(1, 6).every((day) => day) &&
//         !checkList[0] &&
//         !checkList[6]) {
//       // 如果周一到周五的值为 true，且周日和周六的值为 false
//       repeatText.value = appLocalization.dateMondayToFriday;
//     } else {
//       // 返回所有选中的天
//       List<String> selectedDays = [];
//       for (int i = 0; i < checkList.length; i++) {
//         if (checkList[i]) {
//           selectedDays.add(days[i]);
//         }
//       }
//       repeatText.value = selectedDays.join(", ");
//     }
//   }
// }
