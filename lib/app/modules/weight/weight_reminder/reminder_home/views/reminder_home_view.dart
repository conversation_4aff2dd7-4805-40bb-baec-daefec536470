

// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/widget/custom_app_bar.dart';
// import 'package:aiCare/app/core/widget/custom_left_back.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_reminder/widgets/pressure_remind_line.dart';
// import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
// import 'package:aiCare/app/modules/weight/model/weight_reminder.dart';
// import 'package:aiCare/app/modules/weight/weight_reminder/reminder_home/controllers/reminder_home_controller.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/widgets.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';
// import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

// import '../../../../../services/screenAdapter.dart';

// class ReminderHomeView extends BaseView<ReminderHomeController> {
//   ReminderHomeView({
//     super.key,
//   }) : super(parentPaddings: [
//           ScreenAdapter.width(16),
//           // 0,
//           0,
//           ScreenAdapter.width(16),
//           ScreenAdapter.height(16)
//         ], bgColor: AppColors.homeBgColor, banner: HomeBanner());

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return CustomAppBar(
//       appBarTitleText: appLocalization.bloodPressureRemind,
//       actions: [
//         _rightIcon(),
//       ],
//       padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
//       isBackButtonEnabled: true,
//       leading: CustomLeftBack(),
//     );
//   }

//   @override
//   Widget body(BuildContext context) {
//     return SingleChildScrollView(
//         child: Obx(
//       () => controller.remindList.length != 0
//           ? Container(
//             margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//             height: ScreenAdapter.height(812),
//             child: ListView.builder(
//               physics:
//                   NeverScrollableScrollPhysics(), // 禁用ListView的滚动，避免与SingleChildScrollView的滚动冲突
//               shrinkWrap: true, // 使ListView仅占据它的子项所需的空间
//               itemCount: controller
//                   .remindList.length, // 列表的长度包括PressureRemindCard和_line
//               itemBuilder: (context, index) {
//                 return InkWell(
//                   onTap: (){
//                     controller.toEdit(index);
//                   },
//                   child: _card(
//                   data: controller.remindList.value[index],
//                   index: index,
//                 ),
//                 );
//               },
//             ),
//           )
//           : _notion(),
//     ));
//   }
// }

// class _rightIcon extends StatelessWidget {
//   ReminderHomeController controller = Get.find();
//    _rightIcon({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: () {
//         controller.toEdit(-1);
//       },
//       splashColor: Colors.transparent, // 去除水波纹效果
//       highlightColor: Colors.transparent, // 去除高亮效果
//       child: SizedBox(
//         width: ScreenAdapter.width(22),
//         height: ScreenAdapter.height(22),
//         child: Center(
//           // 使用 Center 小部件使 SVG 图片居中
//           child: SvgPicture.asset(
//             Assets.images.plusOuterCircle,
//             fit: BoxFit.contain, // 确保图片按比例显示
//           ),
//         ),
//       ),
//     );
//   }
// }

// class _notion extends StatelessWidget with BaseWidgetMixin {
//   _notion({super.key});

//   @override
//   Widget body(BuildContext context) {
//     return SizedBox(
//       width: double.infinity,
//       height: ScreenAdapter.height(812),
//       child: Stack(
//         children: [
//           Positioned(
//               top: ScreenAdapter.height(180),
//               left: 0,
//               right: 0,
//               child: SizedBox(
//                 height: ScreenAdapter.height(163.76),
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     SizedBox(
//                       width: ScreenAdapter.width(133.5),
//                       height: ScreenAdapter.height(129.76),
//                       child: SvgPicture.asset(
//                         Assets.images.pressureReminderNoting,
//                         fit: BoxFit.contain,
//                       ),
//                     ),
//                     Text(
//                       appLocalization.bloodPressureRemindNoting,
//                       style: TextStyle(
//                           color: AppColors.Color999,
//                           fontSize: ScreenAdapter.fontSize(16),
//                           height: ScreenAdapter.fontSize(22.4 / 16),
//                           fontWeight: FontWeight.w500),
//                     )
//                   ],
//                 ),
//               ))
//         ],
//       ),
//     );
//   }
// }

// class _card extends StatefulWidget {
//   final WeightReminder data;
//   final int index;

//   // 构造函数，使用命名参数，并使用 `required` 标记这些参数为必填项
//   _card({
//     Key? key,
//     required this.index,
//     required this.data,
//   }) : super(key: key);

//   @override
//   _cardState createState() => _cardState();
// }

// class _cardState extends State<_card> with SingleTickerProviderStateMixin {
//   late AnimationController _controller;
//   late Animation<Offset> _offsetAnimation;

//   final ReminderHomeController controller = Get.find();

//   @override
//   void initState() {
//     super.initState();
//     _controller = AnimationController(
//       vsync: this,
//       duration: Duration(milliseconds: 150),
//     );

//     // 设置偏移量动画，设置为滑动屏幕宽度的25%
//     _offsetAnimation = Tween<Offset>(begin: Offset.zero, end: Offset(-0.2, 0.0))
//         .animate(_controller);
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }


//     // 新增一个方法来重置滑动偏移
//   void _resetSwipeWithoutAnimation() {
//     _controller.value = 0.0; // 直接将动画控制器的值设为0，避免动画
//   }

//   @override
//   Widget build(BuildContext context) {
//     return

//         GestureDetector(
//       onHorizontalDragUpdate: (details) {
//         if (details.primaryDelta! < -1) {
//           _controller.forward(); // 向左滑动时开始动画
//         } else if (details.primaryDelta! > 5) {
//           _controller.reverse(); // 向右滑动时还原动画
//         }
//       },
//       child: Stack(
//         children: [
//           // Container(
//           //   height: ScreenAdapter.height(66),
//           //   margin: EdgeInsets.only(right: ScreenAdapter.width(12)),
//           //   color: Colors.red,
//           //   alignment: Alignment.centerRight,

//           // ),

//           Positioned.fill(
//               child: Container(
//             color: Colors.red,
//             margin: EdgeInsets.only(bottom: ScreenAdapter.height(widget.index != controller.remindList.length - 1? 32:16)),
//             child: Stack(
//               children: [
//                 Positioned(
//                     top: 0,
//                     right: 0,
//                     child: InkWell(
//                       onTap: () async{
//                         _resetSwipeWithoutAnimation();
//                         controller.detelteRemind(widget.index);
                        
//                       },
//                       child: Container(
//                         width: ScreenAdapter.width(75),
//                         height: ScreenAdapter.height(68),
//                         child: Center(
//                           child: Text(
//                             controller.appLocalization.wordsDelete,
//                             style: TextStyle(
//                                 fontSize: ScreenAdapter.fontSize(16),
//                                 height: 1,
//                                 fontWeight: FontWeight.w400,
//                                 color: AppColors.colorWhite),
//                           ),
//                         ),
//                       ),
//                     ))
//               ],
//             ),
//           )),
//           SlideTransition(
//             position: _offsetAnimation,
//             child: IntrinsicHeight(
//               child: Container(
//                 width: ScreenAdapter.width(343),
//                 color: AppColors.homeBgColor,
                
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Text(
//                           widget.data.date,
//                           style: TextStyle(
//                             fontWeight: FontWeight.w300,
//                             fontSize: ScreenAdapter.fontSize(48),
//                             height: ScreenAdapter.fontSize(48 / 48),
//                             color: AppColors.Color333,
//                           ),
//                         ),
//                         Expanded(
//                             child: SizedBox(
//                           height: ScreenAdapter.height(48),
//                           child: Text(
//                             "test",
//                             style: TextStyle(color: Colors.transparent),
//                           ),
//                         )),
//                         Container(
//                           width: ScreenAdapter.width(42),
//                           height: ScreenAdapter.height(26),
//                           margin:
//                               EdgeInsets.only(right: ScreenAdapter.width(8)),
//                           child: Obx(() => CupertinoSwitch(
//                                 value: controller
//                                     .remindList[widget.index].switchValue,
//                                 onChanged: (bool value) {
//                                   controller.switchRecord(value, widget.index);
//                                 },
//                               )),
//                         ),
//                       ],
//                     ),
//                     Container(
//                       width: ScreenAdapter.width(220),
//                       child: Text(
//                         "${widget.data.label}, ${controller.switchRepeatText(widget.data.repeatList)}",
//                         overflow: TextOverflow.ellipsis,
//                         maxLines: 1,
//                         style: TextStyle(
//                           fontWeight: FontWeight.w400,
//                           fontSize: ScreenAdapter.fontSize(14),
//                           height: ScreenAdapter.fontSize(19.6 / 14),
//                           color: AppColors.Color666,
//                         ),
//                       ),
//                     ),
//                     widget.index != controller.remindList.length - 1
//                         ? pressureRemindLine(margin: 16)
//                         : SizedBox(height: ScreenAdapter.height(16),)
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
