// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-10-09 15:18:11
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-10-09 16:57:21
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/weight/weight_reminder/reminder_home/bindings/reminder_home_binding.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:get/get.dart';

// import '../controllers/reminder_home_controller.dart';

// class ReminderHomeBinding extends Bindings {
//   @override
//   void dependencies() {
//     Get.lazyPut<ReminderHomeController>(
//       () => ReminderHomeController(),
//     );
//   }
// }
