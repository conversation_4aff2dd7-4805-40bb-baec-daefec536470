// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-10-09 14:34:36
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-10-09 14:48:05
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/weight/weight_target/widgets/target_pie.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/widgets.dart';

// class TargetPie extends StatelessWidget with BaseWidgetMixin {
//   final List<String> paths = [
//     Assets.images.weightTargetPie1.path,
//     Assets.images.weightTargetPie2.path,
//     Assets.images.weightTargetPie3.path,
//     Assets.images.weightTargetPie4.path,
//   ];
//   final int index;
//   TargetPie({super.key, required this.index});

//   @override
//   Widget body(BuildContext context) {
//     return Container(
//       width: ScreenAdapter.width(308),
//       height: ScreenAdapter.width(308),
//       child: Image.asset(paths[index]),
//     );
//   }
// }
