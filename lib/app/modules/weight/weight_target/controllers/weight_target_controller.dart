// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-10-09 14:19:29
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-10-09 14:23:45
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/weight/weight_target/controllers/weight_target_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:get/get.dart';

// class WeightTargetController extends BaseController {
//   //TODO: Implement WeightTargetController

//   final count = 0.obs;
//   @override
//   void onInit() {
//     super.onInit();
//   }

//   @override
//   void onReady() {
//     super.onReady();
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   void increment() => count.value++;
// }
