/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-02-28 17:06:56
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-02-28 17:33:08
 * @FilePath: /RPM-APP/lib/app/modules/account_security/controllers/account_security_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/data/repository/auth_repository_impl.dart';
import 'package:get/get.dart';

class AccountSecurityController extends BaseController {
  AuthRepositoryImpl authRepositoryImpl = AuthRepositoryImpl();

  void deleteAccount() async {
    showLoading();

    String text = await authRepositoryImpl.deleteAccount();
    Get.snackbar("prompt", text);

    hideLoading();
  }
}
