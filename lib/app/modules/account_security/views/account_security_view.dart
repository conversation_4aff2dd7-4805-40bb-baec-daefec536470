/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-02-28 17:11:16
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-15 16:31:51
 * @FilePath: /RPM-APP/lib/app/modules/account_security/views/account_security_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/modules/account_security/controllers/account_security_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

class AccountSecurityView extends BaseView<AccountSecurityController> {
  AccountSecurityView({
    super.key,
  }) : super(
          // ltrb
          bgColor: AppColors.colorWhite,
          statusBarColor: AppColors.colorWhite,
  );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: T.userAccountSecurity.tr,
      // customHeight: ScreenAdapter.height(31),
      // appBarColor: AppColors.colorWhite,
    );
  }

  @override
  Widget body(BuildContext context) {
    // TODO: implement body
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16),vertical: ScreenAdapter.height(16)),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              logger.d("点击删除上号");
              showDialogDelete();
            },
            child: Container(
              height: ScreenAdapter.height(28),
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          color: AppColors.homeBgColor,
                          width: 1,
                          style: BorderStyle.solid))),
              padding: EdgeInsets.only(bottom: ScreenAdapter.height(8)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    T.asDeleteAccount.tr,
                    style:
                        normalF14H19C333.copyWith(fontWeight: FontWeight.w600),
                  ),
                  SizedBox.square(
                    dimension: ScreenAdapter.height(12),
                    child: SvgPicture.asset(Assets.images.rightArrow48),
                  )
                ],
              ),
            ),
          ),
          Expanded(child: SizedBox()),
          logOutButton(),
          Gap(ScreenAdapter.height(12))
        ],
      ),
    );
  }

  void showDialogLogOut() {
    // Get.back();
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Center(
                child: Text(T.wordsPrompt.tr,
                    style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(20),
                        height: 28 / 20.0,
                        color: AppColors.Color333,
                        fontWeight: FontWeight.w500))),
            titlePadding: EdgeInsets.only(
              top: ScreenAdapter.height(16),
            ),
            contentPadding: EdgeInsets.only(
                top: ScreenAdapter.height(10),
                left: ScreenAdapter.width(20),
                right: ScreenAdapter.width(20)),
            actionsPadding: EdgeInsets.only(
                top: ScreenAdapter.height(24),
                bottom: ScreenAdapter.height(10),
                left: ScreenAdapter.width(32),
                right: ScreenAdapter.width(32)),
            content: Text(
              T.asLogOutP.tr,
              style: normalF16H22C666.copyWith(color: AppColors.Color333),
            ),
            actions: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                      onTap: () {
                        Navigator.of(context).pop(); // 关闭对话框
                        controller.authRepositoryImpl.logout();
                        // Get.back();
                        
                      },
                      child: Container(
                        padding: EdgeInsets.all(1),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          gradient: LinearGradient(
                            colors: [
                              Color(0xFF50DCFF),
                              Color(0xFF2278FF)
                            ], // 渐变颜色
                            begin: Alignment.topLeft,
                            end: Alignment.topRight,
                          ),
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24),
                              color: Colors.white),
                          height: ScreenAdapter.height(30),
                          padding: EdgeInsets.symmetric(
                              horizontal: ScreenAdapter.width(32)),
                          child: Center(
                            child: Text(
                              T.wordsYes.tr,
                              style: normalF14H19C333.copyWith(
                                  color: AppColors.lightBlue),
                            ),
                          ),
                        ),
                      )),
                  Gap(ScreenAdapter.width(24)),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pop(); // 关闭对话框
                    },
                    child: Container(
                      height: ScreenAdapter.height(32),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24),
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFF57EBFF),
                            // Color(0xFF2278FF)
                            AppColors.lightBlue
                          ], // 渐变颜色
                          begin: Alignment.topLeft,
                          end: Alignment.topRight,
                        ),
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: ScreenAdapter.width(32)),
                      child: Center(
                        child: Text(
                          T.wordsCancel.tr,
                          style: normalF14H19C333.copyWith(
                              color: AppColors.colorWhite),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          );
        });
  }

  void showDialogDelete() {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Center(
                child: Text(T.wordsPrompt.tr,
                    style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(20),
                        height: 28 / 20.0,
                        color: AppColors.Color333,
                        fontWeight: FontWeight.w500))),
            titlePadding: EdgeInsets.only(
              top: ScreenAdapter.height(16),
            ),
            contentPadding: EdgeInsets.only(
                top: ScreenAdapter.height(10),
                left: ScreenAdapter.width(20),
                right: ScreenAdapter.width(20)),
            actionsPadding: EdgeInsets.only(
                top: ScreenAdapter.height(24),
                bottom: ScreenAdapter.height(10),
                left: ScreenAdapter.width(32),
                right: ScreenAdapter.width(32)),
            content: Text(
              T.asDeleteAccountP.tr,
              style: normalF16H22C666.copyWith(color: AppColors.Color333),
            ),
            actions: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                      onTap: () {
                        Navigator.of(context).pop(); // 关闭对话框
                        controller.deleteAccount();
                      },
                      child: Container(
                        padding: EdgeInsets.all(1),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          gradient: LinearGradient(
                            colors: [
                              Color(0xFF50DCFF),
                              Color(0xFF2278FF)
                            ], // 渐变颜色
                            begin: Alignment.topLeft,
                            end: Alignment.topRight,
                          ),
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24),
                              color: Colors.white),
                          height: ScreenAdapter.height(30),
                          padding: EdgeInsets.symmetric(
                              horizontal: ScreenAdapter.width(32)),
                          child: Center(
                            child: Text(
                              T.wordsYes.tr,
                              style: normalF14H19C333.copyWith(
                                  color: AppColors.lightBlue),
                            ),
                          ),
                        ),
                      )),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pop(); // 关闭对话框
                    },
                    child: Container(
                      height: ScreenAdapter.height(32),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24),
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFF57EBFF),
                            // Color(0xFF2278FF)
                            AppColors.lightBlue
                          ], // 渐变颜色
                          begin: Alignment.topLeft,
                          end: Alignment.topRight,
                        ),
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: ScreenAdapter.width(32)),
                      child: Center(
                        child: Text(
                          T.wordsCancel.tr,
                          style: normalF14H19C333.copyWith(
                              color: AppColors.colorWhite),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          );
        });
  }

  Widget logOutButton() {
    return InkWell(
      onTap: () {
        showDialogLogOut();
      },
      child: Container(
        width: ScreenAdapter.width(255),
        height: ScreenAdapter.height(38),
        // color: Colors.black,
        decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft, // 从左到右
              end: Alignment.centerRight, // 结束方向为右边
              colors: [
                Color(0xFF57EBFF), // #57EBFF
                Color(0xFF1B6BFF), // #1B6BFF
              ],
            ),
            borderRadius: BorderRadius.circular(24)),
        alignment: Alignment.center,
        child: Text(
          T.loginOut.tr,
          style: normalF16H22C666.copyWith(
              color: AppColors.colorWhite, fontWeight: FontWeight.w500),
        ),
      ),
    );
  }
}
