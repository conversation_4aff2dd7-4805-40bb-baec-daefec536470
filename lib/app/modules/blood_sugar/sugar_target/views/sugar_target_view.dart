// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import '../controllers/sugar_target_controller.dart';

// class SugarTargetView extends GetView<SugarTargetController> {
//   const SugarTargetView({Key? key}) : super(key: key);
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('SugarTargetView'),
//         centerTitle: true,
//       ),
//       body: const Center(
//         child: Text(
//           'SugarTargetView is working',
//           style: TextStyle(fontSize: 20),
//         ),
//       ),
//     );
//   }
// }
