// import 'package:get/get.dart';

// class SugarTargetController extends GetxController {
//   //TODO: Implement SugarTargetController

//   final count = 0.obs;
//   @override
//   void onInit() {
//     super.onInit();
//   }

//   @override
//   void onReady() {
//     super.onReady();
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   void increment() => count.value++;
// }
