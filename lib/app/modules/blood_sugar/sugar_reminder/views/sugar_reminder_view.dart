// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import '../controllers/sugar_reminder_controller.dart';

// class SugarReminderView extends GetView<SugarReminderController> {
//   const SugarReminderView({Key? key}) : super(key: key);
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('SugarReminderView'),
//         centerTitle: true,
//       ),
//       body: const Center(
//         child: Text(
//           'SugarReminderView is working',
//           style: TextStyle(fontSize: 20),
//         ),
//       ),
//     );
//   }
// }
