// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-11-25 15:47:37
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-27 17:07:14
//  * @FilePath: /rpmappmaster/lib/app/modules/blood_sugar/sugar_home/views/sugar_home_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/modules/blood_sugar/sugar_home/widgets/sugar_pie_chart.dart';
// import 'package:aiCare/app/modules/home/<USER>/custom_home_bar.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import '../controllers/sugar_home_controller.dart';

// class SugarHomeView extends BaseView<SugarHomeController> {
//     SugarHomeView({
//     super.key,
//   }) : super(
//           parentPaddings: [0, 0, 0, 0],
//           bgColor: AppColors.homeBgColor,
//         );

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return null;
//   }

//     @override
//   Widget body(BuildContext context) {
//     return SingleChildScrollView(
//       child: ConstrainedBox(
//         constraints: BoxConstraints(
//           minHeight: ScreenAdapter.height(812-44), // Set the minimum height
//         ),
//         child: Column(
//           children: [
//             CustomHomeBar(),
//           SugarPieChart(),
          
//           // OxygenPieChart(),
//           // OxygenLineChart(),
//           // CustomDomain(list: controller.domainList),
//           // OxygenDomain(),
//         ],
//       ),
//     ));
//   }
  
// }
