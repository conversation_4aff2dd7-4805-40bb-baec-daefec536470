// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-11-25 15:47:37
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-26 15:07:20
//  * @FilePath: /rpmappmaster/lib/app/modules/blood_sugar/sugar_home/controllers/sugar_home_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:get/get.dart';

// class SugarHomeController extends BaseController {
//   //TODO: Implement SugarHomeController

//   final count = 0.obs;
//   @override
//   void onInit() {
//     super.onInit();
//   }

//   @override
//   void onReady() {
//     super.onReady();
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   void increment() => count.value++;
// }
