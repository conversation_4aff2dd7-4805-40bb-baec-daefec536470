// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-11-26 15:08:57
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-26 15:46:22
//  * @FilePath: /rpmappmaster/lib/app/modules/blood_sugar/sugar_home/widgets/sugar_pie_chart.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';

// class SugarPieChart extends StatelessWidget {
//   const SugarPieChart({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       width: ScreenAdapter.width(343),
//       height: ScreenAdapter.height(288),
//       decoration: BoxDecoration(
//         borderRadius:
//             BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
//         color: AppColors.colorWhite,
//       ),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//         Container(
//           width: ScreenAdapter.width(240),
//           height: ScreenAdapter.height(240),
//           child: Stack(
//             alignment: Alignment.center,
//             children: [
//               Image.asset(Assets.images.sugarPieBg.path)
//             ],
//           ),
//         )
//       ],)
//     );
//   }
// }