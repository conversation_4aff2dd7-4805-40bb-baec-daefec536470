// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import '../controllers/sugar_record_controller.dart';

// class SugarRecordView extends GetView<SugarRecordController> {
//   const SugarRecordView({Key? key}) : super(key: key);
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('SugarRecordView'),
//         centerTitle: true,
//       ),
//       body: const Center(
//         child: Text(
//           'SugarRecordView is working',
//           style: TextStyle(fontSize: 20),
//         ),
//       ),
//     );
//   }
// }
