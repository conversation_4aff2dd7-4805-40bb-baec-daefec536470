import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/modules/signup/controllers/signup_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

class SignupErrorText extends StatelessWidget with BaseWidgetMixin {
  final SignupController controller = Get.find();
  SignupErrorText({super.key});

  @override
  Widget body(BuildContext context) {
    return Obx(() => controller.errorText.isNotEmpty
        ? SizedBox(
            height: ScreenAdapter.height(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  'assets/images/error_info.svg',
                  semanticsLabel: 'My Icon',
                  width: ScreenAdapter.width(16),
                  height: ScreenAdapter.height(16),
                ),
                SizedBox(width: ScreenAdapter.width(4)),
                Text(
                  controller.errorText.value,
                  style: TextStyle(
                      color: AppColors.errorTextColor,
                      fontSize: ScreenAdapter.fontSize(12),
                      height: 1.0),
                )
              ],
            ),
          )
        : SizedBox(
            height: ScreenAdapter.height(16),
          ));
  }
}
