import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/modules/signup/controllers/signup_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SignupButton extends StatelessWidget with BaseWidgetMixin {
  final SignupController controller = Get.find();
  SignupButton({super.key});

  @override
  Widget body(BuildContext context) {
    return //登录
        SizedBox(
      width: ScreenAdapter.width(285),
      height: ScreenAdapter.height(47),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: StadiumBorder(
            side: BorderSide(
              color:
                  AppColors.loginSubTitleColor, // Specify the border color here
              width: ScreenAdapter.width(2), // Specify the border width here
            ),
          ),
          backgroundColor: Colors.white,
        ),
        onPressed: () {
          controller.doSignup();
        },
        child: Text(
          T.signUp.tr,
          style: TextStyle(
            color: AppColors.loginButtonBgColor,
            fontWeight: FontWeight.bold,
            fontSize: ScreenAdapter.fontSize(16),
          ),
        ),
      ),
    );
  }
}
