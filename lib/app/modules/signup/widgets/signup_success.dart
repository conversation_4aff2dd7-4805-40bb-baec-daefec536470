import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/gen/assets.gen.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:aiCare/app/services/screenAdapter.dart';

class SignupSuccess extends StatelessWidget with BaseWidgetMixin {
  SignupSuccess({
    super.key,
  });

  @override
  Widget body(BuildContext context) {
    return FutureBuilder(
        future: Future.delayed(Duration(seconds: 3), () {
          Get.offNamed(Routes.LOGIN);
        }),
        builder: (context, snapshot) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Center(
                  child: SizedBox(
                      height: ScreenAdapter.height(180),
                      width: ScreenAdapter.width(180),
                      // color: Colors.grey, // 添加背景颜色以便更好地看到容器的边界
                      child: Center(
                        child: Image.asset(
                          Assets.images.checkCircle.path,
                          width: ScreenAdapter.width(105),
                          height: ScreenAdapter.height(125),
                        ),
                      ))),
              Center(
                child: Text(
                  T.signUpSuccess.tr,
                  style: TextStyle(
                    fontSize: ScreenAdapter.fontSize(16),
                  ),
                ),
              ),
            ],
          );
        });
  }
}
