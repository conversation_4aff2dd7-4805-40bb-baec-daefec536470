import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/modules/signup/controllers/signup_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:super_tooltip/super_tooltip.dart';

class SignupInput extends StatelessWidget {
  final SignupController controller = Get.find();
   RxInt index=0.obs;
  final String hintText;
  final TextEditingController textController;

  SignupInput({
    super.key,
    required this.index,
    required this.hintText,
    required this.textController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: ScreenAdapter.height(60),
      decoration: BoxDecoration(
        color: Colors.black12,
        borderRadius: BorderRadius.circular(ScreenAdapter.width(12)),
      ),
      child: Obx(() {
        return TextField(
          controller: textController,
          obscureText: _getObscureText(),
          cursorColor: Colors.black,
          decoration: InputDecoration(
            hintText: hintText,
            border: InputBorder.none,
            suffixIcon: _getSuffixIcon(),
            contentPadding: EdgeInsets.fromLTRB(
              ScreenAdapter.width(10),
              ScreenAdapter.height(20),
              ScreenAdapter.width(10),
              ScreenAdapter.height(20),
            ),
          ),
        );
      }),
    );
  }

  bool _getObscureText() {
    switch (index.value) {
      case 0:
        return false; // 默认第一个输入框（如密码）需要隐藏文本
      case 1:
        return !controller.passwordVisible.value; // 根据密码可见状态切换
      case 2:
        return !controller.confirmPasswordVisible.value; // 根据确认密码可见状态切换
      default:
        return false;
    }
  }

  Widget _getSuffixIcon() {
    if (index.value == 0) return const SizedBox();

    if (index.value == 1) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: <Widget>[
          SuperTooltip(
            content: Text(
              T.passwordRegular.tr,
              style: const TextStyle(color: AppColors.textColorWhite),
            ),
            backgroundColor: Colors.black26,
            shadowColor: Colors.black26,
            arrowTipDistance: ScreenAdapter.height(15),
            child: const Icon(CupertinoIcons.exclamationmark_circle),
          ),
          IconButton(
            onPressed: () {
              controller.passwordVisible.value =
                  !controller.passwordVisible.value;
            },
            icon: Obx(() => controller.passwordVisible.value
                ? const Icon(Icons.visibility)
                : const Icon(Icons.visibility_off)),
          ),
        ],
      );
    } else {
      return IconButton(
        icon: Obx(() => controller.confirmPasswordVisible.value
            ? const Icon(Icons.visibility)
            : const Icon(Icons.visibility_off)),
        onPressed: () {
          controller.confirmPasswordVisibleChange();
        },
      );
    }
  }
}