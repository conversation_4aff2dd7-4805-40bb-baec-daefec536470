/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-23 14:57:38
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-08-16 13:45:58
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/signup/views/signup_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/app_styles.dart';
import 'package:aiCare/app/core/widget/user_agreement.dart';
import 'package:aiCare/app/modules/signup/widgets/signup_button.dart';
import 'package:aiCare/app/modules/signup/widgets/signup_error_text.dart';
import 'package:aiCare/app/modules/signup/widgets/signup_input.dart';
import 'package:aiCare/app/modules/signup/widgets/signup_success.dart';

import 'package:flutter/material.dart';

import 'package:aiCare/app/core/base/view/base_view.dart';

import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:get/get.dart';

import '../controllers/signup_controller.dart';

class SignUpView extends BaseView<SignupController> {
  SignUpView({
    super.key,
  });

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: T.signUp.tr,
      // customHeight: ScreenAdapter.height(65),
      // bottomWidget: lineLightGreey,
      // appBarColor: AppColors.colorWhite,
    );
    // return null;
  }

  @override
  Widget body(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      child: Obx(() => controller.showSuccess.value
        ? SignupSuccess()
        : Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              SizedBox(height: ScreenAdapter.height(120)),
              SignupInput(
                index: 0.obs,
                hintText: T.emailHintText.tr,
                textController: controller.accountController,
              ),
              SizedBox(
                height: ScreenAdapter.height(20),
              ),
              SignupInput(
                index: 1.obs,
                hintText: T.passwordHintText.tr,
                textController: controller.passwordController,
              ),
              SizedBox(
                height: ScreenAdapter.height(20),
              ),
              SignupInput(
                index: 2.obs,
                hintText: T.passwordConfirmHintText.tr,
                textController: controller.confirmPasswordController,
              ),
              SizedBox(
                height: ScreenAdapter.height(20),
              ),
              SignupErrorText(),
              UserAgreement(controller: controller),
              SizedBox(height: ScreenAdapter.height(40)),
              SignupButton(),
            ],
          )),
    );
  }
}
