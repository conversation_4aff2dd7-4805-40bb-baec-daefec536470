import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/data/repository/auth_repository_impl.dart';

class SignupController extends BaseController {
  //Authentication repository; Used to handle network interfaces related to login
  AuthRepositoryImpl authRepositoryImpl = AuthRepositoryImpl();

  //Account, password and confirm passord text controller
  TextEditingController accountController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();

  //Determine whether the user agreement has been read,
  //and whether to log in using a phone number or email address
  RxBool userReadPolicy = false.obs;
  RxBool phoneOrEmail = false.obs;

  //Are password boxes and duplicate password boxes visible
  RxBool passwordVisible = false.obs;
  RxBool confirmPasswordVisible = false.obs;

  //Error prompt text
  RxString errorText = "".obs;

  //Is the registration success page displayed
  RxBool showSuccess = false.obs;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }

  /**
   * @description: Set the value of whether to read the user agreement
   * @return null
   */
  setUserReadPolic(newValue) async {
    userReadPolicy.value = newValue;
  }

  /**
   * @description: Verify whether the password complies with the verification rules
   * @return bool
   */
  bool passwordValidate(String password) {
    RegExp lowercase = new RegExp(r"(?=.*[a-z])\w+");
    RegExp uppercase = new RegExp(r"(?=.*[A-Z])\w+");
    RegExp num = new RegExp(r"(?=.*[0-9])\w+");
    RegExp special = new RegExp(r"(?=.*[!@#$%^&*?])\w+");
    bool hasLowercase = lowercase.hasMatch(password);
    bool hasUppercase = uppercase.hasMatch(password);
    bool hasNum = num.hasMatch(password);
    bool hasSpecial = special.hasMatch(password);
    if ((hasLowercase && hasUppercase && hasNum) ||
        (hasLowercase && hasUppercase && hasSpecial) ||
        (hasLowercase && hasNum && hasSpecial) ||
        (hasUppercase && hasNum && hasSpecial)) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * @description: Used to handle registration button click events
   * @return {*}
   */
  doSignup() async {
    var emailNull = GetUtils.isNullOrBlank(accountController.value.text);

    var passwordNull = GetUtils.isNullOrBlank(passwordController.value.text);

    var confirmPasswordNull =
        GetUtils.isNullOrBlank(confirmPasswordController.value.text);
    if (emailNull!) {
      errorText.value = T.emailError.tr;
      return false;
    } else if (!GetUtils.isEmail(accountController.value.text)) {
      errorText.value = T.emailError.tr;
      return false;
    } else {
      if (passwordNull!) {
        errorText.value = (T.passwordError.tr);
        return false;
      } else if (passwordController.value.text.length < 8) {
        errorText.value = T.passwordLengthError.tr;
        return false;
      } else if (!passwordValidate(passwordController.value.text)) {
        errorText.value = T.passwordError.tr;
        return false;
      }
      if (confirmPasswordNull!) {
        errorText.value = T.passwordConfirmError.tr;
        return false;
      } else if (passwordController.value.text !=
          confirmPasswordController.value.text) {
        errorText.value = T.passwordNotMatch;
        return false;
      }
    }
    if (userReadPolicy.value == false) {
      errorText.value = T.commonCheckPolicy.tr;
      return false;
    } else {
      callDataService(
          authRepositoryImpl.signUp(
              accountController.value.text, passwordController.value.text),
          onSuccess: (response) {
        // 检查响应中是否包含 user_exists 错误
        if (response.toString().contains('user_exists')) {
          // 用户已存在，显示错误信息
          errorText.value = T.userAlreadyExists.tr;
          logger.d("用户已存在：" + response.toString());
        } else {
          // 注册成功
          showSuccess.value = true;
          logger.d("注册成功：" + response.toString());
        }
        // showSuccess.value = true;
        // logger.d("注册成功："+response.toString());
      }, onError: (error) {
        // errorText.value = T.signUpError.tr;
        logger.d("注册失败：" + error.toString());
      }, onComplete: () {
        logger.d("注册完成：");
        hideLoading();
      });
      //注册请求
      // bool result = await authRepositoryImpl.signUp(
      //     accountController.value.text, passwordController.value.text);
      // if (result) {
      //   showSuccess.value = result;
      // } else {
      //   errorText.value = T.signUpError.tr;
      // }
    }
  }

  /**
   * @description: Password box switch visibility
   * @return {*}
   */
  passwordVisibleChange() {
    passwordVisible.value = !passwordVisible.value;
  }

  /**
   * @description: Confirm Password box switch visibility
   * @return {*}
   */
  confirmPasswordVisibleChange() {
    confirmPasswordVisible.value = !confirmPasswordVisible.value;
  }
}
