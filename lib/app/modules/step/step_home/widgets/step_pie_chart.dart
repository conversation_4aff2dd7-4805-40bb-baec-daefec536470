// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/widget/bluetooth/custom_bluetooth_icon.dart';
// import 'package:aiCare/app/modules/step/step_home/controllers/step_home_controller.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';

// class StepsPieChart extends StatefulWidget {
//   const StepsPieChart({super.key});

//   @override
//   State<StatefulWidget> createState() => StepsPieChartState();
// }

// class StepsPieChartState extends State<StepsPieChart> {
//   final StepHomeController controller = Get.find();

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
//       width: ScreenAdapter.width(343),
//       height: ScreenAdapter.height(288),
//       decoration: BoxDecoration(
//         borderRadius:
//             BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
//         color: AppColors.colorWhite,
//       ),
//       child: Stack(
//         children: [
//           CustomBluetoothIcon(),

//           //水滴图
//           Positioned(
//               top: ScreenAdapter.height(20),
//               // left: ScreenAdapter.width(112),
//               left: 0,
//               right: 0,
//               child: Column(
//                 children: [
//                   SizedBox(
//                     height: ScreenAdapter.height(215),
//                     child: AspectRatio(
//                       aspectRatio: 1,
//                       child: Image.asset(Assets.images.stepPieNo.path),
//                     ),
//                   ),

//                   // Obx(()=> CustomLiquid(
//                   //     amplitude: !controller.homeController.oxygenData.value.isNull() ? controller.homeController.oxygenData.value.percentage!.toDouble() :0,
//                   //     // amplitude: controller.lastValue.value.percentage!=null? 90 :0,
//                   //     parentWidth: 151,
//                   //     childWidth: 137,
//                   //     dataStyle: TextStyle(
//                   //         fontWeight: FontWeight.w500,
//                   //         fontSize: ScreenAdapter.fontSize(36),
//                   //         height: 42.19 / 36,
//                   //         color: AppColors.colorWhite),
//                   //     parentBdWidth: 2)),
//                   // Container(
//                   //   // margin: EdgeInsets.only(top: ScreenAdapter.height(59)),
//                   //   width: ScreenAdapter.width(151),
//                   //   height: ScreenAdapter.height(151),
//                   //   // margin: EdgeInsets.symmetric(vertical: ScreenAdapter.height(20)),
//                   //   child: Echarts(
//                   //     extensions: [liquidScript],
//                   //     option: controller.option.value,
//                   //   ),
//                   // ),

//                   Container(
//                       width: ScreenAdapter.width(92),
//                       height: ScreenAdapter.height(14),
//                       margin: EdgeInsets.only(top: ScreenAdapter.height(6)),
//                       child: Row(
//                         children: [
//                           // Obx(()=> !controller.homeController.oxygenData.value.isNull() ? Text(
//                           //   DateFormat('yyyy-MM-dd').format(controller.homeController.oxygenData.value.date!),
//                           //   style: TextStyle(
//                           //     // fontFamily: "PingFang SC",
//                           //     fontSize: ScreenAdapter.fontSize(10),
//                           //     height: ScreenAdapter.height(14 / 10),
//                           //     fontWeight: FontWeight.w400,
//                           //     color: AppColors.Color666,
//                           //   ),
//                           // ):SizedBox()),

//                           // Expanded(child: Container()),
//                           // Obx(()=> !controller.homeController.oxygenData.value.isNull() ? Text(
//                           //   DateFormat('HH:mm').format(controller.homeController.oxygenData.value.date!),
//                           //   style: TextStyle(
//                           //     // fontFamily: "PingFang SC",
//                           //    fontSize: ScreenAdapter.fontSize(10),
//                           //     height: ScreenAdapter.height(14 / 10),
//                           //     fontWeight: FontWeight.w400,
//                           //     color: AppColors.Color666,
//                           //   ),
//                           // ):SizedBox()),
//                         ],
//                       )),
//                 ],
//               )),

//           Positioned(
//               top: ScreenAdapter.height(104),
//               left: 0,
//               right: 0,
//               child: Center(
//                 child: Text(
//                   "--",
//                   style: TextStyle(
//                       fontWeight: FontWeight.w500,
//                       fontSize: ScreenAdapter.fontSize(36),
//                       color: AppColors.Color666),
//                 ),
//               )),
//               Positioned(
//               top: ScreenAdapter.height(127),
//               right: ScreenAdapter.width(105),
//               child: Center(
//                 child: Text(
//                   controller.appLocalization.steps,
//                   style: TextStyle(
//                       fontWeight: FontWeight.w500,
//                       fontSize: ScreenAdapter.fontSize(12),
//                       color: AppColors.Color666),
//                 ),
//               )),
//               Positioned(
//               top: ScreenAdapter.height(148),
//               // right: ScreenAdapter.width(105),
//               left: 0,
//               right: 0,

//               child: Center(
//                 child: Container(
//                   padding: EdgeInsets.symmetric(
//                     horizontal: ScreenAdapter.width(16),
//                     vertical: ScreenAdapter.height(2)
//                   ),
//                   decoration: BoxDecoration(
//                     border: Border.all(
//                       color: AppColors.Color999
//                     ),
//                     color: AppColors.Color999.withOpacity(0.08),
//                     borderRadius: BorderRadius.circular(10)
//                   ),
//                   child: Text(
//                     "--",
//                     style: TextStyle(
//                       fontWeight: FontWeight.w500,
//                       fontSize: ScreenAdapter.fontSize(10),
//                       color: AppColors.Color999
//                     ),
//                   ),
//                 ),
//               )),
//         ],
//       ),
//     );
//   }
// }
