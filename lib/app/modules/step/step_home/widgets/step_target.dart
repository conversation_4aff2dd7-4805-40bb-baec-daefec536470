// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2025-03-26 07:16:13
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-03-26 13:43:14
//  * @FilePath: /RPM-APP/lib/app/modules/step/step_home/widgets/step_target.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';

// class StepsTarget extends StatelessWidget with BaseWidgetMixin {
//   StepsTarget({super.key});

//   @override
//   Widget body(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.symmetric(
//           vertical: ScreenAdapter.height(8),
//           horizontal: ScreenAdapter.width(16)),
//       decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(8), color: Colors.white),
//       child: Column(
//         // mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Flex(
//             direction: Axis.horizontal,
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Padding(
//                 padding: EdgeInsets.only(
//                     top: ScreenAdapter.height(10.5),
//                     left: ScreenAdapter.width(10),
//                     right: ScreenAdapter.width(4)),
//                 child: Text(
//                   appLocalization.stepsTargetProgress,
//                   style: normalF12H17C999.copyWith(
//                       color: AppColors.Color333, fontWeight: FontWeight.w500),
//                 ),
//               ),
//               Container(
//                 margin: EdgeInsets.only(top: ScreenAdapter.height(10)),
//                 height: ScreenAdapter.height(18),
//                 child: AspectRatio(
//                   aspectRatio: 1,
//                   child: Image.asset(
//                     Assets.images.healthGoalIcon.path,
//                     fit: BoxFit.fitHeight,
//                   ),
//                 ),
//               ),
//               Expanded(child: SizedBox()),
//               InkWell(
//                 onTap: () {
//                   logger.d("去目标设置页面");
//                 },
//                 child: Padding(
//                   padding: EdgeInsets.only(
//                     right: ScreenAdapter.width(10),
//                     top: ScreenAdapter.height(12),
//                   ),
//                   child: Text(
//                     appLocalization.commonModifyTarget,
//                     style: normalF12H17C333.copyWith(
//                         fontWeight: FontWeight.w500,
//                         color: AppColors.lightBlue),
//                   ),
//                 ),
//               )
//             ],
//           ),
//           Container(
//               margin: EdgeInsets.only(
//                   top: ScreenAdapter.height(18), left: ScreenAdapter.width(10)),
//               // alignment: Alignment.bottomCenter,
//               child: RichText(
//                 text: TextSpan(children: [
//                   TextSpan(
//                       text: "--",
//                       style: TextStyle(
//                           fontSize: ScreenAdapter.fontSize(15),
//                           height: 21.0 / 15,
//                           fontWeight: FontWeight.w500,
//                           color: AppColors.Color666)),
//                   WidgetSpan(
//                     child: SizedBox(width: ScreenAdapter.width(2)),
//                   ),
//                   TextSpan(
//                       text: "/",
//                       style: normalF12H17C999.copyWith(
//                           color: AppColors.Color666,
//                           fontWeight: FontWeight.w500)),
//                   WidgetSpan(
//                     child: SizedBox(width: ScreenAdapter.width(2)),
//                   ),
//                   TextSpan(
//                       text: "--",
//                       style: normalF12H17C999.copyWith(
//                           color: AppColors.Color666,
//                           fontWeight: FontWeight.w500)),
//                   TextSpan(
//                       text: appLocalization.steps,
//                       style: normalF12H17C666.copyWith(
//                           fontWeight: FontWeight.w500)),
//                 ]),
//               )),
//           Container(
//             width: double.infinity,
//             height: ScreenAdapter.height(12),
//             margin: EdgeInsets.symmetric(
//                 vertical: ScreenAdapter.height(12),
//                 horizontal: ScreenAdapter.width(10)),
//             decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(12),
//                 color: AppColors.ColorEF),
//           ),
//           Container(
//             margin: EdgeInsets.only(
//                 top: ScreenAdapter.height(10),
//                 left: ScreenAdapter.width(10),
//                 right: ScreenAdapter.width(10)),
//             height: ScreenAdapter.height(1),
//             color: AppColors.homeBgColor,
//           ),
//           Container(
//             padding: EdgeInsets.symmetric(
//               vertical: ScreenAdapter.height(8),
//             ),
//             margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
//             decoration: BoxDecoration(
//                 border: Border(
//                     bottom: BorderSide(
//                         color: AppColors.homeBgColor,
//                         width: ScreenAdapter.height(1)))),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Center(
//                   child: Flex(
//                     direction: Axis.horizontal,
//                     children: [
//                       Text(
//                         appLocalization.stepsCaloBurned,
//                         style: normalF12H17C999.copyWith(
//                             fontWeight: FontWeight.w500,
//                             color: AppColors.Color333),
//                       ),
//                       Gap(ScreenAdapter.width(4)),
//                       SizedBox(
//                         height: ScreenAdapter.height(22),
//                         child: AspectRatio(
//                           aspectRatio: 1,
//                           child: Image.asset(
//                             Assets.images.stepCalories.path,
//                             fit: BoxFit.fitHeight,
//                           ),
//                         ),
//                       )
//                     ],
//                   ),
//                 ),
//                 Gap(ScreenAdapter.height(10)),
//                 RichText(
//                     text: TextSpan(children: [
//                   TextSpan(
//                       text: "--",
//                       style: TextStyle(
//                           fontSize: ScreenAdapter.fontSize(15),
//                           height: 21.0 / 15,
//                           fontWeight: FontWeight.w500,
//                           color: AppColors.Color666)),
//                   WidgetSpan(
//                     child: SizedBox(width: ScreenAdapter.width(4)),
//                   ),
//                   TextSpan(
//                       text: appLocalization.wordsKcal, style: normalF12H17C666)
//                 ])),
//                 Gap(ScreenAdapter.height(4)),
//                 Container(
//                   width: double.infinity,
//                   height: ScreenAdapter.height(29),
//                   decoration: BoxDecoration(
//                       borderRadius: BorderRadius.circular(4),
//                       color: AppColors.ColorEF),
//                   child: Center(
//                     child: Text(
//                       "No data",
//                       style: normalF12H17C666,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
        
//           Container(
//             padding: EdgeInsets.only(
//               top: ScreenAdapter.height(8),
//               bottom: ScreenAdapter.height(18)
              
//             ),
//             margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),

//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Center(
//                   child: Flex(
//                     direction: Axis.horizontal,
//                     children: [
//                       Text(
//                         appLocalization.stepsDistance,
//                         style: normalF12H17C999.copyWith(
//                             fontWeight: FontWeight.w500,
//                             color: AppColors.Color333),
//                       ),
//                       Gap(ScreenAdapter.width(4)),
//                       SizedBox(
//                         height: ScreenAdapter.height(22),
//                         child: AspectRatio(
//                           aspectRatio: 1,
//                           child: Image.asset(
//                             Assets.images.stepDistance.path,
//                             fit: BoxFit.fitHeight,
//                           ),
//                         ),
//                       )
//                     ],
//                   ),
//                 ),
//                 Gap(ScreenAdapter.height(10)),
//                 RichText(
//                     text: TextSpan(children: [
//                   TextSpan(
//                       text: "--",
//                       style: TextStyle(
//                           fontSize: ScreenAdapter.fontSize(15),
//                           height: 21.0 / 15,
//                           fontWeight: FontWeight.w500,
//                           color: AppColors.Color666)),
//                   WidgetSpan(
//                     child: SizedBox(width: ScreenAdapter.width(4)),
//                   ),
//                   TextSpan(
//                       text: appLocalization.wordsKm, style: normalF12H17C666)
//                 ])),
//                 Gap(ScreenAdapter.height(4)),
//                 Container(
//                   width: double.infinity,
//                   height: ScreenAdapter.height(29),
//                   decoration: BoxDecoration(
//                       borderRadius: BorderRadius.circular(4),
//                       color: AppColors.ColorEF),
//                   child: Center(
//                     child: Text(
//                       "No data",
//                       style: normalF12H17C666,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
        
//         ],
//       ),
//     );
//   }
// }
