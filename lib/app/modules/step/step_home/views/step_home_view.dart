// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2025-03-24 14:19:40
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-03-26 12:45:52
//  * @FilePath: /RPM-APP/lib/app/modules/step/step_home/views/step_home_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/modules/home/<USER>/custom_home_bar.dart';
// import 'package:aiCare/app/modules/step/step_home/widgets/step_line_chart.dart';
// import 'package:aiCare/app/modules/step/step_home/widgets/step_pie_chart.dart';
// import 'package:aiCare/app/modules/step/step_home/widgets/step_target.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import '../controllers/step_home_controller.dart';

// class StepHomeView extends BaseView<StepHomeController> {
//   StepHomeView({
//     super.key,
//   }) : super(
//           parentPaddings: [0, 0, 0, 0],
//           bgColor: AppColors.homeBgColor,
//         );

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return null;
//   }

//   @override
//   Widget body(BuildContext context) {
//     return SingleChildScrollView(
//       child: ConstrainedBox(
//         constraints: BoxConstraints(
//           minHeight: ScreenAdapter.height(812-44), // Set the minimum height
//         ),
//         child: Column(
//           children: [
//             CustomHomeBar(),
//             StepsPieChart(),
//             StepsLineChart(),
//             StepsTarget(),
//             // OxygenDomain(),
//           ],
//         ),
//       ),
//     );
//   }
// }
