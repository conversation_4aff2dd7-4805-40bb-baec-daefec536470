// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2025-03-24 14:19:40
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-03-26 12:19:21
//  * @FilePath: /RPM-APP/lib/app/modules/step/step_home/controllers/step_home_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
// import 'package:get/get.dart';

// class StepHomeController extends BaseController {
//   //TODO: Implement StepHomeController
//   HomeController homeController = Get.find();
//   RxList<bool> requestParamList = [false,false,false].obs;
//    RxInt timeFrame = 0.obs;


//   @override
//   void onInit() {
//     super.onInit();
//   }

//   @override
//   void onReady() {
//     super.onReady();
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   setTimeFrame(int value) {
//     // logger.e("message");
//     timeFrame.value = value;
//     // refresh();
//     logger.d("重新设置了timeFrame");
//     // logger.d(timeFrame.value);
//   }
// }
