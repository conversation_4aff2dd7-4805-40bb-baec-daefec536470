/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-04-01 14:33:39
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-04-24 17:03:19
 * @FilePath: /RPM-APP/lib/app/modules/sleep/sleep_home/views/sleep_home_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/widget/custom_home_bar.dart';
import 'package:aiCare/app/modules/sleep/sleep_home/widgets/sleep_line_chart.dart';
import 'package:aiCare/app/modules/sleep/sleep_home/widgets/sleep_management.dart';
import 'package:aiCare/app/modules/sleep/sleep_home/widgets/sleep_pie_chart.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/sleep_home_controller.dart';

class SleepHomeView extends BaseView<SleepHomeController> {
    SleepHomeView({
    super.key,
  }) : super(
          bgColor: AppColors.homeBgColor,
          statusBarColor: Colors.white,
          
        );

  @override
  Widget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: ScreenAdapter.height(812-44), // Set the minimum height
        ),
        child: Column(
          children: [
            CustomHomeBar(),
            SleepPieChart(),
            // SleepLineChart(),
            SleepManagement()
            // CustomDomain(list: controller.domainList),
            // OxygenDomain(),
          ],
        ),
      ),
    );
  }
}
