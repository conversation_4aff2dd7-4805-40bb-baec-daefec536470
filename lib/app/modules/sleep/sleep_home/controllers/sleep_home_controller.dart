/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-04-01 14:33:39
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-04-21 16:13:00
 * @FilePath: /RPM-APP/lib/app/modules/sleep/sleep_home/controllers/sleep_home_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/data/model/aizo_sleep_data.dart';
import 'package:aiCare/app/data/repository/bluetooth_repository_impl.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/sleep/sleep_home/widgets/sleep_duration_charts.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';

class SleepHomeController extends BaseController {
  HomeController homeController = Get.find<HomeController>();
  RxList<SleepDetailChart> sleepDetails = RxList<SleepDetailChart>();
  
  @override
  void onInit() {
    super.onInit();
    // getData();
    // getStorage();
    // createSleepDurationData()
    initSleepDetails();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    
  }
  
  void initSleepDetails() async{
    logger.d("initSleepDetails");
    sleepDetails.value = await createSleepDurationData(details: homeController.aizoSleepData.value.details, parentWidth: ScreenAdapter.width(300), totalDuration: homeController.aizoSleepData.value.totalDuration - homeController.aizoSleepData.value.awakeDuration);
    
    //删除里面的 awake 数据
    sleepDetails.value.removeWhere((element) => element.model == SleepStage.awake || element.model == SleepStage.unknown || element.model == SleepStage.notWorn);
    logger.d(sleepDetails.value.toString());
  }

}
