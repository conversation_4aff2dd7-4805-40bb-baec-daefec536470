/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-04-24 16:58:36
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-14 15:05:58
 * @FilePath: /RPM-APP/lib/app/modules/sleep/sleep_home/widgets/sleep_management.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:aiCare/app/modules/sleep/sleep_home/controllers/sleep_home_controller.dart';
import 'package:aiCare/app/modules/sleep/sleep_home/widgets/sleep_duration_charts.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

class SleepManagement extends StatelessWidget with BaseWidgetMixin {
  SleepManagement({super.key});

  @override
  Widget body(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: ScreenAdapter.width(16),
          vertical: ScreenAdapter.height(12)),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Column(
        children: [
          _Title(),
          _SleepDurationCharts(),
          _TimeCards(),
          // _Naps(),
          // _BreathQuality(),
          _Holistic(),
        ],
      ),
    );
  }
}

class _Title extends StatelessWidget with BaseWidgetMixin {
  HomeController homeController = Get.find();
  @override
  Widget body(BuildContext context) {
    return Container(
      width: double.infinity,
      // height: ScreenAdapter.height(80),
      constraints: BoxConstraints(minHeight: ScreenAdapter.height(80)),
      child: Column(
        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            T.sleepManagement.tr,
            style: normalF12H17C999.copyWith(
                fontWeight: FontWeight.w500, color: AppColors.Color666),
          ),
          Container(
            margin: EdgeInsets.only(
                top: ScreenAdapter.height(4), left: ScreenAdapter.width(2)),
            child: Text(
              homeController.aizoSleepData.value.date.year != 2000
                  ? "${homeController.aizoSleepData.value.date.year}/${homeController.aizoSleepData.value.date.month.toString().padLeft(2, '0')}/${homeController.aizoSleepData.value.date.day.toString().padLeft(2, '0')}"
                  : "--",
              style: normalF12H17C999,
            ),
          ),
          Container(
              margin: EdgeInsets.only(
                  top: ScreenAdapter.height(8), left: ScreenAdapter.width(2)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        "${(homeController.aizoSleepData.value.totalDuration - homeController.aizoSleepData.value.awakeDuration) ~/ 60}h${(homeController.aizoSleepData.value.totalDuration - homeController.aizoSleepData.value.awakeDuration) % 60}min",
                        style: normalF16H22C666.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.lightBlue),
                      ),
                      Container(
                        margin: EdgeInsets.only(left: ScreenAdapter.width(6)),
                        padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(6)),
                        decoration: BoxDecoration(
                          color: Color(0xFFFCD166).withOpacity(0.15),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Color(0xFFFCD166)),
                        ),
                        child: Text(
                          T.wordsPayAttention.tr,
                          style: normalF12H17C333.copyWith(color: Color(0xFFFCD166)),
                        ),
                      )
                    ],
                  ),
                  Text(
                    T.sleepTotalDurationNight.tr,
                    style: normalF12H17C999,
                  )
                ],
              ))
        ],
      ),
    );
  }
}

class _SleepDurationCharts extends StatelessWidget with BaseWidgetMixin {
  _SleepDurationCharts({super.key});
  HomeController homeController = Get.find();
  SleepHomeController controller = Get.find();
  @override
  Widget body(BuildContext context) {
    return Container(
      width: ScreenAdapter.width(300),
      height: ScreenAdapter.height(278),
      margin: EdgeInsets.only(top: ScreenAdapter.height(24)),
      // padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      child: Obx(()=> SleepDurationChartWidget(
        details: controller.sleepDetails.value,
        startTime: homeController.aizoSleepData.value.startTime,
        endTime: homeController.aizoSleepData.value.endTime,
        heightUnit: 1/8.0,
        titleHeight: controller.sleepDetails.value.isNotEmpty ? ScreenAdapter.height(45) : 0,
        titleGap: ScreenAdapter.height(10),
        xAxisTitleOffset: ScreenAdapter.width(8),
        xAxisTitleHeight: ScreenAdapter.height(14),
        bgColor: Colors.white,

      )),
    );
  }
}

class _TimeCards extends StatelessWidget with BaseWidgetMixin {
  _TimeCards({super.key});
  HomeController homeController = Get.find();
  @override
  Widget body(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: ScreenAdapter.height(24)),
      // padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      child: Column(
        // crossAxisAlignment: Cr,
        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: ScreenAdapter.width(150),
                height: ScreenAdapter.height(120),
                decoration: BoxDecoration(
                    color: AppColors.lightBlue.withOpacity(0.04),
                    borderRadius: BorderRadius.circular(6)),
                child: Column(
                  // mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(
                          top: ScreenAdapter.height(4),
                          left: ScreenAdapter.width(8)),
                      child: Text(
                        T.wordsDeep.tr,
                        style: normalF12H17C999.copyWith(
                            color: AppColors.Color666,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
                      child: Column(
                        children: [
                          SizedBox(
                            height: ScreenAdapter.height(24),
                            child: AspectRatio(
                              aspectRatio: 1,
                              child: Image.asset(homeController
                                          .aizoSleepData.value.deepDuration >
                                      0
                                  ? Assets.images.sleepDeep.path
                                  : Assets.images.sleepDeepNone.path),
                            ),
                          ),
                          Gap(ScreenAdapter.height(8)),
                          homeController.aizoSleepData.value.deepDuration > 0
                              ? RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text:
                                            '${(homeController.aizoSleepData.value.deepDuration ~/ 60)}',
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.lightBlueText,
                                          fontWeight: FontWeight.w500,
                                          fontSize: ScreenAdapter.fontSize(16),
                                        ),
                                      ),
                                      TextSpan(
                                        text: T.wordsH.tr + " ",
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.Color666,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                            '${homeController.aizoSleepData.value.deepDuration % 60}',
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.lightBlueText,
                                          fontWeight: FontWeight.w500,
                                          fontSize: ScreenAdapter.fontSize(16),
                                        ),
                                      ),
                                      TextSpan(
                                        text: T.wordsMin.tr,
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.Color666,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : Text(
                                  "--",
                                  style: normalF12H17C999.copyWith(
                                    color: AppColors.Color666,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                          Gap(ScreenAdapter.height(8)),
                          Text(
                              homeController.aizoSleepData.value.deepDuration >
                                      0
                                  ? T.wordsNormal.tr
                                  : T.wordsData.tr,
                              style: normalF12H17C999),
                        ],
                      ),
                    )
                  ],
                ),
              ),
              Container(
                width: ScreenAdapter.width(150),
                height: ScreenAdapter.height(120),
                decoration: BoxDecoration(
                    color: AppColors.lightBlue.withOpacity(0.04),
                    borderRadius: BorderRadius.circular(6)),
                child: Column(
                  // mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(
                          top: ScreenAdapter.height(4),
                          left: ScreenAdapter.width(8)),
                      child: Text(
                        T.wordsLight.tr,
                        style: normalF12H17C999.copyWith(
                            color: AppColors.Color666,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
                      child: Column(
                        children: [
                          SizedBox(
                            height: ScreenAdapter.height(24),
                            child: AspectRatio(
                              aspectRatio: 1,
                              child: Image.asset(Assets.images.sleepLight.path),
                            ),
                          ),
                          Gap(ScreenAdapter.height(8)),
                          homeController.aizoSleepData.value.lightDuration > 0
                              ? RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text:
                                            '${(homeController.aizoSleepData.value.lightDuration ~/ 60)}',
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.lightBlueText,
                                          fontWeight: FontWeight.w500,
                                          fontSize: ScreenAdapter.fontSize(16),
                                        ),
                                      ),
                                      TextSpan(
                                        text: T.wordsH.tr + " ",
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.Color666,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                            '${homeController.aizoSleepData.value.lightDuration % 60}',
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.lightBlueText,
                                          fontWeight: FontWeight.w500,
                                          fontSize: ScreenAdapter.fontSize(16),
                                        ),
                                      ),
                                      TextSpan(
                                        text: T.wordsMin.tr,
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.Color666,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : Text(
                                  "--",
                                  style: normalF12H17C999.copyWith(
                                    color: AppColors.Color666,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                          Gap(ScreenAdapter.height(8)),
                          Text(
                              homeController.aizoSleepData.value.lightDuration >
                                      0
                                  ? T.wordsPayAttention.tr
                                  : T.wordsData.tr,
                              style: normalF12H17C999),
                        ],
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
          Gap(ScreenAdapter.height(12)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: ScreenAdapter.width(150),
                height: ScreenAdapter.height(120),
                decoration: BoxDecoration(
                    color: AppColors.lightBlue.withOpacity(0.04),
                    borderRadius: BorderRadius.circular(6)),
                child: Column(
                  // mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(
                          top: ScreenAdapter.height(4),
                          left: ScreenAdapter.width(8)),
                      child: Text(
                        T.wordsRem.tr,
                        style: normalF12H17C999.copyWith(
                            color: AppColors.Color666,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
                      child: Column(
                        children: [
                          SizedBox(
                            height: ScreenAdapter.height(24),
                            child: AspectRatio(
                              aspectRatio: 1,
                              child: Image.asset(homeController
                                          .aizoSleepData.value.remDuration >
                                      0
                                  ? Assets.images.sleepRem.path
                                  : Assets.images.sleepRemNone.path),
                            ),
                          ),
                          Gap(ScreenAdapter.height(8)),
                          homeController.aizoSleepData.value.remDuration > 0
                              ? RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text:
                                            '${(homeController.aizoSleepData.value.remDuration ~/ 60)}',
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.lightBlueText,
                                          fontWeight: FontWeight.w500,
                                          fontSize: ScreenAdapter.fontSize(16),
                                        ),
                                      ),
                                      TextSpan(
                                        text: T.wordsH.tr + " ",
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.Color666,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                            '${homeController.aizoSleepData.value.remDuration % 60}',
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.lightBlueText,
                                          fontWeight: FontWeight.w500,
                                          fontSize: ScreenAdapter.fontSize(16),
                                        ),
                                      ),
                                      TextSpan(
                                        text: T.wordsMin.tr,
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.Color666,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : Text(
                                  "--",
                                  style: normalF12H17C999.copyWith(
                                    color: AppColors.Color666,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                          Gap(ScreenAdapter.height(8)),
                          Text(
                              homeController.aizoSleepData.value.remDuration > 0
                                  ? T.wordsPayAttention.tr
                                  : T.wordsData.tr,
                              style: normalF12H17C999),
                        ],
                      ),
                    )
                  ],
                ),
              ),
              Container(
                width: ScreenAdapter.width(150),
                height: ScreenAdapter.height(120),
                decoration: BoxDecoration(
                    color: AppColors.lightBlue.withOpacity(0.04),
                    borderRadius: BorderRadius.circular(6)),
                child: Column(
                  // mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(
                          top: ScreenAdapter.height(4),
                          left: ScreenAdapter.width(8)),
                      child: Text(
                        T.wordsAwake.tr,
                        style: normalF12H17C999.copyWith(
                            color: AppColors.Color666,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
                      child: Column(
                        children: [
                          SizedBox(
                            height: ScreenAdapter.height(24),
                            child: AspectRatio(
                              aspectRatio: 1,
                              child: Image.asset(Assets.images.sleepAwake.path),
                            ),
                          ),
                          Gap(ScreenAdapter.height(8)),
                          homeController.aizoSleepData.value.awakeDuration > 0
                              ? RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text:
                                            '${(homeController.aizoSleepData.value.awakeDuration ~/ 60)}',
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.lightBlueText,
                                          fontWeight: FontWeight.w500,
                                          fontSize: ScreenAdapter.fontSize(16),
                                        ),
                                      ),
                                      TextSpan(
                                        text: T.wordsH.tr + " ",
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.Color666,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                            '${homeController.aizoSleepData.value.awakeDuration % 60}',
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.lightBlueText,
                                          fontWeight: FontWeight.w500,
                                          fontSize: ScreenAdapter.fontSize(16),
                                        ),
                                      ),
                                      TextSpan(
                                        text: T.wordsMin.tr,
                                        style: normalF12H17C999.copyWith(
                                          color: AppColors.Color666,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : Text(
                                  "--",
                                  style: normalF12H17C999.copyWith(
                                    color: AppColors.Color666,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                          Gap(ScreenAdapter.height(8)),
                          Text(
                              homeController.aizoSleepData.value.awakeDuration >
                                      0
                                  ? T.wordsPayAttention.tr
                                  : T.wordsData.tr,
                              style: normalF12H17C999),
                        ],
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}

class _Naps extends StatelessWidget with BaseWidgetMixin {
  _Naps({super.key});

  @override
  Widget body(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(minHeight: ScreenAdapter.height(90)),
      margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
      padding: EdgeInsets.only(
          top: ScreenAdapter.height(6), bottom: ScreenAdapter.height(6)),
      decoration: BoxDecoration(
        color: AppColors.greyF9Color,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        // mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: EdgeInsets.only(left: ScreenAdapter.width(8)),
            child: Text(
              T.sleepNaps.tr,
              style: normalF16H22C666.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          // Gap(ScreenAdapter.height(12)),
          Container(
            margin: EdgeInsets.only(
                top: ScreenAdapter.height(12), bottom: ScreenAdapter.height(8)),
            height: ScreenAdapter.height(24),
            child: Row(
              children: [
                Gap(ScreenAdapter.width(10)),
                AspectRatio(
                  aspectRatio: 1,
                  child: SizedBox(
                    height: ScreenAdapter.height(24),
                    child: Image.asset(Assets.images.sleepNap.path),
                  ),
                ),
                Gap(ScreenAdapter.width(12)),
                Text(
                  T.sleepTotalNap.tr,
                  style: normalF12H17C999.copyWith(
                      fontWeight: FontWeight.w600, color: AppColors.Color666),
                ),
                Expanded(child: SizedBox()),
                Text(
                  "--",
                  style: normalF12H17C999.copyWith(
                      fontWeight: FontWeight.w500, color: AppColors.Color666),
                ),
                Gap(ScreenAdapter.width(22))
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(12)),
            // height: ScreenAdapter.height(14),
            constraints: BoxConstraints(minHeight: ScreenAdapter.height(14)),
            child: Text(
              T.sleepNapNote1.tr, style: normalF12H17C999,
              softWrap: true, // 显式声明换行策略
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          )
        ],
      ),
    );
  }
}

class _BreathQuality extends StatelessWidget with BaseWidgetMixin {
  _BreathQuality({super.key});

  @override
  Widget body(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(minHeight: ScreenAdapter.height(90)),
      margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
      padding: EdgeInsets.only(
          top: ScreenAdapter.height(6), bottom: ScreenAdapter.height(12)),
      decoration: BoxDecoration(
        color: AppColors.greyF9Color,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        // mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: EdgeInsets.only(left: ScreenAdapter.width(8)),
            child: Text(
              T.sleepBreathQuality.tr,
              style: normalF16H22C666.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          // Gap(ScreenAdapter.height(12)),
          Container(
            margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
            height: ScreenAdapter.height(24),
            child: Row(
              children: [
                Gap(ScreenAdapter.width(10)),
                AspectRatio(
                  aspectRatio: 1,
                  child: SizedBox(
                    height: ScreenAdapter.height(24),
                    child: Image.asset(Assets.images.sleepBreath.path),
                  ),
                ),
                Gap(ScreenAdapter.width(12)),
                Text(
                  "0",
                  style: normalF14H19C666.copyWith(fontWeight: FontWeight.w600),
                ),
                Gap(ScreenAdapter.width(12)),
                Text(
                  "--",
                  style: normalF12H17C999.copyWith(
                      fontWeight: FontWeight.w500, color: AppColors.Color666),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: ScreenAdapter.height(2)),
            padding: EdgeInsets.only(left: ScreenAdapter.width(10)),
            child: Text(
              "--",
              style: normalF16H22C666.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
            margin: EdgeInsets.only(top: ScreenAdapter.height(4)),
            // height: ScreenAdapter.height(14),
            // constraints: BoxConstraints(minHeight: ScreenAdapter.height(14)),
            child: Text(
              T.sleepOxygenDuring.tr,
              style: normalF12H17C999,
            ),
          ),
          Container(
            width: double.infinity,
            padding:
                EdgeInsets.symmetric(horizontal: ScreenAdapter.width(36.5)),
            // padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
            margin: EdgeInsets.only(top: ScreenAdapter.height(10)),
            // height: ScreenAdapter.height(14),
            // constraints: BoxConstraints(minHeight: ScreenAdapter.height(14)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Gap(ScreenAdapter.width(36.5)),
                _subTitle(Color(0xFF22B2A1), T.wordsGood.tr),
                _subTitle(Color(0xFFCDE4FD), T.wordsNormal.tr),
                _subTitle(Color(0xFFFCD267), T.wordsPayAttention.tr),
                // Gap(ScreenAdapter.width(36.5)),
              ],
            ),
          )
        ],
      ),
    );
  }
}

Widget _subTitle(Color color, String text) {
  return SizedBox(
    child: Row(
      children: [
        Container(
          margin: EdgeInsets.only(right: ScreenAdapter.width(2)),
          width: ScreenAdapter.height(6),
          height: ScreenAdapter.height(6),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(ScreenAdapter.height(3)),
              color: color),
        ),
        Text(
          text,
          style: normalF12H17C999,
        )
      ],
    ),
  );
}

class _Holistic extends StatelessWidget with BaseWidgetMixin {
  _Holistic({super.key});

  @override
  Widget body(BuildContext context) {
    return Container();
  }
}
