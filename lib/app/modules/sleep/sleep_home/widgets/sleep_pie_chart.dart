/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-04-01 15:09:27
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-30 13:41:40
 * @FilePath: /RPM-APP/lib/app/modules/sleep/sleep_home/widgets/sleep_pie_chart.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:math';
import 'dart:ui' as ui;

import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

class SleepPieChart extends StatelessWidget with BaseWidgetMixin {
  TabsController tabsController = Get.find();
  HomeController homeController = Get.find();
  
  final double minHeight;
  final double innerHeight;
  final double iconHeight;
  final double arcWidth;
  final double chartSize;
  final bool showBottomContent;
  final TextStyle hoursMinutesStyle;
  final TextStyle unitStyle;
  final TextStyle dateStyle;
  final double textGap;

  SleepPieChart({
    super.key,
    this.minHeight = 288,
    this.innerHeight = 150,
    this.iconHeight = 32,
    this.arcWidth = 16.5,
    this.chartSize = 212,
    this.showBottomContent = true,
    this.textGap = 6,
    TextStyle? hoursMinutesStyle,
    TextStyle? unitStyle,
    TextStyle? dateStyle,
  }) : 
    this.hoursMinutesStyle = hoursMinutesStyle ?? TextStyle(
      color: AppColors.lightBlue,
      fontSize: ScreenAdapter.fontSize(32),
      fontWeight: FontWeight.w500
    ),
    this.unitStyle = unitStyle ?? normalF16H22C666.copyWith(fontWeight: FontWeight.w500),
    this.dateStyle = dateStyle ?? normalF12H17C999.copyWith(color: AppColors.Color666);

  @override
  Widget body(BuildContext context) {
    RxBool sleepDataIsNull = (homeController.aizoSleepData.value.date.year == 2000).obs;
    // logger.d("睡眠数据是否为空：$sleepDataIsNull");
    // logger.d("睡眠数据：${homeController.aizoSleepData.value.toString()}");
    // logger.d("睡眠数据：${homeController.aizoSleepData.value.totalDuration / 60 /8}");

    return Obx(() => Container(
          constraints: BoxConstraints(minHeight: ScreenAdapter.height(minHeight)),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8), color: Colors.white),
          margin: EdgeInsets.symmetric(vertical: ScreenAdapter.height(10)),
          width: double.infinity,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [

              showBottomContent ? Gap(ScreenAdapter.height(18)) : SizedBox(),

              Stack(alignment: Alignment.center, children: [
                CustomPaint(
                  painter: ArcPainter(
                    progress: sleepDataIsNull.value ? 0.0 : (homeController.aizoSleepData.value.totalDuration - homeController.aizoSleepData.value.awakeDuration) / 60 / 8,
                      arcWidth: ScreenAdapter.height(arcWidth),
                      sunImage: tabsController.sunImage.value,
                      moonImage: tabsController.moonImage.value,
                      innerImage: tabsController.innerImage.value,
                      innerHeight: ScreenAdapter.height(innerHeight),
                      iconHeight: ScreenAdapter.height(iconHeight)),
                  size: Size(
                      ScreenAdapter.height(chartSize), ScreenAdapter.height(chartSize)),
                ),
                sleepDataIsNull.value?
                buildTimeText(0, 0,DateTime(2000))
                :buildTimeText((homeController.aizoSleepData.value.totalDuration - homeController.aizoSleepData.value.awakeDuration) ~/ 60, (homeController.aizoSleepData.value.totalDuration - homeController.aizoSleepData.value.awakeDuration) % 60,homeController.aizoSleepData.value.date),

              ]),
              
              if (showBottomContent) ...[
                Gap(ScreenAdapter.height(12)),

                Container(
                  constraints:
                      BoxConstraints(minHeight: ScreenAdapter.height(37)),
                  padding: EdgeInsets.symmetric(
                      vertical: ScreenAdapter.height(10),
                      horizontal: ScreenAdapter.width(16)),
                  width: ScreenAdapter.width(316),
                  decoration: BoxDecoration(
                      color: AppColors.lightBlue.withOpacity(0.12),
                      borderRadius: BorderRadius.circular(4)),
                  child: Center(
                    child: Text(
                      T.healthProposalAchieved.tr,
                      softWrap: true,
                      style: normalF12H17C999.copyWith(
                          color: AppColors.Color666, fontWeight: FontWeight.w500),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                Gap(ScreenAdapter.height(8))
              ],
            ],
          ),
        ));
  }

  Widget buildTimeText(int hours, int minutes,DateTime date) {
    return Column(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: "$hours",
                style: hoursMinutesStyle,
              ),
              WidgetSpan(child: SizedBox(width: ScreenAdapter.width(textGap))),
              TextSpan(
                text: T.wordsHrs.tr,
                style: unitStyle,
              ),
              WidgetSpan(child: SizedBox(width: ScreenAdapter.width(textGap))),
              TextSpan(
                text: "$minutes",
                style: hoursMinutesStyle,
              ),
              WidgetSpan(child: SizedBox(width: ScreenAdapter.width(textGap))),
              TextSpan(
                text: T.wordsMin.tr,
                style: unitStyle,
              )
            ],
          ),
        ),
        if (showBottomContent)
          Text(
            date.year != 2000 
              ? "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}"
              : "",
            style: dateStyle,
          )
      ],
    );
  }
}

class ArcPainter extends CustomPainter {
  final double progress; // 0.0 ~ 1.0
  ui.Image? sunImage;
  ui.Image? moonImage;
  ui.Image? innerImage;
  final double arcWidth;
  final double iconHeight; // 图标宽度
  final double innerHeight; // 内阴影宽度

  final Color arcColor;

  ArcPainter({
    required this.progress,
    required this.sunImage,
    required this.moonImage,
    required this.innerImage,
    this.iconHeight = 32,
    this.innerHeight = 150,
    this.arcWidth = 20.0,
    this.arcColor = Colors.blue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2 - arcWidth;
    final sweepAngle = 2 * pi * progress;


    // // 绘制内阴影（模拟效果）
    // _drawInnerShadow(canvas, center, radius, arcWidth);
    //绘制外圈
    _drawBaseArc(canvas, center, radius,sweepAngle);
    _drawInner(canvas, center, radius);
    _drawSunWithShadow(canvas, center, radius);
    _drawMoonWithShadow(canvas, center, radius,sweepAngle);
  }

  void _drawBaseArc(Canvas canvas, Offset center, double radius,double sweepAngle) {
    // 圆弧背景阴影
    canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius + arcWidth / 2),
        -pi / 2,
        pi * 2,
        false,
        Paint()
          ..color = Color(0xFFF4F2F2)
          ..style = PaintingStyle.stroke
          ..strokeWidth = arcWidth
        // ..maskFilter = const MaskFilter.blur(BlurStyle.inner, 8),
        );

    // 主圆弧
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius + arcWidth / 2),
      -pi / 2,
      // pi * 1.5,
      sweepAngle,
      false,
      Paint()
        ..color = arcColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = arcWidth
        ..strokeCap = StrokeCap.round
        ..maskFilter = const MaskFilter.blur(BlurStyle.inner, 6),
    );
  }

  void _drawSunWithShadow(Canvas canvas, Offset center, double radius) {
    final angle = -pi / 2 ;
    final position = _calculatePosition(center, radius, angle);

    if (moonImage != null) {
      final Rect sourceRect = Rect.fromLTWH(
          0, 0, moonImage!.width.toDouble(), moonImage!.height.toDouble());
      final Rect targetRect = Rect.fromCenter(
        center: position,
        width: iconHeight,
        height: iconHeight,
      );

      // 太阳阴影
      canvas.drawCircle(
        position + const Offset(0, 4),
        iconHeight / 2,
        Paint()
          ..color = Colors.black.withOpacity(0.04)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8),
      );

      // 太阳图标
      canvas.drawImageRect(
        moonImage!,
        sourceRect,
        targetRect,
        Paint()..filterQuality = FilterQuality.high,
      );
    }
  }

  void _drawInner(Canvas canvas, Offset center, double radius) {
    const angle = -pi / 2;

    if (innerImage != null) {
      final Rect sourceRect = Rect.fromLTWH(
          0, 0, innerImage!.width.toDouble(), innerImage!.height.toDouble());
      final Rect targetRect = Rect.fromCenter(
        center: center,
        width: innerHeight,
        height: innerHeight,
      );

      // 太阳图标
      canvas.drawImageRect(
        innerImage!,
        sourceRect,
        targetRect,
        Paint()..filterQuality = FilterQuality.high,
      );
    }
  }

  void _drawMoonWithShadow(Canvas canvas, Offset center, double radius, double sweepAngle) {
    final angle = -pi / 2 + sweepAngle;
    final position = _calculatePosition(center, radius, angle);

    if (sunImage != null) {
      final Rect sourceRect = Rect.fromLTWH(
          0, 0, sunImage!.width.toDouble(), sunImage!.height.toDouble());
      final Rect targetRect = Rect.fromCenter(
        center: position,
        width: iconHeight,
        height: iconHeight,
      );

      // 太阳阴影
      canvas.drawCircle(
        position + const Offset(0, 4),
        iconHeight / 2,
        Paint()
          ..color = Colors.black.withOpacity(0.04)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8),
      );

      // 太阳图标
      canvas.drawImageRect(
        sunImage!,
        sourceRect,
        targetRect,
        Paint()..filterQuality = FilterQuality.high,
      );
    }
  }

  Offset _calculatePosition(Offset center, double radius, double angle) {
    return center +
        Offset(
          cos(angle) * (radius + arcWidth / 2),
          sin(angle) * (radius + arcWidth / 2),
        );
  }

  void _drawInnerShadow(
      Canvas canvas, Offset center, double radius, double width) {
    final shadowPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = width + 4
      ..maskFilter = MaskFilter.blur(BlurStyle.inner, 8);

    canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius + width / 2 + 2),
        -pi / 2,
        pi * 1.5,
        false,
        shadowPaint);
  }

  @override
  bool shouldRepaint(ArcPainter oldDelegate) {
    return oldDelegate.moonImage != moonImage ||
        oldDelegate.sunImage != sunImage;
  }
}
