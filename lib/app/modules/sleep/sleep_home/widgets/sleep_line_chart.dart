/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-04-01 15:09:27
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-04-01 15:13:33
 * @FilePath: /RPM-APP/lib/app/modules/sleep/sleep_home/widgets/sleep_pie_chart.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';

class SleepLineChart extends StatelessWidget with BaseWidgetMixin{
   SleepLineChart({super.key});

  @override
  Widget body(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16),vertical: ScreenAdapter.height(10)),
      height: ScreenAdapter.height(1721),
      width: double.infinity,
      child: Image.asset(
        Assets.images.sleepImage2.path
      ),
    );
  }
}