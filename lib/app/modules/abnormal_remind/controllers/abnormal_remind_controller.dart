// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-12-10 10:46:36
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-12-10 14:38:29
//  * @FilePath: /rpmappmaster/lib/app/modules/abnormal_remind/controllers/abnormal_remind_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:aiCare/app/core/translations/translation_keys.dart';
// import 'package:aiCare/app/core/utils/logger_singleton.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// class AbnormalRemindController extends BaseController {
//   int index = 5;
//   List<String> titleList = [];
//   List<List<String>> unitList = [];
//   List<TextEditingController> textControllers = [];
//   List<FocusNode> focusNodes = [];
//   RxList<List<bool>> onclick = [
//     [true]
//   ].obs;

//   RxList<int> selectIndexList = [100, 10, 11, 13, 16].obs;
//   DateTime currentDate = DateTime.now(); // 当前时间
//   RxString selectDateString = "".obs;

//   @override
//   void onInit() {
//     super.onInit();
//     // 获取传递的参数
//     // index = Get.arguments['index'];
//     // logger.d("当前获取的index${index}");
//     toSwitch();
//     // initList();
//   }

//   @override
//   void onClose() {
//     // 释放所有文本控制器
//     textControllers.forEach((controller) => dispose());
//     super.onClose();
//   }

//   void toSwitch() {
//     switch (index) {
//       case 2:
//         titleList = [
//           T.wordsTime.tr,
//           T.wordsSys.tr,
//           T.wordsDia.tr,
//           T.commonHeartRate.tr
//         ];
//         unitList = [
//           [T.wordsMmHg.tr],
//           [T.wordsMmHg.tr],
//           [T.commonTimesMinuteBig.tr]
//         ];

//         textControllers =
//             List.generate(titleList.length - 1, (i) => TextEditingController());
//         focusNodes =
//             List.generate(titleList.length - 1, (i) => FocusNode()); // 焦点节点
//         return;
//       case 3:
//         titleList = [
//           T.wordsTime.tr,
//           T.wordsWeight.tr,
//           T.wordsHeight.tr,
//         ];
//         unitList = [
//           [T.wordsKg.tr, T.wordsLb.tr],
//           [T.wordsCm.tr, T.wordsInch.tr],
//         ];
//         onclick.value = [
//           [true, false],
//           [true, false]
//         ];
//         onclick.refresh();

//         textControllers =
//             List.generate(titleList.length - 1, (i) => TextEditingController());
//         focusNodes =
//             List.generate(titleList.length - 1, (i) => FocusNode()); // 焦点节点

//         return;
//       case 5:
//         titleList = [
//           // appLocalization.abnormalRemind,
//           T.abnormalRemindHighRate.tr,
//           T.abnormalRemindLowRate.tr,
//         ];
//         unitList = [
//           [T.wordsBPM.tr],
//           [T.wordsBPM.tr],
//         ];
//         onclick.value = [
//           [true],
//           [true]
//         ];
//         onclick.refresh();

//         textControllers =
//             List.generate(titleList.length, (i) => TextEditingController());
//         focusNodes =
//             List.generate(titleList.length, (i) => FocusNode()); // 焦点节点

//         return;
//       default:
//         // logger.d("运行了没有？");
//         // logger.d(titleList.length);
//         return;
//     }
//   }

//   onTap(int i, BuildContext context) {
//     logger.d(i);
//       logger.d("跳转到控制器");
//       // 获取焦点并移动光标
//       focusNodes[i].requestFocus();
//       // 将光标定位到 i-1 的文本控制器上
//       textControllers[i].selection = TextSelection.fromPosition(
//         TextPosition(offset: textControllers[i - 1].text.length),
//       );
    
//   }

//   int getPickerItemCount(int i) {
//     switch (i) {
//       case 0:
//         return 100;
//       case 1:
//         return 12;
//       case 2:
//         return 31;
//       case 3:
//         return 24;
//       case 4:
//         return 60;
//       default:
//         return 10;
//     }
//   }

//   String getPickerItemText(int i, int index) {
//     switch (i) {
//       case 0:
//         return (currentDate.year - 100 + index + 1).toString();
//       case 1:
//       case 2:
//       case 3:
//       case 4:
//         return (index + 1).toString();
//       default:
//         return "报错";
//     }
//   }
// }
