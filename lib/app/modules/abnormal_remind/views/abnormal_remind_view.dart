// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/translations/translation_keys.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/custom_app_bar.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_svg/svg.dart';

// import 'package:get/get.dart';

// import '../controllers/abnormal_remind_controller.dart';

// class AbnormalRemindView extends BaseView<AbnormalRemindController> {
//   AbnormalRemindView({
//     super.key,
//   }) : super(
//           bgColor: AppColors.colorWhite,
//           statusBarColor: Colors.white,
//         );

//   @override
//   Widget? appBar(BuildContext context) {
//     return CustomAppBar(
//       title: T.abnormalRemind.tr,
//       backgroundColor: AppColors.colorWhite,
//     );
//   }

//   @override
//   Widget body(BuildContext context) {
//     // logger.d(controller.titleList.length);
//     return Container(
//         width: ScreenAdapter.width(343),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//           Column(
//             children: List.generate(controller.titleList.length, (index) {
//               // logger.d("当前循环的是第几行：$index");
//               return InkWell(
//                 onTap: () {
//                   controller.onTap(index,context);
//                 },
//                 child: Container(
//                   padding: EdgeInsets.only(top: ScreenAdapter.height(12)),
//                   child: Column(
//                     children: [
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           Text(
//                             controller.titleList[index],
//                             style: TextStyle(
//                                 fontWeight: FontWeight.w600,
//                                 fontSize: ScreenAdapter.fontSize(14),
//                                 height: 19.6 / 14,
//                                 color: AppColors.lightBlue),
//                           ),
//                           Flexible(
//                                   child: Container(
//                                   margin: EdgeInsets.only(
//                                       left: ScreenAdapter.width(24)),
//                                   child: Row(
//                                     mainAxisAlignment:
//                                         MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       Container(
//                                         // color: Colors.red,
//                                         width: ScreenAdapter.width(100),
//                                         height: ScreenAdapter.height(20),
//                                         child: TextField(
//                                           controller:
//                                               controller.textControllers[
//                                                   index ], // 绑定相应的文本控制器
//                                           focusNode: controller
//                                               .focusNodes[index ], // 绑定焦点
//                                           // keyboardType:
//                                           //     TextInputType.numberWithOptions(decimal: false), // 限制键盘为数值输入
//                                           inputFormatters: <TextInputFormatter>[
//                                             FilteringTextInputFormatter
//                                                 .digitsOnly, // 仅允许输入数字
//                                           ],
//                                           // keyboardType: TextInputType.numberWithOptions(),
//                                           maxLength: 3,
//                                           cursorColor:
//                                               AppColors.Color333, // 设置光标颜色为黑色
//                                           style: TextStyle(
//                                               fontWeight: FontWeight.w600,
//                                               fontSize:
//                                                   ScreenAdapter.fontSize(14),
//                                               height: 19.6 / 14,
//                                               color: AppColors.Color333),
//                                           textAlign: TextAlign.left, // 文本靠右对齐
//                                           // textInputAction: TextInputAction.done, // 设置完成按钮
//                                           decoration: InputDecoration(
//                                               border: InputBorder.none, // 去掉下划线
//                                               isDense: true, // 减小内边距
//                                               contentPadding:
//                                                   EdgeInsets.zero, // 设置内边距为0
//                                               counterText: ""),
//                                         ),
//                                       ),
//                                       SizedBox(
//                                         width: ScreenAdapter.width(4),
//                                       ),

//                                       SizedBox(
//                                               child: Row(
//                                               children: List.generate(
//                                                   controller.unitList[index]
//                                                       .length, (j) {
//                                                 // logger.d("开始");
//                                                 // logger.d(index-1);
//                                                 // logger.d(j);
//                                                 // logger.d("结束");
//                                                 // logger.d(controller.onclick);
//                                                 return Obx(()=> InkWell(
//                                                   onTap: () {
//                                                     // controller.unitSwitch(
//                                                     //     index-1, j);
//                                                   },
//                                                   child: Container(
//                                                     margin: j == 0
//                                                         ? EdgeInsets.only(
//                                                             right: ScreenAdapter
//                                                                 .width(4))
//                                                         : EdgeInsets.zero,
//                                                     padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
//                                                     height:
//                                                         ScreenAdapter.height(
//                                                             18),
//                                                     decoration: BoxDecoration(
//                                                         border: Border.all(
//                                                             width: ScreenAdapter
//                                                                 .width(1),
//                                                             color: controller
//                                                                         .onclick
//                                                                         .value[index]
//                                                                     [j]
//                                                                 ? AppColors
//                                                                     .lightBlue
//                                                                 : AppColors
//                                                                     .greyLineColor),
//                                                         color: controller
//                                                                 .onclick
//                                                                 .value[index][j]
//                                                             ? AppColors
//                                                                 .lightBlue15
//                                                             : AppColors
//                                                                 .greyLineColor
//                                                                 .withOpacity(0.15),
//                                                         borderRadius: BorderRadius.circular(ScreenAdapter.width(16))),
//                                                     child: Text(
//                                                         controller
//                                                             .unitList[index][j],
//                                                         style: normalF12H17C333.copyWith(
//                                                             fontWeight:
//                                                                 FontWeight.w500,
//                                                             color: controller
//                                                                         .onclick
//                                                                         .value[
//                                                                     index][j]
//                                                                 ? AppColors
//                                                                     .lightBlue
//                                                                 : AppColors
//                                                                     .greyLineColor)),
//                                                   ),
//                                                 )
//                                               );
//                                               }),
//                                             ))
                                   
//                                     ],
//                                   ),
//                                 ))
//                         ],
//                       ),
//                       SizedBox(
//                         height: ScreenAdapter.height(8),
//                       ),
//                       Container(
//                         width: ScreenAdapter.width(343),
//                         height: ScreenAdapter.height(1),
//                         color: AppColors.homeBgColor,
//                       )
//                     ],
//                   ),
//                 ),
//               );
//             }),
//           ),

//           //底部确认按钮
//           Container(
//             width: ScreenAdapter.width(343),
//             height: ScreenAdapter.height(38),
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(ScreenAdapter.width(24)),
//               gradient: LinearGradient(
//                 begin: Alignment.centerLeft, // 从左到右
//                 end: Alignment.centerRight, // 结束方向为右边
//                 colors: [
//                   Color(0xFF57EBFF), // #57EBFF
//                   Color(0xFF1B6BFF), // #1B6BFF
//                 ],
//               ),
//             ),
//             child: Center(
//               child: Text(
//                 T.wordsOk.tr,
//                 style: normalF16H22C666.copyWith(
//                     fontWeight: FontWeight.w500, color: AppColors.colorWhite),
//               ),
//             ),
//           ),
//         ]));
//   }
// }
