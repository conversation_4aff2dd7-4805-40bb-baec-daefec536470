import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/modules/home/<USER>/home_view.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/user/user_home/views/user_view.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

class TabsView extends StatelessWidget {
  TabsView({super.key});

  final TabsController controller = Get.find<TabsController>();

  @override
  Widget build(BuildContext context) {
    return Obx(() => Stack(
          children: [
            IndexedStack(
              index: controller.currentIndex.value,
              children: [
                HomeView(),
                User<PERSON><PERSON><PERSON><PERSON><PERSON>(),
              ],
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: BottomNavigationBar(
                currentIndex: controller.currentIndex.value,
                onTap: controller.switchTab,
                selectedItemColor: AppColors.colorPrimary,
                unselectedItemColor: Colors.grey,
                items:  [
              BottomNavigationBarItem(
                icon: controller.currentIndex.value == 0
                    ? SvgPicture.asset(Assets.images.homeOnClick,
                        width: ScreenAdapter.width(30), height: ScreenAdapter.width(30))
                    : SvgPicture.asset(Assets.images.homeNotClick,
                        width: ScreenAdapter.width(30), height: ScreenAdapter.width(30)),
                label: T.bottomNavHome.tr,
              ),
              // BottomNavigationBarItem(
              //     icon: controller.currentIndex.value == 1
              //         ? SvgPicture.asset(Assets.images.healthOnClick,
              //             width: ScreenAdapter.width(30), height: ScreenAdapter.width(30))
              //         : SvgPicture.asset(Assets.images.healthNotClick,
              //             width: ScreenAdapter.width(30), height: ScreenAdapter.width(30)),
              //     label: controller.T.bottomNavHealth),
              BottomNavigationBarItem(
                  icon: controller.currentIndex.value == 1
                      ? SvgPicture.asset(Assets.images.myOnClick,
                          width: ScreenAdapter.width(30), height: ScreenAdapter.width(30))
                      : SvgPicture.asset(Assets.images.myNotClick,
                          width: ScreenAdapter.width(30), height: ScreenAdapter.width(30)),
                  label: T.bottomNavMy.tr),
            
                ],
              ),
            ),
          ],
        ));
  }
} 
