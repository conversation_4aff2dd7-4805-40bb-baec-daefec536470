// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:get/get.dart';

// class HealthInfoController extends BaseController {
//   //TODO: Implement HealthInfoController

//   final count = 0.obs;
//   @override
//   void onInit() {
//     super.onInit();
//   }

//   @override
//   void onReady() {
//     super.onReady();
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   void increment() => count.value++;
// }
