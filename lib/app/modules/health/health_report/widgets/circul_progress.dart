// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2025-03-19 16:07:52
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-03-19 16:12:15
//  * @FilePath: /RPM-APP/lib/app/modules/health/health_report/widgets/circul_progress.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:math';

// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:flutter/material.dart';

// class CircularProgress extends CustomPainter {
//   final double progress; // 0-100
//   final double strokeWidth;
//   final double circleRadius;

//   CircularProgress({
//     required this.progress,
//     this.strokeWidth = 16,
//     this.circleRadius = 8,
//   });

//   @override
//   void paint(Canvas canvas, Size size) {
//     // 背景圆环
//     final backgroundPaint = Paint()
//       ..color = Color(0xFFCBDDFF)
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = strokeWidth;

//     // 进度圆环
//     final progressPaint = Paint()
//       ..color = AppColors.lightBlue
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = strokeWidth
//       ..strokeCap = StrokeCap.round;

//     // 计算中心点和半径
//     final center = Offset(size.width / 2, size.height / 2);
//     final radius = (size.width / 2 - strokeWidth / 2);

//     // 绘制背景圆环
//     canvas.drawCircle(center, radius, backgroundPaint);

//     // 计算进度角度（起点顶部：-π/2，进度角度：progress/100 * 2π）
//     final sweepAngle = (progress / 100) * 2 * pi;
//     canvas.drawArc(
//       Rect.fromCircle(center: center, radius: radius),
//       -pi / 2, // 起始角度顶部
//       sweepAngle,
//       false,
//       progressPaint,
//     );
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) {
//     return true;
//   }
// }
