// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2025-03-19 14:51:17
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-03-19 15:22:49
//  * @FilePath: /RPM-APP/lib/app/modules/health/health_report/controllers/health_report_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:get/get.dart';

// class HealthReportController extends BaseController {
//   //TODO: Implement HealthReportController

//   final count = 0.obs;
//   @override
//   void onInit() {
//     super.onInit();
//   }

//   @override
//   void onReady() {
//     super.onReady();
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   void increment() => count.value++;

//   void toEdit(int i) {}
// }
