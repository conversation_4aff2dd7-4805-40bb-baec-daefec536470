// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2025-03-19 14:51:17
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-03-21 17:06:22
//  * @FilePath: /RPM-APP/lib/app/modules/health/health_report/views/health_report_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/custom_app_bar.dart';
// import 'package:aiCare/app/core/widget/custom_left_back.dart';
// import 'package:aiCare/app/modules/health/health_report/widgets/circul_progress.dart';
// import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:gap/gap.dart';

// import 'package:get/get.dart';

// import '../controllers/health_report_controller.dart';

// class HealthReportView extends BaseView<HealthReportController> {
//   HealthReportView({
//     super.key,
//   }) : super(parentPaddings: [
//           ScreenAdapter.width(16),
//           // 0,
//           0,
//           ScreenAdapter.width(16),
//           // 0,
//           ScreenAdapter.height(16)
//           // 0
//         ], bgColor: AppColors.homeBgColor, banner: HomeBanner());

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return CustomAppBar(
//       appBarTitleText: appLocalization.healthRecoveryReport,
//       actions: [
//         _rightIcon(),
//       ],
//       padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
//       isBackButtonEnabled: true,
//       leading: CustomLeftBack(),
//       appBarColor: AppColors.homeBgColor,
//     );
//   }

//   @override
//   Widget body(BuildContext context) {
//     return SingleChildScrollView(
//       child: Column(
//         children: [
//           HealthScore(),
//           // SleepScore(),
//           ActivityScore(),
//           HeartRateScore(),
//           BloodOxygenScore(),
//           TemperatureScore(),
//           _FooterTitle(),
//         ],
//       ),
//     );
//   }

//   Widget HealthScore() {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       padding: EdgeInsets.only(bottom: ScreenAdapter.height(14)),
//       decoration: BoxDecoration(
//           color: AppColors.lightBlue05, borderRadius: BorderRadius.circular(8)),
//       child: Column(
//         children: [
//           Container(
//             alignment: Alignment.topLeft,
//             margin: EdgeInsets.only(
//                 left: ScreenAdapter.width(12), top: ScreenAdapter.height(8)),
//             child: Text(
//               appLocalization.healthBodyScoreTitle,
//               style: TextStyle(
//                   fontSize: ScreenAdapter.fontSize(15),
//                   height: 21.0 / 15,
//                   fontWeight: FontWeight.w600,
//                   color: AppColors.Color666),
//             ),
//           ),
//           Container(
//             // width: ScreenAdapter.width(216),
//             margin: EdgeInsets.only(
//                 top: ScreenAdapter.height(10),
//                 bottom: ScreenAdapter.height(16),
//                 left: ScreenAdapter.width(16.5),
//                 right: ScreenAdapter.width(16.5)),
//             child: Row(
//               // mainAxisSize: MainAxisSize.min,
//               children: [
//                 Container(
//                   height: ScreenAdapter.height(94),
//                   margin: EdgeInsets.only(right: ScreenAdapter.width(16)),
//                   child: AspectRatio(
//                     aspectRatio: 1,
//                     child: Stack(
//                       alignment: Alignment.center,
//                       children: [
//                         SizedBox(
//                           width: double.infinity,
//                           height: double.infinity,
//                           child: CustomPaint(
//                             painter:
//                                 CircularProgress(strokeWidth: 8, progress: 65),
//                           ),
//                         ),
//                         Container(
//                             margin: EdgeInsets.only(
//                                 top: ScreenAdapter.height(24.5)),
//                             // left: 0,
//                             // right: 0,
//                             child: Column(
//                               children: [
//                                 Text(
//                                   "65",
//                                   style: normalF16H22C666.copyWith(
//                                       color: AppColors.lightBlue),
//                                   textAlign: TextAlign.center,
//                                 ),
//                                 Gap(ScreenAdapter.height(1)),
//                                 SizedBox(
//                                   width: ScreenAdapter.width(62),
//                                   child: Text(
//                                     appLocalization.healthHolistic,
//                                     style: TextStyle(
//                                         fontSize: ScreenAdapter.fontSize(8),
//                                         height: 11.2 / 8,
//                                         color: AppColors.Color999),
//                                     textAlign: TextAlign.center,
//                                     maxLines: 2,
//                                   ),
//                                 )
//                               ],
//                             )),
//                       ],
//                     ),
//                   ),
//                 ),
//                 Container(
//                   // width: ScreenAdapter.width(200),
//                   height: ScreenAdapter.height(96),
//                   decoration: BoxDecoration(
//                       border: Border(
//                           left: BorderSide(
//                     color: AppColors.temperatureBdNormalColor,
//                     width: ScreenAdapter.width(2),
//                   ))),
//                   // alignment: Alignment.center,
//                   padding: EdgeInsets.only(left: ScreenAdapter.width(16)),
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Row(
//                         children: [
//                           SizedBox(
//                             height: ScreenAdapter.height(20.86),
//                             child: Image.asset(
//                               Assets.images.bodyBlueIcon.path,
//                               fit: BoxFit.fitHeight,
//                             ),
//                           ),
//                           Gap(ScreenAdapter.width(10)),
//                           SizedBox(
//                             height: ScreenAdapter.height(20.86),
//                             child: Image.asset(
//                               Assets.images.bodyBlueIcon.path,
//                               fit: BoxFit.fitHeight,
//                             ),
//                           ),
//                           Gap(ScreenAdapter.width(10)),
//                           SizedBox(
//                             height: ScreenAdapter.height(20.86),
//                             child: Image.asset(
//                               Assets.images.bodyBlueIcon.path,
//                               fit: BoxFit.fitHeight,
//                             ),
//                           ),
//                           Gap(ScreenAdapter.width(10)),
//                           SizedBox(
//                             height: ScreenAdapter.height(20.86),
//                             child: Image.asset(
//                               Assets.images.bodyNoIcon.path,
//                               color: Color(0xFFCBDDFF),
//                               fit: BoxFit.fitHeight,
//                             ),
//                           ),
//                           Gap(ScreenAdapter.width(10)),
//                           SizedBox(
//                             height: ScreenAdapter.height(20.86),
//                             child: Image.asset(
//                               Assets.images.bodyNoIcon.path,
//                               color: Color(0xFFCBDDFF),
//                               fit: BoxFit.fitHeight,
//                             ),
//                           ),
//                         ],
//                       ),
//                       Gap(ScreenAdapter.height(3)),
//                       Text(
//                         "${appLocalization.wordsAttention}！",
//                         style: normalF12H17C999.copyWith(
//                             color: Color(0xFF1e1e1e),
//                             fontWeight: FontWeight.w600),
//                       ),
//                       Gap(ScreenAdapter.height(3)),
//                       SizedBox(
//                         width: ScreenAdapter.width(182),
//                         child: Text(
//                           "${appLocalization.healthRingRemind}",
//                           style: normalF12H17C999,
//                           maxLines: 2,
//                         ),
//                       )
//                     ],
//                   ),
//                 )
//               ],
//             ),
//           ),
//           Container(
//             width: double.infinity,
//             height: ScreenAdapter.height(100),
//             margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
//             padding: EdgeInsets.symmetric(
//                 horizontal: ScreenAdapter.width(10),
//                 vertical: ScreenAdapter.height(10)),
//             decoration: BoxDecoration(
//                 color: AppColors.lightBlue05,
//                 borderRadius: BorderRadius.circular(8)),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   appLocalization.healthAdvice,
//                   style: TextStyle(
//                       fontSize: ScreenAdapter.fontSize(15),
//                       height: 21.0 / 15,
//                       color: AppColors.Color666,
//                       fontWeight: FontWeight.w600),
//                 ),
//                 Gap(ScreenAdapter.height(8)),
//                 Expanded(
//                   child: Text(
//                     appLocalization.healthAdviceInfor1,
//                     style: normalF12H17C999.copyWith(color: AppColors.Color666),
//                     maxLines: 3,
//                   ),
//                 )
//               ],
//             ),
//           )
//         ],
//       ),
//     );
//   }

//   Widget SleepScore() {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       decoration: BoxDecoration(
//           color: AppColors.lightBlue07, borderRadius: BorderRadius.circular(8)),
//       child: Column(
//         children: [
//           Container(
//             margin: EdgeInsets.only(
//                 left: ScreenAdapter.width(12), top: ScreenAdapter.width(10)),
//             width: double.infinity,
//             child: Text(
//               appLocalization.healthSleepScoreTitle,
//               style: TextStyle(
//                   fontSize: ScreenAdapter.fontSize(15),
//                   height: 21.0 / 15,
//                   fontWeight: FontWeight.w600,
//                   color: AppColors.Color666),
//             ),
//           ),
//           Container(
//             // margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(17.5),vertical: ScreenAdapter.height(10)),
//             margin: EdgeInsets.fromLTRB(
//                 ScreenAdapter.width(12),
//                 ScreenAdapter.height(10),
//                 ScreenAdapter.width(10),
//                 ScreenAdapter.height(10)),
//             // height: ScreenAdapter.height(94),
//             child: Row(
//               children: [
//                 Container(
//                   color: Colors.red,
//                   height: ScreenAdapter.height(94),
//                   child: AspectRatio(
//                     aspectRatio: 1,
//                   ),
//                   margin: EdgeInsets.only(right: ScreenAdapter.width(12)),
//                 ),
//                 Column(
//                   // mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     _SleepSubTitle(appLocalization.healthTimeAsleep,"6 hours 21 minutes"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.healthSleepEffi,"65%"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.healthFallAsleep,"11:43 PM"),
//                   ],
//                 )
//               ],
//             ),
//           )
//         ],
//       ),
//     );
  
//   }

//   Widget ActivityScore() {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       decoration: BoxDecoration(
//           color: AppColors.lightBlue07, borderRadius: BorderRadius.circular(8)),
//       child: Column(
//         children: [
//           Container(
//             margin: EdgeInsets.only(
//                 left: ScreenAdapter.width(12), top: ScreenAdapter.width(10)),
//             width: double.infinity,
//             child: Text(
//               appLocalization.healthSleepScoreTitle,
//               style: TextStyle(
//                   fontSize: ScreenAdapter.fontSize(15),
//                   height: 21.0 / 15,
//                   fontWeight: FontWeight.w600,
//                   color: AppColors.Color666),
//             ),
//           ),
//           Container(
//             // margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(17.5),vertical: ScreenAdapter.height(10)),
//             margin: EdgeInsets.fromLTRB(
//                 ScreenAdapter.width(12),
//                 ScreenAdapter.height(10),
//                 ScreenAdapter.width(10),
//                 ScreenAdapter.height(10)),
//             // height: ScreenAdapter.height(94),
//             child: Row(
//               children: [
//                 Container(
//                   // color: Colors.red,
//                   height: ScreenAdapter.height(89.35),
//                   child: Stack(
//                     children: [
//                       Image.asset(
//                     Assets.images.healthActivityBlue.path,
//                     fit: BoxFit.fitHeight,
//                   ),
//                   Positioned(
//                     top: ScreenAdapter.height(25.5),
//                     left: 0,
//                     right: 0,
//                     child: Container(
//                     width: ScreenAdapter.width(62),
//                     // margin: EdgeInsets.only(top: ScreenAdapter.height(25.5)),
//                     child: Column(
//                       children: [
//                         Text("64",style: normalF16H22C666.copyWith(color: AppColors.lightBlue),),
//                         Text(appLocalization.healthActivityScore,style: TextStyle(
//                           fontSize: ScreenAdapter.fontSize(8),
//                           height: 11.2/8,
//                           color: AppColors.Color666
//                         ),)
//                       ],
//                     ),
//                   ))
//                     ],
//                   ),
//                   margin: EdgeInsets.only(right: ScreenAdapter.width(12)),
//                 ),
//                 Column(
//                   // mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     _SleepSubTitle(appLocalization.healthDurationExer,"21 minutes"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.healthNumberExer,"2"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.healthNumberActi,"2134"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.healthActivityCon,"76cal"),
//                   ],
//                 )
//               ],
//             ),
//           )
//         ],
//       ),
//     );
  
//   }

//   Widget HeartRateScore() {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       decoration: BoxDecoration(
//           color: AppColors.lightBlue07, borderRadius: BorderRadius.circular(8)),
//       child: Column(
//         // mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Container(
//             margin: EdgeInsets.only(
//                 left: ScreenAdapter.width(12), top: ScreenAdapter.width(10)),
//             width: double.infinity,
//             child: Text(
//               appLocalization.healthHeartRateScoreTitle,
//               style: TextStyle(
//                   fontSize: ScreenAdapter.fontSize(15),
//                   height: 21.0 / 15,
//                   fontWeight: FontWeight.w600,
//                   color: AppColors.Color666),
//             ),
//           ),
//           Container(
//             // margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(17.5),vertical: ScreenAdapter.height(10)),
//             margin: EdgeInsets.fromLTRB(
//                 ScreenAdapter.width(12),
//                 ScreenAdapter.height(10),
//                 ScreenAdapter.width(10),
//                 // ScreenAdapter.height(10)
//                 0
//                 ),
//             // height: ScreenAdapter.height(94),
//             child: Row(
//               children: [
//                 Container(
//                   // color: Colors.red,
//                   height: ScreenAdapter.height(75.5),
//                   child:  Image.asset(
//                     Assets.images.healthHeartRed.path,
//                     fit: BoxFit.fitHeight,
//                   ),
//                   margin: EdgeInsets.only(right: ScreenAdapter.width(12)),
//                 ),
//                 Column(
//                   // mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     _SleepSubTitle(appLocalization.healthResting,"62/min"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.wordsHighest,"112/min"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.wordsLowest,"62/min"),
//                   ],
//                 )
//               ],
//             ),
//           ),
//           Row(
//             children: [
//               Gap(ScreenAdapter.width(12)),
//               SizedBox(
//                 height: ScreenAdapter.height(12),
//                 child: AspectRatio(aspectRatio: 1,
//                 child: Image.asset(
//                   Assets.images.heartIcon.path
//                 ),),

//               ),
//               Gap(ScreenAdapter.width(6)),
//               Text(
//                 appLocalization.wordsAverage+":",
//                 style: normalF12H17C999,
//               ),
//               Gap(4),
//               Text("72/min",style: normalF12H17C999.copyWith(color: AppColors.Color333),)
//             ],
//           ),
//           Container(
//             margin: EdgeInsets.symmetric(vertical: ScreenAdapter.height(10),horizontal: ScreenAdapter.width(12)),
//             child: Text("图片"),
//           ),
//           Container(
//             margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(12)),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 _HeartRangeSubTitle(AppColors.heartRateList[0],">100 over Speed"),
//                 _HeartRangeSubTitle(AppColors.heartRateList[1],"60-100 Speed"),
//                 _HeartRangeSubTitle(AppColors.heartRateList[2],"<60 Slow"),
//               ],
//             ),
//           ),
//           Container(
//             margin: EdgeInsets.only(top: ScreenAdapter.height(4),left: ScreenAdapter.width(12),right: ScreenAdapter.width(12),bottom: ScreenAdapter.height(8)),
//             padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10),vertical: ScreenAdapter.height(4)),
//             width: double.infinity,
//             decoration: BoxDecoration(
//               color: AppColors.lightBlue05,
//                borderRadius: BorderRadius.circular(10)
//             ),
//             child: Column(
//               // mainAxisAlignment: MainAxisAlignment.center,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(appLocalization.wordsProposal,style: normalF12H17C666.copyWith(fontWeight: FontWeight.w600),),
//                 Text(appLocalization.healthProposalNo,style: normalF12H17C333,)
//               ],
//             ),
//           )
//         ],
//       ),
//     );
  
//   }

//   Widget BloodOxygenScore() {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       decoration: BoxDecoration(
//           color: AppColors.lightBlue07, borderRadius: BorderRadius.circular(8)),
//       child: Column(
//         children: [
//           Container(
//             margin: EdgeInsets.only(
//                 left: ScreenAdapter.width(12), top: ScreenAdapter.width(10)),
//             width: double.infinity,
//             child: Text(
//               appLocalization.healthBloodOxygenTitle,
//               style: TextStyle(
//                   fontSize: ScreenAdapter.fontSize(15),
//                   height: 21.0 / 15,
//                   fontWeight: FontWeight.w600,
//                   color: AppColors.Color666),
//             ),
//           ),
//           Container(
//             // margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(17.5),vertical: ScreenAdapter.height(10)),
//             margin: EdgeInsets.fromLTRB(
//                 ScreenAdapter.width(12),
//                 ScreenAdapter.height(10),
                
//                 ScreenAdapter.width(10),
//                 ScreenAdapter.height(10)),
//             // height: ScreenAdapter.height(94),
//             child: Row(
//               children: [
//                 Container(
//                   // color: Colors.red,
//                   height: ScreenAdapter.height(87),
//                   child: Image.asset(
//                     Assets.images.healthOxygen.path,
//                     fit: BoxFit.fitHeight,
//                   ),
//                   margin: EdgeInsets.only(right: ScreenAdapter.width(12)),
//                 ),
//                 Column(
//                   // mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     _SleepSubTitle(appLocalization.health,"21 minutes"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.healthNumberExer,"2"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.healthNumberActi,"2134"),
//                     Gap(ScreenAdapter.height(6)),
//                     _SleepSubTitle(appLocalization.healthActivityCon,"76cal"),
//                   ],
//                 )
//               ],
//             ),
//           )
//         ],
//       ),
//     );
  
//   }

//   Widget TemperatureScore() {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       decoration: BoxDecoration(
//           color: AppColors.lightBlue07, borderRadius: BorderRadius.circular(8)),
//     );
//   }

//   Widget _FooterTitle() {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       decoration: BoxDecoration(color: AppColors.lightBlue05),
//     );
//   }

//   Widget _SleepSubTitle(String title,String value) {
//     return Row(
//       children: [
//         Container(
//           margin: EdgeInsets.only(right: ScreenAdapter.width(6)),
//           decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(ScreenAdapter.height(3)),
//               color: AppColors.searchBgColor),
//           height: ScreenAdapter.height(6),
//           // width: ScreenAdapter.width(6),
//           child: AspectRatio(
//             aspectRatio: 1,
//           ),
//         ),
//         Text(
//           title + ":",
//           style: normalF12H17C999,
//         ),
//         Gap(4),
//         Text(
//           value,
//           style: normalF12H17C999.copyWith(color: AppColors.Color333),
//         )
//       ],
//     );
 
//   }

//   Widget _HeartRangeSubTitle(Color color,String text){
//     return Row(
//       children: [
//         Container(
//           margin: EdgeInsets.only(right: ScreenAdapter.width(2)),
//           decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(ScreenAdapter.height(5)),
//               color: color),
//           height: ScreenAdapter.height(10),
//           // width: ScreenAdapter.width(6),
//           child: AspectRatio(
//             aspectRatio: 1,
//           ),
//         ),
//         Text(
//           text,
//           style: normalF12H17C999,
//         ),
//       ],
//     );
 
//   }
// }

// class _rightIcon extends StatelessWidget {
//   // ReminderHomeController controller = Get.find();
//   HealthReportController controller = Get.find();
//   _rightIcon({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: () {
//         controller.toEdit(-1);
//       },
//       splashColor: Colors.transparent, // 去除水波纹效果
//       highlightColor: Colors.transparent, // 去除高亮效果
//       child: SizedBox(
//         width: ScreenAdapter.width(22),
//         height: ScreenAdapter.height(22),
//         child: Center(
//           // 使用 Center 小部件使 SVG 图片居中
//           child: SvgPicture.asset(
//             Assets.images.plusOuterCircle,
//             fit: BoxFit.contain, // 确保图片按比例显示
//           ),
//         ),
//       ),
//     );
//   }
// }
