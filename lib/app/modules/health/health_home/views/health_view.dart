// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2025-02-28 16:57:04
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-03-19 15:23:00
//  * @FilePath: /RPM-APP/lib/app/modules/health/views/health_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/custom_app_bar.dart';
// import 'package:aiCare/app/modules/health/health_home/widgets/health_pie_canvos.dart';
// import 'package:aiCare/app/routes/app_pages.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:gap/gap.dart';

// import 'package:get/get.dart';

// import '../controllers/health_controller.dart';

// class HealthHomeView extends BaseView<HealthHomeController> {
//   HealthHomeView({
//     super.key,
//   }) : super(
//           parentPaddings: [
//             ScreenAdapter.width(16),
//             ScreenAdapter.height(0),
//             ScreenAdapter.width(16),
//             ScreenAdapter.height(0)
//           ],
//           bgColor: AppColors.homeBgColor,
//         );

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return CustomAppBar(
//       appBarTitleText: appLocalization.healthTitle,
//       leading: SizedBox(),
//       isBottomSide: true,
//     );
//   }

//   @override
//   Widget body(BuildContext context) {
//     return SingleChildScrollView(
//       child: Column(
//         children: [
//           RecoveryBanner(),
//           Gap(ScreenAdapter.height(12)),
//           _information(),
//         ],
//       ),
//     );
//   }

//   Widget RecoveryBanner() {
//     return InkWell(
//       onTap: (){
//         Get.toNamed(Routes.HEALTH_REPORT);//update
//       },
//       child: Container(
//       width: ScreenAdapter.width(343),
//       height: ScreenAdapter.height(349),
//       padding: EdgeInsets.symmetric(
//         horizontal: ScreenAdapter.width(10),
//       ),
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       decoration: BoxDecoration(
//           image: DecorationImage(
//             image: AssetImage(
//               Assets.images.healthBg.path,
//             ), // 正确使用 Assets.gen 生成的 AssetImage
//             colorFilter: ColorFilter.mode(
//               // Colors.transparent.withOpacity(0.35),
//               Colors.white70.withOpacity(0.1),
//               //  Color.w.withOpacity(0.65), // 透明色（白色叠加35%不透明度）
//               BlendMode.srcATop, // 保留原图颜色，仅叠加透明度
//             ),
//           ),
//           borderRadius: BorderRadius.circular(8)),
//       child: Column(
//         children: [
//           //subTitle
//           Container(
//               margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   _subTitle(),
//                   Text(
//                     appLocalization.healthAllRecord,
//                     style: normalF12H17C333.copyWith(color: Color(0XFFF0F0F0)),
//                   )
//                 ],
//               )),
//           //Pie
//           Container(
//             // width: ScreenAdapter.width,
//             height: ScreenAdapter.height(143.54),
//             width: ScreenAdapter.height(280),
//             margin: EdgeInsets.only(
//                 top: ScreenAdapter.height(38),
//                 bottom: ScreenAdapter.height(26)),
//             // margin: EdgeInsets.only(top: ScreenAdapter.height(1)),
//             child: Stack(
//               children: [
//                 Positioned(
//                   child: Container(
//                       // color: Colors.red,
//                       width: double.infinity,
//                       // height: double.infinity,
//                       height: ScreenAdapter.height(140),
//                       child: CustomPaint(
//                         painter: HealthPieCanvos(data: 50.0),
//                       ) // 使用自定义的CustomPainter
//                       ),
//                 ),
//                 Positioned(
//                     bottom: 0,
//                     left: 0,
//                     right: 0,
//                     child: Column(
//                       children: [
//                         Text(
//                           "43",
//                           style: TextStyle(
//                               fontSize: ScreenAdapter.fontSize(32),
//                               height: 44.8 / 32,
//                               color: AppColors.lightBlue,
//                               fontWeight: FontWeight.w500),
//                         ),
//                         Text(
//                           appLocalization.healthScore,
//                           style: normalF12H17C333.copyWith(
//                               color: AppColors.colorWhite,
//                               fontWeight: FontWeight.w500),
//                         ),
//                         Gap(ScreenAdapter.height(8)),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             SizedBox(
//                               // width: ScreenAdapter.width(7.7),
//                               height: ScreenAdapter.height(20.86),
//                               child: Image.asset(
//                                 Assets.images.bodyBlueIcon.path,
//                                 fit: BoxFit.fitHeight,
//                               ),
//                             ),
//                             Gap(ScreenAdapter.width(10)),
//                             SizedBox(
//                               height: ScreenAdapter.height(20.86),
//                               child: Image.asset(
//                                 Assets.images.bodyBlueIcon.path,
//                                 fit: BoxFit.fitHeight,
//                               ),
//                             ),
//                             Gap(ScreenAdapter.width(10)),
//                             SizedBox(
//                               height: ScreenAdapter.height(20.86),
//                               child: Image.asset(
//                                 Assets.images.bodyNoIcon.path,
//                                 fit: BoxFit.fitHeight,
//                               ),
//                             ),
//                             Gap(ScreenAdapter.width(10)),
//                             SizedBox(
//                               height: ScreenAdapter.height(20.86),
//                               child: Image.asset(
//                                 Assets.images.bodyNoIcon.path,
//                                 fit: BoxFit.fitHeight,
//                               ),
//                             ),
//                             Gap(ScreenAdapter.width(10)),
//                             SizedBox(
//                               height: ScreenAdapter.height(20.86),
//                               child: Image.asset(
//                                 Assets.images.bodyNoIcon.path,
//                                 fit: BoxFit.fitHeight,
//                               ),
//                             ),
//                           ],
//                         )
//                       ],
//                     ))
//               ],
//             ),
//           ),

//           //hours
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Column(
//                 children: [
//                   Text(
//                     "5 hrs 21 min",
//                     style:
//                         normalF12H17C999.copyWith(color: AppColors.lightBlue),
//                   ),
//                   Gap(ScreenAdapter.height(2)),
//                   Text(
//                     appLocalization.healthSleepLast,
//                     style: normalF12H17C333.copyWith(color: Colors.white),
//                   )
//                 ],
//               ),
//               Gap(ScreenAdapter.width(48)),
//               Column(
//                 children: [
//                   Text(
//                     "123Kcal",
//                     style:
//                         normalF12H17C999.copyWith(color: AppColors.lightBlue),
//                   ),
//                   Gap(ScreenAdapter.height(2)),
//                   Text(
//                     appLocalization.healthCaloriesDay,
//                     style: normalF12H17C333.copyWith(color: Colors.white),
//                   )
//                 ],
//               ),
//             ],
//           ),

//           //footer
//           Container(
//             alignment: Alignment.center,
//             margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//             height: ScreenAdapter.height(50),
//             decoration: BoxDecoration(
//                 color: AppColors.lightBlue.withOpacity(0.25),
//                 borderRadius: BorderRadius.circular(8)),
//             padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(14)),
//             child: Text(
//               appLocalization.healthSleepTitle,
//               style: normalF12H17C999.copyWith(color: Colors.white),
//               // textAlign: TextAlign.justify,
//             ),
//           )
//         ],
//       ),
//     ),
//     );
//   }

//   Widget _information() {
//     return Container(
//       width: double.infinity,
//       decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(8), color: Colors.white),
//       padding: EdgeInsets.symmetric(
//           vertical: ScreenAdapter.height(12),
//           horizontal: ScreenAdapter.width(12)),
//       margin: EdgeInsets.only(bottom: ScreenAdapter.height(12)),
//       // height: ,
//       child: Column(
//         children: [
//           Container(
//               margin: EdgeInsets.only(bottom: ScreenAdapter.height(4)),
//               child: Row(
//                 children: [
//                   SizedBox(
//                     height: ScreenAdapter.height(16),
//                     child: AspectRatio(
//                       aspectRatio: 1,
//                       child: Image.asset(Assets.images.healthMessageIcon.path),
//                     ),
//                   ),
//                   Gap(ScreenAdapter.width(4)),
//                   Text(
//                     appLocalization.healthInformation,
//                     style: normalF12H17C999.copyWith(color: AppColors.Color333),
//                   ),
//                 ],
//               )),
//           InkWell(
//             onTap: () {
//               logger.d("点击 Health Information Card");
//               Get.toNamed(Routes.HEALTH_INFO);
//             },
//             child: Container(
//               padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(12)),
//               decoration: BoxDecoration(
//                   border:
//                       Border(bottom: BorderSide(color: AppColors.homeBgColor))),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Column(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Container(
//                         width: ScreenAdapter.width(216),
//                         margin:
//                             EdgeInsets.only(bottom: ScreenAdapter.height(6)),
//                         child: Text(
//                           "How to lower blood pressure?",
//                           style: normalF14H19C333.copyWith(
//                               fontWeight: FontWeight.w500),
//                           maxLines: 1,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//                       Container(
//                         alignment: Alignment.centerLeft,
//                         width: ScreenAdapter.width(200),
//                         margin:
//                             EdgeInsets.only(bottom: ScreenAdapter.height(10)),
//                         child: Text(
//                           "How to lower and maintain blood pressure in daily life",
//                           style: TextStyle(
//                               fontSize: ScreenAdapter.fontSize(11),
//                               height: 15.4 / 11,
//                               color: AppColors.Color666),
//                           maxLines: 2,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       )
//                     ],
//                   ),
//                   SizedBox(
//                     width: ScreenAdapter.width(75),
//                     height: ScreenAdapter.height(56),
//                     child: AspectRatio(
//                       aspectRatio: 75.0 / 56.0,
//                       child: Image.asset(Assets.images.healthPicture1.path),
//                     ),
//                   )
//                 ],
//               ),
//             ),
//           ),
//           InkWell(
//             onTap: () {
//               logger.d("点击 Health Information Card");
//               Get.toNamed(Routes.HEALTH_INFO);
//             },
//             child: Container(
//               padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(12)),
//               decoration: BoxDecoration(
//                   border:
//                       Border(bottom: BorderSide(color: AppColors.homeBgColor))),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Column(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Container(
//                         width: ScreenAdapter.width(216),
//                         margin:
//                             EdgeInsets.only(bottom: ScreenAdapter.height(6)),
//                         child: Text(
//                           "What are the consequences of abnormal heart rate",
//                           style: normalF14H19C333.copyWith(
//                               fontWeight: FontWeight.w500),
//                           maxLines: 1,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//                       Container(
//                         alignment: Alignment.centerLeft,
//                         width: ScreenAdapter.width(200),
//                         margin:
//                             EdgeInsets.only(bottom: ScreenAdapter.height(10)),
//                         child: Text(
//                           "Sudden increase in heart rate can lead to excessive heart pressure",
//                           style: TextStyle(
//                               fontSize: ScreenAdapter.fontSize(11),
//                               height: 15.4 / 11,
//                               color: AppColors.Color666),
//                           maxLines: 2,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       )
//                     ],
//                   ),
//                   SizedBox(
//                     width: ScreenAdapter.width(75),
//                     height: ScreenAdapter.height(56),
//                     child: AspectRatio(
//                       aspectRatio: 75.0 / 56.0,
//                       child: Image.asset(Assets.images.healthPicture2.path),
//                     ),
//                   )
//                 ],
//               ),
//             ),
//           ),
//           InkWell(
//             onTap: () {
//               logger.d("点击 Health Information Card");
//               Get.toNamed(Routes.HEALTH_INFO);
//             },
//             child: Container(
//               padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(12)),
//               decoration: BoxDecoration(
//                   border:
//                       Border(bottom: BorderSide(color: AppColors.homeBgColor))),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Column(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Container(
//                         width: ScreenAdapter.width(216),
//                         margin:
//                             EdgeInsets.only(bottom: ScreenAdapter.height(6)),
//                         child: Text(
//                           "What can low blood oxygen cause？",
//                           style: normalF14H19C333.copyWith(
//                               fontWeight: FontWeight.w500),
//                           maxLines: 1,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//                       Container(
//                         alignment: Alignment.centerLeft,
//                         width: ScreenAdapter.width(200),
//                         margin:
//                             EdgeInsets.only(bottom: ScreenAdapter.height(10)),
//                         child: Text(
//                           "What can low blood oxygen cause？Low blood oxygen can lead to memory decline and slow breathing",
//                           style: TextStyle(
//                               fontSize: ScreenAdapter.fontSize(11),
//                               height: 15.4 / 11,
//                               color: AppColors.Color666),
//                           maxLines: 2,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       )
//                     ],
//                   ),
//                   SizedBox(
//                     width: ScreenAdapter.width(75),
//                     height: ScreenAdapter.height(56),
//                     child: AspectRatio(
//                       aspectRatio: 75.0 / 56.0,
//                       child: Image.asset(Assets.images.healthPicture3.path),
//                     ),
//                   )
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );

//     // Positioned(
//     //         top: ScreenAdapter.height(52.32),
//     //         left: ScreenAdapter.width(51.8),
//     //         child: Container(
//     //             // color: Colors.red,
//     //             width: ScreenAdapter.width(225.68), // 设置绘制区域的宽度
//     //             height: ScreenAdapter.width(122.84), // 设置绘制区域的高度
//     //             child: CustomPaint(
//     //               painter: WeightCanvos(
//     //                 // data: 100.0
//     //                 ),
//     //             ) // 使用自定义的CustomPainter
//     //             ),
//     //       ),
//   }

//   Widget _subTitle() {
//     return Row(
//       children: [
//         SizedBox(
//           height: ScreenAdapter.height(16),
//           child: AspectRatio(
//             aspectRatio: 1,
//             child: Image.asset(
//               // Assets.images.healthGoalIcon.path,
//               Assets.images.healthRecoveryIcon.path, // 替换为你的图标路径
//             ),
//           ),
//         ),
//         Gap(ScreenAdapter.width(4)), // 图标与文本间距
//         Text(
//           appLocalization.healthRecoveryReport,
//           style: normalF12H17C999.copyWith(color: Colors.white),
//         )
//       ],
//     );
//   }
// }
