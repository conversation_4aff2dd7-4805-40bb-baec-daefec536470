/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-30 14:46:43
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-07 15:14:41
 * @FilePath: /rpmappmaster/lib/app/modules/user/user_modif/views/user_modif_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:io';

import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';

import '../controllers/user_modif_controller.dart';

class UserModifView extends BaseView<UserModifController> {
  UserModifView({
    super.key,
  }) : super(bgColor: AppColors.homeBgColor,statusBarColor: AppColors.colorWhite,);

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: _getTitle(),
    );
  }

  @override
  Widget body(BuildContext context) {
    return Padding(padding: EdgeInsets.only(top: ScreenAdapter.height(1)),child: Column(children: _getContext()));
  }

  String _getTitle() {
    switch (controller.index) {
      case 0:
        return T.userPicture.tr;
      case 1:
        return T.userSetName.tr;
      default:
        return "出错了";
    }
  }

  List<Widget> _getContext() {
    if (controller.index == 1) return _normalItem();
    if (controller.index == 0)
      return _pictureItem();
    else
      return _normalItem();
  }

  List<Widget> _normalItem() {
    return [
      Container(
        height: ScreenAdapter.height(40),
        padding: EdgeInsets.symmetric(
            horizontal: ScreenAdapter.width(10),
            vertical: ScreenAdapter.height(10)),
        color: Colors.white,
        child: TextField(
          controller: controller.textController,
          cursorColor: AppColors.Color333,
          style: normalF14H19C666.copyWith(fontWeight: FontWeight.w600),

          // scrollPadding: EdgeInsets.zero,
          decoration: InputDecoration(
              border: InputBorder.none, // 去除下划线
              hintText: "", // 提示文字
              contentPadding: EdgeInsets.zero, // 去除内部填充
              isDense: true,
              suffixIcon: InkWell(
                onTap: () {
                  logger.d("点击清空");
                  controller.textController.clear();
                },
                child: UnconstrainedBox(
                  child: Container(
                    alignment: Alignment.topRight,
                    // color: Colors.red,
                    height: ScreenAdapter.height(20),
                    width: ScreenAdapter.height(20),
                    child: AspectRatio(
                      aspectRatio: 1,
                      child: Image.asset(
                        Assets.images.deleteIcon.path,
                        fit: BoxFit.fitHeight,
                      ),
                    ),
                  ),
                ),
              )),
        ),
      ),
      Expanded(child: SizedBox()),
      InkWell(
        onTap: () {
          controller.toSubmit();
        },
        child: Container(
          // width: ScreenAdapter.width(343),
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
          height: ScreenAdapter.height(38),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(ScreenAdapter.width(24)),
            gradient: LinearGradient(
              begin: Alignment.centerLeft, // 从左到右
              end: Alignment.centerRight, // 结束方向为右边
              colors: [
                Color(0xFF57EBFF), // #57EBFF
                Color(0xFF1B6BFF), // #1B6BFF
              ],
            ),
          ),
          child: Center(
            child: Text(
              T.wordsOk.tr,
              style: normalF16H22C666.copyWith(
                  fontWeight: FontWeight.w500, color: AppColors.colorWhite),
            ),
          ),
        ),
      ),
      SizedBox(
        height: ScreenAdapter.height(10),
      ),
    ];
  }

  List<Widget> _pictureItem() {
    return [
      Gap(ScreenAdapter.height(84)),
      Obx(() => InkWell(
            onTap: () {
              _showBottomSheet();
            },
            child: controller.userHomeController.userModel.value.photo != null
                    ? CachedNetworkImage(
                        imageUrl: controller.userHomeController.userModel.value.photo!,
                        imageBuilder: (context, imageProvider) {
                          logger.d("查看imageProvider");
                          logger.d(imageProvider);
                          return Container(
                            height: ScreenAdapter.height(336),
                            width: ScreenAdapter.height(336),
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(
                                  ScreenAdapter.height(336) / 2),
                              image: DecorationImage(
                                image: imageProvider,
                              ),
                            ),
                          );
                        },
                        placeholder: (context, url) {
                          // logger.d("查看url$url");
                          return SizedBox(
                            height: ScreenAdapter.height(336),
                            width: ScreenAdapter.height(336),
                            child: CircularProgressIndicator(
                              color: AppColors.lightBlue,
                            ),
                          );
                        },
                        errorWidget: (context, url, error) => Container(
                            height: ScreenAdapter.height(336),
                            width: ScreenAdapter.height(336),
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(
                                  ScreenAdapter.height(60) / 2),
                              image: DecorationImage(
                                image: AssetImage(Assets.images.avatarDefault.path),
                              ),
                            ),
                          ),
                      )
                    : SizedBox()
            // CircleAvatar(
            //   backgroundColor: Colors.transparent,
            //   radius: ScreenAdapter.width(336) / 2,
            //   backgroundImage:
            //    controller.userHomeController.userModel.value.photo != "" 
            //     ? FileImage(File(controller.userHomeController.userModel.value.photo!))  // 使用本地路径
            //     : 
            //       controller.userHomeController.userModel.value.photo != null &&
            //               controller.userHomeController.userModel.value.photo!
            //                   .isNotEmpty
            //           ? NetworkImage(
            //               controller.userHomeController.userModel.value.photo!)
            //           : AssetImage(Assets.images.avatarDefault.path)
            //               as ImageProvider,
            //   onBackgroundImageError: (exception, stackTrace) {
            //     // Log or handle the error if needed
            //   },
            // ),
          ))
    
    ];
  }

// 显示底部弹出菜单
  void _showBottomSheet() {
    showModalBottomSheet(
      context: Get.context!,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          // padding: EdgeInsets.all(ScreenAdapter.width(32)),
          padding: EdgeInsets.symmetric(
              vertical: ScreenAdapter.height(12),
              horizontal: ScreenAdapter.width(16)),

          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InkWell(
                                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  logger.d("去拍早");
                  controller.takePhoto();
                },
                child: Container(
                  height: ScreenAdapter.height(33),
                  alignment: Alignment.topCenter,
                  child: Text(
                    T.commonShootNow.tr,
                    style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(15),
                        height: 21 / 15.0,
                        fontWeight: FontWeight.w500,
                        color: AppColors.Color666),
                  ),
                ),
              ),
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  logger.d("去相册");
                  controller.pickImage();
                },
                child: Container(
                  height: ScreenAdapter.height(33),
                  alignment: Alignment.bottomCenter,
                  child: Text(
                    T.commonPhotoUpload.tr,
                    style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(15),
                        height: 21 / 15.0,
                        fontWeight: FontWeight.w500,
                        color: AppColors.Color666),
                  ),
                ),
              ),
              Gap(ScreenAdapter.height(16)),
              InkWell(
                onTap: () {
                  Get.back();
                },
                child: Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  height: ScreenAdapter.height(36),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      color: AppColors.lightBlue),
                  child: Text(
                    T.wordsCancel.tr,
                    style: normalF12H17C999.copyWith(
                        color: Colors.white, fontWeight: FontWeight.w500),
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
