/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-30 14:46:43
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-07 16:21:04
 * @FilePath: /rpmappmaster/lib/app/modules/user/user_modif/controllers/user_modif_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:convert';
import 'dart:io';

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/user/user_home/controllers/user_controller.dart';
import 'package:aiCare/app/services/authorityService.dart';
import 'package:aiCare/app/services/toastHelper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

class UserModifController extends BaseController {
  int index = 0;
  TextEditingController textController = TextEditingController();
  UserHomeController userHomeController = Get.find();
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  final ImagePicker _picker = ImagePicker();

  @override
  void onInit() {
    super.onInit();
    index = Get.arguments['index'];
    logger.d("当前获取的index${index}");
  }

  @override
  void onReady() {
    super.onReady();
    initValue();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void toSubmit() async {
    // showLoading();
    Map<String, Object> map = {};
    switch (index) {
      case 0:
        break;
      case 1:
        map["name"] = textController.text; // 添加键值对
        break;
      default:
        return null;
    }
    if (map.length != 0) {
      bool result = await callDataService(defaultRepositoryImpl.patchInfo(map),
          onSuccess: (response) {
        ToastUtil.showSuccess(Get.context!, T.promptModifSuccess.tr);
        userHomeController.userModel.value.name = textController.text;
        userHomeController.userModel.refresh();
        storage.setString(AppValues.userInfo,
            jsonEncode(userHomeController.userModel.value.toJson()));
      });
    }
  }

  void initValue() {
    switch (index) {
      case 0:
        break;
      case 1:
        textController.text = userHomeController.userModel.value.name != null
            ? userHomeController.userModel.value.name!
            : "";
        return;

      default:
        return null;
    }
  }

  // 调用相机拍照
  Future<void> takePhoto() async {
    // 先检查相机权限
    // final hasPermission = await AuthorityService.instance.getCameraPermission(Get.context!);
    // if (!hasPermission) {
    //   ToastHelper.Failed(T.promptCameraNo);
    //   //  logger.d(T.promptCameraNo);
    //   return;
    // }

    final pickedFile = await _picker.pickImage(source: ImageSource.camera);

    if (pickedFile != null) {
      File? croppedFile = await _cropImage(File(pickedFile.path)); // 裁剪图片
      if (croppedFile != null) {
        // showLoading();
        //上传图片
        String imageString = callDataService(
          defaultRepositoryImpl.uploadImage(croppedFile),
          onSuccess: (response) {
            if (response != null) {
//  logger.d(imageString);
              userHomeController.userModel.value.photo = response;
              userHomeController.userModel.refresh();
              storage.setString(AppValues.userInfo,
                  jsonEncode(userHomeController.userModel.value.toJson()));
              Get.back(); // 关闭底部弹窗
            }
          },
        );
        // hideLoading();
      }
    }
  }

  // pickImage() async {
  //   final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
  //   if (pickedFile != null) {
  //     File? croppedFile = await _cropImage(File(pickedFile.path)); // 裁剪图片
  //     if (croppedFile != null) {
  //       showLoading();
  //       //上传图片
  //       String imageString =
  //           await defaultRepositoryImpl.uploadImage(croppedFile);
  //       if (imageString != "") {
  //         logger.d(imageString);
  //         userHomeController.userModel.value.photo = imageString;
  //         userHomeController.userModel.refresh();
  //         storage.setData(AppValues.userInfo,
  //             jsonEncode(userHomeController.userModel.value.toJson()));
  //       }
  //       hideLoading();
  //     }
  //   }
  // }

  pickImage() async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      File? croppedFile = await _cropImage(File(pickedFile.path)); // 裁剪图片
      if (croppedFile != null) {
        // showLoading();
        // 上传图片
        try {
          String imageString = callDataService(
            defaultRepositoryImpl.uploadImage(croppedFile),
            onSuccess: (response) {
              if (response != null) {
                userHomeController.userModel.value.photo = response;
                userHomeController.userModel.refresh();
                storage.setString(AppValues.userInfo,
                    jsonEncode(userHomeController.userModel.value.toJson()));
                Get.back(); // 关闭底部弹窗
              }
            },
          );
        } catch (e) {
          // hideLoading();
          // Get.snackbar('Error', 'Failed to upload image.',
          //     snackPosition: SnackPosition.BOTTOM);
          logger.e(e);
        }

        // 获取设备的存储路径
        // String localPath = await getLocalDirectory();
        String fileName =
            DateTime.now().millisecondsSinceEpoch.toString() + '.jpg'; // 文件名
        // File localFile = File('$localPath/$fileName');

        // 将裁剪后的文件写入本地存储
        // await croppedFile.copy(localFile.path);

        // 可选：保存路径信息
        // logger.d("Saved image to: ${localFile.path}");

        // hideLoading();
      }
    }
  }

  // 裁剪图片
  Future<File?> _cropImage(File imageFile) async {
    final croppedFile = await ImageCropper().cropImage(
      sourcePath: imageFile.path,
      aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1), // 裁剪为正方形
      compressQuality: 90, // 压缩图片质量
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Crop Image',
          toolbarColor: Colors.blue,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: true,
        ),
        IOSUiSettings(
          title: 'Crop Image',
          doneButtonTitle: 'Done',
          cancelButtonTitle: 'Cancel',
        ),
      ],
    );
    return croppedFile != null ? File(croppedFile.path) : null;
  }
}
