/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-30 13:22:16
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-07 15:06:43
 * @FilePath: /rpmappmaster/lib/app/modules/user/user_info/views/user_info_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:io';

import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';

import '../controllers/user_info_controller.dart';

class UserInfoView extends BaseView<UserInfoController> {
  UserInfoView({
    super.key,
  }) : super(bgColor: AppColors.colorWhite,statusBarColor: AppColors.colorWhite);

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: T.userPersonInfo.tr,
      // customHeight: ScreenAdapter.height(44),
      backgroundColor: AppColors.colorWhite,
      // bottomWidget: lineLightGreey,
    );
  }

  @override
  Widget body(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      child: Column(
        children: [0, 1, 2, 3, 4].map((e) {
          if (e == 0) return _topItem();
          return _normalItem(e);
        }).toList(),
      ),
    );
  }

  Widget _topItem() {
    return InkWell(
      onTap: () {
        logger.d("修改头像");
       Get.toNamed(Routes.USER_MODIF, arguments: {"index": 0});
      },
      child: Container(
        height: ScreenAdapter.height(84),
        // color: Colors.red,
        padding: EdgeInsets.only(bottom: ScreenAdapter.height(24)),
        margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
        child: Row(
          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              T.commonProfilePicture.tr,
              style: normalF14H19C666.copyWith(fontWeight: FontWeight.w600),
            ),
            Expanded(child: SizedBox()),
            Obx(()=>
            controller.userHomeController.userModel.value.photo != null
                    ? CachedNetworkImage(
                        imageUrl: controller.userHomeController.userModel.value.photo!,
                        imageBuilder: (context, imageProvider) {
                          logger.d("查看imageProvider");
                          logger.d(imageProvider);
                          return Container(
                            height: ScreenAdapter.height(60),
                            width: ScreenAdapter.height(60),
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(
                                  ScreenAdapter.height(60) / 2),
                              image: DecorationImage(
                                image: imageProvider,
                              ),
                            ),
                          );
                        },
                        placeholder: (context, url) {
                          // logger.d("查看url$url");
                          return SizedBox(
                            height: ScreenAdapter.height(60),
                            width: ScreenAdapter.height(60),
                            child: CircularProgressIndicator(
                              color: AppColors.lightBlue,
                            ),
                          );
                        },
                        errorWidget: (context, url, error) => Container(
                            height: ScreenAdapter.height(60),
                            width: ScreenAdapter.height(60),
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(
                                  ScreenAdapter.height(60) / 2),
                              image: DecorationImage(
                                image: AssetImage(Assets.images.avatarDefault.path),
                              ),
                            ),
                          ),
                      )
                    : SizedBox()
            
            // CircleAvatar(
            //   backgroundColor: Colors.transparent,
            //   radius: ScreenAdapter.height(60)/2,
            //   backgroundImage:
            //     controller.userHomeController.userModel.value.photo != "" 
            //     ? FileImage(File(controller.userHomeController.userModel.value.photo!))  // 使用本地路径
            //     :  controller.userHomeController.userModel.value.photo != null &&
            //               controller.userHomeController.userModel.value.photo!
            //                   .isNotEmpty
            //           ? NetworkImage(
            //               controller.userHomeController.userModel.value.photo!)
            //           : AssetImage(Assets.images.avatarDefault.path)
            //               as ImageProvider,
            //   onBackgroundImageError: (exception, stackTrace) {
            //     // Log or handle the error if needed
            //   },
            // )),
            // CircleAvatar(
            //   radius: ScreenAdapter.height(60)/2,
            //   child:  Obx(
            //         () => controller.userHomeController.userModel.value.photo == null 
            //             ? Image.asset(
            //                 Assets.images.avatarDefault.path,
            //                 fit: BoxFit.contain,
            //               )
            //             : Image.network(
            //                 controller.userHomeController.userModel.value.photo!,
            //                 fit: BoxFit.contain,
            //                 errorBuilder: (context, error, stackTrace) {
            //                   // 如果网络图片加载失败，显示默认头像
            //                   return Image.asset(
            //                     Assets.images.avatarDefault.path,
            //                     fit: BoxFit.contain,
            //                   );
            //                 },
            //               ),
            //       ),
            ),
            
            Gap(ScreenAdapter.width(12)),
            SizedBox(
              height: ScreenAdapter.height(12),
              child: AspectRatio(
                aspectRatio: 1,
                child: SvgPicture.asset(
                  Assets.images.rightArrow48,
                  fit: BoxFit.contain,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _normalItem(int i) {
    return InkWell(
      onTap: () {
        switch (i) {
          case 1:
            logger.d("修改名称");
            Get.toNamed(Routes.USER_MODIF, arguments: {"index": 1});
            return;
          case 2:
            logger.d("修改ID");
            // Get.toNamed(Routes.USER_MODIF, arguments: {"index": 1});
            return;
          case 3:
            logger.d("修改性别");
            _showGenderBottomDialog(Get.context!);
            return;
          case 4:
            logger.d("修改生日");
            _showBirthBottomDialog(Get.context!);
            return;
          default:
            logger.d("_normalItem的i报错了");
        }
      },
      child: Container(
        height: ScreenAdapter.height(69),
        padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(24)),
        decoration: BoxDecoration(
            border: Border(
                top: BorderSide(
          color: Color.fromRGBO(235, 235, 235, 0.6),
        ))),
        child: Row(
          children: [
            Text(
              normalItemLeftText(i),
              style: normalF14H19C666.copyWith(fontWeight: FontWeight.w600),
            ),
            Expanded(child: SizedBox()),
            Obx(()=>Text(
              normalItemRightText(i),
              style: normalF14H19C666.copyWith(fontWeight: FontWeight.w600),
            ),),
            Gap(ScreenAdapter.width(12)),
            SizedBox(
              height: ScreenAdapter.height(12),
              child: AspectRatio(
                aspectRatio: 1,
                child: SvgPicture.asset(
                  Assets.images.rightArrow48,
                  fit: BoxFit.contain,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  String normalItemLeftText(int i) {
    switch (i) {
      case 1:
        return T.wordsName.tr;
      case 2:
        return T.wordsID.tr;
      case 3:
        return T.wordsGender.tr;
      case 4:
        return T.commonDateOfBirth.tr;
      default:
        return "出错了";
    }
  }

  String normalItemRightText(int i) {
    switch (i) {
      case 1:
        return controller.userHomeController.userModel.value.name!=null ? controller.userHomeController.userModel.value.name!: "defaultName1212";
      case 2:
        return controller.userHomeController.userModel.value.userId!=null ? controller.userHomeController.userModel.value.userId!: "defaultID1212";;
      case 3:
          if(controller.userHomeController.userModel.value.gender!=null){
            if(controller.userHomeController.userModel.value.gender! == "M")
              return T.wordsMale.tr;
            if(controller.userHomeController.userModel.value.gender! == "F")
              return T.wordsFemale.tr;
            return T.wordsOther.tr;
          }else  return "DefualtGender";
      case 4:
       if(controller.userHomeController.userModel.value.birthdate!=null){
            if(controller.userHomeController.userModel.value.birthdate == "")
              return "1997.12.13";
            
            return controller.userHomeController.userModel.value.birthdate!;
          }else  return "1997.12.13";
      default:
        return "出错了";
    }
  }

  void _showBirthBottomDialog(BuildContext context) {


    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white, // 设置对话框的背景颜色
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)), // 圆角样式
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(ScreenAdapter.height(16)), // 设置内容的内边距
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min, // 根据内容大小设置弹出高度
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Text(
                      T.wordsCancel.tr,
                      style: normalF16H22C666,
                    ),
                  ),
                  Text(
                    T.wordsTime.tr,
                    style: w600F16H22C333,
                  ),
                  InkWell(
                    onTap: () async {
                      // controller.submitRemind(true);
                      // Navigator.pop(context); // 关闭底部弹窗
                      controller.submitBirthday();
                    },
                    child: Text(
                      T.wordsSave.tr,
                      style:
                          normalF16H22C666.copyWith(color: AppColors.lightBlue),
                    ),
                  )
                ],
              ),
              // SizedBox(height: 16.0),
              Gap(ScreenAdapter.height(28)),
              SizedBox(
                width: double.infinity,
                height: ScreenAdapter.height(240),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 年选择器
                    Expanded(
                      child: CupertinoPicker(
                        itemExtent: 40, // 每一行的高度
scrollController: FixedExtentScrollController(
                      initialItem: controller.years.indexOf(controller.selectedYear.value),
                    ),
                        onSelectedItemChanged: (index) {
                          controller.selectedYear.value = controller.years[index];
                          controller.updateDaysInMonth(); // 更新日期范围
                        },
                        children: controller.years
                            .map((year) => Center(child: Text('$year')))
                            .toList(),
                  
                    )),
                    // 月选择器
                    Expanded(
                      child: CupertinoPicker(
                        itemExtent: 40,
                        scrollController: FixedExtentScrollController(
                      initialItem: controller.months.indexOf(controller.selectedMonth.value),
                    ),
                        onSelectedItemChanged: (index) {
                          // setState(() {
                          controller.selectedMonth.value = controller.months[index];
                          controller.updateDaysInMonth(); // 更新日期范围
                          // });
                        },
                        children: controller.months
                            .map((month) => Center(
                                child: month < 10
                                    ? Text('0$month')
                                    : Text('$month')))
                            .toList(),
                      ),
                    ),
                    // 日选择器
                    Expanded(
                      child: Obx(()=>CupertinoPicker(
                        itemExtent: 40,
                        scrollController: FixedExtentScrollController(
                      initialItem: controller.days.indexOf(controller.selectedDay.value),
                    ),
                        onSelectedItemChanged: (index) {
                      controller.selectedDay.value = controller.days[index];
                    },
                        children: controller.days
                            .map((day) => Center(
                                child: day < 10 ? Text('0$day') : Text('$day')))
                            .toList(),
                      ),
                    )
                    ),
                  
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  
  }

  void _showGenderBottomDialog(BuildContext context) async{
   await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white, // 设置对话框的背景颜色
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)), // 圆角样式
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(ScreenAdapter.height(16)), // 设置内容的内边距
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min, // 根据内容大小设置弹出高度
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Text(
                      T.wordsCancel.tr,
                      style: normalF16H22C666,
                    ),
                  ),
                  InkWell(
                    onTap: () async {
                      // controller.submitRemind(true);
                      controller.submitGender();
                      // Navigator.pop(context); // 关闭底部弹窗
                    },
                    child: Text(
                      T.wordsSave.tr,
                      style:
                          normalF16H22C666.copyWith(color: AppColors.lightBlue),
                    ),
                  )
                ],
              ),
              // SizedBox(height: 16.0),
              Gap(ScreenAdapter.height(28)),
              SizedBox(
                width: double.infinity,
                // height: ScreenAdapter.height(240),
                child: Column(
                  // mainAxisAlignment: MainAxisAlignment.center,
                  // mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      // enableFeedback: false,
                      // splashColor:Colors.transparent,
                      onTap: () {
                       controller.gender.value = "M";
                      },
                      child: Container(
                        height: ScreenAdapter.height(48),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12)
                        ),
                        padding: EdgeInsets.symmetric(
                            vertical: ScreenAdapter.height(12)),
                        // color: Colors.red,
                        alignment: Alignment.center,
                        child: Obx(()=>Text(
                          T.wordsMale.tr,
                          style: normalF16H22C666.copyWith(
                              fontWeight: FontWeight.w600,
                              color: controller.gender.value == "M" ? AppColors.lightBlue : AppColors.Color666
                              ),
                        )),
                      ),
                    ),
                    Container(
                      width:double.infinity,
                      height: 1,
                      color: Color(0xFFF5F5F5),     
                    ),
                    InkWell(
                      onTap: () {
                        controller.gender.value = "F";
                      },
                      child: Container(
                        height: ScreenAdapter.height(48),
                        padding: EdgeInsets.symmetric(
                            vertical: ScreenAdapter.height(12)),
                        // color: Colors.red,
                        alignment: Alignment.center,
                        child: Obx(()=>Text(
                          T.wordsFemale.tr,
                          style: normalF16H22C666.copyWith(
                              fontWeight: FontWeight.w600,
                              color: controller.gender.value == "F" ? AppColors.lightBlue : AppColors.Color666
                              ),
                        )),
                      ),
                    ),
                                     Container(
                      width:double.infinity,
                      height: 1,
                      color: Color(0xFFF5F5F5),     
                    ),
                    InkWell(
                      onTap: () {
                        controller.gender.value = "U";
                      },
                      child: Container(
                        height: ScreenAdapter.height(48),
                        padding: EdgeInsets.symmetric(
                            vertical: ScreenAdapter.height(12)),
                        // color: Colors.red,
                        alignment: Alignment.center,
                        child: Obx(()=>Text(
                          T.wordsOther.tr,
                          style: normalF16H22C666.copyWith(
                              fontWeight: FontWeight.w600,
                              color:controller.gender.value == "U" ? AppColors.lightBlue : AppColors.Color666
                              ),
                        )),
                      ),
                    ),
                 
                 
                 
                  ],
                ),
              )
            ],
          ),
        );
      },
    );

  controller.initGender();
  }
}
