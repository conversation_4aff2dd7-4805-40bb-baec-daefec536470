import 'dart:convert';

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/repository/default_repository.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/user/user_home/controllers/user_controller.dart';
import 'package:aiCare/app/services/toastHelper.dart';
import 'package:get/get.dart';

class UserInfoController extends BaseController {
  UserHomeController userHomeController = Get.find();
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();

  RxString gender = "U".obs;

  // 默认选中的日期
  RxInt selectedYear = DateTime.now().year.obs;
  RxInt selectedMonth = DateTime.now().month.obs;
  RxInt selectedDay = DateTime.now().day.obs;

  RxList<int> days = <int>[].obs; // 初始化为空列表

  // 年、月、日的范围
  late List<int> years; // 2000-2100
  late List<int> months; // 1-12

  // RxBool templateBool = true.obs;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    initGender();
    initDate();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void initGender() {
    if (userHomeController.userModel.value.gender == "M")
      gender.value = "M";
    else if (userHomeController.userModel.value.gender == "F")
      gender.value = "F";
    else
      gender.value = "U";
  }

  void submitGender() async {
    showLoading();
    Map<String, Object> map = {};
    map["gender"] = gender.value; // 添加键值对
    bool result = await defaultRepositoryImpl.patchInfo(map);
    if (result) {
      ToastUtil.showSuccess(Get.context!, T.promptModifSuccess.tr);
      userHomeController.userModel.value.gender = gender.value;
      userHomeController.userModel.refresh();
      storage.setString(AppValues.userInfo,
          jsonEncode(userHomeController.userModel.value.toJson()));
      hideLoading();
      Get.back();
    } else {
      ToastUtil.showError(Get.context!, T.promptModifFailed.tr);
      hideLoading();
    }
  }

  void initDate() {
    // 年、月、日的范围
    years = List.generate(121, (index) => DateTime.now().year - index); // 2000-2100
    months = List.generate(12, (index) => index + 1); // 1-12
    
    if (userHomeController.userModel.value.birthdate != null) {
      try {
        // 将字符串转为 DateTime
        DateTime birthdate =
            DateTime.parse(userHomeController.userModel.value.birthdate!);
        // 提取年、月、日
        selectedYear.value = birthdate.year;
        selectedMonth.value = birthdate.month;
        selectedDay.value = birthdate.day;
        // 输出年月日
        logger.d(
            "Year: ${selectedYear.value}, Month: ${selectedMonth.value}, Day: ${selectedDay.value}.");
      } catch (e) {
        print("日期解析失败: $e");
      }
    }
    updateDaysInMonth();
  }

  // 根据年和月更新日期范围
  void updateDaysInMonth() {
    final int daysInMonth =
        DateTime(selectedYear.value, selectedMonth.value + 1, 0).day;
    days.value = List.generate(daysInMonth, (index) => index + 1); // 1到当前月的最大天数
  }

  void submitBirthday() async{
    String resultString = "${selectedYear.value}-${selectedMonth.value <10 ?'0'+selectedMonth.value.toString() : selectedMonth.value}-${selectedDay.value <10 ?'0'+selectedDay.value.toString() : selectedDay.value}";
    // logger.d(resultString);
        // showLoading();
    Map<String, Object> map = {};
    map["birthdate"] = resultString; // 添加键值对
    bool result = await callDataService(defaultRepositoryImpl.patchInfo(map), onSuccess: (response) {
      ToastUtil.showSuccess(Get.context!, T.promptModifSuccess.tr);
      userHomeController.userModel.value.birthdate = resultString;
      userHomeController.userModel.refresh();
      storage.setString(AppValues.userInfo,
          jsonEncode(userHomeController.userModel.value.toJson()));
          Get.back();
      
    });
    
  }
}
