/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-07 14:35:37
 * @FilePath: /rpmappmaster/lib/app/modules/user/controllers/user_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:convert';

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/data/model/user_model.dart';
import 'package:aiCare/app/data/repository/auth_repository_impl.dart';
import 'package:aiCare/app/data/repository/default_repository.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart';

class UserHomeController extends BaseController {
  AuthRepositoryImpl authRepositoryImpl = AuthRepositoryImpl();
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  BluetoothController bluetoothController = Get.find();
  // Rx<AizoRing> aizoRing = AizoRing(name: "AIZO RING",isSystemConnected: false).obs;
  TabsController tabsController = Get.find();
  Rx<UserModel> userModel = UserModel().obs;
  // 新增请求控制参数
  RxBool requestParam = false.obs;


  RxList<AizoRing> devices = <AizoRing>[].obs;

  @override
  void onInit() async {
    super.onInit();

    // logger.d("userController初始化");
    // if(tabsController.connectList.length!=0){
    //   aizoRing.value = tabsController.connectList.value[0];
    // }
    await getUserInfo();
    // ever(requestParam, (value) {
    //   logger.d("value的值$value");
    //   if (value == true) {
    //     getBluetooth();
        
    //     // getavatarLocal();
    //     // 请求后自动重置状态（根据需求决定是否保留）
    //     requestParam.value = false;
    //     getDevices();
    //   }
    // });
    // ever(tabsController.bluetoothConnect, (bool isConnected) {
    //   if (!isConnected) {
    //     setAllDevicesDisconnected(false);
    //   } else {
    //     setAllDevicesDisconnected(true);
    //   }
    // });
    // refreshData();
  }

  @override
  void onReady() async {
    super.onReady();
    // logger.d("userController准备好了");
  }

  @override
  void onClose() {
    super.onClose();
  }

  void exit() {
    authRepositoryImpl.logout();
  }

  void getBluetooth() async {
    // logger.d("查看已连接设备里面有没有？");
    // logger.d(await storage.getData(AppValues.bluetoothList));
  }

  Future<void> getUserInfo() async {
    try {
      // 1. 首先尝试从本地获取数据
      storage.delete(AppValues.userInfo);
      var localUser = storage.getUserInfo();
      
      
      if (localUser == null) {
        // 2. 本地没有数据，从服务器获取
        try {
          // 使用 callDataService 调用 getInfo
          var result = await defaultRepositoryImpl.getInfo();
          
          // 如果结果是 null（500 错误），创建默认用户
          if (result == null || result.target == null) {
            print("result为空，进行创建");
            await defaultRepositoryImpl.postInfo();
            // 重新获取用户数据
            var temp = await defaultRepositoryImpl.getInfo();
            if(temp != null){
              this.userModel.value = temp;
              storage.setUserInfo(this.userModel.value);
            }else{
              logger.e("Failed to fetch user data after creation");
            }
          } else {
             this.userModel.value = result;
             storage.setUserInfo(this.userModel.value);
          }
          
          // 5. 更新本地数据
          setUserLocal();
        } catch (e) {
          logger.e("Error in server communication: $e");
          // 如果服务器请求失败，使用默认用户数据
          this.userModel.value = UserModel();
          setUserLocal();
        }
      } else {
        // 6. 使用本地数据
        this.userModel.value = localUser;
      }

      // 7. 刷新UI
      this.userModel.refresh();
      // logger.d("User data loaded successfully: ${userModel.value.toJson()}");
    } catch (e) {
      logger.e("Error in getUserInfo: $e");
      // 确保即使出错也设置一个默认值
      this.userModel.value = UserModel();
      this.userModel.refresh();
    }
  }

  // 新增手动触发方法（可选）
  void refreshData() {
    requestParam.value = false;
    requestParam.value = true;
  }

  // void getavatarLocal() async {
  //   logger.d(userModel.value.photo);
  // }

  getDevices() async {
    var jsonData = await storage.getString(AppValues.androidDeviceID);
    if (jsonData != null) {
      try {
        // 解析JSON数据为AizoRing对象
        final decoded = jsonDecode(jsonData);
        List<AizoRing> newDevices = [];
        if (decoded is List) {
          // 如果存储的是列表，遍历每个元素
          newDevices = decoded.map((e) => AizoRing.fromJson(e)).toList();
        } else if (decoded is Map) {
          // 如果存储的是单个对象，转换为列表
          newDevices = [AizoRing.fromJson(decoded)];
        }
        // 使用 assignAll 方法更新列表内容
        devices.assignAll(newDevices);
        // 获取设备数据后检查蓝牙连接状态
        if (!tabsController.bluetoothConnect.value) {
          setAllDevicesDisconnected(false);
        }
      } catch (e) {
        logger.e("解析设备数据失败: $e");
        // return Center(child: Text("数据解析失败"));
      }
      logger.d("有设备历史连接");
    } else {
      logger.d("无设备历史连接");
    }
    if (devices.length != 0) {
      logger.d("当前device:${devices.value[0].toJson()}");
    }
  }

  // 将所有设备的 isSystemConnected 设置为 false
  void setAllDevicesDisconnected(bool value) {
    for (var device in devices) {
      device.isSystemConnected = value;
    }
    devices.refresh(); // 刷新列表以更新 UI
  }

  clearUserLocal() {
     storage.delete(AppValues.userInfo);
  }

  setUserLocal(){
     storage.setString(AppValues.userInfo, jsonEncode(this.userModel.value.toJson()));
  }


  // // 读取数据
  // Future<List<dynamic>?> loadUnitSetting() async {
  //   String? jsonString = await storage.getData(AppValues.unitSetting);
  //   if (jsonString != null) {
  //     // 解析 JSON 字符串
  //     return jsonString.split(';').map((subListString) {
  //       return subListString.split(',').map((itemString) {
  //         if (itemString == 'true') {
  //           return true;
  //         } else if (itemString == 'false') {
  //           return false;
  //         }
  //         // 不进行整数转换，直接返回字符串
  //         return itemString;
  //       }).toList();
  //     }).toList();
  //   }
  //   return null;
  // }
}
