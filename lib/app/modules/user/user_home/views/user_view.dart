/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-15 16:40:29
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/user/views/user_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:io';

import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/model/page_background.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/core/values/text_styles.dart';

import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/modules/bluetooth/bluetooth_connect/views/bluetooth_connect_view.dart';
import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';


import '../controllers/user_controller.dart';

class UserHomeView extends BaseView<UserHomeController> {
  UserHomeView({
    super.key,
  })       : super(
          bgColor: Colors.transparent,
          bgImage: PageBackground(
            imagePath: Assets.images.backBlueBlur.path,
            width: ScreenAdapter.width(375), // 宽度375
            height: ScreenAdapter.height(320), // 高度320
            fit: BoxFit.fitHeight, // 填充方式
            left: 0,
            // top: -MediaQuery.of(Get.context!).padding.top,
            top: 0,
          ),
          statusBarColor: AppColors.homeBgColor,
        );


  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {

    return Padding(padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),child: Column(
      children: [
        // _head(),
        Gap(ScreenAdapter.height(4)),
        _personInfor(),
        Gap(ScreenAdapter.height(12)),
        Obx(() => buildDeviceItem(
              // device: controller.devices.length != 0
              //     ? controller.devices[0]
              //     : AizoRing(
              //         names: "aiRing", isSystemConnected: false, macAddress: ""),
              // index: 0,
              // device: MyBluetoothDevice(remoteId: DeviceIdentifier("123")),
              device: controller.bluetoothController.lastConnectedDevice ?? MyBluetoothDevice(DeviceIdentifier(""), ""),
              index: 0,
              isLast: true,
              userView: true,
            )),
        Gap(ScreenAdapter.height(12)),
        _userCard(),
        // Gap(ScreenAdapter.height(20)),
      ],
    ));
  }
  

  _head() {
    return Container(
      width: double.infinity,
      height: ScreenAdapter.height(28),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          InkWell(
            onTap: () {
              logger.d("点击提醒了");
            },
            child: SizedBox(
              height: ScreenAdapter.height(24),
              child: AspectRatio(
                aspectRatio: 22.0 / 20,
                child: Image.asset(
                  Assets.images.notionMy.path,
                  // height: ScreenAdapter.height(24),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          Gap(ScreenAdapter.width(12)),
          InkWell(
            onTap: () {
              logger.d("点击设置了");
            },
            child: SizedBox(
              height: ScreenAdapter.height(24),
              child: AspectRatio(
                aspectRatio: 22.0 / 20,
                child: Image.asset(
                  Assets.images.settingMy.path,
                  // height: ScreenAdapter.height(24),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  // _personInfor() {
  //   return InkWell(
  //     onTap: () {
  //       logger.d("去个人信息页面");
  //       Get.toNamed(Routes.USER_INFO);
  //     },
  //     child: Container(
  //       width: double.infinity,
  //       height: ScreenAdapter.height(60),
  //       child: Row(
  //         crossAxisAlignment: CrossAxisAlignment.center,
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Obx(() => CircleAvatar(
  //                 backgroundColor: Colors.transparent,
  //                 radius: ScreenAdapter.height(60) / 2,
  //                 backgroundImage: controller.userModel.value.photo != null &&
  //                         controller.userModel.value.photo!.isNotEmpty
  //                     ? NetworkImage(controller.userModel.value.photo!)
  //                     : AssetImage(Assets.images.avatarDefault.path)
  //                         as ImageProvider,
  //                 onBackgroundImageError: (exception, stackTrace) {
  //                   // Log or handle the error if needed
  //                 },
  //               )),
  //           Gap(ScreenAdapter.width(12)),
  //           Obx(
  //             () => Text(
  //               controller.userModel.value.name != null
  //                   ? controller.userModel.value.name!
  //                   : "",
  //               // "",
  //               style: TextStyle(
  //                   fontWeight: FontWeight.w500,
  //                   fontSize: ScreenAdapter.fontSize(20),
  //                   height: 28.0 / 20,
  //                   color: AppColors.Color333),
  //             ),
  //           ),
  //           Expanded(child: SizedBox()),
  //           SizedBox(
  //             height: ScreenAdapter.height(12),
  //             child: AspectRatio(
  //               aspectRatio: 6 / 12,
  //               child: SvgPicture.asset(
  //                 Assets.images.rightArrow48,
  //                 fit: BoxFit.cover,
  //               ),
  //             ),
  //           )
  //         ],
  //       ),
  //     ),
  //   );
  // }

  _personInfor() {
    return InkWell(
      onTap: () {
        logger.d("去个人信息页面");
        Get.toNamed(Routes.USER_INFO);
      },
      child: Container(
        width: double.infinity,
        height: ScreenAdapter.height(60),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Obx(() => controller.userModel.value.photo != null
                    ? CachedNetworkImage(
                        imageUrl: controller.userModel.value.photo!,
                        imageBuilder: (context, imageProvider) {
                          // logger.d("查看imageProvider");
                          // logger.d(imageProvider);
                          return Container(
                            height: ScreenAdapter.height(60),
                            width: ScreenAdapter.height(60),
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(
                                  ScreenAdapter.height(60) / 2),
                              image: DecorationImage(
                                image: imageProvider,
                              ),
                            ),
                          );
                        },
                        placeholder: (context, url) {
                          // logger.d("查看url$url");
                          return SizedBox(
                            height: ScreenAdapter.height(60),
                            width: ScreenAdapter.height(60),
                            child: CircularProgressIndicator(
                              color: AppColors.lightBlue,
                            ),
                          );
                        },
                        errorWidget: (context, url, error) => Container(
                            height: ScreenAdapter.height(60),
                            width: ScreenAdapter.height(60),
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(
                                  ScreenAdapter.height(60) / 2),
                              image: DecorationImage(
                                image: AssetImage(Assets.images.avatarDefault.path),
                              ),
                            ),
                          ),
                      )
                    : SizedBox()
               
                ),
            Gap(ScreenAdapter.width(12)),
            Obx(
              () => Text(
                controller.userModel.value.name != null
                    ? controller.userModel.value.name!
                    : "",
                style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: ScreenAdapter.fontSize(20),
                    height: 28.0 / 20,
                    color: AppColors.Color333),
              ),
            ),
            Expanded(child: SizedBox()),
            SizedBox(
              height: ScreenAdapter.height(12),
              child: AspectRatio(
                aspectRatio: 6 / 12,
                child: SvgPicture.asset(
                  Assets.images.rightArrow48,
                  fit: BoxFit.cover,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  _userCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.colorWhite,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: EdgeInsets.symmetric(
          horizontal: ScreenAdapter.width(16),
          vertical: ScreenAdapter.height(8)),
      child: Column(
        children: [0, 1, 2].map((i) => _userCardItem(i)).toList(),
      ),
    );
  }

  String iconList(int i) {
    switch (i) {
      case 0:
        return Assets.images.healthGoalIcon.path;
      case 1:
        return Assets.images.unitSettingIcon.path;
      case 2:
        return Assets.images.accountSecurityIcon.path;
      default:
        return "出错了";
    }
  }

  textList(int i) {
    switch (i) {
      case 0:
        return T.userHealthGoal.tr;
      case 1:
        return T.userUnitSetting.tr;
      case 2:
        return T.userAccountSecurity.tr;
      default:
        return "出错了";
    }
  }

  Widget _userCardItem(int i) {
    return InkWell(
      onTap: () {
        switch (i) {
          case 0:
            // logger.d("health Goal Setting");
            Get.toNamed(Routes.GOAL_SETTING);
            break;
          case 1:
            // logger.d("unit Setting");
            Get.toNamed(Routes.UNIT_SETTING);
            break;
          case 2:
            // logger.d("account Setting");
            Get.toNamed(Routes.ACCOUNT_SECURITY);
            break;
          default:
            logger.d("index出错");
        }
      },
      child: Container(
        width: double.infinity,
        height: ScreenAdapter.height(40),
        child: Row(
          children: [
            SizedBox(
              height: ScreenAdapter.height(24),
              child: AspectRatio(
                aspectRatio: 1,
                child: Image.asset(
                  // Assets.images.healthGoalIcon.path,
                  iconList(i),
                ),
              ),
            ),
            Gap(ScreenAdapter.width(8)),
            Text(
              // appLocalization.userHealthGoal,
              textList(i),
              style: TextStyle(
                color: AppColors.Color666,
                fontSize: ScreenAdapter.fontSize(13),
                height: 14.71 / 13,
              ),
            ),
            Expanded(child: SizedBox()),
            SizedBox(
              height: ScreenAdapter.height(8),
              child: AspectRatio(
                aspectRatio: 4.5 / 8,
                child: SvgPicture.asset(
                  Assets.images.rightArrow48,
                  fit: BoxFit.cover,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   return Scaffold(
  //     appBar: AppBar(
  //       title: Text('UserView'),
  //       centerTitle: true,
  //     ),
  //     body: Column(
  //       children: [
  //         Center(
  //           child: TestI10nButton(),
  //         ),
  //         InkWell(
  //             onTap: () {
  //               controller.exit();
  //             },
  //             child: Container(
  //               width: ScreenAdapter.width(100),
  //               height: ScreenAdapter.height(28),
  //               child: Center(child: Text("LogOut"),),
  //             )),

  //       ],
  //     ),
  //   );
  // }
}
