/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-05-08 07:50:57
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-14 15:06:27
 * @FilePath: /RPM-APP/lib/app/modules/fitness/fitness_home/views/fitness_home_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:math';

import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/modules/fitness/fitness_home/widgets/fitness_holistic_painter.dart';
import 'package:aiCare/app/modules/fitness/fitness_home/widgets/fitness_ring_painter.dart';
import 'package:aiCare/app/core/widget/custom_home_bar.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';

import 'package:get/get.dart';

import '../controllers/fitness_home_controller.dart';

class FitnessHomeView extends BaseView<FitnessHomeController> {
  FitnessHomeView({
    super.key,
  }) : super(
          bgColor: AppColors.homeBgColor,
          statusBarColor: Colors.white,
        );

  @override
  Widget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: ScreenAdapter.height(812 - 44), // Set the minimum height
        ),
        child: Column(
          children: [
            CustomHomeBar(),
            _PieChart(),
            // _HolisticCard(),
            // OxygenLineChart(),
            // CustomDomain(list: controller.domainList),
            // OxygenDomain(),
          ],
        ),
      ),
    );
  }

  Widget _PieChart() {
    return Container(
      margin: EdgeInsets.only(
          top: ScreenAdapter.height(12),
          left: ScreenAdapter.width(16),
          right: ScreenAdapter.width(16)),
      width: double.infinity,
      constraints: BoxConstraints(minHeight: ScreenAdapter.height(288)),
      // height: ScreenAdapter.height(288),
      decoration: BoxDecoration(
        color: AppColors.colorWhite,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        // mainAxisSize: MainAxisSize.max,
        children: [
          Container(
              margin: EdgeInsets.only(
                  top: ScreenAdapter.height(8), right: ScreenAdapter.width(8)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    // T.fitnessAdjustGoals.tr,
                    "",
                    style: normalF12H17C999,
                  ),
                  Gap(ScreenAdapter.width(4)),
                  // SizedBox(
                  //   height: ScreenAdapter.height(6),
                  //   child: AspectRatio(
                  //     aspectRatio: 1 / 2.0,
                  //     child: SvgPicture.asset(Assets.images.rightArrow48),
                  //   ),
                  // )
                ],
              )),
          Container(
            margin: EdgeInsets.only(
              left: ScreenAdapter.width(12),
              right: ScreenAdapter.width(12),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              // mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  // width: ScreenAdapter.width(134),
                  constraints: BoxConstraints(
                    minWidth: ScreenAdapter.width(134),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T.fitnessDailyAct.tr,
                        style: normalF14H19C333.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.lightBlue,
                        ),
                      ),
                      //获得小卡片
                      ..._getPieChartSub()
                    ],
                  ),
                ),
                Expanded(child: SizedBox()),
                _RingChart()
              ],
            ),
          ),
          // Expanded(child: SizedBox()),
          Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(
                top: ScreenAdapter.height(30),
                bottom: ScreenAdapter.height(14)),
            width: double.infinity,
            constraints: BoxConstraints(minHeight: ScreenAdapter.height(17)),
            child: Obx(() {
              final date = controller.date.value;
              // 判断是否为默认时间
              if (date.year == 2000 && date.month == 1 && date.day == 1) {
                return Text("", style: normalF12H17C999.copyWith(fontWeight: FontWeight.w500, color: AppColors.Color666));
              }
              final localDateTime = date.toLocal();
              final formatted = DateFormat('yyyy-MM-dd hh:mm a').format(localDateTime);
              return Text(
                formatted,
                style: normalF12H17C999.copyWith(
                    fontWeight: FontWeight.w500, color: AppColors.Color666),
              );
            }),
          )
        ],
      ),
    );
  }

  List<Widget> _getPieChartSub() {
    return List.generate(
        3,
        (index) => _PieChartSubCard(
              marginTop: index == 0 ? 18 : 20, // 第一个元素间距 18，其余 20
              color: controller.pieSub.value.colrs[index],
              title: controller.pieSub.value.title[index],
              mainNum: controller.pieSub.value.mainNum[index],
              subNum: controller.pieSub.value.subNum[index],
            ));
  }

  Widget _PieChartSubCard(
      {required double marginTop,
      required Color color,
      required String title,
      num? mainNum,
      num? subNum}) {
    return Container(
        margin: EdgeInsets.only(top: marginTop),
        padding: EdgeInsets.only(left: ScreenAdapter.width(2)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  margin: EdgeInsets.only(right: ScreenAdapter.width(4)),
                  width: ScreenAdapter.width(3),
                  height: ScreenAdapter.height(8),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3), color: color),
                ),
                Text(
                  // appLocalization.steps,
                  title,
                  style: normalF12H17C666.copyWith(fontWeight: FontWeight.w500),
                )
              ],
            ),
            Gap(ScreenAdapter.height(6)),
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                children: [
                  // 第一个文本片段
                  WidgetSpan(
                    alignment: PlaceholderAlignment.bottom, // 关键属性
                    child: Text(
                      // "--",
                      mainNum == null ? "--" : mainNum.toString(),
                      style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(20),
                        height: 22.0 / 20,
                        fontWeight: FontWeight.w500,
                        color: AppColors.Color999,
                      ),
                    ),
                  ),
                  // 间距
                  WidgetSpan(
                    child: SizedBox(width: ScreenAdapter.width(2)),
                    alignment: PlaceholderAlignment.bottom, // 保持对齐一致
                  ),
                  // 第二个文本片段
                  WidgetSpan(
                    alignment: PlaceholderAlignment.bottom, // 关键属性
                    child: Text(
                      "/",
                      style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(10),
                        height: 12.0 / 10,
                        fontWeight: FontWeight.w500,
                        color: AppColors.Color999,
                      ),
                    ),
                  ),
                  // 间距
                  WidgetSpan(
                    child: SizedBox(width: ScreenAdapter.width(2)),
                    alignment: PlaceholderAlignment.bottom, // 保持对齐一致
                  ),
                  // 第二个文本片段
                  WidgetSpan(
                    alignment: PlaceholderAlignment.bottom, // 关键属性
                    child: Text(
                      // "--",
                      subNum == null ? "--" : subNum.toString(),
                      style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(10),
                        height: 12.0 / 10,
                        fontWeight: FontWeight.w500,
                        color: AppColors.Color999,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  // 构建指示器小图标
  Widget _buildRingIndicator(
      double centerX, double top, String imageString, int index) {
    return Positioned(
      top: top - ScreenAdapter.height(13 / 2.0), // 15为图标高度一半
      // left: centerX - ScreenAdapter.height(13/2.0), // 15为图标宽度一半
      left: index != 0
          ? centerX - ScreenAdapter.height(13 / 2 + 0.8)
          : centerX - ScreenAdapter.height(13 / 2),
      child: SizedBox(
        height: ScreenAdapter.height(13),
        child: AspectRatio(
          aspectRatio: 1,
          child: Image.asset(imageString),
        ),
      ),
    );
  }

  Widget _RingChart() {
    return Container(
      margin: EdgeInsets.only(top: ScreenAdapter.height(32)),
      height: ScreenAdapter.height(172),
      width: ScreenAdapter.height(172),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final containerSize = constraints.biggest;
          final centerX = containerSize.width / 2;
          final centerY = containerSize.height / 2;

          final strokeWidth = ScreenAdapter.height(18);

          // 圆环参数计算
          final stepRadius =
              containerSize.width / 2 - ScreenAdapter.height(20) / 2;
          final distanceRadius =
              stepRadius - strokeWidth - ScreenAdapter.height(2); // 中间留出间距
          final consumRadius =
              distanceRadius - strokeWidth - ScreenAdapter.height(2);

          return Stack(
            alignment: Alignment.center,
            children: [
              // 背景图片
              AspectRatio(
                aspectRatio: 1,
                child: Image.asset(
                  Assets.images.fitnessPie.path,
                  fit: BoxFit.cover,
                ),
              ),

              // 绘制圆环
              Obx(
                () => CustomPaint(
                  size: containerSize,
                  painter: FitnessRingPainter(
                    stepRadius: stepRadius,
                    stepProgress: controller.stepProgress.value != 0
                        ? controller.stepProgress.value /
                            controller.stepGoal.value
                        : 0.00000000001,
                    distanceRadius: distanceRadius,
                    distanceProgress: controller.distanceProgress.value != 0
                        ? controller.distanceProgress.value /
                            controller.distanceGoal.value
                        : 0.00000000001,
                    consumRadius: consumRadius,
                    consumProgress: controller.consumProgress.value != 0
                        ? controller.consumProgress.value /
                            controller.consumGoal.value
                        : 0.00000000001,
                    strokeWidth: strokeWidth,
                    colorList: controller.pieSub.value.colrs,
                  ),
                ),
              ),

              // 外层圆环图标
              _buildRingIndicator(centerX, centerY - stepRadius,
                  Assets.images.fitnessStep.path, 0),
              // 中层圆环图标
              _buildRingIndicator(centerX, centerY - distanceRadius,
                  Assets.images.fitnessDistance.path, 1),
              // 内层圆环图标
              _buildRingIndicator(centerX, centerY - consumRadius,
                  Assets.images.fitnessConsumFire.path, 2),
            ],
          );
        },
      ),
    );
  }

  Widget _HolisticCard() {
    return Container(
      // height: ScreenAdapter.height(336),
      width: double.infinity,
      margin: EdgeInsets.symmetric(
          horizontal: ScreenAdapter.width(16),
          vertical: ScreenAdapter.height(8)),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(6)),
      child: Column(
        children: [
          //title
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(
                left: ScreenAdapter.width(8), top: ScreenAdapter.height(8)),
            child: Text(
              T.healthHolistic.tr,
              style: normalF12H17C999.copyWith(
                  color: AppColors.Color666, fontWeight: FontWeight.w500),
            ),
          ),
          //score
          Container(
            width: ScreenAdapter.width(280),
            height: ScreenAdapter.height(100),
            margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
            child: Row(
              children: [
                Container(
                    height: ScreenAdapter.height(100),
                    width: ScreenAdapter.height(100),
                    child: Stack(
                      children: [
                        Positioned(
                            top: ScreenAdapter.height(50),
                            left: ScreenAdapter.height(50),
                            child: CustomPaint(
                              painter: FitnessHolisticPainter(
                                  strokeWidth: ScreenAdapter.height(8),
                                  radius: ScreenAdapter.height(100 / 2) -
                                      ScreenAdapter.height(8 / 2)),
                            )),
                        Positioned(
                            top: ScreenAdapter.height(29),
                            left: 0,
                            right: 0,
                            child: Text(
                              "--",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  fontSize: ScreenAdapter.fontSize(36),
                                  height: 42.0 / 36,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.Color999),
                            )),
                            Positioned(
                            top: ScreenAdapter.height(71),
                            left: 0,
                            right: 0,
                            child: Text(
                              T.wordsScore.tr,
                              textAlign: TextAlign.center,
                              style: normalF12H17C999.copyWith(
                                height: 12.0/10,
                              ),
                            )),
                      ],
                    )),
                Expanded(child: SizedBox()),
                Container(
                  // margin: EdgeInsets.symmetric(
                  //     horizontal: ScreenAdapter.width(14)),
                  height: ScreenAdapter.height(100),
                  // width: ScreenAdapter.width(2),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.searchBdColor,width: 1)
                  ),
                  // color: AppColors.searchBdColor,
                  
                ),
                Expanded(child: SizedBox()),
                Container(
                  width: ScreenAdapter.width(150),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            height: ScreenAdapter.height(48),
                            child: AspectRatio(
                              aspectRatio: 1,
                              child: Image.asset(
                                Assets.images.fitnessHolisticPeopleNo.path,
                              ),
                            ),
                          ),
                          Gap(ScreenAdapter.width(8)),
                          Text(T.commonNoData.tr,
                          style: TextStyle(
                            fontSize: ScreenAdapter.fontSize(20),
                            height: 28.0/20,
                            color: AppColors.Color999,
                            fontWeight: FontWeight.w500
                          ),)
                        ],
                      ),
                      // Gap(ScreenAdapter.height(8))
                      Container(
                        margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
                        width: double.infinity,
                        // height: ,
                        constraints: BoxConstraints(
                          minHeight: ScreenAdapter.height(28)
                        ),
                        child: Text(
                          T.fitnessPromptNo.tr,
                          style: normalF12H17C999,
                          softWrap:true,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
          //sheet
          Container(
            margin: EdgeInsets.only(top: ScreenAdapter.height(17.5),bottom: ScreenAdapter.height(10)),
            width: ScreenAdapter.width(287),
            child: Column(
              children: [
                _HolisticSubSheet(T.fitnessStayAct.tr),
                _HolisticSubSheet(T.fitnessBeneficialAct.tr),
                _HolisticSubSheet(T.fitnessExerciseVol.tr),
                _HolisticSubSheet(T.fitnessExerciseFre.tr),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _HolisticSubSheet(String title){
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: ScreenAdapter.height(8)),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: normalF12H17C999,
              ),
              Text(
                "--",
                style: normalF12H17C999,
              )
            ],
          ),
          Gap(ScreenAdapter.height(4)),
          Container(
            width: double.infinity,
            height: ScreenAdapter.height(6),
            decoration: BoxDecoration(
              color: Color(0xFFDEDEDE),
              borderRadius: BorderRadius.circular(4)
            ),
          ),
        ],
      ),
    );
  }
}
