import 'dart:math';

import 'package:flutter/material.dart';

class FitnessHolisticPainter extends CustomPainter {
  final double strokeWidth;
  final double radius;

  FitnessHolisticPainter({required this.strokeWidth,required this.radius});

  @override
  void paint(Canvas canvas, Size size) {
    final center = size.center(Offset.zero);
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

      paint.color = Color(0xFFDEDEDE);

    canvas.drawArc(
        // Rect.fromCircle(center: center, radius: radii[i]),
        Rect.fromCircle(center: center, radius: radius),
        -pi / 2, // 起始角度（与原逻辑一致）
        2 * pi, // 绘制整圆（与原逻辑一致）
        false,
        paint,
      );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
