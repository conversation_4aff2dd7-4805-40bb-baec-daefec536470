/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-05-08 10:56:34
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-08 11:32:30
 * @FilePath: /RPM-APP/lib/app/modules/fitness/fitness_home/widgets/fitness_ring_painter.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// 自定义绘制器
import 'dart:math';

import 'package:flutter/material.dart';

class FitnessRingPainter extends CustomPainter {
  final double strokeWidth;
  final List<double> radii; // 整合外/中/内层半径 [stepRadius, middistanceRadiusdle, consumRadius]
  final List<Color> colorList;
  final List<double> progressi; // 新增进度参数

  FitnessRingPainter({
    required double stepRadius,
    required double stepProgress,
    required double distanceRadius,
    required double distanceProgress,
    required double consumRadius,
    required double consumProgress,
    required this.strokeWidth,
    required this.colorList,
  }) : radii = [stepRadius, distanceRadius, consumRadius], progressi = [stepProgress,distanceProgress,consumProgress]; // 构造时合并半径;

  @override
  void paint(Canvas canvas, Size size) {
    final center = size.center(Offset.zero);
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    // 遍历半径列表，逐个绘制圆环（自动匹配colorList对应索引颜色）
    for (int i = 0; i < radii.length; i++) {
      paint.color = colorList[i];
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radii[i]),
        -pi / 2, // 起始角度（与原逻辑一致）
        2 * pi * progressi[i], // 绘制整圆（与原逻辑一致）
        false,
        paint,
      );
    } 
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
