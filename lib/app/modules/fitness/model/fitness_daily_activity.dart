/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-05-08 14:39:42
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-31 16:50:31
 * @FilePath: /RPM-APP/lib/app/modules/fitness/model/fitness_daily_activity.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class FitnessDailyActivity {
   int step;
   int distance;
   int consum;
  DateTime date;

  FitnessDailyActivity.withDefault() 
      : step = 0,
        distance = 0,
        consum = 0,
        date = DateTime(2000, 1, 1);

  FitnessDailyActivity({
    required this.step,
    required this.distance,
    required this.consum,
    required this.date,
  });

  factory FitnessDailyActivity.fromJson(Map<String, dynamic> json) {
    int calorie = json['calorie'] as int;
    return FitnessDailyActivity(
      step: json['number'] ?? 0,
      distance: json['distance'] ?? 0,
      // consum: int.parse(json['calorie'])/10 ?? 0,
      consum: (calorie/10).toInt(),
      date: DateTime.parse(json['date_time']) ?? DateTime(2000, 1, 1),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'step': step,
      'distance': distance,
      'consum': consum,
    };
  }

  @override
  String toString() {
    return 'FitnessDailyActivity(step: $step, distance: $distance, consum: $consum, date: $date)';
  }
}