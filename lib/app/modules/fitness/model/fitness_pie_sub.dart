/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-05-08 10:14:53
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-08 10:20:12
 * @FilePath: /RPM-APP/lib/app/modules/fitness/model/fitness_pie_sub.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:ui';

class FitnessPieSub {
   List<Color> colrs;
   List<num?> mainNum;
   List<num?> subNum;
   List<String> title;

  FitnessPieSub({
    required this.colrs,
    required this.mainNum,
    required this.subNum,
    required this.title,
  });

  FitnessPieSub copyWith({
    List<Color>? colrs,
    List<num?>? mainNum,
    List<num?>? subNum,
    List<String>? title,
  }) {
    return FitnessPieSub(
      colrs: colrs ?? this.colrs,
      mainNum: mainNum ?? this.mainNum,
      subNum: subNum ?? this.subNum,
      title: title ?? this.title,
    );
  }
}