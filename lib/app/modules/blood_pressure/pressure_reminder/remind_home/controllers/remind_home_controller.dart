// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-08-28 14:56:42
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-12-06 09:04:50
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_pressure/pressure_reminder/remind_home/controllers/remind_home_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:convert';

// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:aiCare/app/data/model/remind_data.dart';

// import 'package:aiCare/app/routes/app_pages.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:get/get.dart';

// class RemindHomeController extends BaseController {
//   RxList<RemindData> remindList = <RemindData>[].obs;

//   @override
//   void onInit() {
//     super.onInit();
//     initList();
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   void initList() async {
//     await storage.setData(
//       "pressure_remind_list",
//       jsonEncode([
//         RemindData(
//           date: "06:12",
//           label: "Remind",
//           switchValue: true,
//           repeatList: [true, true, true, true, true, true, true],
//         ),
//         RemindData(
//           date: "07:12",
//           label: "Remind",
//           switchValue: false,
//           repeatList: [true, true, true, true, true, true, false],
//         ),
//       ]),
//     );
//     var jsonData = await storage.getData("pressure_remind_list");
//     // 反序列化为 List<PressureReminder> 对象
//     List<RemindData> result = (jsonDecode(jsonData) as List)
//         .map((item) => RemindData.fromJson(item))
//         .toList();

//     logger.d(result.toList());
//     remindList.value = result;

//     // remindList.value = [
//     //   PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: true,repeatList: [true,true,true,true,true,true,true]),

//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: true,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: true,repeatList: [true,true,true,true,true,true,true]),
//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: true,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     // ];
//   }

//   void showAddSheet() {}

//   void switchRecord(bool value, int index) {
//     remindList.value[index].switchValue = value;
//     remindList.refresh();
//     logger.d(remindList.value[index].switchValue);
//   }

//   String switchRepeatText(List<bool> checkList) {
//     // 获取所有选中的星期的索引
//     List<String> days = [
//       appLocalization.dateSunday, // 星期日
//       appLocalization.dateMonday, // 星期一
//       appLocalization.dateTuesday, // 星期二
//       appLocalization.dateWednesday, // 星期三
//       appLocalization.dateThursday, // 星期四
//       appLocalization.dateFriday, // 星期五
//       appLocalization.dateSaturday // 星期六
//     ];
//     String repeatText;

//     if (checkList.every((day) => day)) {
//       // 如果所有的值都为 true
//       repeatText = appLocalization.dateEveryday;
//     } else if (checkList.sublist(1, 6).every((day) => day) &&
//         !checkList[0] &&
//         !checkList[6]) {
//       // 如果周一到周五的值为 true，且周日和周六的值为 false
//       repeatText = appLocalization.dateMondayToFriday;
//     } else {
//       // 返回所有选中的天
//       List<String> selectedDays = [];
//       for (int i = 0; i < checkList.length; i++) {
//         if (checkList[i]) {
//           selectedDays.add(days[i]);
//         }
//       }
//       repeatText = selectedDays.join(", ");
//     }
//     return repeatText;
//   }

//   detelteRemind(int index) async {
//     logger.d("i:$index");
//     remindList.removeAt(index); // 删除指定索引的元素

//     remindList.refresh();
//     await storage.setData("oxygen_remind_list", jsonEncode(remindList.value));

//     List<RemindData> result =
//         (jsonDecode(await storage.getData("oxygen_remind_list")) as List)
//             .map((item) => RemindData.fromJson(item))
//             .toList();
//     logger.d("??");
//     logger.d(result.toList()[0].toJson());
//   }

//   void toEdit(int index) {
//     var data = index == -1 ? null : remindList.value[index];
//     Get.toNamed(Routes.PRESSURE_REMIND_EDIT,
//         arguments: {"index": index, "data": data})?.then((result) {
//       if (result != null && result is RemindData) {
//         if (index == -1) {
//           // 如果 index 是 -1，表示是添加新数据的情况
//           logger.d("添加数据");
//           remindList.add(result);
//         } else {
//           logger.d("更新数据");
//           // 如果 index 不是 -1，表示是编辑已有数据的情况
//           remindList[index] = result;
//         }
//         remindList.refresh(); // 刷新列表以更新 UI
//         storage.setData("oxygen_remind_list", jsonEncode(remindList.value));
//       } else {
//         logger.d("oxygen_remind_list无数据");
//       }
//     });
//   }

// }
