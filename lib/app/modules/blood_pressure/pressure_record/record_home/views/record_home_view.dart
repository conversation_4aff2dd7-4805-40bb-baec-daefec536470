// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-02 14:11:21
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-09-04 12:59:30
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_pressure/pressure_record/record_home/views/record_home_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/widget/custom_app_bar.dart';
// import 'package:aiCare/app/core/widget/record_card.dart';
// import 'package:aiCare/app/core/widget/record_search.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import '../controllers/record_home_controller.dart';

// class RecordHomeView extends BaseView<RecordHomeController> {
//     RecordHomeView({
//     super.key,
//   }) : super(
//           parentPaddings: [0, 0, 0, 0],
//           bgColor: AppColors.colorWhite,
//         );

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return CustomAppBar(
//       appBarTitleText: appLocalization.measurementRecord,
//       customHeight: ScreenAdapter.height(44),
//       appBarColor: AppColors.colorWhite,
//     );
//   }
  
//   @override
//   Widget body(BuildContext context) {
//     return Stack(
//       children: [
//         Column(
//           children: [
//             SizedBox(
//               height: ScreenAdapter.height(64),
//             ),
//             Expanded(
//               child: Obx(() => ListView.builder(
//                     itemCount: controller.filterList.value.length,
//                     itemBuilder: (context, index) {
//                       final data = controller.filterList.value[index];
//                       return RecordCard(index: 1, data: data);
//                     },
//                   )),
//             ),
//           ],
//         ),
        
//         Obx(() => controller.searchBool.value
//             ? Positioned.fill(
//                 child: InkWell(
//                 onTap: controller.doClose,
//                 child: Container(
//                   color: Colors.black.withOpacity(0.6), // 半透明的阴影色
//                 ),
//               ))
//             : SizedBox()),
        
//         Positioned(
//           top: 0,
//           left: 0,
//           right: 0,
//           child: RecordSearch(controller: controller,),
//         ),

//       ],
//     );
//   }

// }
