// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-02 14:11:21
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-09-09 16:30:57
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_pressure/pressure_record/record_home/controllers/record_home_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:math';

// import 'package:aiCare/app/core/base/controller/record_search_controller.dart';

// import 'package:aiCare/app/modules/blood_pressure/model/pressure_data.dart';

// import 'package:get/get.dart';

// class RecordHomeController extends RecordSearchController<PressureData> {

//   //List of fake data
//   final List<PressureData> maskData = List.generate(100, (index) {
//     return PressureData(
//       sysPressure: Random().nextInt(141) + 60, 
//       diaPressure: Random().nextInt(111) + 30, 
//       date: DateTime.now().subtract(Duration(days: Random().nextInt(365))), 
//       pulse: Random().nextInt(81) + 40, dataSource: Random().nextInt(3),
      
//       );
//   });

//   //Filtered list
//   late RxList<PressureData> filterList =
//       RxList<PressureData>();

//   @override
//   void onInit() {
//     super.onInit();
//     initList();
//     filterList.value = maskData;
//     update();
//   }


//   @override
//   void onClose() {
//     super.onClose();
//   }




//   /**
//    * @description: Click Finish to start processing filtering rules
//    * @return null
//    */
//   doFinish() {
//     searchBool.value = false;
//     filterList.value = maskData;
//     // 根据 selectSeverity 进行筛选
//     if (selectSeverity.value != 99) {
//       filterList.value = maskData.where((item) {
//         return item.getTotalLevel() == selectSeverity.value;
//       }).toList();
//     }

//     // 获取当前日期
//     DateTime currentDate = DateTime.now();

//     // 根据 selectFuzzyTime 进行筛选
//     logger.d("开始日期筛选");
//     logger.d(selectFuzzyTime.value);
//     filterList.value = filterList.value.where((item) {
//       DateTime itemDate = item.date;
      
//       switch (selectFuzzyTime.value) {
//         case 0:
//           // 今天
//           return itemDate.year == currentDate.year &&
//               itemDate.month == currentDate.month &&
//               itemDate.day == currentDate.day;
//         case 1:
//           // 近七天
//           return itemDate.isAfter(currentDate.subtract(Duration(days: 7)));
//         case 2:
//           // 近一个月
//           return itemDate.isAfter(currentDate.subtract(Duration(days: 30)));
//         case 3:
//           // 一年内
//           return itemDate.isAfter(currentDate.subtract(Duration(days: 365)));
//         case 99:
//           // 精确时间筛选
//           logger.d(firstData.value);
//           if (firstData.value.any((element) => element != "0000" && element != "00" ) &&
//               lateData.value.any((element) => element != "0000" && element != "00" )) {
//                 logger.d("进入if了");
//             DateTime firstDate = DateTime(
//               int.parse(firstData.value[0]), // 假设年份前面是20
//               int.parse(firstData.value[1]),
//               int.parse(firstData.value[2]),
//             );
//             DateTime lateDate = DateTime(
//               int.parse(lateData.value[0]), // 假设年份前面是20
//               int.parse(lateData.value[1]),
//               int.parse(lateData.value[2]),
//             );
//             return itemDate.isAfter(firstDate) && itemDate.isBefore(lateDate);
//           }
//           return true;
//         default:
//           // 其他情况
//           return true;
//       }
//     }).toList();

//     logger.d(filterList.value);
//     update();
//   }



//   /**
//    * @description: init text List
//    * @return null
//    */
//   void initList() {
//     severityList.value = [
// appLocalization.wordsLow, 
// appLocalization.wordsNormal, 
// appLocalization.wordsMild, 
// appLocalization.wordsModerate, 
// appLocalization.wordsSerious,
//     ];
//     dateList.value = [
//       appLocalization.dateToday,
//       appLocalization.dateLastWeek,
//       appLocalization.dateLastMonth,
//       appLocalization.dateLastYear,
//     ];
//   }
// }
