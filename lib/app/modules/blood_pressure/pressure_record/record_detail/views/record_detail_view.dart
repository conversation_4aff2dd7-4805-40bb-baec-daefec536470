// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import '../controllers/record_detail_controller.dart';

// class RecordDetailView extends GetView<RecordDetailController> {
//   const RecordDetailView({Key? key}) : super(key: key);
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('RecordDetailView'),
//         centerTitle: true,
//       ),
//       body: const Center(
//         child: Text(
//           'RecordDetailView is working',
//           style: TextStyle(fontSize: 20),
//         ),
//       ),
//     );
//   }
// }
