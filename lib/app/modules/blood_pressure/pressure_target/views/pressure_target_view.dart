// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-05 10:35:21
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-10-09 15:03:49
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_pressure/pressure_target/views/pressure_target_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/custom_app_bar.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_target/widgets/target_pie.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';

// import 'package:get/get.dart';
// import 'package:logger/logger.dart';

// import '../controllers/pressure_target_controller.dart';

// class PressureTargetView extends BaseView<PressureTargetController> {
//   PressureTargetView({
//     super.key,
//   }) : super(
//             parentPaddings: [0, 0, 0, 0],
//             bgColor: Colors.transparent,
//             bgBanner: _banner(),
//             headAll: true,
//             headColr: AppColors.homeBgColor,
//             bgIcon: _notionIcon());
//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return CustomAppBar(
//       appBarTitleText: "",
//       leading: _leftBack(),
//       appBarColor: Colors.transparent,
//     );
//   }

//   @override
//   Widget body(BuildContext context) {
//     return SingleChildScrollView(
//       child: Container(
//         // color: Colors.red,
//         // 添加一个 Container 或者 SizedBox 来限定尺寸
//         width: ScreenAdapter.width(375), // 确保 Stack 有宽度约束
//         height: ScreenAdapter.height(812), // 设定一个高度
//         child: Stack(
//           children: [
//             // banner 从顶部显示，覆盖到状态栏和标题栏
//             // notionIcon(),

//             _card(),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class _banner extends StatelessWidget {
//   const _banner({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Positioned(
//         top: 0,
//         child: IgnorePointer(
//           child: Container(
//             // width: double.infinity,
//             width: ScreenAdapter.width(375),
//             height: ScreenAdapter.height(268),

//             decoration: BoxDecoration(
//                 color: Colors.transparent,
//                 image: DecorationImage(
//                     image: AssetImage(
//                       Assets.images.targetBg.path,
//                     ),
//                     fit: BoxFit.fitWidth)),
//           ),
//         ));
//   }
// }

// class _leftBack extends StatelessWidget {
//   const _leftBack({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: () {
//         print("点击返回");
//         Get.back();
//       },
//       splashColor: Colors.transparent, // 去除水波纹效果
//       highlightColor: Colors.transparent, // 去除高亮效果
//       child: Row(
//         children: [
//           Container(
//             width: ScreenAdapter.width(8.97),
//             height: ScreenAdapter.height(16.26),
//             margin: EdgeInsets.only(left: ScreenAdapter.width(21.51)),
//             // color: Colors.red,
//             child: Center(
//               // 使用 Center 小部件使 SVG 图片居中
//               child: Image.asset(
//                 Assets.images.backIconWhite.path,
//                 fit: BoxFit.contain, // 确保图片按比例显示
//                 // color: Colors.white,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// class _notionIcon extends StatelessWidget {
//   const _notionIcon({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Positioned(
//         top: MediaQuery.of(context).padding.top - ScreenAdapter.height(32),
//         left: ScreenAdapter.width(104.05),
//         child: SizedBox(
//           width: ScreenAdapter.width(159.95),
//           height: ScreenAdapter.height(148),
//           child: SvgPicture.asset(
//             Assets.images.notionsIcon,
//           ),
//         ));
//   }
// }

// class _card extends StatelessWidget with BaseWidgetMixin {
//   _card({super.key});

//   @override
//   Widget body(BuildContext context) {
//     return Positioned(
//         top: ScreenAdapter.height(88),
//         child: Container(
//           width: ScreenAdapter.width(343),
//           // height: ScreenAdapter.width(525),
//           margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),

//           decoration: BoxDecoration(
//               color: AppColors.colorWhite,
//               borderRadius: BorderRadius.circular(ScreenAdapter.width(12))),
//           child: Column(
//             children: [
//               Container(
//                 margin: EdgeInsets.symmetric(
//                     horizontal: ScreenAdapter.width(20),
//                     vertical: ScreenAdapter.height(20)),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Container(
//                       decoration: BoxDecoration(
//                           image: DecorationImage(
//                               image:
//                                   AssetImage(Assets.images.targetTitleBg.path),
//                               fit: BoxFit.contain)),
//                       child: Text(
//                         appLocalization.bloodPressureSetGoals,
//                         style: TextStyle(
//                             fontWeight: FontWeight.w600,
//                             fontSize: ScreenAdapter.fontSize(24),
//                             height: 28.13 / 24,
//                             color: AppColors.lightBlue),
//                       ),
//                     ),
//                     Row(
//                       children: [
//                         Text(
//                           appLocalization.wordsUnit + ":",
//                           style: normalF14H19C666.copyWith(
//                             fontWeight: FontWeight.w500,
//                           ),
//                         ),
//                         InkWell(
//                           onTap: () {
//                             logger.d("切换单位");
//                           },
//                           child: Container(
//                             margin: EdgeInsets.symmetric(
//                                 horizontal: ScreenAdapter.width(4)),
//                             padding: EdgeInsets.symmetric(
//                                 horizontal: ScreenAdapter.width(8)),
//                             decoration: BoxDecoration(
//                                 color: AppColors.lightBlue15,
//                                 border: Border.all(
//                                     color: AppColors.lightBlue,
//                                     width: ScreenAdapter.width(1)),
//                                 borderRadius: BorderRadius.circular(
//                                     ScreenAdapter.width(16))),
//                             child: Text(
//                               appLocalization.wordsMmHg,
//                               style: lightBlueF10,
//                             ),
//                           ),
//                         ),
//                         InkWell(
//                           onTap: () {
//                             logger.d("切换单位");
//                           },
//                           child: Container(
//                             padding: EdgeInsets.symmetric(
//                                 horizontal: ScreenAdapter.width(8)),
//                             decoration: BoxDecoration(
//                                 color: AppColors.Color999S15,
//                                 border: Border.all(
//                                     color: AppColors.Color999,
//                                     width: ScreenAdapter.width(1)),
//                                 borderRadius: BorderRadius.circular(
//                                     ScreenAdapter.width(16))),
//                             child: Text(
//                               appLocalization.wordsKpa,
//                               style: noneBlueF10,
//                             ),
//                           ),
//                         ),
//                       ],
//                     )
//                   ],
//                 ),
//               ),
//               //sys
//               Container(
//                 margin: EdgeInsets.only(
//                     top: ScreenAdapter.height(10),
//                     left: ScreenAdapter.width(24)),
//                 child: Row(
//                   children: [
//                     Text(
//                       appLocalization.wordsSys,
//                       style: TextStyle(
//                           fontWeight: FontWeight.w600,
//                           fontSize: ScreenAdapter.fontSize(14),
//                           height: 19.6 / 14,
//                           color: AppColors.lightBlue),
//                     ),
//                     SizedBox(
//                       width: ScreenAdapter.width(4),
//                     ),
//                     Text(
//                       "(${appLocalization.wordsMmHg})",
//                       style: normalF12H17C999.copyWith(
//                           fontWeight: FontWeight.w500),
//                     )
//                   ],
//                 ),
//               ),
//               TargetPie(
//                 left: 44.0,
//                 top: 36.3,
//                 parentWidth: 242,
//                 parentTop: 12,
//                 width: 148,
//               ),
//               //dia
//               Container(
//                 margin: EdgeInsets.only(
//                     top: ScreenAdapter.height(29),
//                     left: ScreenAdapter.width(24)),
//                 child: Row(
//                   children: [
//                     Text(
//                       appLocalization.wordsDia,
//                       style: TextStyle(
//                           fontWeight: FontWeight.w600,
//                           fontSize: ScreenAdapter.fontSize(14),
//                           height: 19.6 / 14,
//                           color: AppColors.lightBlue),
//                     ),
//                     SizedBox(
//                       width: ScreenAdapter.width(4),
//                     ),
//                     Text(
//                       "(${appLocalization.wordsMmHg})",
//                       style: normalF12H17C999.copyWith(
//                           fontWeight: FontWeight.w500),
//                     )
//                   ],
//                 ),
//               ),
//               TargetPie(
//                 left: 44.0,
//                 top: 36.3,
//                 parentWidth: 242,
//                 parentTop: 12,
//                 width: 148,
//               ),
//               //button
//               _button()
//             ],
//           ),
//           // color: Colors.red,
//         ));
//   }
// }

// class _button extends StatelessWidget with BaseWidgetMixin {
//   _button({super.key});

//   @override
//   Widget body(BuildContext context) {
//     return InkWell(
//       onTap: () {
//         logger.d("提交目标与设定");
//       },
//       child: Center(
//         child: Container(
//             width: ScreenAdapter.width(188),
//             height: ScreenAdapter.height(42),
//             margin: EdgeInsets.only(
//                 top: ScreenAdapter.height(32),
//                 bottom: ScreenAdapter.height(24)),
//             decoration: BoxDecoration(
//                 image: DecorationImage(
//                     image: AssetImage(
//               Assets.images.targetButton.path,
//             ))),
//             child: Center(
//               child: Text(
//                 appLocalization.wordsOk,
//                 style: TextStyle(
//                     fontWeight: FontWeight.w600,
//                     fontSize: ScreenAdapter.fontSize(16),
//                     height: 22.4 / 16,
//                     color: AppColors.colorWhite),
//               ),
//             )),
//       ),
//     );
//   }
// }
