// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-05 16:56:26
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-09-09 17:16:10
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_pressure/pressure_target/widgets/target_pie.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_target/controllers/pressure_target_controller.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:get/get.dart';
// import 'package:get/get_connect/http/src/utils/utils.dart';

// class TargetPie extends StatelessWidget {
//   final PressureTargetController controller = Get.find();
//   final double left;
//   final double top;
//   final double width;
//   final double parentWidth;
//   final double parentTop;
//   TargetPie(
//       {super.key,
//       required this.left,
//       required this.top,
//       required this.width,
//       required this.parentWidth,
//       required this.parentTop});

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.only(
//         top: ScreenAdapter.height(parentTop),
//       ),
//       width: ScreenAdapter.width(parentWidth),
//       height: ScreenAdapter.width(parentWidth / 2),
//       child: Stack(
//         children: [
//           Positioned(
//               top: 0,
//               left: 0,
//               child: SizedBox(
//                 width: ScreenAdapter.width(parentWidth),
//                 // height: ScreenAdapter.width(parentWidth),
//                 child: SvgPicture.asset(
//                   Assets.images.targetPieNormal,
//                   fit: BoxFit.fitWidth,
//                 ),
//               )),
//               Positioned(
//                 top: ScreenAdapter.width(61.3),
//                 left: ScreenAdapter.width(96),
//                 child: Column(
//                     mainAxisAlignment: MainAxisAlignment.end,
//                     children: [
//                       Text(
//                         controller.appLocalization.wordsNormal,
//                         style: normalF12H17C999,
//                       ),
//                       Text(
//                         "82",
//                         style: TextStyle(
//                             fontWeight: FontWeight.w600,
//                             fontSize: ScreenAdapter.fontSize(20),
//                             height: 28 / 20,
//                             color: AppColors.pressureTargetTextList[1]),
//                       ),
//                       // SizedBox(height: ScreenAdapter.height(8),)
//                     ],
//                   ))

//         ],
//       ),
//     );
//   }
// }
