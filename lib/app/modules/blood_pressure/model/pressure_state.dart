/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-20 10:26:45
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-08-20 10:32:18
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_pressure/model/pressure_state.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class PressureState {
  final String title;
  final String unit;
  final int data;
  final String? buttonText;

  PressureState({
    required this.title,
    required this.unit,
    required this.data,
     this.buttonText,
  });
}
