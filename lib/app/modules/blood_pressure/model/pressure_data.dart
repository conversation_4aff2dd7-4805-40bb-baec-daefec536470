/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-22 13:52:45
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-09-03 15:47:39
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/blood_pressure/model/pressure_data.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:math';

class PressureData {
  int sysPressure;
  int diaPressure;
  DateTime date;
  int pulse;
  int dataSource;

  // Constructor
  PressureData({
    required this.sysPressure,
    required this.diaPressure,
    required this.date,
    required this.pulse,
    required this.dataSource,
  });

  String displayData() {
    return 'Systolic Pressure: $sysPressure, Diastolic Pressure: $diaPressure, Date: ${date.toString()}, Pulse: $pulse,dataSource:$dataSource';
  }

  int getSysLevel() {
    if (sysPressure < 90) {
      return 0;
    } else if (sysPressure < 140) {
      return 1;
    } else if (sysPressure < 160) {
      return 2;
    } else if (sysPressure < 180) {
      return 3;
    } else {
      return 4;
    }
  }

  int getDiaLevel() {
    if (diaPressure < 60) {
      return 0;
    } else if (diaPressure < 90) {
      return 1;
    } else if (diaPressure < 100) {
      return 2;
    } else if (diaPressure < 110) {
      return 3;
    } else {
      return 4;
    }
  }

  int getTotalLevel() {
    int sys = this.getSysLevel();
    int dia = this.getDiaLevel();
    if (sys == dia) {
      return sys;
    } else {
      return max(sys, dia);
    }
  }
}
