// import 'dart:convert';

// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:aiCare/app/core/model/domains.dart';
// import 'package:aiCare/app/core/render/fl_chart/image_dot_painter.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/modules/blood_pressure/model/pressure_state.dart';
// import 'package:aiCare/app/modules/home/<USER>/custom_home_bar.dart';
// import 'package:aiCare/app/routes/app_pages.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// class PressureHomeController extends BaseController {
//   //TODO: Implement PressureHomeController
//   RxString lineOption = "".obs;
//   List<Domains> domainList = [];
//   List<PressureState> stateList = [];

//   List<String> date = [
//     '06/01',
//     '06/02',
//     '06/03',
//     '06/04',
//     '06/05',
//     '06/06',
//     '06/07'
//   ];

//   List<double> diaPressure = [135, 130, 134, 138, 140, 132, 135]; //舒张压
//   List<double> sysPressure = [85, 80, 85, 92, 87, 89, 80]; //收缩压

//   RxInt diaCenter = 135.obs;
//   RxInt sysCenter = 80.obs;

//   List<List<FlSpot>> spots = [
//     [129, 126, 156, 159, 152, 126, 128]
//         .asMap()
//         .entries
//         .map((entry) =>
//             FlSpot((entry.key + 1).toDouble(), entry.value.toDouble()))
//         .toList(),
//     [88, 82, 92, 98, 96, 82, 86]
//         .asMap()
//         .entries
//         .map((entry) =>
//             FlSpot((entry.key + 1).toDouble(), entry.value.toDouble()))
//         .toList()
//   ];

//   double leftMax = 220;
//   double leftMin = 40;
//   double leftInterval = 10;
//   TextStyle leftStyle = normalF12H17C666;
//   TextStyle bottomStyle = normalF12H17C666;
//   Color bgColor = AppColors.pressureRemindBg;

//   List<Color> spotsColor = [AppColors.pressureDia, AppColors.pressureSys];
//   List<FlDotData> dotStyle = [
//     FlDotData(
//         show: true,
//         getDotPainter: (spot, perent, barData, index) {
//           if (spot.y < 120 || spot.y > 139) {
//             return GlowDotCirclePainter(
//                 radius: ScreenAdapter.width(3), //点的半径
//                 color: AppColors.errorPoint, //点的颜色
//                 strokeWidth: ScreenAdapter.width(1), //点的边框宽度
//                 strokeColor: AppColors.colorWhite, //点边框的颜色
//                 glowColor: AppColors.errorPoint,
//                 glowRadius: ScreenAdapter.width(5));
//           }
//           return FlDotCirclePainter(
//             radius: ScreenAdapter.width(3), //点的半径
//             color: AppColors.pressureDia, //点的颜色
//             strokeWidth: ScreenAdapter.width(1), //点的边框宽度
//             strokeColor: Colors.white, //点边框的颜色
//           );
//         }),
//     FlDotData(
//         show: true,
//         getDotPainter: (spot, perent, barData, index) {
//           if (spot.y < 80 || spot.y > 89) {

//             return GlowDotCirclePainter(
//                 radius: ScreenAdapter.width(3), //点的半径
//                 color: AppColors.errorPoint, //点的颜色
//                 strokeWidth: ScreenAdapter.width(1), //点的边框宽度
//                 strokeColor: AppColors.colorWhite, //点边框的颜色
//                 glowColor: AppColors.errorPoint,
//                 glowRadius: ScreenAdapter.width(5));
//           }
//           return FlDotCirclePainter(
//             radius: ScreenAdapter.width(3), //点的半径
//             color: AppColors.pressureSys, //点的颜色
//             strokeWidth: ScreenAdapter.width(1), //点的边框宽度
//             strokeColor: Colors.white, //点边框的颜色
//           );
//         }),
//   ];



//   Color getDynamicColor(double yValue) {
//     return ((yValue >=120 && yValue <140)|| (yValue>=80 && yValue <90) )
//         ? AppColors.lightBlue // 红色
//         : AppColors.errorPoint; // 蓝色
//   }

//   late LineTouchData lineTouchData; // 使用 late 初始化

//   @override
//   void onInit() {
//     super.onInit();
    
//     updateLineOption();
//     initList();
//     lineTouchData = LineTouchData(
//       enabled: true,
//       handleBuiltInTouches: false,
//       touchTooltipData: LineTouchTooltipData(
//         tooltipRoundedRadius: ScreenAdapter.width(12),
//         tooltipPadding: EdgeInsets.symmetric(
//           horizontal: ScreenAdapter.width(8),
//           vertical: ScreenAdapter.height(2),
//         ),
//         maxContentWidth: 100,
//         getTooltipColor: (touchedSpot) {
//           return getDynamicColor(touchedSpot.y).withOpacity(0.15); // 蓝色
//         },
//         tooltipBorder: BorderSide(
//           color: AppColors.colorWhite, // 默认颜色
//           width: ScreenAdapter.width(0.5),
//         ),
//         getTooltipItems: (touchedSpots) {
// // 准备提示项列表
//   List<LineTooltipItem> tooltipItems = [];

//   for (int i = 0; i < touchedSpots.length; i++) {
//     LineBarSpot touchedSpot = touchedSpots[i];
//     String label = touchedSpot.barIndex == 0 ? '${appLocalization.wordsSys}' : '${appLocalization.wordsDia}';

//     tooltipItems.add(LineTooltipItem(
//       '${touchedSpot.y.toStringAsFixed(1)}',
//       normalF12H17C333.copyWith(color: getDynamicColor(touchedSpot.y)),
//     ));
//   }

//   return tooltipItems;


 
//         },
//       ),

//       getTouchedSpotIndicator:
//           (LineChartBarData barData, List<int> spotIndexes) {
//         return spotIndexes.map((index) {
          
//           double yValue = barData.spots[index].y; // 获取 Y 值
//           FlLine indicatorLine = FlLine(
//             color: getDynamicColor(yValue).withOpacity(0.65), // 蓝色
//             strokeWidth: ScreenAdapter.width(0),
//             dashArray: [2, 2],
//           );

//           return TouchedSpotIndicatorData(
//             indicatorLine, // 返回相应颜色的虚线
//             FlDotData(show: false), // 不显示点
//           );
//         }).toList();
//       },
//       getTouchLineStart: (barData, spotIndex) {
//         return 0.0; // 返回左上角的坐标
//       },
//       getTouchLineEnd: (barData, spotIndex) {
//         return barData.spots[spotIndex].y; // 返回触摸点的 y 值
//       },
//     );
  


//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   void updateLineOption() {
//     // lineOption.value = jsonEncode({
//     //   'series': [
//     //     {
//     //       'type': 'line',
//     //       'smooth': true,
//     //       'symbol': 'circle',
//     //       'data': diaPressure,
//     //       'lineStyle': {
//     //         'color': '#3AC055',
//     //       },
//     //       'itemStyle': {
//     //         'color': '#3AC055', // 设置标点的颜色
//     //         'borderColor': '#FFFFFF', // 设置标点的边框颜色（可选）
//     //         'borderWidth': 1, // 设置标点的边框宽度（可选）
//     //       },
//     //     },
//     //     {
//     //       'type': 'line',
//     //       'smooth': true,
//     //       'symbol': 'circle',
//     //       'data': sysPressure,
//     //       'lineStyle': {
//     //         'color': '#FF8549',
//     //       },
//     //       'itemStyle': {
//     //         'color': '#FF8549', // 设置标点的颜色
//     //         'borderColor': '#FFFFFF', // 设置标点的边框颜色（可选）
//     //         'borderWidth': 1, // 设置标点的边框宽度（可选）
//     //       },
//     //     }
//     //   ],
//     //   'grid': {
//     //     'backgroundColor': 'rgba(204, 229, 255, 0.3)',
//     //     'borderColor': 'rgb(255, 255, 255)',
//     //     'show': true,
//     //     'height': ScreenAdapter.height(160),
//     //     'width': ScreenAdapter.width(280),
//     //     'bottom': ScreenAdapter.height(30),
//     //   },
//     //   'xAxis': {
//     //     'type': 'category',
//     //     'boundaryGap': true,
//     //     'axisTick': {
//     //       'show': false,
//     //       'alignWithLabel': true,
//     //     },
//     //     'axisLabel': {
//     //       'show': true,
//     //       'interval': 5,
//     //       'textStyle': {
//     //         'color': '#666',
//     //         'fontSize': ScreenAdapter.fontSize(10),
//     //         'fontWeight': 400,
//     //         'lineHeight': ScreenAdapter.fontSize(14 / 10),
//     //       }
//     //     },
//     //     'data': date,
//     //   },
//     //   'yAxis': {
//     //     'type': 'value',
//     //     'min': 40,
//     //     'max': 220,
//     //     // 'splitNumber': 6,
//     //     'interval': 30, // 每30单位为一个间隔
//     //     'splitLine': {
//     //       'show': true,
//     //       'lineStyle': {
//     //         'type': 'dashed',
//     //       },
//     //     },
//     //     'axisLabel': {
//     //       'showMaxLabel': false, // 不显示最大标签
//     //       'textStyle': {
//     //         'color': '#666',
//     //         'fontSize': ScreenAdapter.fontSize(10),
//     //         'fontWeight': 400,
//     //         'lineHeight': ScreenAdapter.fontSize(14 / 10),
//     //       }
//     //     }
//     //   }
//     // });
  
//   }

//   void initList() {
//     domainList = [
//       Domains(
//         images: Assets.images.record,
//         text: appLocalization.measurementRecord,
//         tap: () {
//           Get.toNamed(Routes.PRESSURE_RECORD_HOME);
//         },
//       ),
//       Domains(
//         images: Assets.images.setting,
//         text: appLocalization.measurementSettings,
//         tap: () {
//           Get.toNamed(Routes.PRESSURE_TARGET);
//         },
//       ),
//       Domains(
//         images: Assets.images.notion,
//         text: appLocalization.measurementRemind,
//         tap: () {
//           logger.d("onclick");
//           Get.toNamed(Routes.PRESSURE_REMIND_HOME);
//         },
//       ),
//     ];
//     stateList = [
//       PressureState(
//         title: appLocalization.bloodPressureSystolic,
//         unit: "mmHg",
//         data: 68,
//         buttonText: dataSwitch(68),
//       ),
//       PressureState(
//         title: appLocalization.bloodPressureDiastolic,
//         unit: "mmHg",
//         data: 180,
//         buttonText: dataSwitch(180),
//       ),
//       PressureState(
//           title: appLocalization.wordsPulse,
//           unit: appLocalization.wordsMinute,
//           data: 65),
//     ];
//   }

//   String dataSwitch(int data) {
//     return appLocalization.wordsNormal;
//   }
// }
