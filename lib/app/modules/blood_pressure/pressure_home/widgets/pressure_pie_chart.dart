// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/model/position_top_left.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/bluetooth/custom_bluetooth_icon.dart';
// import 'package:aiCare/app/core/widget/custom_pressure_chart.dart';
// import 'package:aiCare/app/modules/blood_pressure/model/pressure_data.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_home/controllers/pressure_home_controller.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_home/widgets/pressure_stat_card.dart';
// import 'package:aiCare/app/services/l10nService.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';

// class PressurePieChart extends StatefulWidget {
//   const PressurePieChart({super.key});

//   @override
//   State<StatefulWidget> createState() => PressurePieChartState();
// }

// class PressurePieChartState extends State<PressurePieChart> {
//   final PressureHomeController controller = Get.find();

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
//       width: ScreenAdapter.width(343),
//       // height: ScreenAdapter.height(288),
//       decoration: BoxDecoration(
//         borderRadius:
//             BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
//         color: AppColors.colorWhite,
//       ),
//       child: Column(
//         children: [
//           _PressurePie(index: 4),
//           //状态值
//           PressureStatCard(
//             date: DateTime.now(),
//           ),
//         ],
//       ),
//     );
//   }
// }

// class _PressurePie extends StatelessWidget with BaseWidgetMixin {
//   final int index;
//   final List<PositionTopLeft> piePosList = [
//     PositionTopLeft(
//         top: ScreenAdapter.width(50), left: ScreenAdapter.width(73)),
//     PositionTopLeft(
//       top: ScreenAdapter.width(10),
//       left: ScreenAdapter.width(80),
//     ),
//     PositionTopLeft(
//       top: ScreenAdapter.width(64),
//       left: ScreenAdapter.width(85),
//     ),
//     PositionTopLeft(
//       top: ScreenAdapter.width(82),
//       left: ScreenAdapter.width(105),
//     ),
//     PositionTopLeft(
//       top: ScreenAdapter.width(98),
//       left: ScreenAdapter.width(151.5),
//     ),
//   ];

//   final List<double> widthList = [
//     ScreenAdapter.width(206),
//     ScreenAdapter.width(190),
//     ScreenAdapter.width(180),
//     ScreenAdapter.width(141),
//   ];

//   final l10nService l10n = l10nService();

//   _PressurePie({super.key, required this.index});

//   @override
//   Widget body(BuildContext context) {
//     String switchText() {
//       switch (index) {
//         case 0:
//           return appLocalization.wordsLow;
//         case 1:
//           return appLocalization.wordsNormal;
//         case 2:
//           return appLocalization.wordsMild;
//         case 3:
//           return appLocalization.wordsModerate;
//         case 4:
//           return appLocalization.wordsSerious;
//         default:
//           return "--";
//       }
//     }

//     return SizedBox(
//       width: ScreenAdapter.width(343),
//       height: ScreenAdapter.height(173),
//       child: Stack(
//         clipBehavior: Clip.none, // 设置为 Clip.none 允许超出边界的内容显示
//         children: [
//           //文字
//           Positioned(
//               left: ScreenAdapter.width(50),
//               top: ScreenAdapter.height(114),
//               child: Text(
//                 appLocalization.wordsLow,
//                 style: normalF12H17C999,
//               )),
//           Positioned(
//               left: ScreenAdapter.width(l10n.isChinese() ? 88 : 80),
//               top: ScreenAdapter.height(58),
//               child: Text(
//                 appLocalization.wordsNormal,
//                 style: normalF12H17C999,
//               )),
//           Positioned(
//               left: ScreenAdapter.width(166),
//               top: ScreenAdapter.height(34),
//               child: Text(
//                 appLocalization.wordsMild,
//                 style: normalF12H17C999,
//               )),
//           Positioned(
//               left: ScreenAdapter.width(242),
//               top: ScreenAdapter.height(58),
//               child: Text(
//                 appLocalization.wordsModerate,
//                 style: normalF12H17C999,
//               )),
//           Positioned(
//               left: ScreenAdapter.width(281),
//               top: ScreenAdapter.height(114),
//               child: Text(
//                 appLocalization.wordsSerious,
//                 style: normalF12H17C999,
//               )),

//           //血压图
//           CustomPressureChart(
//             // data: PressureData(
//             //     date: DateTime.now(),
//             //     sysPressure: 68,
//             //     diaPressure: 180,
//             //     pulse: 65,
//             //     dataSource: 0),
//             posList: piePosList,
//             widthList: widthList,
//             textHeight: ScreenAdapter.width(47),
//             titleTextStyle: TextStyle(
//                 color: AppColors.Color999,
//                 fontWeight: FontWeight.w400,
//                 fontSize: ScreenAdapter.fontSize(12),
//                 height: ScreenAdapter.fontSize(16.8 / 12)),
//             textTextStyle: TextStyle(
//                 fontWeight: FontWeight.w600,
//                 fontSize: ScreenAdapter.fontSize(20),
//                 height: ScreenAdapter.fontSize(28.0 / 20)),
//           ),

//           CustomBluetoothIcon(),
//         ],
//       ),
//     );
//   }
// }
