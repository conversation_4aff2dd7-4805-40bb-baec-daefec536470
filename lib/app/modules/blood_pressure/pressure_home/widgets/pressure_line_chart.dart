// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/custom_line.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_home/controllers/pressure_home_controller.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_echarts/flutter_echarts.dart';
// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';

// import "dart:convert";

// import '../../../../core/values/app_colors.dart';

// class PressureLineChart extends StatefulWidget {
//   const PressureLineChart({Key? key}) : super(key: key);

//   @override
//   State<PressureLineChart> createState() => _LineChartState();
// }

// class _LineChartState extends State<PressureLineChart> {
//   final PressureHomeController controller = Get.find();

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//         margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
//         // padding:EdgeInsets.symmetric(vertical:  ScreenAdapter.height(12),horizontal: ScreenAdapter.width(12)),
//         padding: EdgeInsets.only(top: ScreenAdapter.height(12),left: ScreenAdapter.width(12),right: ScreenAdapter.width(12)),
//         width: ScreenAdapter.width(343),
//         // height: ScreenAdapter.height(243),
//         decoration: BoxDecoration(
//           borderRadius:
//               BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
//           color: AppColors.colorWhite,
//         ),
//         child: Stack(
//           children: [
//             Positioned(
//                 right: 0,
//                 top: ScreenAdapter.height(8),
//                 child: Row(
//                   children: [
//                     Container(
//                       width: ScreenAdapter.width(5),
//                       height: ScreenAdapter.height(5),
//                       decoration: BoxDecoration(
//                           borderRadius: BorderRadius.circular(5),
//                           color: AppColors.pressureSys),
//                     ),
//                     Container(
//                       margin: EdgeInsets.only(
//                           left: ScreenAdapter.width(4),
//                           right: ScreenAdapter.width(12)),
//                       child: Text(
//                         controller.appLocalization.wordsSystolic,
//                         style: normalF12H17C666,
//                       ),
//                     ),
//                     Container(
//                       width: ScreenAdapter.width(5),
//                       height: ScreenAdapter.height(5),
//                       decoration: BoxDecoration(
//                           borderRadius: BorderRadius.circular(5),
//                           color: AppColors.pressureDia),
//                     ),
//                     Container(
//                       margin: EdgeInsets.only(
//                           left: ScreenAdapter.width(4),
//                           right: ScreenAdapter.width(12)),
//                       child: Text(
//                         controller.appLocalization.wordsDiastolic,
//                         style: normalF12H17C666,
//                       ),
//                     ),
//                   ],
//                 )),
//             Column(
//               children: [
//                 Container(
//                   // width: ScreenAdapter.width(343),
//                   margin: EdgeInsets.only(
//                       bottom: ScreenAdapter.height(12)),

//                   height: ScreenAdapter.height(17),
//                   child: Row(
//                     children: <Widget>[
//                       SizedBox(
//                         width: ScreenAdapter.width(16),
//                         height: ScreenAdapter.height(16),
//                         child: Image.asset(Assets.images.trendIcon.path),
//                       ),
//                       Container(
//                           margin: EdgeInsets.symmetric(
//                               horizontal: ScreenAdapter.width(4)),
//                           child: Text(
//                             controller.appLocalization.bloodPressureTrend,
//                             style: TextStyle(
//                                 fontSize: ScreenAdapter.fontSize(12),
//                                 height: ScreenAdapter.fontSize(16.8 / 12),
//                                 color: AppColors.Color333,
//                                 fontWeight: FontWeight.w500),
//                           ))
//                     ],
//                   ),
//                 ),
//                 Container(
//                   width: ScreenAdapter.width(324),
//                   height: ScreenAdapter.height(189),
//                   // margin: EdgeInsets.only(left: ScreenAdapter.width(12)),
//                   child: CustomLine(
//                     key: UniqueKey(),
//                     lineTouchData: controller.lineTouchData,
//                       spots: controller.spots,
//                       leftMin: controller.leftMin,
//                       leftMax: controller.leftMax,
//                       leftInterval: controller.leftInterval,
//                       leftStyle: controller.leftStyle,
//                       bgColor: controller.bgColor,
//                       spotsColor: controller.spotsColor,
//                       spotsWidth: ScreenAdapter.width(1),
//                       dotStyle: controller.dotStyle,
//                       bottomStyle: controller.bottomStyle,
//                       listData: [
//                         " ",
//                         " "
//                       ],),
//                 )
//               ],
//             )
//           ],
//         ));
//   }
// }
