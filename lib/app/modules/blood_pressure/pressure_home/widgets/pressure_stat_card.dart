// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/modules/blood_pressure/pressure_home/controllers/pressure_home_controller.dart';
// import 'package:aiCare/app/services/l10nService.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';

// class PressureStatCard extends StatelessWidget with BaseWidgetMixin {
//   final PressureHomeController controller = Get.find();
//   final DateTime date;
//   l10nService l10n = l10nService();
//   PressureStatCard({super.key, required this.date});

//   @override
//   Widget body(BuildContext context) {
//     return Container(
//         width: ScreenAdapter.width(251),
//         height: ScreenAdapter.height(100),
//         margin: l10n.isChinese()
//             ? EdgeInsets.only(
//                 left: ScreenAdapter.width(46),
//                 // top: ScreenAdapter.height(8),
//                 right: ScreenAdapter.width(46),
//                 bottom: ScreenAdapter.height(16))
//             : EdgeInsets.only(
//                 left: ScreenAdapter.width(23),
//                 // top: ScreenAdapter.height(8),
//                 right: ScreenAdapter.width(23),
//                 bottom: ScreenAdapter.height(16)),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             SizedBox(
//               height: ScreenAdapter.height(14),
//               child: Text(
//                 DateFormat('yyyy-MM-dd HH:mm').format(date),
//                 style: TextStyle(
//                     fontWeight: FontWeight.w400,
//                     fontSize: ScreenAdapter.fontSize(10),
//                     height: ScreenAdapter.fontSize(14.0 / 10),
//                     color: AppColors.Color666),
//               ),
//             ),
//             // Expanded(child: SizedBox()),
//             Expanded(child: SizedBox()),
//             SizedBox(
//               height: ScreenAdapter.height(80),
//               child: Row(
//                 // mainAxisSize: MainAxisSize.min,
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: controller.stateList
//                     .map((value) => _PressureStateItem(
//                           title: value.title,
//                           unit: value.unit,
//                           data: value.data,
//                           buttonText: value.buttonText,
//                         ))
//                     .toList()),
//             )
//           ],
//         ));
//   }
// }

// class _PressureStateItem extends StatelessWidget {
//   final String title;
//   final String unit;
//   final int data;
//   final String? buttonText;
//   l10nService l10n = l10nService();

//   _PressureStateItem(
//       {super.key,
//       required this.title,
//       required this.unit,
//       required this.data,
//       this.buttonText});

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Expanded(child: SizedBox()),
//         Text(
//           title,
//           style: TextStyle(
//               color: AppColors.Color333,
//               fontSize: l10n.isChinese()
//                   ? ScreenAdapter.fontSize(12)
//                   : ScreenAdapter.fontSize(10),
//               height: l10n.isChinese()
//                   ? ScreenAdapter.fontSize(16.8 / 12)
//                   : ScreenAdapter.fontSize(14 / 10),
//               fontWeight: FontWeight.w500),
//         ),
//         Text(
//           unit,
//           style: TextStyle(
//               color: AppColors.Color999,
//               fontSize: ScreenAdapter.fontSize(8),
//               height: ScreenAdapter.fontSize(11.2 / 8),
//               fontWeight: FontWeight.w500),
//         ),
//         Text(
//           data.toString(),
//           style: TextStyle(
//               color: AppColors.Color333,
//               fontSize: ScreenAdapter.fontSize(20),
//               height: ScreenAdapter.fontSize(28.0 / 20),
//               fontWeight: FontWeight.w500),
//         ),
//         buttonText != null && buttonText!.isNotEmpty
//             ? Container(
//                 // width: ScreenAdapter.width(60),
//                 // height: ScreenAdapter.height(20),
//                 padding:
//                     EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
//                 decoration: BoxDecoration(
//                   color: AppColors.pressureButton,
//                   borderRadius: BorderRadius.circular(ScreenAdapter.width(16)),
//                 ),
//                 child: Center(
//                   child: Text(
//                     buttonText!,
//                     style: TextStyle(
//                       color: AppColors.pressureTextList[1],
//                       fontWeight: FontWeight.w500,
//                       fontSize: ScreenAdapter.fontSize(10),
//                       height: ScreenAdapter.fontSize(14.0 / 10),
//                     ),
//                   ),
//                 ),
//               )
//             : Expanded(child: SizedBox())
//       ],
//     );
//   }
// }
