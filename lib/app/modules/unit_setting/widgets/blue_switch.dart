/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-03-05 11:02:36
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-05 11:24:10
 * @FilePath: /RPM-APP/lib/app/modules/unit_setting/widgets/blue_switch.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
class CustomBlueSwitch extends StatelessWidget {
  RxInt value;

  CustomBlueSwitch({
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => GestureDetector(
        onTap: () {
          value.value = value.value == 1 ? 0 : 1;
        },
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          width: ScreenAdapter.width(32),
          height: ScreenAdapter.height(18),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(9),
            color: value.value == 1 ? AppColors.lightBlue : Colors.grey[300],
          ),
          child: Stack(
            children: [
              AnimatedAlign(
                duration: Duration(milliseconds: 200),
                alignment: value.value == 1 ? Alignment.centerRight : Alignment.centerLeft,
                child: Container(
                  margin: EdgeInsets.all(ScreenAdapter.height(3)),
                  width: ScreenAdapter.height(12),
                  height: ScreenAdapter.height(12),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}