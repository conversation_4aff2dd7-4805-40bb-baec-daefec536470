/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-03-03 15:56:03
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-30 16:30:36
 * @FilePath: /rpmappmaster/lib/app/modules/unit_setting/controllers/unit_setting_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UnitSettingController extends BaseController {
  // List<RxBool> heightUnit = [true.obs, false.obs, false.obs];
  // List<RxBool> weightUnit = [true.obs, false.obs];
  // List<dynamic> bloodOxygenUnit = [1.obs as RxInt, "2".obs];
  // List<dynamic> temperatureUnit = [1.obs as RxInt, true.obs, false.obs];
  // List<RxBool> pressureUnit = [true.obs, false.obs];
  // List<RxBool> glucoseUnit = [true.obs, false.obs];
  TabsController tabsController = Get.find();
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  List<String> getSubTitle(int index) {
    switch (index) {
      case 0:
        return [
          T.wordsCm.tr,
          T.wordsFeet.tr,
          T.wordsInches.tr,
        ];
      case 1:
        return [
          T.unitSMetricSystem.tr,
          T.unitSImperialSystem.tr,
        ];
      case 2:
        return [
          T.unitSAutomatic.tr,
          T.unitSLowBloodReminder.tr,
        ];
      case 3:
        return [
          T.unitSAutomatic.tr,
          T.unitSCentigrade.tr,
          T.unitSFahrenheit.tr
        ];
      case 4:
        return [
          T.wordsMmHg.tr,
          T.wordsKpa.tr,
        ];
      case 5:
        return [T.commonMgDl.tr, T.wordsMmolL.tr];
      default:
        return [];
    }
  }

  void updateBoolUnit(int index, int type) {
    if (type == 0) {
      // 遍历所有元素，将其设置为 false
      for (var i = 0; i < tabsController.unitSetting[0].length; i++) {
        tabsController.unitSetting[0][i].value = false;
      }

      // 将指定下标的元素设置为 true
      tabsController.unitSetting[0][index].value = true;
    } else if (type == 1) {
      // 遍历所有元素，将其设置为 false
      for (var i = 0; i < tabsController.unitSetting[1].length; i++) {
        tabsController.unitSetting[1][i].value = false;
      }

      // 将指定下标的元素设置为 true
      tabsController.unitSetting[1][index].value = true;
    } else if (type == 3) {
      // 遍历所有元素，将其设置为 false
      for (var i = 1; i < tabsController.unitSetting[3].length; i++) {
        tabsController.unitSetting[3][i].value = false;
      }

      // 将指定下标的元素设置为 true
      tabsController.unitSetting[3][index].value = true;
    } else if (type == 4) {
      // 遍历所有元素，将其设置为 false
      for (var i = 0; i < tabsController.unitSetting[4].length; i++) {
        tabsController.unitSetting[4][i].value = false;
      }
      // 将指定下标的元素设置为 true
      tabsController.unitSetting[4][index].value = true;
    } else if (type == 5) {
      // 遍历所有元素，将其设置为 false
      for (var i = 0; i < tabsController.unitSetting[5].length; i++) {
        tabsController.unitSetting[5][i].value = false;
      }
      // 将指定下标的元素设置为 true
      tabsController.unitSetting[5][index].value = true;
    }

    tabsController.setUnit();
  }

  void updateIntUnit(int index, int type) {
    // logger.d("$index $type");
    if (type == 2) {
      // bloodOxygenUnit[index].value = !bloodOxygenUnit[index].value;
      if (tabsController.unitSetting[2][index].value == 0)
        tabsController.unitSetting[2][index].value = 1;
      else
        tabsController.unitSetting[2][index].value = 0;
    } else if (type == 3) {
      if (tabsController.unitSetting[3][index].value == 0)
        tabsController.unitSetting[3][index].value = 1;
      else
        tabsController.unitSetting[3][index].value = 0;
    }
    tabsController.setUnit();
  }

  void customShowModalBottomSheet() {
    RxString temp = RxString(tabsController.unitSetting[2][1].value);

    showModalBottomSheet(
      context: Get.context!,
      backgroundColor: Colors.white, // 设置对话框的背景颜色
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)), // 圆角样式
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.fromLTRB(ScreenAdapter.width(16), 0,
              ScreenAdapter.width(16), ScreenAdapter.height(10)), // 设置内容的内边距
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min, // 根据内容大小设置弹出高度
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(
                            color: AppColors.ColorF5,
                            width: 1,
                            style: BorderStyle.solid))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                            bottom: ScreenAdapter.height(8),
                            top: ScreenAdapter.height(12)),
                        child: Text(
                          T.wordsCancel.tr,
                          style: normalF12H17C999.copyWith(
                              color: AppColors.lightBlue),
                        ),
                      ),
                    ),
                    InkWell(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () async {
                          // submitSelect();
                          tabsController.unitSetting[2][1].value = temp.value;
                          Get.back();
                        },
                        child: Container(
                          padding: EdgeInsets.only(
                              bottom: ScreenAdapter.height(8),
                              top: ScreenAdapter.height(12)),
                          child: Text(
                            T.wordsSave.tr,
                            style: normalF12H17C999.copyWith(
                                color: AppColors.lightBlue),
                          ),
                        ))
                  ],
                ),
              ),
              // SizedBox(height: 16.0),
              // Gap(ScreenAdapter.height(28)),
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  temp.value = "0";

                  logger.d("点击1");
                },
                child: Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  padding: EdgeInsets.only(
                      top: ScreenAdapter.height(16),
                      bottom: ScreenAdapter.height(8)),
                  child: Obx(() => Text(
                        "75%",
                        style: normalF14H19C666.copyWith(
                            fontWeight: FontWeight.w500,
                            color: temp.value == "0"
                                ? AppColors.lightBlue
                                : AppColors.Color666),
                      )),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: AppColors.ColorF5,
                              width: 1,
                              style: BorderStyle.solid))),
                ),
              ),
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  temp.value = "1";
                },
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(
                      top: ScreenAdapter.height(16),
                      bottom: ScreenAdapter.height(8)),
                  child: Obx(() => Text(
                        "80%",
                        style: normalF14H19C666.copyWith(
                            fontWeight: FontWeight.w500,
                            color: temp.value == "1"
                                ? AppColors.lightBlue
                                : AppColors.Color666),
                      )),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: AppColors.ColorF5,
                              width: 1,
                              style: BorderStyle.solid))),
                ),
              ),
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  temp.value = "2";
                },
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(
                      top: ScreenAdapter.height(16),
                      bottom: ScreenAdapter.height(8)),
                  child: Obx(() => Text(
                        "85%",
                        style: normalF14H19C666.copyWith(
                            fontWeight: FontWeight.w500,
                            color: temp.value == "2"
                                ? AppColors.lightBlue
                                : AppColors.Color666),
                      )),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: AppColors.ColorF5,
                              width: 1,
                              style: BorderStyle.solid))),
                ),
              ),
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  temp.value = "3";
                },
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(
                      top: ScreenAdapter.height(16),
                      bottom: ScreenAdapter.height(8)),
                  child: Obx(() => Text(
                        "90%",
                        style: normalF14H19C666.copyWith(
                            fontWeight: FontWeight.w500,
                            color: temp.value == "3"
                                ? AppColors.lightBlue
                                : AppColors.Color666),
                      )),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: AppColors.ColorF5,
                              width: 1,
                              style: BorderStyle.solid))),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  // test() async {
  //   try {
  //     logger.d('=== 开始 Firebase Crashlytics 测试 ===');

  //     // 1. 设置用户标识
  //     FirebaseCrashlytics.instance.setUserIdentifier('test_user_123');
  //     logger.d('已设置用户标识: test_user_123');

  //     // 2. 设置自定义键值对
  //     FirebaseCrashlytics.instance.setCustomKey('test_screen', 'unit_setting');
  //     FirebaseCrashlytics.instance.setCustomKey('test_version', '1.0.0');
  //     FirebaseCrashlytics.instance.setCustomKey(
  //         'test_timestamp', DateTime.now().millisecondsSinceEpoch);
  //     logger.d('已设置自定义键值对');

  //     // 3. 记录日志
  //     FirebaseCrashlytics.instance.log('用户点击了测试按钮');
  //     FirebaseCrashlytics.instance.log('API 请求开始: /api/test');
  //     FirebaseCrashlytics.instance.log('测试时间: ${DateTime.now()}');
  //     logger.d('已记录测试日志');

  //     // 4. 检查崩溃收集状态
  //     bool isEnabled =
  //         FirebaseCrashlytics.instance.isCrashlyticsCollectionEnabled;
  //     logger.d('崩溃收集状态: $isEnabled');

  //     // 5. 检查上次是否崩溃
  //     bool didCrash =
  //         await FirebaseCrashlytics.instance.didCrashOnPreviousExecution();
  //     logger.d('上次是否崩溃: $didCrash');

  //     // 6. 只有在自动收集禁用时才检查未发送报告
  //     if (!isEnabled) {
  //       bool hasUnsentReports =
  //           await FirebaseCrashlytics.instance.checkForUnsentReports();
  //       logger.d('是否有未发送报告: $hasUnsentReports');

  //       if (hasUnsentReports) {
  //         logger.d('正在发送未发送的报告...');
  //         await FirebaseCrashlytics.instance.sendUnsentReports();
  //         logger.d('未发送报告已发送');
  //       }
  //     } else {
  //       logger.d('自动收集已启用，无需手动检查未发送报告');
  //     }

  //     // 7. 记录一个测试错误（非致命）
  //     logger.d('记录测试错误...');
  //     FirebaseCrashlytics.instance.recordError(
  //       '这是一个测试错误',
  //       StackTrace.current,
  //       reason: '测试 Crashlytics 功能',
  //       fatal: false,
  //     );
  //     logger.d('测试错误已记录');

  //     // 8. 等待一下让错误上报
  //     await Future.delayed(Duration(seconds: 2));
  //     logger.d('等待 2 秒让错误上报...');

  //     // 9. 最后触发崩溃
  //     logger.d('即将触发测试崩溃...');
  //     logger.d('应用将在 3 秒后崩溃，请查看 Firebase 控制台');

  //     // 给用户一些时间看到日志
  //     await Future.delayed(Duration(seconds: 3));

  //     // 触发崩溃
  //     FirebaseCrashlytics.instance.crash();
  //   } catch (e, stackTrace) {
  //     // 如果测试过程中出现异常，也记录下来
  //     logger.e('测试过程中出现异常: $e');
  //     FirebaseCrashlytics.instance.recordError(
  //       e,
  //       stackTrace,
  //       reason: 'Crashlytics 测试方法异常',
  //       fatal: false,
  //     );
  //   }
  // }

  // //如果直接崩溃的话，会有什么报错给firebase控制台呢？
  // test2() async {
  //   try {
  //     logger.d('=== 开始测试直接崩溃 ===');

  //     // 等待一下让日志输出
  //     await Future.delayed(Duration(seconds: 1));

  //     // 方法1: 抛出一个未捕获的异常
  //     logger.d('即将抛出未捕获的异常...');
  //     throw Exception('这是一个测试异常 - 未被捕获');

  //   } catch (e, stackTrace) {
  //     // 这里不应该被执行到，因为我们想要未捕获的异常
  //     logger.e('意外捕获了异常: $e');
  //     FirebaseCrashlytics.instance.recordError(
  //       e,
  //       stackTrace,
  //       reason: '意外捕获的测试异常',
  //       fatal: false,
  //     );
  //   }
  // }

  // // 测试不同类型的崩溃
  // test3() async {
  //   try {
  //     logger.d('=== 开始测试空指针异常 ===');

  //     // 等待一下让日志输出
  //     await Future.delayed(Duration(seconds: 1));

  //     // 方法2: 空指针异常
  //     String? nullString;
  //     logger.d('即将访问空对象...');
  //     print(nullString!.length); // 这会导致空指针异常

  //   } catch (e, stackTrace) {
  //     logger.e('捕获了空指针异常: $e');
  //     // 记录为非致命错误
  //     FirebaseCrashlytics.instance.recordError(
  //       e,
  //       stackTrace,
  //       reason: '空指针异常测试',
  //       fatal: false,
  //     );
  //     rethrow; // 重新抛出异常，让它成为未捕获的异常
  //   }
  // }

  // // 测试数组越界
  // test4() async {
  //   try {
  //     logger.d('=== 开始测试数组越界 ===');

  //     // 等待一下让日志输出
  //     await Future.delayed(Duration(seconds: 1));

  //     // 方法3: 数组越界
  //     List<String> list = ['item1', 'item2'];
  //     logger.d('即将访问越界索引...');
  //     print(list[10]); // 这会导致数组越界异常

  //   } catch (e, stackTrace) {
  //     logger.e('捕获了数组越界异常: $e');
  //     FirebaseCrashlytics.instance.recordError(
  //       e,
  //       stackTrace,
  //       reason: '数组越界异常测试',
  //       fatal: false,
  //     );
  //     rethrow; // 重新抛出异常
  //   }
  // }

  // // 测试纯 Flutter 崩溃 - 不使用 Firebase Crashlytics，让其自动捕获
  // test5() async {
  //   logger.d('=== 开始测试纯 Flutter 崩溃（无 try-catch） ===');

  //   // 设置一些上下文信息，但不使用 Firebase Crashlytics
  //   logger.d('用户操作：点击了 test5 按钮');
  //   logger.d('当前时间：${DateTime.now()}');
  //   logger.d('测试场景：纯 Flutter 异常，让 Firebase 自动捕获');

  //   // 等待一下让日志输出
  //   await Future.delayed(Duration(seconds: 2));

  //   logger.d('即将触发纯 Flutter 异常...');

  //   // 直接抛出异常，不使用任何 try-catch
  //   // Firebase Crashlytics 应该会自动捕获这个未处理的异常
  //   throw StateError('这是一个纯 Flutter StateError - 测试 Firebase 自动捕获功能');
  // }

  // // 测试另一种 Flutter 原生异常
  // test6() async {
  //   logger.d('=== 开始测试 Flutter 类型转换异常 ===');

  //   logger.d('准备进行错误的类型转换...');
  //   await Future.delayed(Duration(seconds: 1));

  //   // 类型转换异常 - 不使用 try-catch
  //   dynamic value = "这是一个字符串";
  //   logger.d('即将进行错误的类型转换...');

  //   // 这会导致类型转换异常
  //   int number = value as int; // 尝试将字符串转换为 int
  //   print('转换结果: $number'); // 这行不会被执行
  // }

  // // 测试空指针异常（更直接的方式）
  // test7() async {
  //   logger.d('=== 开始测试直接空指针访问 ===');

  //   await Future.delayed(Duration(seconds: 1));

  //   // 直接访问 null 对象的方法
  //   String? nullString;
  //   logger.d('即将访问 null 对象的方法...');

  //   // 这会导致 NoSuchMethodError - 故意使用 ! 来强制访问 null
  //   nullString!.toUpperCase(); // 强制解包 null 值
  // }
}
