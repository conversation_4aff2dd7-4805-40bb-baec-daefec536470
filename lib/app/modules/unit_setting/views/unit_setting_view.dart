/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-03-03 15:56:03
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-29 10:56:02
 * @FilePath: /rpmappmaster/lib/app/modules/unit_setting/views/unit_setting_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/modules/unit_setting/widgets/blue_switch.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';

import '../controllers/unit_setting_controller.dart';

class UnitSettingView extends BaseView<UnitSettingController> {
  UnitSettingView({
    super.key,
  }) : super(
          // ltrb
          // ltrb
          bgColor: AppColors.colorWhite,
        );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: T.unitSTitle.tr,
      backgroundColor: AppColors.colorWhite,
    );
  }

  @override
  Widget body(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      child: SingleChildScrollView(
        child: Container(
          constraints: BoxConstraints(
            minHeight: ScreenAdapter.height(812),
          ),
          child: Column(
            children: [
              // HeightUnit(),
              // _parentContainer(T.unitSHeightUnit,
              //     controller.getSubTitle(0), controller.heightUnit, 0),
              // _parentContainer(T.unitSWeightUnit,
              //     controller.getSubTitle(1), controller.weightUnit, 1),
              _parentContainer(T.bloodOxygen.tr, controller.getSubTitle(2),
                  controller.tabsController.unitSetting[2], 2),
              _parentContainer(
                  T.unitSTemperatureUnit.tr,
                  controller.getSubTitle(3),
                  controller.tabsController.unitSetting[3],
                  3),

//               TextButton(
//     onPressed: () => throw Exception(),
//     child: const Text("Throw Test Exception"),
// ),

              // ElevatedButton(
              //   onPressed: () => controller.test(),
              //   child: const Text("触发测试崩溃"),
              // ),

              // const SizedBox(height: 10),

              // ElevatedButton(
              //   onPressed: () => controller.test2(),
              //   child: const Text("测试未捕获异常"),
              //   style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
              // ),

              // const SizedBox(height: 10),

              // ElevatedButton(
              //   onPressed: () => controller.test3(),
              //   child: const Text("测试空指针异常"),
              //   style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              // ),

              // const SizedBox(height: 10),

              // ElevatedButton(
              //   onPressed: () => controller.test4(),
              //   child: const Text("测试数组越界"),
              //   style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
              // ),

              // const SizedBox(height: 10),

              // ElevatedButton(
              //   onPressed: () => controller.test5(),
              //   child: const Text("纯Flutter崩溃(StateError)"),
              //   style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              // ),

              // const SizedBox(height: 10),

              // ElevatedButton(
              //   onPressed: () => controller.test6(),
              //   child: const Text("类型转换异常"),
              //   style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
              // ),

              // const SizedBox(height: 10),

              // ElevatedButton(
              //   onPressed: () => controller.test7(),
              //   child: const Text("空指针方法调用"),
              //   style: ElevatedButton.styleFrom(backgroundColor: Colors.brown),
              // )

              // Container(
              //   color: Colors.blue,
              //   constraints: BoxConstraints(maxHeight: 90),
              //   child: ListView.builder(
              //       shrinkWrap: true,
              //       itemCount: 1,
              //       itemBuilder: (context, index) {
              //         return Container(
              //           margin: EdgeInsets.symmetric(vertical: 10),
              //           height: 10,
              //           color: Colors.red,
              //         );
              //       }),
              // )
              // _parentContainer(T.unitSBloodPressureUnit,
              //     controller.getSubTitle(4), controller.tabsController.unitSetting[4], 4),
              // _parentContainer(T.unitSBloodGlucoseUnits,
              //     controller.getSubTitle(5), controller.glucoseUnit, 5),
            ],
          ),
        ),
      ),
    );
  }

  Widget HeightUnit() {
    return Container();
  }

  Widget _parentContainer(
      String title, List<String> subtitle, List<dynamic> value, int type) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: normalF12H17C999.copyWith(fontWeight: FontWeight.w500),
        ),
        Container(
            margin: EdgeInsets.only(
                top: ScreenAdapter.height(8), bottom: ScreenAdapter.height(24)),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8), color: Colors.white),
            child: Column(
                children: List.generate(subtitle.length, (index) {
              // 构建每个子项
              return Container(
                // padding: EdgeInsets.symmetric(
                //     horizontal: ScreenAdapter.width(12),
                //     vertical: ScreenAdapter.height(12)),
                padding: EdgeInsets.only(
                  top: ScreenAdapter.height(4),
                  bottom: ScreenAdapter.height(4),
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                  // 如果不是最后一个子项，添加下边框
                  border: (index != subtitle.length - 1)
                      ? Border(
                          bottom: BorderSide(
                            color: AppColors.homeBgColor,
                            width: 1,
                          ),
                        )
                      : null,
                ),
                child: _buildSubtitleItem(
                    subtitle[index], value[index], index, type),
              );
            })))
      ],
    );
  }

  Widget _buildSubtitleItem(
      String subtitle, dynamic value, int index, int type) {
    if (value is RxBool) {
      return InkWell(
        onTap: () {
          logger.d("点击按钮");
          controller.updateBoolUnit(index, type);
        },
        child: Container(
          height: ScreenAdapter.height(36),
          padding: EdgeInsets.symmetric(
              vertical: ScreenAdapter.height(8),
              horizontal: ScreenAdapter.width(12)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                subtitle,
                style: normalF14H19C333.copyWith(fontWeight: FontWeight.w500),
              ),
              _settingRadio(value)
            ],
          ),
        ),
      );
    } else if (value is RxString) {
      return InkWell(
        onTap: () {
          logger.d("点击按钮，打开下拉选择");
          // controller.updateBoolUnit(index, type);
          // controller.updateIntUnit(index, type);
          controller.customShowModalBottomSheet();
        },
        child: Container(
          height: ScreenAdapter.height(36),
          padding: EdgeInsets.symmetric(
              vertical: ScreenAdapter.height(8),
              horizontal: ScreenAdapter.width(12)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                subtitle,
                style: normalF14H19C333.copyWith(fontWeight: FontWeight.w500),
              ),
              // _settingSwitch(value, index, type)
              // CustomBlueSwitch(
              //   value: value,
              // )
              _settingString(value, index, type)
            ],
          ),
        ),
      );
    } else if (value is RxInt) {
      // type = 2; // 如果是 RxInt，则是 2
      return InkWell(
        onTap: () {
          logger.d("点击按钮");
          // controller.updateBoolUnit(index, type);
          controller.updateIntUnit(index, type);
        },
        child: Container(
          height: ScreenAdapter.height(36),
          padding: EdgeInsets.symmetric(
              vertical: ScreenAdapter.height(8),
              horizontal: ScreenAdapter.width(12)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                subtitle,
                style: normalF14H19C333.copyWith(fontWeight: FontWeight.w500),
              ),
              // _settingSwitch(value, index, type)
              CustomBlueSwitch(
                value: value,
              )
            ],
          ),
        ),
      );
    } else {
      return Container();
    }

    // return Container();
  }

  Widget _settingRadio(RxBool value) {
    return Container(
      height: ScreenAdapter.height(14),
      child: Obx(
        () => AnimatedSwitcher(
          duration: Duration(milliseconds: 300), // 动画时长
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          child: value.value == true
              ? Image.asset(
                  Assets.images.settingButtonOn.path,
                  key: ValueKey('on'), // 为每个图片设置唯一的 Key
                )
              : Image.asset(
                  Assets.images.settingButtonNo.path,
                  key: ValueKey('off'), // 为每个图片设置唯一的 Key
                ),
        ),
      ),
    );
  }

  Widget _settingString(RxString value, int index, int type) {
    if (type == 2) {
      return Row(
        children: [
          Obx(() => Text(listen7590(value),
              style: normalF14H19C666.copyWith(
                  fontWeight: FontWeight.w500, color: AppColors.lightBlue))),
          Gap(ScreenAdapter.width(8)),
          SizedBox(
            height: ScreenAdapter.height(8),
            child: AspectRatio(
              aspectRatio: 4.5 / 8,
              child: SvgPicture.asset(
                Assets.images.rightArrow48,
                fit: BoxFit.cover,
              ),
            ),
          )
        ],
      );
    } else
      return Container();
  }

  listen7590(RxString value) {
    switch (value.value) {
      case "0":
        return "75%";
      case "1":
        return "80%";
      case "2":
        return "85%";
      case "3":
        return "90%";
      default:
        return "";
    }
  }
}
