import 'dart:math';

import 'package:aiCare/app/core/enum/temperature_unit.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/modules/related_infor/controllers/related_infor_controller.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class RelatedRightIcon extends StatefulWidget {
  const RelatedRightIcon({super.key});
  
  @override
  State<RelatedRightIcon> createState() => _RelatedRightIconState();
}

class _RelatedRightIconState extends State<RelatedRightIcon> {
  bool _isDropdownVisible = false; // 控制选择列表是否显示
  TabsController tabsController = Get.find();
  RelatedInforController relatedInforController = Get.find();




  @override
  Widget build(BuildContext context) {
    return relatedInforController.index == 2 ? Container(
      margin: EdgeInsets.only(right: ScreenAdapter.width(8)),
      child: IconButton(
      // padding: EdgeInsets.zero,
      // iconSize: ScreenAdapter.width(16),
      icon: SizedBox(
        width: ScreenAdapter.width(16),
        height: ScreenAdapter.width(16),
        child: Image.asset(Assets.images.unitSwitch.path),
      ),
      onPressed: () {
        // 弹出下拉菜单
        showMenu(
          context: context,
          position: RelativeRect.fromLTRB(300.0, 100.0, 0.0, 0.0),
          items: [
            PopupMenuItem<String>(
              value: 'true',
              child:Obx(()=> Text('℃', style: tabsController.temperatureUnit.value != true ? normalF14H19C666 : normalF14H19C333.copyWith(
                color: AppColors.lightBlue
              ),),)
            ),
            PopupMenuItem<String>(
              value: 'false',
              child: Obx(()=>Text('℉', style: tabsController.temperatureUnit.value != false ? normalF14H19C666 : normalF14H19C333.copyWith(
                color: AppColors.lightBlue
              ),),)
            ),
          ],
          elevation: 8.0,
        ).then((value) {
          if (value != null) {
            if(value == "true") {
              tabsController.temperatureUnit.value = true;
              SecureStorageService.instance.setTemperatureUnit(TemperatureUnit.celsius);
            }
            else {
              tabsController.temperatureUnit.value = false;
              SecureStorageService.instance.setTemperatureUnit(TemperatureUnit.fahrenheit);
            }
            // tabsController.update();
            // tabsController.temperatureUnit.value = value;
          }
        });
      },
    ),
    ):SizedBox();
  }
}
