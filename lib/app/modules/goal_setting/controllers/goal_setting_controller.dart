/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-03-03 15:55:52
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-18 13:55:06
 * @FilePath: /rpmappmaster/lib/app/modules/goal_setting/controllers/goal_setting_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/data/model/my_goal.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/user/user_home/controllers/user_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/services/toastHelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:get/get.dart';
import 'package:string_validator/string_validator.dart';

class GoalSettingController extends BaseController {
  // TabsController tabsController = Get.find();
  UserHomeController userHomeController = Get.find();
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  bool isUpdate = false;
  //TODO: Implement GoalSettingController
  List<TextEditingController> textList = [
    TextEditingController(text: "6000"),
    TextEditingController(text: "82"),
    TextEditingController(text: "120"),
    TextEditingController(text: "120"),
    TextEditingController(text: "60"),
  ];
  List<FocusNode> focusNode = [
    FocusNode(),
    FocusNode(),
    FocusNode(),
    FocusNode(),
    FocusNode(),
  ];
  List<RxInt> intList = [
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
  ];

  bool _hasFocus = false; // 用于记录是否有任何一个 FocusNode 拥有焦点

  @override
  void onInit() {
    super.onInit();
    for (int i = 0; i < focusNode.length; i++) {
      logger.d("FocusNode $i initialized: ${focusNode[i] != null}");
      logger.d("TextEditingController $i initialized: ${textList[i] != null}");
    }

    for (int i = 0; i < focusNode.length; i++) {
      focusNode[i].addListener(() {
        _hasFocus = focusNode.any((node) => node.hasFocus);
        if (!_hasFocus) {
          // 所有 FocusNode 都失去焦点时收起键盘
          logger.d("所有焦点失去，收起键盘");
          FocusScope.of(Get.context!).unfocus();
          validateInputRange(i);
        }
      });
    }

    getIntList();

    // textList[1].text = tabsController.goalSetting.value.bloodPressure[0].toString();
    // textList[2].text = tabsController.goalSetting.value.bloodPressure[1].toString();
  }

  @override
  void onReady() {
    super.onReady();
  }

  void validateInputRange(int index) {
    isUpdate = true;
    String value = textList[index].text;
    if (index == 0) {
      if (value.isNotEmpty) {
        int number = int.tryParse(value) ?? 0;
        // 这里可以根据不同的输入框设置不同的范围验证逻辑
        if (number < 1000) {
          textList[index].text = "1000";
          textList[index].selection = TextSelection.fromPosition(
            TextPosition(offset: textList[index].text.length),
          );
        } else if (number > 100000) {
          textList[index].text = "100000";
          textList[index].selection = TextSelection.fromPosition(
            TextPosition(offset: textList[index].text.length),
          );
        }
      } else {
        textList[index].text = "1000";
        textList[index].selection = TextSelection.fromPosition(
          TextPosition(offset: textList[index].text.length),
        );
      }
      // 可以添加更多的输入框范围验证逻辑
    }
  }

  @override
  void onClose() {
    // textEditingController.dispose();
    //这里进行一个网络接口请求，更新目标设置

    FocusScope.of(Get.context!).unfocus();
    textList.forEach((controller) => controller.dispose());
    focusNode.forEach((node) => node.dispose());
    super.onClose();
  }

  void customShowModalBottomSheet(int i) {
    logger.d("打开的下拉选项${i}");
    // RxString temp = RxString(tabsController.unitSetting[2][1].value);
    RxInt temp = RxInt(intList[i].value);

    showModalBottomSheet(
      context: Get.context!,
      backgroundColor: Colors.white, // 设置对话框的背景颜色
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)), // 圆角样式
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.fromLTRB(ScreenAdapter.width(16), 0,
              ScreenAdapter.width(16), ScreenAdapter.height(10)), // 设置内容的内边距
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min, // 根据内容大小设置弹出高度
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(
                            color: AppColors.ColorF5,
                            width: 1,
                            style: BorderStyle.solid))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                            bottom: ScreenAdapter.height(8),
                            top: ScreenAdapter.height(12)),
                        child: Text(
                          T.wordsCancel.tr,
                          style: normalF12H17C999.copyWith(
                              color: AppColors.lightBlue),
                        ),
                      ),
                    ),
                    InkWell(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () async {
                          // submitSelect();
                          // tabsController.unitSetting[2][1].value = temp.value;
                          intList[i].value = temp.value;
                          isUpdate = true;
                          logger.d("保存");
                          Get.back();
                        },
                        child: Container(
                          padding: EdgeInsets.only(
                              bottom: ScreenAdapter.height(8),
                              top: ScreenAdapter.height(12)),
                          child: Text(
                            T.wordsSave.tr,
                            style: normalF12H17C999.copyWith(
                                color: AppColors.lightBlue),
                          ),
                        ))
                  ],
                ),
              ),
              // SizedBox(height: 16.0),
              // Gap(ScreenAdapter.height(28)),
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  temp.value = 0;

                  logger.d("点击1");
                },
                child: Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  padding: EdgeInsets.only(
                      top: ScreenAdapter.height(16),
                      bottom: ScreenAdapter.height(8)),
                  child: Obx(() => Text(
                        "70-80",
                        style: normalF14H19C666.copyWith(
                            fontWeight: FontWeight.w500,
                            color: temp.value == 0
                                ? AppColors.lightBlue
                                : AppColors.Color666),
                      )),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: AppColors.ColorF5,
                              width: 1,
                              style: BorderStyle.solid))),
                ),
              ),
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  temp.value = 1;
                },
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(
                      top: ScreenAdapter.height(16),
                      bottom: ScreenAdapter.height(8)),
                  child: Obx(() => Text(
                        "80-90",
                        style: normalF14H19C666.copyWith(
                            fontWeight: FontWeight.w500,
                            color: temp.value == 1
                                ? AppColors.lightBlue
                                : AppColors.Color666),
                      )),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: AppColors.ColorF5,
                              width: 1,
                              style: BorderStyle.solid))),
                ),
              ),
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  temp.value = 2;
                },
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(
                      top: ScreenAdapter.height(16),
                      bottom: ScreenAdapter.height(8)),
                  child: Obx(() => Text(
                        "90-100",
                        style: normalF14H19C666.copyWith(
                            fontWeight: FontWeight.w500,
                            color: temp.value == 2
                                ? AppColors.lightBlue
                                : AppColors.Color666),
                      )),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: AppColors.ColorF5,
                              width: 1,
                              style: BorderStyle.solid))),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void getIntList() async {
    //如果 userModel.value.target为空，则设置为默认值
    //通过 patchInfo 接口推送默认值
    if (userHomeController.userModel.value.target == null) {
      logger.d("userModel.value.target为空，推送默认值");
      //如果推送成功，则更新userModel.value
      var defaultTarget = MyGoalClass(
          step: 6000,
          bloodPressure: [80, 100],
          bloodGlucose: BloodGlucoseSub(
            wakeUp: GlucoseSub(timeRange: ["06AM-07AM"], values: [0]),
            breakfast: GlucoseSub(timeRange: [
              "07AM-09AM",
              "09AM-11AM",
            ], values: [
              0,
              0
            ]),
            lunch: GlucoseSub(timeRange: [
              "11AM-01PM",
              "01PM-05PM",
            ], values: [
              0,
              0
            ]),
            dinner: GlucoseSub(timeRange: [
              "05PM-07PM",
              "07PM-08PM",
            ], values: [
              0,
              0
            ]),
            bedtime: GlucoseSub(timeRange: ["08PM-10PM"], values: [0]),
            midnight: GlucoseSub(timeRange: ["10PM-06AM"], values: [0]),
          ),
          reminders: [120, 60]);
      var result = await defaultRepositoryImpl.patchInfo({
        "target": defaultTarget,
      });
      if (result) {
        userHomeController.userModel.value.target = defaultTarget;
        //设置内存中
        userHomeController.setUserLocal();
        logger.d("推送成功");
      } else {
        logger.d("推送失败");
        ToastUtil.showError(Get.context!, T.dataParseFailedRetry.tr);
      }
      logger.d("查看userModel");
      logger.d(userHomeController.userModel.value.toJson());
      intList[0].value = userHomeController
          .userModel.value.target!.bloodGlucose.wakeUp.values[0];
      intList[1].value = userHomeController
          .userModel.value.target!.bloodGlucose.breakfast.values[0];
      intList[2].value = userHomeController
          .userModel.value.target!.bloodGlucose.breakfast.values[1];
      intList[3].value = userHomeController
          .userModel.value.target!.bloodGlucose.lunch.values[0];
      intList[4].value = userHomeController
          .userModel.value.target!.bloodGlucose.lunch.values[1];
      intList[5].value = userHomeController
          .userModel.value.target!.bloodGlucose.dinner.values[0];
      intList[6].value = userHomeController
          .userModel.value.target!.bloodGlucose.dinner.values[1];
      intList[7].value = userHomeController
          .userModel.value.target!.bloodGlucose.bedtime.values[0];
      intList[8].value = userHomeController
          .userModel.value.target!.bloodGlucose.midnight.values[0];

      textList[0].text =
          userHomeController.userModel.value.target!.step.toString();
      textList[1].text = userHomeController
          .userModel.value.target!.bloodPressure[0]
          .toString();
      textList[2].text = userHomeController
          .userModel.value.target!.bloodPressure[1]
          .toString();
      textList[3].text =
          userHomeController.userModel.value.target!.reminders[0].toString();
      textList[4].text =
          userHomeController.userModel.value.target!.reminders[1].toString();
    } else {
      logger.d("userModel.value.target不为空，直接读取");
      intList[0].value = userHomeController
          .userModel.value.target!.bloodGlucose.wakeUp.values[0];
      intList[1].value = userHomeController
          .userModel.value.target!.bloodGlucose.breakfast.values[0];
      intList[2].value = userHomeController
          .userModel.value.target!.bloodGlucose.breakfast.values[1];
      intList[3].value = userHomeController
          .userModel.value.target!.bloodGlucose.lunch.values[0];
      intList[4].value = userHomeController
          .userModel.value.target!.bloodGlucose.lunch.values[1];
      intList[5].value = userHomeController
          .userModel.value.target!.bloodGlucose.dinner.values[0];
      intList[6].value = userHomeController
          .userModel.value.target!.bloodGlucose.dinner.values[1];
      intList[7].value = userHomeController
          .userModel.value.target!.bloodGlucose.bedtime.values[0];
      intList[8].value = userHomeController
          .userModel.value.target!.bloodGlucose.midnight.values[0];

      textList[0].text =
          userHomeController.userModel.value.target!.step.toString();
      textList[1].text = userHomeController
          .userModel.value.target!.bloodPressure[0]
          .toString();
      textList[2].text = userHomeController
          .userModel.value.target!.bloodPressure[1]
          .toString();
      textList[3].text =
          userHomeController.userModel.value.target!.reminders[0].toString();
      textList[4].text =
          userHomeController.userModel.value.target!.reminders[1].toString();
    }
  }

  // 将 intList 写回数据源
  void setIntList() {
    userHomeController.userModel.value.target!.bloodGlucose.wakeUp.values[0] =
        intList[0].value;
    userHomeController.userModel.value.target!.bloodGlucose.breakfast
        .values[0] = intList[1].value;
    userHomeController.userModel.value.target!.bloodGlucose.breakfast
        .values[1] = intList[2].value;
    userHomeController.userModel.value.target!.bloodGlucose.lunch.values[0] =
        intList[3].value;
    userHomeController.userModel.value.target!.bloodGlucose.lunch.values[1] =
        intList[4].value;
    userHomeController.userModel.value.target!.bloodGlucose.dinner.values[0] =
        intList[5].value;
    userHomeController.userModel.value.target!.bloodGlucose.dinner.values[1] =
        intList[6].value;
    userHomeController.userModel.value.target!.bloodGlucose.bedtime.values[0] =
        intList[7].value;
    userHomeController.userModel.value.target!.bloodGlucose.midnight.values[0] =
        intList[8].value;

    userHomeController.userModel.value.target!.step =
        int.parse(textList[0].text);
    userHomeController.userModel.value.target!.bloodPressure[0] =
        int.parse(textList[1].text);
    userHomeController.userModel.value.target!.bloodPressure[1] =
        int.parse(textList[2].text);
    userHomeController.userModel.value.target!.reminders[0] =
        int.parse(textList[3].text);
    userHomeController.userModel.value.target!.reminders[1] =
        int.parse(textList[4].text);

    // 如果需要持久化保存，这里可以添加更新逻辑
    userHomeController.userModel.refresh(); // 示例：触发状态更新

    userHomeController.setUserLocal();
    logger.d("查看修改后的userModel");
    logger.d(userHomeController.userModel.value.toJson());
  }

  mySave() {
    logger.d("点击");
    // if (isUpdate) {
    // logger.d("进行了修改，需要上传");
    setIntList();

    callDataService(
        defaultRepositoryImpl.postGoalSetting(
            userHomeController.userModel.value.target!), onSuccess: (response) {
      logger.d("上传成功");
      ToastUtil.showSuccess(Get.context!, T.success.tr);
    });
    // ToastHelper.Successful(T.success.tr);
    // ToastUtil.showSuccess(Get.context!, T.success.tr);
  }
  // }
}
