/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-03-03 15:55:52
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-31 13:55:16
 * @FilePath: /rpmappmaster/lib/app/modules/goal_setting/views/goal_setting_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';

import '../controllers/goal_setting_controller.dart';

class GoalSettingView extends BaseView<GoalSettingController> {
  GoalSettingView({
    super.key,
  }) : super(
          // ltrb
          bgColor: AppColors.loginPrimaryColor,
          statusBarColor: AppColors.colorWhite,
        );
  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: T.hgsTitle.tr,
      backgroundColor: AppColors.colorWhite,
    );
  }

  @override
  Widget body(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      child: SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _StepTarget(),
          _BloodPressureGoal(),
          _BloodGlucoseGoal(),
          _ReminderHeaderRate(),
        ],
      ),
    ));
  }

  Widget _StepTarget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      // mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          T.hgsStepTarget.tr,
          style: normalF12H17C999.copyWith(fontWeight: FontWeight.w500),
        ),
        _textField([T.hgsDailyGoal.tr], 0, 0)
      ],
    );
  }

  Widget _BloodPressureGoal() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      // mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          T.hgsBloodPressureGoal.tr,
          style: normalF12H17C999.copyWith(fontWeight: FontWeight.w500),
        ),
        _textField([T.wordsSys.tr, T.wordsDia.tr], 1, 0)
      ],
    );
  }

  Widget _BloodGlucoseGoal() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      // mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          T.hgsBloodGlucoseGoal.tr,
          style: normalF12H17C999.copyWith(fontWeight: FontWeight.w500),
        ),
        _bottomSheet([
          T.wordsWakeUp.tr,
          T.wordsBreakfast.tr,
          T.wordsLunch.tr,
          T.wordsDinner.tr,
          T.wordsBedtime.tr,
          T.wordsMidnight.tr
        ], 0)
      ],
    );
  }

  Widget _ReminderHeaderRate() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      // mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          T.hgsReminderHeartRate.tr,
          style: normalF12H17C999.copyWith(fontWeight: FontWeight.w500),
        ),
        _textField([
          T.hgsHighReminderHeartRate.tr,
          T.hgsLowReminderHeartRate.tr
        ], 3, 1)
      ],
    );
  }

  Widget _textField(List<String> subTitle, int index, int type) {
    return Container(
        width: double.infinity,
        // height: ScreenAdapter.height(44),
        margin: EdgeInsets.only(
            top: ScreenAdapter.height(8), bottom: ScreenAdapter.height(24)),
        padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(12)),
        decoration: BoxDecoration(
          color: AppColors.colorWhite,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: List.generate(subTitle.length, (val) {
            return GestureDetector(
                onTap: () {
                  logger.d("执行获取焦点${index + val}");
                  // 检查当前 FocusScope 的状态
                  logger.d(
                      "Current FocusScope has focused node: ${FocusScope.of(Get.context!).hasFocus}");
                  // 清除所有焦点
                  FocusScope.of(Get.context!).unfocus();
                  controller.focusNode[index + val].requestFocus();
                },
                child: IntrinsicHeight(
                  child: Row(
                    // mainAxisAlignment: MainAxisAlignment.,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Text(
                        // T.hgsDailyGoal,
                        subTitle[val],
                        style: normalF14H19C333.copyWith(
                            fontWeight: FontWeight.w500),
                      ),
                      Expanded(
                          child: AbsorbPointer(
                        child: Container(
                          height: double.infinity,
                          // height: 20,
                        ),
                      )),
                      SizedBox(
                        width: 60,
                        // height: ScreenAdapter.height(20),
                        child: TextField(
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          scrollPadding: EdgeInsets.zero,
                          textAlign: TextAlign.right,
                          controller: controller.textList[index + val],
                          focusNode: controller.focusNode[index + val],
                          style: normalF14H19C333.copyWith(
                            fontWeight: FontWeight.w500,
                            color: AppColors.lightBlue,
                          ),
                          cursorColor: Colors.black,
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            errorBorder: InputBorder.none,
                            disabledBorder: InputBorder.none,
                          ),
                        ),
                      ),
                      if (type == 1)
                        Text(
                          T.commonTimesMinuteSmall.tr,
                          style: normalF12H17C333.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppColors.lightBlue),
                        ),
                      SizedBox(width: ScreenAdapter.width(4)),
                      SizedBox(
                        height: ScreenAdapter.height(8),
                        child: AspectRatio(
                          aspectRatio: 4.5 / 8,
                          child: SvgPicture.asset(
                            'assets/images/right_arrow_48.svg', // 请替换为实际的 SVG 图片路径
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ],
                  ),
                ));
          }),
        ));
  }

  _bottomSheet(List<String> subTitle, int index) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(
        top: ScreenAdapter.height(8),
        bottom: ScreenAdapter.height(24),
      ),
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(12)),
      decoration: BoxDecoration(
        color: AppColors.colorWhite,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Builder(
        builder: (context) {
          // 生成前缀和数组，计算累积时间段数
          List<int> prefixSum = [0];
          for (int i = 0; i < subTitle.length; i++) {
            prefixSum.add(prefixSum.last + _bloodGlucosSubTime(i).length);
          }

          return Column(
            children: List.generate(subTitle.length, (val) {
              return GestureDetector(
                onTap: () {
                  // 点击事件逻辑
                },
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: AppColors.homeBgColor,
                        width: 1,
                      ),
                    ),
                  ),
                  padding: EdgeInsets.only(bottom: ScreenAdapter.height(8)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
                        child: Text(
                          subTitle[val],
                          style: normalF14H19C333.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(
                          _bloodGlucosSubTime(val).length,
                          (value) {
                            return InkWell(
                              onTap: () {
                                // 使用前缀和计算正确索引
                                int index = prefixSum[val] + value;
                                controller.customShowModalBottomSheet(index);
                              },
                              child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.only(
                                    top: ScreenAdapter.height(8)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      _bloodGlucosSubTime(val)[value],
                                      style: normalF12H17C333,
                                    ),
                                    Expanded(child: Container()),
                                    Obx(
                                      () => Text(
                                        getGlucoseText(controller.intList[prefixSum[val] + value].value),
                                        style: normalF14H19C333.copyWith(
                                          color: AppColors.lightBlue,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: ScreenAdapter.width(4)),
                                    SizedBox(
                                      height: ScreenAdapter.height(8),
                                      child: AspectRatio(
                                        aspectRatio: 4.5 / 8,
                                        child: SvgPicture.asset(
                                          'assets/images/right_arrow_48.svg',
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
          );
        },
      ),
    );
  }

  String getGlucoseText(int i) {
    switch (i) {
      case 0:
        return "70/80";
      case 1:
        return "80/90";
      case 2:
        return "90/100";
      default:
        return "null";
    }
  }

  List<String> _bloodGlucosSubTime(int i) {
    switch (i) {
      case 0:
        return ["06AM-07AM"];
      case 1:
        return [
          "${T.wordsBeforeMeal.tr}    06AM-07AM",
          "${T.wordsAfterMeal.tr}    09AM-11AM",
        ];
      case 2:
        return [
          "${T.wordsBeforeMeal.tr}    11AM-01PM",
          "${T.wordsAfterMeal.tr}    01PM-05PM",
        ];
      case 3:
        return [
          "${T.wordsBeforeMeal.tr}    05PM-07PM",
          "${T.wordsAfterMeal.tr}    07PM-09PM",
        ];
      case 4:
        return ["08PM-10PM"];
      case 5:
        return ["10PM-06AM"];
      default:
        return ["-1"];
    }
  }
}
