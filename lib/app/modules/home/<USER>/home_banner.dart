/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-26 10:05:11
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-08-26 17:01:02
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/home/<USER>/home_banner.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class HomeBanner extends StatelessWidget {
  const HomeBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
        // top: ScreenAdapter.height(-50),
        top: 0,
        // left: ScreenAdapter.width(48),
        right: 0,
        child: IgnorePointer(
            ignoring: true,
            child: Image.asset(
                Assets.images.backBlueBlur.path,
                fit: BoxFit.cover, // 确保图片按比例显示
                width: ScreenAdapter.width(327),
                height: ScreenAdapter.height(220),
                // color: Colors.red,
              )));
  }
}
