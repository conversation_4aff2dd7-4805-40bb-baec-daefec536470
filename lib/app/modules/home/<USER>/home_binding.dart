/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-18 11:05:21
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/home/<USER>/home_binding.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/controllers/blood_oxygen_controller.dart';
import 'package:aiCare/app/modules/blood_pressure/pressure_home/controllers/pressure_home_controller.dart';
import 'package:aiCare/app/modules/health/health_home/controllers/health_controller.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/controllers/temperature_home_controller.dart';
import 'package:aiCare/app/modules/weight/weight_home/controllers/weight_home_controller.dart';
import 'package:get/get.dart';

import '../controllers/home_controller.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {

    // Get.lazyPut<TemperatureHomeController>(
    //   () => TemperatureHomeController(),
    // );
    // Get.lazyPut<PressureHomeController>(
    //   () => PressureHomeController(),
    // );

    // Get.lazyPut<HealthController>(
    //   () => HealthController(),
    // );
    //     Get.lazyPut<WeightHomeController>(
    //   () => WeightHomeController(),
    // );
  }
}
