/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-13 16:12:46
 * @FilePath: /rpmappmaster/lib/app/modules/home/<USER>/home_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/model/page_background.dart';
import 'package:aiCare/app/core/model/position_top_left.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_liquid.dart';
import 'package:aiCare/app/core/widget/custom_temperature_chart.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/views/blood_oxygen_view.dart';
import 'package:aiCare/app/modules/blood_pressure/pressure_home/views/pressure_home_view.dart';
import 'package:aiCare/app/modules/blood_sugar/sugar_home/views/sugar_home_view.dart';
import 'package:aiCare/app/core/widget/custom_home_bar.dart';
import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
import 'package:aiCare/app/modules/sleep/sleep_home/widgets/sleep_pie_chart.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/views/temperature_home_view.dart';
import 'package:aiCare/app/modules/weight/weight_home/views/weight_home_view.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:aiCare/app/core/base/view/base_view.dart';
import '../controllers/home_controller.dart';

class HomeView extends BaseView<HomeController> {
  HomeView({
    super.key,
  }) : super(
          // bgColor: AppColors.,
          // statusBarColor: AppColors.homeBgColor,
          bgColor: Colors.transparent,
          bgImage: PageBackground(
            imagePath: Assets.images.backBlueBlur.path,
            width: ScreenAdapter.width(375), // 宽度375
            height: ScreenAdapter.height(320), // 高度320
            fit: BoxFit.fitHeight, // 填充方式
            left: 0,
            top: 0,
          ),
        );

  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {
    return Container(
        // padding: EdgeInsets.only(top: ScreenAdapter.height(44)),
        constraints: BoxConstraints(
          minHeight: ScreenAdapter.height(200),
        ),
        child: GridView.builder(
          shrinkWrap: true, // GridView 根据内容高度调整
          padding: EdgeInsets.only(
              left: ScreenAdapter.width(77 / 3),
              right: ScreenAdapter.width(77 / 3),
              top: ScreenAdapter.height(28),
              bottom: ScreenAdapter.height(28)),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2, // 每行两个子项
            crossAxisSpacing: ScreenAdapter.width(77 / 3), // 水平间距计算公式
            mainAxisSpacing: ScreenAdapter.height(16), // 垂直间距
            mainAxisExtent: ScreenAdapter.width(149), // 固定子项高度
          ),
          itemCount: 8, // 修改为实际需要的组件数量
          itemBuilder: (context, index) {
            switch (index) {
              case 0:
                return _OverOxygen();
              case 1:
                return _OverTemperature();
              case 2:
                return _OverHeartRate();
              case 3:
                return _OverFitness();
              case 4:
                return _OverSleep();
              default:
                return const SizedBox.shrink(); // 使用 const 构造器
            }
          },
        ));
  }

  _OverSleep() {
    return InkWell(
      onTap: () {
        // 跳转到血氧页面
        Get.toNamed(Routes.SLEEP_HOME);
        CustomHomeBar().setCenterValue(4);
        CustomHomeBar().index = 4;
      },
      child: Obx(() => Container(
            width: ScreenAdapter.width(149),
            height: ScreenAdapter.width(149),
            decoration: BoxDecoration(
              color: AppColors.colorWhite,
              borderRadius: BorderRadius.circular(ScreenAdapter.width(12)),
            ),
            padding: EdgeInsets.only(
                top: ScreenAdapter.height(12),
                left: ScreenAdapter.width(12),
                right: ScreenAdapter.width(12)),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    //title
                    Text(
                      T.sleep.tr,
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: ScreenAdapter.fontSize(12),
                          height: ScreenAdapter.fontSize(16.8 / 12),
                          color: AppColors.Color333),
                    ),
                    //icon
                    Image.asset(
                      Assets.images.sleepIcon.path,
                      width: ScreenAdapter.width(16),
                      height: ScreenAdapter.height(16),
                    )
                  ],
                ),
                controller.aizoSleepData.value.date.year == 2000
                    ? Center(
                        child: Container(
                          margin:
                              EdgeInsets.only(top: ScreenAdapter.height(17)),
                          child: Image.asset(
                            Assets.images.homeOverNone.path,
                            width: ScreenAdapter.width(80),
                            height: ScreenAdapter.width(80),
                          ),
                        ),
                      )
                    : SleepPieChart(
                      minHeight: 86,
                      innerHeight: 61.28,
                      iconHeight: 13.07,
                      arcWidth: 7,
                      chartSize: 86,
                      showBottomContent: false,
                      hoursMinutesStyle:normalF12H17C999.copyWith(color: AppColors.lightBlue,fontWeight: FontWeight.w500),
                      unitStyle: TextStyle(color: AppColors.Color666,fontWeight: FontWeight.w500,fontSize: ScreenAdapter.fontSize(10)),
                      textGap: 0,
                    )
              ],
            ),
          )),
    );
  }

  _OverOxygen() {
    return InkWell(
      onTap: () {
        // 跳转到血氧页面
        Get.toNamed(Routes.OXYGEN_HOME);
        CustomHomeBar().setCenterValue(0);
        CustomHomeBar().index = 0;
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.colorWhite,
          borderRadius: BorderRadius.circular(ScreenAdapter.width(12)),
        ),
        padding: EdgeInsets.only(
            top: ScreenAdapter.height(12),
            left: ScreenAdapter.width(12),
            right: ScreenAdapter.width(12)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                //title
                Text(
                  T.bloodOxygen.tr,
                  style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(12),
                      height: ScreenAdapter.fontSize(16.8 / 12),
                      color: AppColors.Color333),
                ),
                //icon
                Image.asset(
                  Assets.images.oxygenIcon.path,
                  width: ScreenAdapter.width(16),
                  height: ScreenAdapter.height(16),
                )
              ],
            ),
            Center(
                child: Container(
              margin: EdgeInsets.only(top: ScreenAdapter.height(24)),
              child: Obx(
                () => CustomLiquid(
                    amplitude: controller.oxygenData.value.isNull()
                        ? 0.0
                        : controller.oxygenData.value.percentage!.toDouble(),
                    parentWidth: 72,
                    childWidth: 64,
                    dataStyle: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: ScreenAdapter.fontSize(16),
                        height: 18.75 / 16,
                        color: AppColors.colorWhite),
                    parentBdWidth: 2),
              ),
            ))
          ],
        ),
      ),
    );
  }

  _OverPressure() {
    // final HomeController controller = Get.find();
    final List<PositionTopLeft> piePosList = [
      PositionTopLeft(
          top: ScreenAdapter.height(0), left: ScreenAdapter.width(0)),
      PositionTopLeft(
        top: -ScreenAdapter.height(20),
        left: ScreenAdapter.width(3.5),
      ),
      PositionTopLeft(
        top: ScreenAdapter.height(6),
        left: ScreenAdapter.width(6),
      ),
      PositionTopLeft(
        top: ScreenAdapter.height(16),
        left: ScreenAdapter.width(16),
      ),
      PositionTopLeft(
        top: ScreenAdapter.height(30),
        left: ScreenAdapter.width(40),
      ),
    ];

    final List<double> widthList = [
      ScreenAdapter.width(103),
      ScreenAdapter.width(95),
      ScreenAdapter.width(90),
      ScreenAdapter.width(70.5),
    ];

    return InkWell(
      onTap: () {
        // controller.selectPage(2);
        Get.toNamed(Routes.PRESSURE_HOME);
        CustomHomeBar().setCenterValue(1);
        CustomHomeBar().index = 1;
      },
      child: Container(
        width: ScreenAdapter.width(149),
        height: ScreenAdapter.width(149),
        decoration: BoxDecoration(
          color: AppColors.colorWhite,
          borderRadius: BorderRadius.circular(ScreenAdapter.width(12)),
        ),
        padding: EdgeInsets.only(
            top: ScreenAdapter.height(12),
            left: ScreenAdapter.width(12),
            right: ScreenAdapter.width(12)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                //title
                Text(
                  T.bloodPressure.tr,
                  style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(12),
                      height: ScreenAdapter.fontSize(16.8 / 12),
                      color: AppColors.Color333),
                ),
                //icon
                Image.asset(
                  Assets.images.pressureIcon.path,
                  width: ScreenAdapter.width(16),
                  height: ScreenAdapter.height(16),
                )
              ],
            ),
            Center(
              child: Container(
                width: ScreenAdapter.width(103),
                height: ScreenAdapter.width(52),
                margin: EdgeInsets.only(top: ScreenAdapter.height(29)),
                child:
                    // CustomPressureChart(
                    //   // data: PressureData(
                    //   //     date: DateTime.now(),
                    //   //     sysPressure: 68,
                    //   //     diaPressure: 180,
                    //   //     pulse: 65,
                    //   //     dataSource: 0),
                    //   posList: piePosList,
                    //   widthList: widthList,
                    //   // textWidth: ScreenAdapter.width(26),
                    //   textHeight: ScreenAdapter.height(16),
                    //   textTextStyle: TextStyle(
                    //       fontWeight: FontWeight.w500,
                    //       fontSize: ScreenAdapter.fontSize(10),
                    //       height: ScreenAdapter.fontSize(14.0 / 10)),
                    //   titleShow: false,
                    // ),
                    SizedBox(),
              ),
            )
          ],
        ),
      ),
    );
  }

  _OverTemperature() {
    return InkWell(
      onTap: () {
        // controller.selectPage(3);
        Get.toNamed(Routes.TEMPERATURE_HOME);
        CustomHomeBar().setCenterValue(1);
        CustomHomeBar().index = 1;
      },
      child: Container(
        width: ScreenAdapter.width(149),
        height: ScreenAdapter.width(149),
        decoration: BoxDecoration(
          color: AppColors.colorWhite,
          borderRadius: BorderRadius.circular(ScreenAdapter.width(12)),
        ),
        padding: EdgeInsets.only(
            top: ScreenAdapter.height(9),
            left: ScreenAdapter.width(12),
            right: ScreenAdapter.width(12)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                //title
                Text(
                  T.temperature.tr,
                  style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(12),
                      height: ScreenAdapter.fontSize(16.8 / 12),
                      color: AppColors.Color333),
                ),
                //icon
                Image.asset(
                  Assets.images.temperatureIcon.path,
                  width: ScreenAdapter.width(16),
                  height: ScreenAdapter.height(16),
                )
              ],
            ),
            Container(
              margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(2)),
              child:
                  Obx(
                    () => CustomTemperatureChart(
                        pointWidth: ScreenAdapter.width(2.7),
                        pointHeight: ScreenAdapter.height(37.35),
                        // data: TemperatureData(
                        //     data: 14, date: DateTime.now(), dataSource: 0),
                        data: controller.temperatureData.value ?? TemperatureData(),
                        big: false,
                        posList: PositionTopLeft(
                            top: ScreenAdapter.height(20.15),
                            left: ScreenAdapter.width(58.15)),
                        width: ScreenAdapter.width(121),
                        height: ScreenAdapter.height(98)),
                  ),
            )
          ],
        ),
      ),
    );
  }

  _OverHeartRate() {
    // logger.d("heartRateData: ${controller.heartRateData.value.isNull()}");
    return InkWell(
      onTap: () {
        // controller.selectPage(3);
        Get.toNamed(Routes.RATE_HOME);
        CustomHomeBar().setCenterValue(2);
        CustomHomeBar().index = 2;
      },
      child: Container(
        width: ScreenAdapter.width(149),
        height: ScreenAdapter.width(149),
        decoration: BoxDecoration(
          color: AppColors.colorWhite,
          borderRadius: BorderRadius.circular(ScreenAdapter.width(12)),
        ),
        padding: EdgeInsets.only(
            top: ScreenAdapter.height(9),
            left: ScreenAdapter.width(12),
            right: ScreenAdapter.width(12)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                //title
                Text(
                  T.heartRate.tr,
                  style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(12),
                      height: ScreenAdapter.fontSize(16.8 / 12),
                      color: AppColors.Color333),
                ),
                //icon
                Image.asset(
                  Assets.images.heartRateIcon.path,
                  width: ScreenAdapter.width(16),
                  height: ScreenAdapter.height(16),
                )
              ],
            ),
            Obx(() {
              bool show = !controller.heartRateData.value.isNull();
              int level = show ? controller.heartRateData.value.getTotalLevel() : 1; // 默认normal
              String severityText = show ? getSeverityString(level) : "--";
              Color severityColor = show ? getSeverityColor(level) : AppColors.Color999;

              return Container(
                  margin: EdgeInsets.only(
                      top: ScreenAdapter.height(8),
                      left: ScreenAdapter.width(11),
                      right: ScreenAdapter.width(11)),
                  padding: EdgeInsets.only(top: ScreenAdapter.height(25)),
                  // color: Colors.red,
                  width: ScreenAdapter.height(103),
                  height: ScreenAdapter.height(103),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: AssetImage(show
                              ? Assets.images.ratePie.path
                              : Assets.images.ratePieNone.path))),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.baseline, // 基线对齐
                        textBaseline: TextBaseline.alphabetic, // 设置基线类型为字母基线
                        children: [
                          Text(
                            // show ? rateData!.data.toString() : "--",
                            show
                                ? controller.heartRateData.value.data.toString()
                                : "--",
                            // "zheshina",
                            style: TextStyle(
                              color: severityColor,
                              // color: AppColors.Color999,
                              fontSize: ScreenAdapter.fontSize(24),
                              height: 28.13 / 24,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: ScreenAdapter.width(4)),
                          Text(
                            T.wordsBPM.tr,
                            style: TextStyle(
                              fontSize: ScreenAdapter.fontSize(8),
                              height: 9.38 / 8,
                              fontWeight: FontWeight.w500,
                              color: AppColors.Color666,
                            ),
                          ),
                          SizedBox(width: ScreenAdapter.width(25)),
                        ],
                      ),
                      // Container(
                      //   height: ScreenAdapter.height(10),
                      //   width: ScreenAdapter.width(28),
                      //   alignment: Alignment.center,
                      //   decoration: BoxDecoration(
                      //       color: show
                      //           ? AppColors.lightBlue.withOpacity(0.08)
                      //           : AppColors.Color999.withOpacity(0.08),
                      //       border: Border.all(
                      //           color: severityColor,
                      //           width: 1),
                      //       borderRadius: BorderRadius.circular(10)),
                      //   // padding: EdgeInsets.symmetric(
                      //   //     horizontal:
                      //   //         // show
                      //   //         // ? ScreenAdapter.width(8)
                      //   //         ScreenAdapter.width(16),
                      //   // vertical: ScreenAdapter.height(2)),
                      //   child: Text(
                      //     severityText,
                      //     style: normalF12H17C333.copyWith(
                      //         // height: ScreenAdapter.height(8),
                      //         fontSize: ScreenAdapter.fontSize(6),
                      //         height: 8.4 / 6,
                      //         color: severityColor),
                      //   ),
                      // )
                  
                    ],
                  ),
                );
              })
          ],
        ),
      ),
    );
  }

  _OverFitness() {
    return InkWell(
      onTap: () {
        Get.toNamed(Routes.FITNESS_HOME);
        CustomHomeBar().setCenterValue(3);
        CustomHomeBar().index = 3;
      },
      child: Container(
        width: ScreenAdapter.width(149),
        height: ScreenAdapter.width(149),
        decoration: BoxDecoration(
          color: AppColors.colorWhite,
          borderRadius: BorderRadius.circular(ScreenAdapter.width(12)),
        ),
        padding: EdgeInsets.only(
            top: ScreenAdapter.height(9),
            left: ScreenAdapter.width(12),
            right: ScreenAdapter.width(12)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  T.fitness.tr,
                  style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(12),
                      height: ScreenAdapter.fontSize(16.8 / 12),
                      color: AppColors.Color333),
                ),
                Image.asset(
                  Assets.images.stepDistance.path,
                  width: ScreenAdapter.width(16),
                  height: ScreenAdapter.height(16),
                )
              ],
            ),
            Gap(ScreenAdapter.height(12)),
            Obx(() => Container(
                  height: ScreenAdapter.height(92),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: AssetImage(
                              controller.fitnessData.value.step == 0
                                  ? Assets.images.stepPieNo.path
                                  : Assets.images.stepPieBlue.path))),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          controller.fitnessData.value.step == 0
                              ? "--"
                              : controller.fitnessData.value.step.toString(),
                          style: normalF14H19C666.copyWith(
                            color: controller.fitnessData.value.step == 0
                                ? AppColors.Color999
                                : AppColors.lightBlue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(T.steps, style: normalF12H17C666)
                      ],
                    ),
                  ),
                ))
          ],
        ),
      ),
    );
  }

  String getSeverityString(int value) {
    switch (value) {
      case 0:
        return T.wordsLow.tr;
      case 1:
        return T.wordsNormal.tr;
      case 2:
        return T.wordsMild.tr;
      case 3:
        return T.wordsSerious.tr;
      default:
        return T.wordsSerious.tr;
    }
  }

  // 根据等级返回颜色
  Color getSeverityColor(int value) {
    return AppColors.recordList[value];
  }
}
