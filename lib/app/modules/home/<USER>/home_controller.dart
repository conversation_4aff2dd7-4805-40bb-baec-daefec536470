/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-31 16:47:49
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/home/<USER>/home_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import 'dart:ui';

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/model/aizo_auto_data.dart';
import 'package:aiCare/app/data/model/aizo_auto_finial_data.dart';
import 'package:aiCare/app/data/model/aizo_sleep_data.dart';
import 'package:aiCare/app/data/repository/bluetooth_repository_impl.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/fitness/model/fitness_daily_activity.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/network/exceptions/api_exception.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:string_validator/string_validator.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:synchronized/synchronized.dart';

class HomeController extends BaseController with WidgetsBindingObserver {
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  BluetoothRepositoryImpl bluetoothRepositoryImpl = BluetoothRepositoryImpl();

  // 新增请求控制参数
  RxBool requestParam = false.obs;

  // 新增数据存储变量（根据实际数据类型定义）
  Rx<HeartRateData> heartRateData = HeartRateData().obs;
  Rx<TemperatureData?> temperatureData = Rx<TemperatureData?>(null);
  Rx<OxygenData> oxygenData = OxygenData().obs;
  Rx<FitnessDailyActivity> fitnessData = FitnessDailyActivity.withDefault().obs;
  Rx<AizoSleepData> aizoSleepData = AizoSleepData.withDefault().obs;

  RxBool heartRateShow = false.obs;
  int i = 0;

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    fetchAllData();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      try {
        final BluetoothController bluetoothController =
            Get.find<BluetoothController>();
        bluetoothController.checkAndUpdateData();
      } catch (e) {
        logger.e("获取 BluetoothController 失败: $e");
      }
    }
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  // 修改数据获取方法
  Future<void> fetchAllData() async {
    try {
      logger.d("开始获取所有数据");

      final results = await Future.wait([
        defaultRepositoryImpl.getHeartRateLast(),
        defaultRepositoryImpl.getTemperatureLast(),
        defaultRepositoryImpl.getOxygenLast(),
        defaultRepositoryImpl.getBodyFitnessLast(),
        defaultRepositoryImpl.getAizoSleepDataLast(),
      ]);

      // 更新数据
      if (results[0] != null) heartRateData.value = results[0] as HeartRateData;
      if (results[1] != null)
        temperatureData.value = results[1] as TemperatureData;
      if (results[2] != null) oxygenData.value = results[2] as OxygenData;
      if (results[3] != null)
        fitnessData.value = results[3] as FitnessDailyActivity;
      if (results[4] != null) aizoSleepData.value = results[4] as AizoSleepData;

      // logger.d("完成了")

      // logger.d("数据获取完成123213");
      // logger.d("heartRateData: ${heartRateData.value.toString()}");
      // logger.d("temperatureData: ${temperatureData.value.toString()}");
      // logger.d("oxygenData: ${oxygenData.value.toString()}");
      // logger.d("fitnessData: ${fitnessData.value.toString()}");
      // fitnessData.
      // logger.d("aizoSleepData: ${aizoSleepData.value.toString()}");
    } on ApiException catch (e) {
      logger.e("API Error: ${e.message}");
      showErrorMessage(e.message);
    } on DioException catch (e) {
      logger.e("Network Error: ${e.message}");
      showErrorMessage(T.errorNetwork.tr);
    } catch (e) {
      logger.e("获取数据失败: $e");
      showErrorMessage(T.errorNetwork.tr);
    } finally {}
  }

  // void getAizoSleepDataLast() async {
  //   logger.d("homeController获取睡眠数据");
  //   var result = await storage.getS(AppValues.aizoSleepLast);
  //   logger.d("homeController获取睡眠数据result：$result");
  //   if (result != null) {
  //     aizoSleepData.value = AizoSleepData.fromJson(json.decode(result));
  //   } else {
  //     result = await repository.getAizoSleepDataLast();
  //     logger.d("homeController获取睡眠数据result：$result");
  //     if(result.date.year != 2000){
  //       await storage.setData(
  //         AppValues.aizoSleepLast, json.encode(result.toJson()));
  //       aizoSleepData.value = result;
  //     }
  //   }
  // }

  // 新增手动触发方法（可选）
  // void refreshData() {
  //   requestParam.value = false;
  //   requestParam.value = true;
  // }

  // ...其他保持原有代码

  // 修改自动上传方法
  // Future<void> aizoAutoUpload() async {
  //   try {
  //     logger.d("开始自动上传数据");
  //     //获取历史日期
  //     DateTime lastUpdateTime = DateTime.now();
  //     String? lastUpdateTimeString = await storage.getString(AppValues.aizoHealthLastUpdateTime);
  //     if (lastUpdateTimeString != null) {
  //       lastUpdateTime = DateTime.parse(lastUpdateTimeString);
  //     } 
  //     logger.d("lastUpdateTime：$lastUpdateTime");
  //     //从lastUpdateTime 的 日期 循环到 当前日期
  //     for (DateTime i = lastUpdateTime; i.isBefore(DateTime.now()); i = i.add(const Duration(days: 1))) {
  //       logger.d("当前日期：$i");
  //       List<AizoAutoFinialData> result = await bluetoothRepositoryImpl.aizoGetHealthData(i);
  //     var list = <AizoAutoData>[];

  //     for (var data in result) {
  //       String formattedDate = DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
  //           .format(data.timeStr.toDate()!.toUtc());

  //       list.add(AizoAutoData(
  //         step: data.steps,
  //         saturation: data.spo2,
  //         dailyHeartRate: data.dailyHeartRate,
  //         temperature: data.bodyTemperature,
  //         dateTime: formattedDate,
  //         distance: data.distance,
  //         calorie: data.calories,
  //       ));
  //     }
  //     logger.d("查看list");
  //     logger.d(list);

  //     await defaultRepositoryImpl.aizoAutoUpload(list);
  //     logger.d("自动上传数据完成");
  //     }
  //   } on ApiException catch (e) {
  //     logger.e("API Error: ${e.message}");
  //     showErrorMessage(e.message);
  //   } on DioException catch (e) {
  //     logger.e("Network Error: ${e.message}");
  //     showErrorMessage("网络连接失败，请检查网络设置");
  //   } catch (e) {
  //     logger.e("自动上传数据失败: $e");
  //     showErrorMessage("自动上传数据失败，请稍后重试");
  //   } finally {}
  // }

Future<void> aizoAutoUpload() async {
  try {
    logger.d("开始自动上传数据");
        // 获取上次更新时间
    String? lastUpdateTimeString =
        await storage.getString(AppValues.aizoHealthLastUpdateTime);

    // 确定循环起始日期（转换为纯日期格式）
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day); // 今天0点
    DateTime start = today; // 默认从今天开始

    if (lastUpdateTimeString != null) {
      final cleanedString = lastUpdateTimeString.split(".")[0];
      DateTime lastUpdate = DateTime.parse(cleanedString);
      start = DateTime(lastUpdate.year, lastUpdate.month, lastUpdate.day); // 取日期部分
    }

    // 确保结束日期包含今天
    DateTime endDate = today.add(const Duration(days: 1)); // 包含今天的结束日期
    
    logger.d("健康数据上传 - 起始日期: $start, 结束日期: $endDate");
    
    // 使用新的循环逻辑
    for (DateTime date = start; 
         date.isBefore(endDate); 
         date = date.add(const Duration(days: 1))) {
      
      logger.d("正在处理健康数据日期: $date");
      
      try {
        List<AizoAutoFinialData> result = await bluetoothRepositoryImpl.aizoGetHealthData(date);
        var list = <AizoAutoData>[];

        for (var data in result) {
          String formattedDate = DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
              .format(data.timeStr.toDate()!.toUtc());

          list.add(AizoAutoData(
            step: data.steps,
            saturation: data.spo2,
            dailyHeartRate: data.dailyHeartRate,
            temperature: data.bodyTemperature,
            dateTime: formattedDate,
            distance: data.distance,
            calorie: data.calories,
          ));
        }
        
        if (list.isNotEmpty) {
          logger.d("准备上传 ${list.length} 条健康数据");
          await defaultRepositoryImpl.aizoAutoUpload(list);
        }

        // 更新存储的最后更新时间（每次成功处理都更新）
        DateTime nextDay = date.add(const Duration(days: 1));
         storage.setString(AppValues.aizoHealthLastUpdateTime, date.toString());
        
      } catch (e) {
        logger.e("处理 $date 健康数据时出错: $e");
        // 部分失败不影响后续日期处理
      }
    }
    
    logger.d("自动上传数据完成");
    //设置全部为 true
    storage.setBool(AppValues.bloodOxygenLineDaysRefresh, true);
    storage.setBool(AppValues.bloodOxygenLineWeeksRefresh, true);
    storage.setBool(AppValues.bloodOxygenLineMonthsRefresh, true);
    storage.setBool(AppValues.heartRateLineDaysRefresh, true);
    storage.setBool(AppValues.heartRateLineWeeksRefresh, true);
    storage.setBool(AppValues.heartRateLineMonthsRefresh, true);
    
  } on ApiException catch (e) {
    logger.e("API Error: ${e.message}");
    showErrorMessage(e.message);
  } on DioException catch (e) {
    logger.e("Network Error: ${e.message}");
    showErrorMessage("网络连接失败，请检查网络设置");
  } catch (e) {
    logger.e("自动上传数据失败: $e");
    showErrorMessage("自动上传数据失败，请稍后重试");
  }
}
}
