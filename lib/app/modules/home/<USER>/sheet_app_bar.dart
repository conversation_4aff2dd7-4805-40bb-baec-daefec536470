// // import 'package:aiCare/app/core/render/dropdown/my_dropdown_menu.dart';
// import 'package:aiCare/app/core/render/dropdown/custom_dropdown_menu.dart';
// import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// // import 'package:flutter/material.dart' hide DropdownMenuEntry;

// import 'package:get/get.dart';

// import '/app/core/values/app_colors.dart';

// // 根据我们的应用程序设计定制的默认应用程序栏
// class SheetAppBar extends StatefulWidget implements PreferredSizeWidget {
//   final List<Widget>? actions;
//   final bool isBackButtonEnabled;
//   final double? customHeight; // 用于接收外界传入的高度
//   final PreferredSizeWidget? bottomWidget; // 新增参数
//   final Color bgColor;

//   SheetAppBar(
//       {Key? key,
//       this.actions,
//       this.isBackButtonEnabled = true,
//       this.customHeight,
//       required this.bgColor,
//       this.bottomWidget // 新增参数
//       })
//       : super(key: key);

//   @override
//   _SheetAppBarState createState() => _SheetAppBarState();

//   @override
//   Size get preferredSize {
//     if (customHeight != null) {
//       return Size.fromHeight(customHeight!); // 如果传入了高度参数，则使用传入的高度
//     } else {
//       return AppBar().preferredSize; // 否则使用原始的 AppBar 的 preferredSize
//     }
//   }
// }

// List<CustomDropdownMenuEntry<String>> buildMenuList(List<String> data) {
//   return data.map((String value) {
//     return CustomDropdownMenuEntry<String>(value: value, label: value);
//   }).toList();
// }

// class _SheetAppBarState extends State<SheetAppBar> {
//   final HomeController controller = Get.find();

//   List<DropdownMenuItem<String>> _buildMenuList(List<String> data) {
//     return data.map((String value) {
//       return DropdownMenuItem<String>(
//           value: value,
//           child: Container(
//               child: Text(
//             value,
//             style: TextStyle(
//               fontSize: ScreenAdapter.fontSize(20), // 设置文字大小
//               color: Colors.black, // 设置文字颜色
//             ),
//           )));
//     }).toList();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Obx(() {
//       if (controller.selectedPageIndex.value == 0) {
//         return SizedBox(
//           height: ScreenAdapter.height(44),
//         ); // 返回一个空的 Widget，当 selectedPageIndex 为 0 时
//       } else {
//         return AppBar(
//           centerTitle: true,
//           elevation: 0,
//           backgroundColor: widget.bgColor,
//           automaticallyImplyLeading: widget.isBackButtonEnabled,
//           actions: widget.actions,
//           iconTheme: const IconThemeData(color: AppColors.appBarIconColor),
//           bottom: widget.bottomWidget,
//           title: Stack(
//             children: [
//               Center(
//                 child: CustomDropdownMenu<String>(
//                   width: ScreenAdapter.width(240),
//                   textStyle: TextStyle(
//                     color: AppColors.Color444,
//                     fontSize: ScreenAdapter.fontSize(20),
//                     fontWeight: FontWeight.w500,
//                     height: ScreenAdapter.fontSize(28 / 20),
//                   ),
//                   menuHeight: ScreenAdapter.height(200), // 菜单列表高度
//                   initialSelection: controller.sheetCenterList
//                       .value[controller.selectedPageIndex.value], // 初始选择
//                   onSelected: controller.onSelect, // 选择回调
//                   dropdownMenuEntries:
//                       buildMenuList(controller.sheetCenterList), // 菜单条目列表
//                   inputDecorationTheme: InputDecorationTheme(
//                     enabledBorder: OutlineInputBorder(
//                       borderSide: BorderSide.none,
//                     ),
//                   ),
//                   menuStyle: MenuStyle(
//                     backgroundColor:
//                         WidgetStateProperty.all(Colors.blue), // 背景颜色
//                     elevation: WidgetStateProperty.all(2), // 阴影高度
//                     padding: WidgetStateProperty.all(EdgeInsets.all(8)), // 内边距
//                     shape: WidgetStateProperty.all(RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(8), // 圆角
//                     )),
//                     side: WidgetStateProperty.all(
//                         BorderSide(color: Colors.grey, width: 1)), // 边框样式
//                   ),
//                   // trailingIcon: Image.asset(
//                   //   Assets.images.vectorPng.path,
//                   //   width: ScreenAdapter.width(12),
//                   //   height: ScreenAdapter.height(24),
//                   //   color: AppColors.Color666,
//                   // ),
//                 ),
//               ),
              
//               Positioned(
//                 top: ScreenAdapter.height(6),
//                 left: ScreenAdapter.width(324),
//                 child: InkWell(
//                   onTap: () {
//                     controller.goRelatedInfor();
//                   },
//                   child: Image.asset(
//                     Assets.images.relatedInfor.path,
//                     width: ScreenAdapter.width(16),
//                     height: ScreenAdapter.height(16),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         );
//       }
//     });
//   }
// }
