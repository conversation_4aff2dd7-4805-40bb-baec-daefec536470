import 'dart:async';
import 'dart:io';
import 'package:aiCare/app/core/enum/region_unit.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/data/repository/default_repository.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/network/dio_provider.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'package:get/get.dart';
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/repository/auth_repository_impl.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/network/exceptions/api_exception.dart';

class LoginController extends BaseController {
  final storage = SecureStorageService.instance;
  //Authentication repository; Used to handle network interfaces related to login
  AuthRepositoryImpl authRepositoryImpl = AuthRepositoryImpl();

  //Account and password text controller
  TextEditingController accountController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  //Error prompt text
  RxString errorText = "".obs;

  //Determine whether the user agreement has been read,
  //and whether to log in using a phone number or email address
  RxBool userReadPolicy = false.obs;
  RxBool phoneOrEmail = false.obs;

  //Resend verification code
  RxString codeText = "".obs;
  Timer? timer;
  int start = 60; // 初始秒数
  RxBool isResend = false.obs;

  RxBool region = true.obs;
  RxBool passwordVisible = false.obs;

  @override
  void onInit() {
    super.onInit();
    // logger.d("loginController初始化");
    // logger.d(await storage.getString(AppValues.idToken));
  }

  passwordVisibleChange() {
    passwordVisible.value = !passwordVisible.value;
  }
  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();

    // logger.d("loginController的idToken");
    // logger.d(await storage.getString(AppValues.idToken));

    if ((await storage.getString(AppValues.idToken)) != null) {
      // Get.toNamed(Routes.TABS);
    } else {
      controllerInitListener();
      codeText.value = T.phoneSendCode.tr;
    }
    updateRegion("国外");
  }

  @override
  void onClose() {
    if (timer != null) {
      timer!.cancel();
    }
    textControllerCloseListener();
    super.onClose();
  }

  /**
   * @description: Text controller initialization listening
   * @return {*}
   */
  void controllerInitListener() {
    accountController.addListener(_onAccountChanged);
  }

  /**
   * @description: Removal, Listening, and Recycling of Text Controllers
   * @return null
   */
  void textControllerCloseListener() {
    accountController.removeListener(_onAccountChanged);
    accountController.dispose();
    passwordController.dispose();
  }

  /**
   * @description: Check if the value in the email input box matches the email type.
   * If it does not match, an error message will pop up
   * @return null
   */
  _onAccountChanged() {
    if (phoneOrEmail.value) {
      if (!GetUtils.isPhoneNumber(accountController.value.text)) {
        errorText.value = T.phoneError;
      } else {
        errorText.value = "";
      }
    } else {
      if (!accountController.value.text.isNotEmpty) {
        errorText.value = "";
        return;
      }
      if (!GetUtils.isEmail(accountController.value.text)) {
        errorText.value = T.emailError;
      } else {
        errorText.value = "";
      }
    }
  }

  /**
   * @description: Set the value of whether to read the user agreement
   * @return null
   */
  setUserReadPolic(newValue) async {
    userReadPolicy.value = newValue;
  }

  /**
   * @description: Domestic users switch login methods
   * @return null
   */
  setAccountSwitch() {
    errorText.value = "";
    phoneOrEmail.value = !phoneOrEmail.value;
  }

  /**
   * @description: Used for Normal Login.
   * Includes domestic and international user email and password login, 
   * mobile phone number and verification code login.
   * @return null
   */
  doLogin() async {
    var accountlNull = GetUtils.isNullOrBlank(accountController.value.text);
    var passwordNull = GetUtils.isNullOrBlank(passwordController.value.text);

    if (userReadPolicy.value == false) {
      //提示需要同意用户协议
      errorText.value = T.commonCheckPolicy.tr;
    } else {
      if (accountlNull!) {
        errorText.value = phoneOrEmail.value ? T.phoneError : T.emailError;
      } else if (passwordNull!) {
        errorText.value = T.passwordError;
      } else if (!GetUtils.isEmail(accountController.value.text) &&
          phoneOrEmail.value == false) {
        errorText.value = T.emailError;
      }
      if (!GetUtils.isPhoneNumber(accountController.value.text) &&
          phoneOrEmail.value == true) {
        errorText.value = T.phoneError;
      } else {
        // showLoading();
        if (phoneOrEmail.value) {
          await callDataService<bool>(
              authRepositoryImpl.loginWithPhoneAndCode(
                  accountController.value.text, passwordController.value.text),
              onSuccess: (result) {
            if (result) {
              showSuccessMessage(T.loginSuccess.tr);
              Get.offAllNamed(Routes.TABS);
            } else {
              errorText.value = T.phoneCodeError;
              showErrorMessage(T.loginFailed.tr);
            }
          }, onError: (error) {
            logger.e("手机验证码登录失败: $error");
            if (error is ApiException) {
              errorText.value = error.message;
            } else {
              errorText.value = T.phoneCodeError;
            }
            showErrorMessage(T.loginFailed.tr);
          });
        } else {
          await callDataService<String>(
              authRepositoryImpl.loginWithEmailAndPassword(
                  accountController.value.text, passwordController.value.text),
              onSuccess: (response) {
            if (response == "true") {
              showSuccessMessage(T.loginSuccess.tr);
              Get.offAllNamed(Routes.TABS);
            } else {
              String responseStr = response.toLowerCase();

              if (responseStr.contains('access_denied') &&
                  (responseStr.contains('verify your email') ||
                      responseStr.contains('email address first'))) {
                errorText.value = T.emailVerificationRequired.tr;
              } else if (responseStr.contains('invalid_grant') ||
                  responseStr.contains('wrong credentials')) {
                errorText.value = T.passwordError.tr;
              } else {
                // 其他未知错误
                errorText.value = T.loginFailed.tr;
              }

              logger.d("密码账号登录出错: $response");
              showErrorMessage(T.loginFailed.tr);
            }
          });
        }
        // await Future.delayed(Duration(seconds: 1));
        // hideLoading();
      }
    }
  }

  /**
   * @description: Used for Google Login.
   * Currently, only foreign users are supported to log in.
   * No response was made to login failure
   * @return null
   */
  googleLogin() async {
    if (userReadPolicy.value == false) {
      errorText.value = T.commonCheckPolicy;
      return;
    }
    showLoading();
    bool result = await authRepositoryImpl.signInWithGoogle();
    if (result) {
      Get.offNamed(Routes.TABS);
    } else {}
    hideLoading();
  }

  /**
   * @description: Used for Apple Login. 
   * Currently, only foreign users are supported to log in to their iOS account using Apple devices.
   * No response was made to login failure
   * @return null
   */
  appleLogin() async {
    if (userReadPolicy.value == false) {
      errorText.value = T.commonCheckPolicy;
      return;
    }
    showLoading();
    if (Platform.isIOS) {
      bool result = await authRepositoryImpl.signInWithApple();
      if (result) {
        Get.offNamed(Routes.TABS);
      } else {}
    } else {
      errorText.value = T.errorMessagesPlatformIosError;
    }
    hideLoading();
  }

  /**
   * @description: Used for WeChat login（incomplete)
   * @return null
   */
  wechatLogin() async {}

  /**
   * @description: Switch registration region, switch between Chinese and English, clear parameter values
   * @return null
   */
  void updateRegion(String newValue) async {
    if (newValue == T.regionChina.tr) {
      storage.setRegion(RegionUnit.zh);
      region.value = true;
    } else {
      storage.setRegion(RegionUnit.en);
      region.value = false;
    }

    errorText.value = "";

    accountController.text = "";
    passwordController.text = "";
    phoneOrEmail.value = false;
    update();
    // logger.d(region.value);
  }

  /**
   * @description: Used for sending verification codes
   * No response was made to login failure
   * @return null
   */
  void sendCode() async {
    if (!GetUtils.isPhoneNumber(accountController.value.text)) {
      errorText.value = T.phoneError.tr;
      return;
    }
    if (isResend.value) {
      return;
    } else {
      isResend.value = true;
    }

    try {
      await callDataService(authRepositoryImpl.sendSms(accountController.text),
          onError: (error) {
        logger.e("发送验证码失败: $error");
        isResend.value = false;
        if (error is ApiException) {
          errorText.value = error.message;
        } else {
          errorText.value = "发送验证码失败，请重试";
        }
      });

      // 停止之前的计时器
      if (timer != null) {
        timer!.cancel();
      }

      // 设置初始值
      start = 60;

      // 更新codeText的初始值
      codeText.value = '${T.phoneResend.tr} ($start S)';

      // 启动新的计时器
      timer = Timer.periodic(Duration(seconds: 1), (timer) {
        if (start == 0) {
          timer.cancel();
          isResend.value = false;
          codeText.value = T.phoneResend.tr;
        } else {
          start--;
          codeText.value = '${T.phoneResend.tr} (${start}S)';
        }
        update();
      });
    } catch (e) {
      logger.e("发送验证码异常: $e");
      isResend.value = false;
      errorText.value = "发送验证码失败，请重试";
    }
  }
}
