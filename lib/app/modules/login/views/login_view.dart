/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-15 11:50:42
 * @FilePath: /rpmappmaster/lib/app/modules/login/views/login_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import 'dart:io';

import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/widget/user_agreement.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../controllers/login_controller.dart';

class LoginView extends BaseView<LoginController> {
  LoginView({
    super.key,
  });

  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        height: ScreenAdapter.height(812) - MediaQuery.of(Get.context!).padding.top - MediaQuery.of(Get.context!).padding.bottom,
        padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(24)),
        child: Stack(
          children: [
            Column(
              children: [
                _LoginInput(),
                _LoginButton(),
                _SingUpButton(),
                _OtherIcon(),
                Expanded(child: Container()),
                UserAgreement(
                  controller: controller,
                ),
              ],
            ),
            _SwitchRegion(),
          ],
        ),
      ),
    );
  }

  //切换地区
  _SwitchRegion() {
    return Positioned(
        top: ScreenAdapter.height(6),
        right: ScreenAdapter.width(12),
        child: Obx(()=>DropdownButton(
          padding: EdgeInsets.zero,
          underline: SizedBox(),
          hint: Text(
            controller.region.value?T.regionChina.tr : T.regionAmerica.tr,
            style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: ScreenAdapter.fontSize(14),
                color: AppColors.loginRegionColor,
                height: ScreenAdapter.fontSize(24 / 14)),
          ),
          onChanged: (String? value) {
            // logger.d("切换了${value}");
            controller.updateRegion(value!);
          },
          items: [T.regionChina.tr, T.regionAmerica.tr]
              .map((String item) => DropdownMenuItem<String>(
                    value: item,
                    child: Text(
                      item,
                      style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: ScreenAdapter.fontSize(14),
                          color: AppColors.loginRegionColor,
                          height: ScreenAdapter.fontSize(24 / 14)),
                    ),
                  ))
              .toList(),
        )));
  }
  
  //登录输入框
  _LoginInput() {
    return Obx(() => //输入框
        Container(
          margin: EdgeInsets.only(top: ScreenAdapter.height(64)),
          width: ScreenAdapter.width(327),
          height: ScreenAdapter.height(220),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              //标题栏
              SizedBox(
                width: double.infinity,
                height: ScreenAdapter.height(32),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      // width: ScreenAdapter.width(52),
                      height: ScreenAdapter.height(32),
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Text(
                          controller.phoneOrEmail.value ? T.phone.tr : T.email.tr,
                          textAlign: TextAlign.left,
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenAdapter.fontSize(20),
                              height: ScreenAdapter.height(32 / 20)),
                        ),
                      ),
                    ),
                    Obx(() => controller.region.value
                        ? InkWell(
                            onTap: () {
                              controller.setAccountSwitch();
                            },
                            child: SizedBox(
                              width: ScreenAdapter.width(150),
                              height: ScreenAdapter.height(32),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  SizedBox(
                                      // width: ScreenAdapter.width(83),
                                      height: ScreenAdapter.height(17),
                                      child: Align(
                                        alignment: Alignment.bottomCenter,
                                        child: Text(
                                          controller.phoneOrEmail.value
                                              ? T.loginEmail.tr
                                              : T.loginPhone.tr,
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            color: AppColors.loginSubTitleColor,
                                            height:
                                                ScreenAdapter.height(17 / 14),
                                            // fontSize: ScreenAdapter.fontSize(14)
                                          ),
                                        ),
                                      )),
                                  Align(
                                    alignment: Alignment.center,
                                    child: SvgPicture.asset(
                                      'assets/images/array_right_green.svg',
                                      semanticsLabel: 'My Icon',
                                      width: ScreenAdapter.width(16),
                                      height: ScreenAdapter.height(16),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          )
                        : SizedBox())
                  ],
                ),
              ),
              //输入框
              Container(
                margin: EdgeInsets.fromLTRB(
                    0, ScreenAdapter.height(8), 0, ScreenAdapter.height(20)),
                // padding: EdgeInsets.fromLTRB(ScreenAdapter.width(5), ScreenAdapter.height(20), ScreenAdapter.width(10), ScreenAdapter.height(10)),
                padding: const EdgeInsets.all(0),
                width: double.infinity,
                height: ScreenAdapter.height(48),
                decoration: BoxDecoration(
                  color: Colors.black12,
                  borderRadius: BorderRadius.circular(ScreenAdapter.width(12)),
                ),
                child: TextField(
                  // obscureText:true,
                  // focusNode: controller.accountFocusNode,
                  controller: controller.accountController,
                  cursorColor: Colors.black,
                  style: TextStyle(
                    fontSize: ScreenAdapter.fontSize(16),
                    height: ScreenAdapter.height(24 / 16),
                  ),
                  
                  decoration: InputDecoration(
                    
                    hintText: controller.phoneOrEmail.value
                        ? T.phoneHintText.tr
                        : T.emailHintText.tr,
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      vertical: (ScreenAdapter.height(48) -
                              ScreenAdapter.fontSize(16) * 1.5) /
                          2, // 垂直居中
                      horizontal: ScreenAdapter.width(10),
                    ),
                  ),
                ),
              ),

              //标题栏
              SizedBox(
                width: double.infinity,
                height: ScreenAdapter.height(32),
                child: Align(
                  alignment: Alignment.bottomLeft,
                  child: Text(
                    controller.phoneOrEmail.value ? T.phoneCode.tr : T.password.tr,
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: ScreenAdapter.fontSize(20)),
                  ),
                ),
              ),
              //输入框+验证码发送按钮
              Stack(
                children: [
                  Container(
                    margin: EdgeInsets.fromLTRB(
                        0, ScreenAdapter.height(8), 0, ScreenAdapter.height(8)),
                    // padding: EdgeInsets.fromLTRB(ScreenAdapter.width(10), ScreenAdapter.height(20), ScreenAdapter.width(10), ScreenAdapter.height(20)),
                    width: double.infinity,
                    height: ScreenAdapter.height(48),
                    decoration: BoxDecoration(
                      color: Colors.black12,
                      borderRadius:
                          BorderRadius.circular(ScreenAdapter.width(12)),
                    ),
                    child: Obx(()=>TextField(
                      key: const Key('signin-password-input'),
                      controller: controller.passwordController,
                      cursorColor: Colors.black,
                      obscureText: controller.phoneOrEmail.value
                          ? false
                          : controller.passwordVisible.value, // 设为密码输入框
                      decoration: InputDecoration(
                        hintText: controller.phoneOrEmail.value
                            ? T.phoneCodeHintText.tr
                            : T.passwordHintText.tr,
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          vertical: (ScreenAdapter.height(48) -
                                  ScreenAdapter.fontSize(16) * 1.5) /
                              2, // 垂直居中
                          horizontal: ScreenAdapter.width(10),
                        ),
                        suffixIcon: _getSuffixIcon(),
                        // labelText: 'Username',
                      ),
                    )),
                  ),
                  Obx(() => controller.phoneOrEmail.value
                      ? Positioned(
                          top: ScreenAdapter.height(10),
                          right: ScreenAdapter.width(18),
                          child: TextButton(
                            onPressed: () {
                              controller.sendCode();
                            },
                            style: ButtonStyle(
                              overlayColor:
                                  WidgetStateProperty.resolveWith<Color?>(
                                (Set<WidgetState> states) {
                                  if (states.contains(WidgetState.pressed)) {
                                    return AppColors.loginHintColor
                                        .withOpacity(0.1); // 点击时的颜色
                                  }
                                  return null; // 默认状态
                                },
                              ),
                              padding: WidgetStateProperty.all<EdgeInsets>(
                                  EdgeInsets.all(0)), // 内边距
                            ),
                            child: Obx(() => Text(
                                  controller.codeText.value,
                                  style: TextStyle(
                                      fontSize: ScreenAdapter.fontSize(16),
                                      height: ScreenAdapter.height(24 / 16),
                                      color: AppColors.loginSubTitleColor),
                                )),
                          ),
                        )
                      : SizedBox())
                ],
              ),
              //错误提示
              Obx(() => controller.errorText.isNotEmpty
                  ? SizedBox(
                      height: ScreenAdapter.height(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SvgPicture.asset(
                            'assets/images/error_info.svg',
                            semanticsLabel: 'My Icon',
                            width: ScreenAdapter.width(16),
                            height: ScreenAdapter.height(16),
                          ),
                          SizedBox(width: ScreenAdapter.width(4)),
                          Text(
                            controller.errorText.value,
                            style: TextStyle(
                                color: AppColors.errorTextColor,
                                fontSize: ScreenAdapter.fontSize(12),
                                height: 1.0),
                          )
                        ],
                      ),
                    )
                  : SizedBox(
                      height: ScreenAdapter.height(16),
                    ))
            ],
          ),
        ));
  }



  //登录按钮
  _LoginButton() {
    return //登录
        Container(
      margin: EdgeInsets.only(top: ScreenAdapter.height(110)),
      width: double.infinity,
      height: ScreenAdapter.height(47),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: const StadiumBorder(),
          backgroundColor: AppColors.loginButtonBgColor,
        ),
        onPressed: () async {
          controller.doLogin();
        },
        child: Text(
          T.login.tr,
          style: TextStyle(
            height: ScreenAdapter.height(24) / ScreenAdapter.height(20),
            color: AppColors.loginPrimaryColor,
            fontWeight: FontWeight.bold,
            fontSize: ScreenAdapter.fontSize(20),
          ),
        ),
      ),
    );
  }
    Widget _getSuffixIcon(){
    if(!controller.phoneOrEmail.value) return  IconButton(
        icon: Obx(() => controller.passwordVisible.value
            ? const Icon(Icons.visibility)
            : const Icon(Icons.visibility_off)),
        onPressed: () {
          controller.passwordVisibleChange();
        },
      );
    else return SizedBox();
  }
  
  //注册按钮
  _SingUpButton() {
        return //注册
        Container(
      margin: EdgeInsets.only(top: ScreenAdapter.height(24)),
      width: double.infinity,
      height: ScreenAdapter.height(47),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: StadiumBorder(
            side: BorderSide(
              color:
                  AppColors.loginSubTitleColor, // Specify the border color here
              width: ScreenAdapter.width(2), // Specify the border width here
            ),
          ),
          backgroundColor: Colors.white,
        ),
        onPressed: () {
          Get.toNamed(Routes.SIGNUP);
        },
        child: Text(
          T.signUp.tr,
          style: TextStyle(
            color: AppColors.loginButtonBgColor,
            fontWeight: FontWeight.bold,
            fontSize: ScreenAdapter.fontSize(20),
          ),
        ),
      ),
    );
  }
  
  //其他登录方式
  _OtherIcon() {
        return //其他登录方式
        Container(
      margin: EdgeInsets.only(top: ScreenAdapter.height(38)),
      width: ScreenAdapter.width(245),
      height: ScreenAdapter.height(90),
      child: Column(
        children: [
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  T.loginOther.tr,
                  style: TextStyle(
                    color: AppColors
                        .loginOtherTextColor, // Adjust text color as needed
                    height: ScreenAdapter.height(24) / ScreenAdapter.height(16),
                    fontWeight: FontWeight.bold, // Optional: for bold text
                    fontSize: ScreenAdapter.fontSize(16),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: ScreenAdapter.height(20),
          ),
          Obx(() => controller.region.value
              ? Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      // 如果需要在国内版显示其他内容，可在这里添加
                    ],
                  ),
                )
              : Expanded(
                  child: Row(
                    mainAxisAlignment: Platform.isIOS
                        ? MainAxisAlignment.spaceBetween
                        : MainAxisAlignment.center,
                    children: <Widget>[
                      if (Platform.isIOS) // 判断是否为苹果设备
                        SizedBox(
                          width: ScreenAdapter.width(39),
                          height: ScreenAdapter.height(39),
                          child: GestureDetector(
                            onTap: () {
                              // 按钮点击事件处理
                              print("Apple login tapped!");
                              controller.appleLogin();
                            },
                            child: SvgPicture.asset(
                              'assets/images/apple.svg',
                              semanticsLabel: 'Apple Icon',
                              width: ScreenAdapter.width(39),
                              height: ScreenAdapter.height(39),
                            ),
                          ),
                        ),
                      SizedBox(
                        width: ScreenAdapter.width(39),
                        height: ScreenAdapter.height(39),
                        child: GestureDetector(
                          onTap: () {
                            // 按钮点击事件处理
                            print("Google login tapped!");
                            controller.googleLogin();
                          },
                          child: Image.asset(
                            Assets.images.google.path,
                            width: ScreenAdapter.width(39), // 设置图片宽度
                            height: ScreenAdapter.height(39), // 设置图片高度
                          ),
                        ),
                      ),
                    ],
                  ),
                ))
        ],
      ),
    );
  }
}
