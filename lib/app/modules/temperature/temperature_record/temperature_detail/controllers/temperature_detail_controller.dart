/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-14 11:11:52
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-06 14:11:20
 * @FilePath: /rpmappmaster/lib/app/modules/temperature/temperature_record/temperature_detail/controllers/temperature_detail_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:convert';

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/modules/temperature/temperature_record/record_home/controllers/record_home_controller.dart';
import 'package:get/get.dart';

class TemperatureDetailController extends BaseController {
  final recordHomeController = Get.find<RecordHomeController>();
  TemperatureData? model;
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();

  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    logger.d("获得的model:$model");
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void toDelete() async {
    // showLoading();
    logger.d("删除的元素");
    logger.d(model!.date);
    await callDataService(defaultRepositoryImpl.deleteTemperature(model!.date!));
    // hideLoading();
    
    // Remove the item using the new method
    recordHomeController.removeItemByDate(model!.date!);
    
    Get.back();
  }
}
