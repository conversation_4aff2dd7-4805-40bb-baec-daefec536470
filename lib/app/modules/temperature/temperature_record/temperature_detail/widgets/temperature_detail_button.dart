import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/modules/temperature/temperature_record/temperature_detail/controllers/temperature_detail_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class TemperatureDetailButton extends StatelessWidget with BaseWidgetMixin {
  TemperatureDetailController controller = Get.find();
  TemperatureDetailButton({super.key});

  @override
  Widget body(BuildContext context) {
    return InkWell(
      onTap: () {
        controller.toDelete();
      },
      child: Container(
          width: ScreenAdapter.width(208),
          height: ScreenAdapter.height(38),
          decoration: BoxDecoration(
              color: AppColors.searchButton,
              border: Border.all(
                  color: AppColors.lightBlue, width: ScreenAdapter.width(1)),
              borderRadius: BorderRadius.circular(ScreenAdapter.width(24))),
          child: Center(
            child: Text(
              T.commonDeleteRecord.tr,
              style: TextStyle(
                  color: AppColors.lightBlue,
                  fontSize: ScreenAdapter.fontSize(16),
                  fontWeight: FontWeight.w500,
                  height: ScreenAdapter.fontSize(22.4 / 16)),
            ),
          )),
    );
  }
}
