/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-02 17:25:36
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-31 16:03:19
 * @FilePath: /rpmappmaster/lib/app/modules/temperature/temperature_record/temperature_detail/widgets/temperature_detail_title.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_decoration.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_shadow.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/temperature/temperature_record/temperature_detail/controllers/temperature_detail_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/widgets.dart' hide BoxDecoration, BoxShadow;
import 'package:get/get.dart';
import 'package:intl/intl.dart';


class TemperatureDetailTitle extends StatelessWidget with BaseWidgetMixin {
 final TabsController tabsController = Get.find();

 TemperatureDetailController controller = Get.find();
  TemperatureDetailTitle({super.key});


  
  @override
  Widget body(BuildContext context) {
        return Center(
      child: SizedBox(
        height: ScreenAdapter.height(46),
        // width: ScreenAdapter.width(242),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  T.measurementResult.tr+"：",
                  style: TextStyle(
                      color: AppColors.Color333,
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(16),
                      height: ScreenAdapter.fontSize(22.4 / 16)),
                ),
                Text(
                  getText(),
                  style: TextStyle(
                      color: AppColors.detailList[controller.model!.getTotalLevel()-1],
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(16),
                      height: ScreenAdapter.fontSize(22.4 / 16)),
                ),
                SizedBox(
                  width: ScreenAdapter.width(12),
                ),
                Container(
                  // width: ScreenAdapter.width(50),
                  height: ScreenAdapter.height(20),
                  padding: EdgeInsets.symmetric(
                      horizontal: ScreenAdapter.width(14),
                      vertical: ScreenAdapter.height(3)),
                  decoration: BoxDecoration(
                    color: AppColors.recordList[controller.model!.getTotalLevel()],
                    borderRadius:
                        BorderRadius.circular(ScreenAdapter.width(16)),
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromRGBO(255, 255, 255, 0.45),
                        offset: Offset(0, 2),
                        blurRadius: ScreenAdapter.width(4),
                        spreadRadius: 0.0,
                        inset: true,
                      ),
                      BoxShadow(
                        color: Color.fromRGBO(255, 255, 255, 0.45),
                        offset: Offset(0, -2),
                        blurRadius: ScreenAdapter.width(4),
                        spreadRadius: 0.0,
                        inset: true,
                      ),
                    ],
                  ),
                  child: Text(
                    getSeverityString(controller.model!.getTotalLevel()-1),
                    style: TextStyle(
                        color: AppColors.colorWhite,
                        fontSize: ScreenAdapter.fontSize(10),
                        height: ScreenAdapter.fontSize(14 / 10),
                        fontWeight: FontWeight.w500),
                  ),
                )
              ],
            ),
            
            Text(
              "${T.measurementTime.tr}:  ${DateFormat('yyyy-MM-dd HH:mm:ss').format(controller.model!.date!.toLocal())}",
              style: TextStyle(
                  color: AppColors.Color999,
                  fontWeight: FontWeight.w500,
                  fontSize: ScreenAdapter.fontSize(10),
                  height: ScreenAdapter.fontSize(14 / 10)),
            ),


          ],
        ),
      ),
    );
  
  }
  String getSeverityString(int value) {
    switch (value) {
      case 0:
        return T.wordsLow.tr;
      case 1:
        return T.wordsNormal.tr;
      case 2:
        return T.wordsMild.tr;
      case 3:
        return T.wordsModerate.tr;
      default:
        return T.wordsSerious.tr;
    }
  }

  String getText() {
    if (tabsController.unitSetting[3][1] == true && controller.model?.data!=0) {
      return controller.model!.data!.toStringAsFixed(1);
    }else {
      return (controller.model!.data! * 1.8 + 32).toStringAsFixed(1);
    }
  }
}