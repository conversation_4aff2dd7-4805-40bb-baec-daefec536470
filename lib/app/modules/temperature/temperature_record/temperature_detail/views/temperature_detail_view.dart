/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-14 11:11:52
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-04 14:24:54
 * @FilePath: /rpmappmaster/lib/app/modules/temperature/temperature_record/temperature_detail/views/temperature_detail_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/model/position_top_left.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/core/widget/custom_liquid.dart';
import 'package:aiCare/app/core/widget/custom_temperature_chart.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_record/record_detail/widgets/oxygen_detail_banner.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_record/record_detail/widgets/oxygen_detail_sub.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/widgets/temperature_pointer.dart';
import 'package:aiCare/app/modules/temperature/temperature_record/temperature_detail/widgets/temperature_detail_button.dart';
import 'package:aiCare/app/modules/temperature/temperature_record/temperature_detail/widgets/temperature_detail_title.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/temperature_detail_controller.dart';

class TemperatureDetailView extends BaseView<TemperatureDetailController> {
  TemperatureDetailView({
    super.key,
  }) : super(
          bgColor: AppColors.homeBgColor,
          statusBarColor: AppColors.homeBgColor,
        );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: "",
      backgroundColor: AppColors.homeBgColor,
    );
  }

  @override
  Widget body(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      width: double.infinity,
      child: Column(
        children: [
          Container(
              width: ScreenAdapter.width(226),
              height: ScreenAdapter.width(205),
              margin: EdgeInsets.only(top: ScreenAdapter.height(10)),
              child: Stack(
                children: [
                  CustomTemperatureChart(
                    width: ScreenAdapter.width(226),
                    height: ScreenAdapter.width(205),
                    posList: PositionTopLeft(left: ScreenAdapter.width(110),top: ScreenAdapter.width(32)),
                    pointHeight: ScreenAdapter.width(83),
                    pointWidth: ScreenAdapter.width(6),
                    data: controller.model,
                  ),


                ],
              )),
          SizedBox(
            height: ScreenAdapter.height(36),
          ),
          TemperatureDetailTitle(),
          SizedBox(
            height: ScreenAdapter.height(12),
          ),
          OxygenDetailSub(),
          Expanded(child: SizedBox()),
          TemperatureDetailButton(),
          SizedBox(
            height: ScreenAdapter.height(20),
          )
        ],
      ),
    );
  }
}


