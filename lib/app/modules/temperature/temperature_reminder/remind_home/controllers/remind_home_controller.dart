// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-19 11:56:27
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-09-19 12:28:41
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/temperature/temperature_reminder/remind_home/controllers/remind_home_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:convert';

// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:aiCare/app/modules/temperature/model/temperature_reminder.dart';
// import 'package:aiCare/app/routes/app_pages.dart';
// import 'package:get/get.dart';

// class RemindHomeController extends BaseController {

//   RxList<TemperatureReminder> remindList = <TemperatureReminder>[].obs;



//   @override
//   void onInit() {
//     super.onInit();
//   }

//   @override
//   void onReady() {
//     super.onReady();
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//     void initList() async {
//     await storage.setData(
//       "pressure_remind_list",
//       jsonEncode([
//         TemperatureReminder(
//           date: "06:12",
//           switchValue: true,
//         ),
//         TemperatureReminder(
//           date: "07:12",
//           switchValue: false,
//         ),
//       ]),
//     );
//     var jsonData = await storage.getData("pressure_remind_list");
//     // 反序列化为 List<PressureReminder> 对象
//     List<TemperatureReminder> result = (jsonDecode(jsonData) as List)
//         .map((item) => TemperatureReminder.fromJson(item))
//         .toList();

//     logger.d(result.toList());
//     remindList.value = result;

//     // remindList.value = [
//     //   PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: true,repeatList: [true,true,true,true,true,true,true]),

//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: true,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: true,repeatList: [true,true,true,true,true,true,true]),
//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: true,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //  PressureReminder(
//     //       date: "06:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     //   PressureReminder(
//     //       date: "07:12", label: "Remind", switchValue: false,repeatList: [true,true,true,true,true,true,true]),
//     // ];
//   }

//   void showAddSheet() {}

//   void switchRecord(bool value, int index) {
//     remindList.value[index].switchValue = value;
//     remindList.refresh();
//     logger.d(remindList.value[index].switchValue);
//   }


//   detelteRemind(int index) async {
//     logger.d("i:$index");
//     remindList.removeAt(index); // 删除指定索引的元素

//     remindList.refresh();
//     await storage.setData("temperature_remind_list", jsonEncode(remindList.value));

//     List<TemperatureReminder> result =
//         (jsonDecode(await storage.getData("temperature_remind_list")) as List)
//             .map((item) => TemperatureReminder.fromJson(item))
//             .toList();
//     logger.d("??");
//     logger.d(result.toList()[0].toJson());
//   }

//   void toEdit(int index) {
//     var data = index == -1 ? null : remindList.value[index];
//     Get.toNamed(Routes.TEMPERATURE_REMIND_EDIT,
//         arguments: {"index": index, "data": data})?.then((result) {
//       if (result != null && result is TemperatureReminder) {
//         if (index == -1) {
//           // 如果 index 是 -1，表示是添加新数据的情况
//           logger.d("添加数据");
//           remindList.add(result);
//         } else {
//           logger.d("更新数据");
//           // 如果 index 不是 -1，表示是编辑已有数据的情况
//           remindList[index] = result;
//         }
//         remindList.refresh(); // 刷新列表以更新 UI
//         storage.setData("pressure_remind_list", jsonEncode(remindList.value));
//       } else {
//         logger.d("pressure_reminder_edit无数据");
//       }
//     });
//   }


// }
