// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-19 12:29:47
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-09-20 17:24:32
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/temperature/temperature_reminder/remind_edit/controllers/remind_edit_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:ffi';

// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:aiCare/app/modules/temperature/model/temperature_reminder.dart';
// import 'package:flutter/widgets.dart';
// import 'package:get/get.dart';
// import 'package:get/get_rx/get_rx.dart';

// import '../../remind_home/controllers/remind_home_controller.dart';

// class RemindEditController extends BaseController {
//   RemindHomeController remindHomeController = Get.find();
//   TextEditingController lableController = TextEditingController();
//   List<FixedExtentScrollController> pickerController = [];

//   RxList<int> selectIndexList = [8, 8, 10].obs;


//   RxBool addSwitch = true.obs;
//   int selectIndex = -1;
//   Rxn<TemperatureReminder> data = Rxn<TemperatureReminder>();

//   RxString repeatText = "".obs;
//   RxList<bool> checkList = [
//     true,
//   ].obs;

//   @override
//   void onInit() {
//     super.onInit();
//     initAdd();
//   }

//   @override
//   void onClose() {
//     lableController.dispose();
//     super.onClose();
//   }

//   void initAdd() {
//     // 获取传递的参数
//     final arguments = Get.arguments;
//     selectIndex = arguments['index'] ?? -1;
//     data.value = arguments['data'] ?? null;
//     logger.d(data.toJson());
//     logger.d("initAdd");
//     if (selectIndex == -1) {
//       selectIndexList.value = [8, 8, 10];
//       selectIndexList.refresh();
//       lableController.text = appLocalization.bloodPressureRemind;
//       addSwitch.value = true;
//       checkList.value = [
//         true,
//         true,
//         true,
//         true,
//         true,
//         true,
//         true,
//       ];
//     } else {
//       List<String> dateList = data.value!.date.split(":");
//       selectIndexList.value[0] = int.parse(dateList[0]);
//       selectIndexList.value[1] = int.parse(dateList[1]);
//       selectIndexList.value[2] = int.parse(dateList[2]);
//       // logger.d("$leftSelectIndex,$rightSelectIndex");
//     }
//     pickerController = [
//       FixedExtentScrollController(initialItem: selectIndexList.value[0]),
//       FixedExtentScrollController(initialItem: selectIndexList.value[1]),
//       FixedExtentScrollController(initialItem: selectIndexList.value[2])
//     ];

//     switchRepeatText();

//     update();
//   }

//   void submitRemind(bool or) {
//     if (selectIndex == -1 || or == true) {

//       String oneDate = selectIndexList.value[0] < 10? "0${selectIndexList.value[0]}": selectIndexList.value[0].toString();
//       String twoDate = selectIndexList.value[1] < 10? "0${selectIndexList.value[1]}": selectIndexList.value[1].toString();
//       String threeDate = selectIndexList.value[2] < 10? "0${selectIndexList.value[2]}": selectIndexList.value[2].toString();
//       // String rightDate = rightSelectIndex.value < 10? "0${rightSelectIndex.value}": rightSelectIndex.value.toString();

//       logger.d("选中的闹钟值：$oneDate,$twoDate,$threeDate");
//       TemperatureReminder result = TemperatureReminder(
//         date: "$oneDate:$twoDate:$threeDate",
//         switchValue:
//             selectIndex == -1 ? addSwitch.value : data.value!.switchValue,
//       );
//       logger.d(result.toJson());
//       Get.back(result: result);
//       // logger.d("left: $leftSelectIndex,right: $rightSelectIndex,label: ${lableController.text},snooze: $addSwitch");
//     } else {
//       remindHomeController.detelteRemind(selectIndex);
//       Get.back();
//     }

//     // return true;
//   }



//   void switchScroller(int index, int i) {
//     selectIndexList.value[index] = i;
//     selectIndexList.refresh();
//   }


//   void switchRecord(bool value) {
//     if (selectIndex == -1) {
//       addSwitch.value = value;
//     } else {
//       data.value!.switchValue = value;
//       data.refresh();
//     }
//   }

//   void switchRepeatText() {
//     // 获取所有选中的星期的索引
//     List<String> days = [
//       appLocalization.dateSunday, // 星期日
//       appLocalization.dateMonday, // 星期一
//       appLocalization.dateTuesday, // 星期二
//       appLocalization.dateWednesday, // 星期三
//       appLocalization.dateThursday, // 星期四
//       appLocalization.dateFriday, // 星期五
//       appLocalization.dateSaturday // 星期六
//     ];

//     if (checkList.every((day) => day)) {
//       // 如果所有的值都为 true
//       repeatText.value = appLocalization.dateEveryday;
//     } else if (checkList.sublist(1, 6).every((day) => day) &&
//         !checkList[0] &&
//         !checkList[6]) {
//       // 如果周一到周五的值为 true，且周日和周六的值为 false
//       repeatText.value = appLocalization.dateMondayToFriday;
//     } else {
//       // 返回所有选中的天
//       List<String> selectedDays = [];
//       for (int i = 0; i < checkList.length; i++) {
//         if (checkList[i]) {
//           selectedDays.add(days[i]);
//         }
//       }
//       repeatText.value = selectedDays.join(", ");
//     }
//   }

  
// }
