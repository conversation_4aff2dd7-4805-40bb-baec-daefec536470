// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-19 12:29:47
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-09-26 14:11:01
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/temperature/temperature_reminder/remind_edit/views/remind_edit_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/core/widget/innner_outer_container.dart';
// import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import '../controllers/remind_edit_controller.dart';

// class RemindEditView extends BaseView<RemindEditController> {
//   RemindEditView({
//     super.key,
//   }) : super(parentPaddings: [
//           // ScreenAdapter.width(16),
//           0,
//           0,
//           // ScreenAdapter.width(16),
//           0,
//           0
//         ], bgColor: AppColors.homeBgColor, banner: HomeBanner());

//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return null;
//   }

//   @override
//   Widget body(BuildContext context) {
//     // TODO: implement body
//     return Container(
//         padding: EdgeInsets.symmetric(
//             horizontal: ScreenAdapter.width(16),
//             vertical: ScreenAdapter.height(16)),
//         decoration: BoxDecoration(
//             color: Colors.white,
//             boxShadow: [
//               BoxShadow(
//                 color: Color(0x0D000000), // 阴影颜色，0x0D 表示透明度为 13% 的黑色
//                 offset: Offset(0, -8), // x 轴偏移量为 0，y 轴偏移量为 -8px
//                 blurRadius: 24, // 模糊半径为 24px
//                 spreadRadius: 0, // 扩展半径为 0
//               ),
//             ],
//             borderRadius: BorderRadius.only(
//                 topLeft: Radius.circular(ScreenAdapter.width(16)),
//                 topRight: Radius.circular(ScreenAdapter.width(16)))),
//         child: Column(children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               InkWell(
//                 onTap: () {
//                   Get.back();
//                 },
//                 child: Text(
//                   appLocalization.wordsCancel,
//                   style: normalF16H22C666,
//                 ),
//               ),
//               Text(
//                 appLocalization.commonTimedAlarmLock,
//                 style: w600F16H22C333,
//               ),
//               InkWell(
//                 onTap: () async {
//                   controller.submitRemind(true);
//                   // Navigator.pop(context); // 关闭底部弹窗
//                 },
//                 child: Text(
//                   appLocalization.wordsSave,
//                   style: normalF16H22C666,
//                 ),
//               )
//             ],
//           ),
//           Container(
//             margin: EdgeInsets.only(top: ScreenAdapter.height(36)),
//             height: ScreenAdapter.height(216),
//             child: Stack(
//               alignment: Alignment.center,
//               children: [
//                 IgnorePointer(
//                   ignoring: true, // 确保中间的文本不会拦截手势
//                   child: Container(
//                     padding: EdgeInsets.only(top: ScreenAdapter.height(8)),
//                     height: ScreenAdapter.height(42),
//                     alignment: Alignment.center,
//                     decoration: BoxDecoration(
//                         color: AppColors.homeBgColor,
//                         borderRadius:
//                             BorderRadius.circular(ScreenAdapter.width(8))),
//                     child: Row(
//                       children: [
//                         SizedBox(
//                           width: ScreenAdapter.width(72.5),
//                         ),
//                         Text(
//                           appLocalization.wordsHours,
//                           style: normalF12H17C999.copyWith(
//                               color: AppColors.Color333,
//                               fontWeight: FontWeight.w500),
//                         ),
//                         SizedBox(
//                           width: ScreenAdapter.width(80.5),
//                         ),
//                         Text(
//                           appLocalization.wordsMin,
//                           style: normalF12H17C999.copyWith(
//                               color: AppColors.Color333,
//                               fontWeight: FontWeight.w500),
//                         ),
//                         SizedBox(
//                           width: ScreenAdapter.width(90.5),
//                         ),
//                         Text(
//                           appLocalization.wordsSec,
//                           style: normalF12H17C999.copyWith(
//                               color: AppColors.Color333,
//                               fontWeight: FontWeight.w500),
//                         )
//                       ],
//                     ),
//                   ),
//                 ),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     SizedBox(
//                       width: ScreenAdapter.width(0),
//                     ),
//                     _picker(
//                       index: 0,
//                     ),
//                     _picker(
//                       index: 1,
//                     ),
//                     _picker(
//                       index: 2,
//                     ),
//                     SizedBox(
//                       width: ScreenAdapter.width(0),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ),
//           _button(
//             controller: controller,
//           ),
//         ]));
//   }
// }

// class _picker extends StatelessWidget {
//   RemindEditController controller = Get.find();

//   final int index;
//   _picker({super.key, required this.index});

//   @override
//   Widget build(BuildContext context) {
//     return Expanded(
//         child: Obx(
//       () => CupertinoPicker(
//         scrollController: controller.pickerController[index],
//         selectionOverlay: Container(
//           color: Colors.transparent, // 设置中间选中行的背景颜色为 FAFAFA
//         ),
//         itemExtent: ScreenAdapter.height(34),
//         onSelectedItemChanged: (int i) {
//           // 右边选择框选中改变时的逻辑
//           controller.switchScroller(index, i);
//         },
//         children: List<Widget>.generate(index == 0 ? 24 : 60, (int i) {
//           bool isSelected = i == (controller.selectIndexList[index]);
//           String text = i < 10 ? "0$i" : i.toString();
//           return Center(
//               child: Text(
//             text,
//             style: TextStyle(
//                 color: isSelected ? AppColors.Color333 : AppColors.Color999,
//                 fontSize: isSelected
//                     ? ScreenAdapter.fontSize(24)
//                     : ScreenAdapter.fontSize(18),
//                 height: isSelected ? 33.6 / 24 : 25.2 / 18,
//                 fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400),
//           ));
//         }),
//       ),
//     ));
//   }
// }

// class _button extends StatelessWidget with BaseWidgetMixin {
//   RemindEditController controller;
//   _button({super.key, required this.controller});

//   @override
//   Widget body(BuildContext context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         InkWell(
//           onTap: () {
//             logger.d("点击了");
//             Get.back();
//           },
//           child: Container(
//             width: ScreenAdapter.width(65),
//             height: ScreenAdapter.width(65),
//             decoration: BoxDecoration(
//                 color: Colors.transparent, shape: BoxShape.circle),
//             child: CustomPaint(
//               painter: GradientBorderPainter(
//                 paintColor: [
//                   AppColors.ColorA4S15,
//                   AppColors.ColorA4S15,
//                 ],
//                 width: 1,
//               ),
//               child: Center(
//                 child: Container(
//                   width: ScreenAdapter.width(57),
//                   height: ScreenAdapter.height(57),
//                   decoration: BoxDecoration(
//                       shape: BoxShape.circle, color: AppColors.ColorA4S20),
//                   child: Center(
//                     child: Text(
//                       appLocalization.wordsCancel,
//                       style: normalF12H17C999.copyWith(
//                           color: AppColors.Color333,
//                           fontWeight: FontWeight.w500),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//         InkWell(
//           onTap: () {
//             logger.d("点击了");
//             controller.submitRemind(true);
//           },
//           child: Container(
//             width: ScreenAdapter.width(65),
//             height: ScreenAdapter.width(65),
//             decoration: BoxDecoration(
//                 color: Colors.transparent, shape: BoxShape.circle),
//             child: CustomPaint(
//               painter: GradientBorderPainter(
//                 paintColor: [
//                   AppColors.temperatureStartS15,
//                   AppColors.temperatureStartS15,
//                 ],
//                 width: 1,
//               ),
//               child: Center(
//                 child: Container(
//                   width: ScreenAdapter.width(57),
//                   height: ScreenAdapter.height(57),
//                   decoration: BoxDecoration(
//                       shape: BoxShape.circle,
//                       color: AppColors.temperatureStartS15),
//                   child: Center(
//                     child: Text(
//                       appLocalization.wordsStart,
//                       style: normalF12H17C999.copyWith(
//                           color: AppColors.temperatureStart,
//                           fontWeight: FontWeight.w500),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
