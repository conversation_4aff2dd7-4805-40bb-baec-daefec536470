/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-23 14:42:59
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-07 14:21:43
 * @FilePath: /rpmappmaster/lib/app/modules/temperature/model/temperature_data.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:convert';

class TemperatureData {
  double? data;
  DateTime? date;
  int? dataSource; // 数据来源

  // 构造函数
  TemperatureData({
    this.data,
    this.date,
    this.dataSource,
  });

  // 格式化输出方法
  @override
  String toString() =>
      'TemperatureData(data: $data, date: $date, dataSource: $dataSource)';

  // 根据温度计算程度
  int getTotalLevel() {
    if (data == null) {
      return 9;
    } else if (data! >= 39.0) {
      return 4;
    } else if (data! >= 37.2) {
      return 3;
    } else if (data! >= 36.0) {
      return 1;
    } else {
      return 0;
    }
  }

  // 复制方法
  TemperatureData copyWith({
    double? data,
    DateTime? date,
    int? dataSource,
  }) {
    return TemperatureData(
      data: data ?? this.data,
      date: date ?? this.date,
      dataSource: dataSource ?? this.dataSource,
    );
  }

  // 将对象转换为 Map
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'data': data,
      'date': date?.millisecondsSinceEpoch, // 将 DateTime 转换为毫秒
      'dataSource': dataSource,
    };
  }

  // 从 Map 创建对象
  factory TemperatureData.fromMap(Map<String, dynamic> map) {
    return TemperatureData(
      data: map['data'] != null ? map['data'] as double : null,
      date: map['date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['date'] as int)
          : null,
      dataSource: map['dataSource'] != null ? map['dataSource'] as int : null,
    );
  }

  // 将对象转换为 JSON 字符串
  String toJson() => json.encode(toMap());

  // 从 JSON 字符串创建对象
  factory TemperatureData.fromJson(String source) =>
      TemperatureData.fromMap(json.decode(source) as Map<String, dynamic>);

    // 从 JSON 创建对象
  factory TemperatureData.fromJsonToAPI(Map<String, dynamic> json) {
    return TemperatureData(
      dataSource: json['type'] as int? ?? 1, // 默认值为 1
      data:
          (json['temperature'] as double?), // saturation 对应 percentage
      date: json['date_time'] != null
          ? DateTime.parse(json['date_time'])
          : null, // 解析时间字符串
    );
  }

  // 重写 == 运算符，用于对象比较
  @override
  bool operator ==(covariant TemperatureData other) {
    if (identical(this, other)) return true;

    return other.data == data &&
        other.date == date &&
        other.dataSource == dataSource;
  }

  // 重写 hashCode，用于对象比较
  @override
  int get hashCode => data.hashCode ^ date.hashCode ^ dataSource.hashCode;
}
