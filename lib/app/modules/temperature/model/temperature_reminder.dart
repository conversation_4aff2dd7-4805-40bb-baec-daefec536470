/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-09-19 12:25:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-09-19 12:25:40
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/temperature/model/temperature_reminder.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class TemperatureReminder {
  String date;

  bool switchValue;


  TemperatureReminder({
    required this.date,
    required this.switchValue,
  });

  // 将对象转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'switchValue': switchValue,
    };
  }

  // 从 JSON 创建对象
  factory TemperatureReminder.fromJson(Map<String, dynamic> json) {
    return TemperatureReminder(
      date: json['date'] as String,
      switchValue: json['switchValue'] as bool,
    );
  }
}