import 'dart:convert';
import 'dart:ffi';
import 'dart:ui';

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/model/domains.dart';
import 'package:aiCare/app/core/render/fl_chart/image_dot_painter.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/fl_chart/scatter_plot.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/modules/user/user_home/controllers/user_controller.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class TemperatureHomeController extends BaseController {
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  HomeController homeController = Get.find();
  TabsController tabsController = Get.find();
  // UserHomeController userHomeController = Get.find();

  List<Domains> domainList = [];

  RxString lineOption = "".obs;

  RxList<String> listDate =
      ['06/01', '06/02', '06/03', '06/04', '06/05', '06/06', '06/07'].obs;

  RxList<double> listValue = [34.2, 34.0, 35.0, 36.0, 36.2, 37.5, 38.4].obs;

  RxList<List<FlSpot>> spots = [
    [0]
        .asMap()
        .entries
        .map((entry) =>
            FlSpot((entry.key + 1).toDouble(), entry.value.toDouble()))
        .toList(),
  ].obs;

  double leftMax = 40;
  double leftMin = 34;
  double leftInterval = 1;
  TextStyle leftStyle = normalF12H17C666;
  TextStyle bottomStyle = normalF12H17C666;
  Color bgColor = AppColors.pressureRemindBg;

  List<Color> spotsColor = [AppColors.lightBlue];
  List<FlDotData> dotStyle = [
    FlDotData(
        show: true,
        getDotPainter: (spot, perent, barData, index) {
          if (spot.y < 36.2 || spot.y > 37.2) {
            return GlowDotCirclePainter(
                radius: ScreenAdapter.width(3), //点的半径
                color: AppColors.errorPoint, //点的颜色
                strokeWidth: ScreenAdapter.width(1), //点的边框宽度
                strokeColor: AppColors.colorWhite, //点边框的颜色
                glowColor: AppColors.errorPoint,
                glowRadius: ScreenAdapter.width(5));
          }
          return FlDotCirclePainter(
            radius: ScreenAdapter.width(3), //点的半径
            color: AppColors.lightBlue, //点的颜色
            strokeWidth: ScreenAdapter.width(1), //点的边框宽度
            strokeColor: Colors.white, //点边框的颜色
          );
        }),
  ];

  Color getDynamicColor(double yValue) {
    return (yValue < 36.2 || yValue > 37.2)
        ? AppColors.errorPoint // 红色
        : AppColors.lightBlue; // 蓝色
  }

  late LineTouchData lineTouchData; // 使用 late 初始化

  Rx<TemperatureData> lastValue = TemperatureData().obs;
  RxList<TemperatureData> valueList = <TemperatureData>[].obs;
  RxList<double> dayDataList = [0.0, 0.0, 0.0].obs;

  // 新增请求控制参数
  RxBool requestParam = true.obs;

  BluetoothController bluetoothController = Get.find();


  List<RxBool> requestParamList = [false.obs, false.obs, false.obs];

  GlobalKey<MyScatterPlotState> temperatureLineKey = GlobalKey();

  RxInt timeFrame = 0.obs;

  @override
  void onInit() {
    super.onInit();
    everFunction();
    initDomainList();
    initRequestParamList();
   
  }

  initRequestParamList() {
    requestParamList[0].value =
        storage.getBool(AppValues.heartRateLineDaysRefresh) ?? true;
    requestParamList[1].value =
        storage.getBool(AppValues.heartRateLineWeeksRefresh) ?? true;
    requestParamList[2].value =
        storage.getBool(AppValues.heartRateLineMonthsRefresh) ?? true;
  }

  requestParamListChange(int index, bool value) {
    requestParamList[index].value = value;
    storage.setBool(
        AppValues.heartRateLineDaysRefresh, requestParamList[0].value);
    storage.setBool(
        AppValues.heartRateLineWeeksRefresh, requestParamList[1].value);
    storage.setBool(
        AppValues.heartRateLineMonthsRefresh, requestParamList[2].value);
  }

  everFunction() {
    // 监听 requestParam 的变化
    ever(requestParam, (value) {
      if (value) {
        //刷新
      }
      // 这里可以做你需要的处理
    });

    ever(bluetoothController.isConnected, (connected) {
      if (!connected) {
        hideLoading();
      }
    });
  }

  setTimeFrame(int value) {
    // logger.e("message");
    timeFrame.value = value;
    // refresh();
    // logger.d("重新设置了timeFrame");
    // logger.d(timeFrame.value);
  }

  @override
  void onReady() {
    super.onReady();
     getData();
  }

  @override
  void onClose() {
    super.onClose();
  }
  initDomainList(){

    domainList = [
      // Domains(
      //   images: Assets.images.setting,
      //   text: T.measurementSettings.tr,
      //   tap: () {
      //     logger.d("onclick");
      //     Get.toNamed(Routes.TEMPERATURE_REMIND_HOME);
      //   },
      // ),
      // Domains(
      //   images: Assets.images.notion,
      //   text: appLocalization.measurementRemind,
      //   tap: () {
      //     logger.d("onclick");
      //   },
      // ),
      Domains(
        images: Assets.images.record,
        text: T.measurementRecord.tr,
        tap: () {
          logger.d("onclick");
          Get.toNamed(Routes.TEMPERATURE_RECORD_HOME);
        },
      ),
    ];
  
  }


  void updateLineOption() async {
    setListData();

    dayDataList.value[2] = 110;
    logger.d("查看取值范围");
    logger.d(valueList);
    valueList.value.forEach((data) {
      if (data.data! > dayDataList.value[1])
        dayDataList.value[1] = data.data!;
      if (data.data! < dayDataList.value[2])
        dayDataList.value[2] = data.data!;
    });
    if(dayDataList.value[2] ==110) dayDataList.value[2] =0;
    dayDataList.refresh();
    
  }

  void refreshData() {
    requestParam.value = true;
    requestParam.value = false;
  }

  void getData({DateTime? fromDate, DateTime? toDate}) async {
    showLoading();

    try {
      //默认获取近一周的数据
      fromDate ??= DateTime.now().subtract(Duration(days: 1)).toUtc();
      toDate ??= DateTime.now().toUtc();
      // fromDate.

      // 格式化日期为 ISO8601 格式
      String formattedFromDate =
          DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(fromDate.toUtc());
      String formattedToDate =
          DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(toDate.toUtc());

      logger.d(toDate);
      logger.d("起始日期78：$formattedFromDate");
      logger.d("结束日期：$formattedToDate");

      // 获取存储的数据
      var storedTemperatureList =
           storage.getString(AppValues.bodyTemperatureList);
      var storedTemperatureAverage =
           storage.getString(AppValues.bodyTemperatureAverage);

      // defaultRepositoryImpl.postOxygen(90, DateTime.now().toUtc());
      if (storedTemperatureList != null &&
          storedTemperatureAverage != null &&
          requestParam != true) {
        // 转换存储的字符串数据为实际的对象
        valueList.value = (jsonDecode(storedTemperatureList) as List)
            .map((item) => TemperatureData.fromJson(item)) // 假设 OxygenData 是一个模型类
            .toList();

        dayDataList.value[0] = double.tryParse(storedTemperatureAverage) ?? 0.0;
        logger.d("使用存储的体温数据");
      } else {
        logger.d("额外请求体温数据");
        // 否则进行数据请求
        final results = await Future.wait([
          // 数据列表
          defaultRepositoryImpl.getTemperature(
              formattedFromDate, formattedToDate),
          // 平均值
          defaultRepositoryImpl.getTemperature(formattedFromDate, formattedToDate,
              types: "null"),
        ]);
        logger.d("查看平均值 ${results[1]}");

        // 存储获取的数据
        valueList.value = results[0].reversed.toList();
        valueList.refresh();
        if (results[1][0].data != null && results[1][0].data != -1) {
          dayDataList.value[0] = results[1][0].data!;

        }

        // 将数据存入本地存储
         storage.setString(
            AppValues.bodyTemperatureList, jsonEncode(valueList.value));
         storage.setString(
            AppValues.bodyTemperatureAverage, dayDataList.value[0].toString());
          requestParam.value = false;
      }

      updateLineOption();
    } catch (e) {
      logger.e("获取体温数据失败: $e");
      // 网络请求失败时显示错误提示
      ToastUtil.showError(Get.context!, T.errorNetwork.tr);
    } finally {
      // 确保无论成功还是失败都会隐藏loading
      hideLoading();
    }
  }

  setListData() {
    // 获取当前日期并格式化为 MM/dd 格式
    DateTime currentDate = DateTime.now();
    String currentFormattedDate = DateFormat('MM/dd').format(currentDate);

    // 更新 listDate 最后一个日期为当前日期
    listDate[listDate.length - 1] = currentFormattedDate;

    // 将剩余的日期设置为当前日期前的几天
    for (int i = listDate.length - 2; i >= 0; i--) {
      // 当前日期减去 i 天
      DateTime previousDate =
          currentDate.subtract(Duration(days: listDate.length - 1 - i));
      listDate[i] = DateFormat('MM/dd').format(previousDate);
    }
    listDate.refresh();
  }

  String getText(num value) {
    if (tabsController.unitSetting[3][1].value) {
      return value.toStringAsFixed(1);
    } else {
      return (value * 1.8 + 32).toStringAsFixed(1);
    }
  }

   void measure() async{
    logger.d("查看isConnected:");
    BluetoothController bluetoothController = Get.find();
    logger.d(bluetoothController.isConnected.value);
    if (bluetoothController.isConnected.value) {
      showLoading();
      // bluetoothController.bluetoothRepository.aizoGetAllData();
      bluetoothController.aizoMeasureTemperature(
          setMeasurement: setMeasurement);
      // logger.d("查看打印的result");

      // hideLoading();
    } else {
      ToastUtil.showError(Get.context!, T.bluetoothNotConnected.tr);
    }
  }

  void setMeasurement(String value) async{
    logger.d("查看setMeasurement: $value");
    if (value.contains("in progress")) {
      // measurementStatus.value = T.measurementInProgress.tr;
      // return;
    }else
    if (value.contains("started")) {
      // measurementStatus.value = T.measurementStarted.tr;
      ToastUtil.showSuccess(Get.context!, T.measurementStarted.tr);
    } else if (value.contains("successful")) {
      logger.d("查看值");
      logger.d(value);
      //获取最新值
      HomeController homeController = Get.find();
      await homeController.fetchAllData();
      ToastUtil.showSuccess(Get.context!, T.measurementSuccess.tr);
      getData();
      requestParamList[0].value = true;
      requestParamList[1].value = true;
      requestParamList[2].value = true;
      storage.setBool(AppValues.bodyTemperatureLineDaysRefresh, true);
      storage.setBool(AppValues.bodyTemperatureLineWeeksRefresh, true);
      storage.setBool(AppValues.bodyTemperatureLineMonthsRefresh, true);

      hideLoading();
    } else if (value.contains("error")) {
      // measurementStatus.value = T.measurementError.tr;
      ToastUtil.showError(Get.context!, T.measurementError.tr);
      hideLoading();
    }
  }
  
  
}
