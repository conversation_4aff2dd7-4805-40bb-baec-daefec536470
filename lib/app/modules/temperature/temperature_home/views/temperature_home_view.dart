/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-13 14:44:50
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-27 16:19:29
 * @FilePath: /rpmappmaster/lib/app/modules/temperature/temperature_home/views/temperature_home_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/core/widget/custom_domain.dart';
import 'package:aiCare/app/core/widget/custom_home_bar.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/widgets/temperature_line_chart.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/widgets/temperature_pie_chart.dart';
import 'package:aiCare/app/services/screenAdapter.dart';

import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/temperature_home_controller.dart';

class TemperatureHomeView extends BaseView<TemperatureHomeController> {
     TemperatureHomeView({
    super.key,
  }) : super(
          bgColor: AppColors.homeBgColor,
          statusBarColor: Colors.white,
        );
  
  @override
  PreferredSizeWidget? appBar(BuildContext context) {
    return null;
  }
  
  @override
  Widget body(BuildContext context) {
        return SingleChildScrollView(
            child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: ScreenAdapter.height(812-44), // Set the minimum height
        ),
        child: Column(
          children: [
            CustomHomeBar(),
          TemperaturePieChart(),
          TemperatureLineChart(),
          CustomDomain(list: controller.domainList),
        ],
      ),
    ));
  }
}
