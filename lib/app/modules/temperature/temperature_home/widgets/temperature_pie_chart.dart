import 'package:aiCare/app/core/model/position_top_left.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/bluetooth/custom_bluetooth_icon.dart';

import 'package:aiCare/app/core/widget/custom_temperature_chart.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/controllers/temperature_home_controller.dart';

import 'package:aiCare/app/modules/temperature/temperature_home/widgets/temperature_stat_card.dart';

import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';

class TemperaturePieChart extends StatefulWidget {
  TemperaturePieChart({super.key});

  @override
  State<TemperaturePieChart> createState() => _TemperaturePieChartState();
}

class _TemperaturePieChartState extends State<TemperaturePieChart> {
  final TemperatureHomeController controller = Get.find();

  final List<double> widthList = [
    ScreenAdapter.width(180),
    ScreenAdapter.width(136),
  ];
  getFormatTime() {
    // print("查看tempe");
    // print(controller.homeController.temperatureData);
    if(controller.homeController.temperatureData == null) return " ";
    if(controller.homeController.temperatureData.value!.date == null) return " ";
    final date = controller.homeController.temperatureData.value!.date;
    if (date == null) return " ";
    return DateFormat("yyyy-MM-dd  HH:mm").format(date.toLocal());
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenAdapter.width(343),
      height: ScreenAdapter.height(288+25+8+20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8), // 设置 8px 圆角
        color: AppColors.colorWhite,
      ),
      margin: EdgeInsets.only(
          top: ScreenAdapter.height(8), bottom: ScreenAdapter.height(8)),
      child: Stack(
        children: [
          //体温图
          Positioned(
            top: ScreenAdapter.height(19),
            left: ScreenAdapter.width(64),
            right: ScreenAdapter.width(53),
            child: Obx(() => CustomTemperatureChart(
                  posList: PositionTopLeft(
                      top: ScreenAdapter.height(32),
                      left: ScreenAdapter.width(110)),
                  pointWidth: ScreenAdapter.width(6),
                  pointHeight: ScreenAdapter.height(83),
                  numberStyle: TextStyle(
                      fontSize: ScreenAdapter.fontSize(24),
                      fontWeight: FontWeight.w500,
                      height: ScreenAdapter.fontSize(33.6 / 24)),
                  unitStyle: TextStyle(
                      fontSize: ScreenAdapter.fontSize(20),
                      fontWeight: FontWeight.w400,
                      height: ScreenAdapter.fontSize(20.0 / 24)),
                  width: ScreenAdapter.width(226),
                  height: ScreenAdapter.height(195),
                  data: controller.homeController.temperatureData.value,
                  text: true,
                )),
          ),

          CustomBluetoothIcon(),

                    //测量按钮
          Positioned(
            top: ScreenAdapter.height(225),
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  onTap: (){
                    controller.measure();
                  },
                  child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
                  height: ScreenAdapter.height(25),
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(Assets.images.measureButton.path),
                      fit: BoxFit.cover,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    T.measure.tr,
                    style: normalF12H17C999.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                ),
              ],
            ),
          ),


          //状态值
          Positioned(
              top: ScreenAdapter.height(243+25),
              left: ScreenAdapter.width(0),
              width: ScreenAdapter.width(343),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: ScreenAdapter.width(58),
                  ),
                  Obx(
                    () => TemperatureStatCard(
                      text: T.wordsAverage.tr,
                      value: controller.dayDataList.value[0],
                    ),
                  ),
                  Expanded(child: Container()),
                  Obx(
                    () => TemperatureStatCard(
                      text: T.wordsMaximum.tr,
                      value: controller.dayDataList.value[1],
                      isHighlighted: true,
                    ),
                  ),
                  Expanded(child: Container()),
                  Obx(
                    () => TemperatureStatCard(
                      text: T.wordsMinimum.tr,
                      value: controller.dayDataList.value[2],
                    ),
                  ),
                  SizedBox(
                    width: ScreenAdapter.width(58),
                  ),
                ],
              )),
                      Positioned(
            left: 0,
            right: 0,
            bottom: ScreenAdapter.height(8),
            child: Center(child: Obx(() => Text(
                !controller.homeController.heartRateData.value.isNull() ? getFormatTime() : " ",
                style: TextStyle(
                    fontSize: ScreenAdapter.fontSize(12),
                    height: 16.8 / 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.Color999),
              )),)),
        
        ],
      ),
    );
  }


}
