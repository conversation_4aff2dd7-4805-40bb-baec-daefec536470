/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-12 16:43:54
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-15 16:49:37
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/temperature/temperature_home/widgets/temperature_line_chart.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_line.dart';
import 'package:aiCare/app/core/widget/fl_chart/scatter_plot.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/controllers/temperature_home_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_echarts/flutter_echarts.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import "dart:convert";

import '../../../../core/values/app_colors.dart';

class TemperatureLineChart extends StatefulWidget {
  const TemperatureLineChart({Key? key}) : super(key: key);

  @override
  State<TemperatureLineChart> createState() => _LineChartState();
}

class _LineChartState extends State<TemperatureLineChart> {
  final TemperatureHomeController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          left: ScreenAdapter.width(12),
          right: ScreenAdapter.width(12)),
      width: ScreenAdapter.width(343),
      // height: ScreenAdapter.height(269),
      decoration: BoxDecoration(
        borderRadius:
            BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
        color: AppColors.colorWhite,
      ),
      child: 
            
      Column(
        children: [
                      Container(
              // width: ScreenAdapter.width(343),
              // margin: EdgeInsets.only(bottom: ScreenAdapter.height(12)),
              height: ScreenAdapter.height(48),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: <Widget>[
                  Container(
                    padding: EdgeInsets.only(
                        top: ScreenAdapter.height(16),
                        bottom: ScreenAdapter.height(12)),
                    width: ScreenAdapter.width(16),
                    height: ScreenAdapter.height(44),
                    child: Image.asset(Assets.images.trendIcon.path),
                  ),
                  Container(
                      padding: EdgeInsets.only(
                          top: ScreenAdapter.height(16),
                          bottom: ScreenAdapter.height(12)),
                      margin: EdgeInsets.symmetric(
                          horizontal: ScreenAdapter.width(4)),
                      child: Text(T.heartRate.tr,
                          style: normalF12H17C999.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppColors.Color333))),
                  // Expanded(child: SizedBox()),
                  Spacer(),
                  // TextButton(onPressed: () {  }, child: Text("Nihao"),),
                  _rightButton(),
                ],
              ),
            ),

       


            Obx(() => MyScatterPlot(
              key: controller.temperatureLineKey,
                index:2,
                requestParamList:controller.requestParamList,
                timeFrame: controller.timeFrame.value,
                width: ScreenAdapter.width(324),
                height: ScreenAdapter.height(250),
                headerStyle: ScatterHeaderStyle(
                  margin: EdgeInsets.only(bottom: ScreenAdapter.height(8)),
                  mainTitleStyle: normalF16H22C666.copyWith(
                    color: AppColors.Color333,
                    fontWeight: FontWeight.w600,
                  ),
                  unitStyle: normalF12H17C999,
                  timeStyle:
                      normalF12H17C666.copyWith(fontWeight: FontWeight.w500),
                ), 
                chartStyle: ScatterChartStyle(
                  dotColor:AppColors.lightBlue, 
                  verticalTH: ScreenAdapter.height(18), 
                  horizontalTW: ScreenAdapter.width(22), 
                  backgroundColor: AppColors.lightBlue.withOpacity(0.03), 
                  padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(40)), 
                  verticalTextStyle: normalF12H17C666, 
                  horizontalTextStyle: normalF12H17C666, 
                  ),
                  requestParamListChange: controller.requestParamListChange,
                  unit: controller.tabsController.unitSetting[3][1].value ? '℃' : '℉'
                ),
                
                ),
            Gap(ScreenAdapter.height(12)),

          

          // Container(
          //   // width: ScreenAdapter.width(343),
          //   margin: EdgeInsets.only(bottom: ScreenAdapter.height(12)),

          //   height: ScreenAdapter.height(17),
          //   child: 
            
            
          //   Row(
          //     children: <Widget>[
          //       SizedBox(
          //         width: ScreenAdapter.width(16),
          //         height: ScreenAdapter.height(16),
          //         child: Image.asset(Assets.images.trendIcon.path),
          //       ),
          //       Container(
          //           margin: EdgeInsets.symmetric(
          //               horizontal: ScreenAdapter.width(2)),
          //           child: Text(
          //             T.temperatureTrend.tr,
          //             style: TextStyle(
          //                 fontSize: ScreenAdapter.fontSize(12),
          //                 height: 16.8 / 12,
          //                 color: AppColors.Color333,
          //                 fontWeight: FontWeight.w500),
          //           ))
          //     ],
            
            
          //   ),
          // ),



          
          // Container(
          //   // margin: EdgeInsets.only(left: ScreenAdapter.width(12)),
          //   width: ScreenAdapter.width(319),
          //   height: ScreenAdapter.height(215),
          //   child: Obx(() => CustomLine(
          //       key: UniqueKey(),
          //       spots: controller.spots.value,
          //       leftMin: controller.leftMin,
          //       leftMax: controller.leftMax,
          //       leftInterval: controller.leftInterval,
          //       leftStyle: controller.leftStyle,
          //       bgColor: controller.bgColor,
          //       spotsColor: controller.spotsColor,
          //       spotsWidth: ScreenAdapter.width(1),
          //       dotStyle: controller.dotStyle,
          //       lineTouchData: controller.lineTouchData,
          //       listData: controller.listDate.value,
          //       temperatureUnit:
          //           controller.tabsController.unitSetting[3][1].value,
          //       bottomStyle: controller.bottomStyle)),
          // )
        ],
      ),
    );
  }

  _rightButton() {
    return Container(
      // width: ScreenAdapter.width(105),
      // height: ScreenAdapter.height(32),
      decoration: BoxDecoration(
        color: AppColors.Coloreee,
        borderRadius: BorderRadius.circular(5),
      ),
      padding: EdgeInsets.symmetric(
          vertical: ScreenAdapter.height(4),
          horizontal: ScreenAdapter.width(6)),
      child: Row(children: List.generate(3, (i) => _rightButtonItem(i))),
    );
  }

  _rightButtonItem(int i) {
    return InkWell(
        onTap: () {
          controller.setTimeFrame(i);
        },
        child: Obx(
          () => Container(
            decoration: BoxDecoration(
                color: controller.timeFrame.value == i
                    ? AppColors.colorWhite
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(3),
                boxShadow: controller.timeFrame.value == i
                    ? [
                        BoxShadow(
                          color: Color(0x14000000),
                          blurRadius: 2,
                          offset: Offset(-ScreenAdapter.width(1), 0),
                          spreadRadius: 0,
                        ),
                        BoxShadow(
                          color: Color(0x14000000),
                          blurRadius: 4,
                          offset: Offset(0, ScreenAdapter.height(4)),
                          spreadRadius: 0,
                        )
                      ]
                    : []),
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(
                vertical: ScreenAdapter.height(4),
                horizontal: ScreenAdapter.width(16)),
            height: ScreenAdapter.height(28),
            child: Text(
              _rightGetText(i),
              style: normalF12H17C666,
            ),
          ),
        ));
  }

  _rightGetText(int i) {
    switch (i) {
      case 0:
        return T.wordsDay.tr;
      case 1:
        return T.wordsWeek.tr;
      case 2:
        return T.wordsMonth.tr;
    }
  }
}
