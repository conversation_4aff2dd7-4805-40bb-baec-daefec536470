/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-12 13:54:51
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-12 15:10:20
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/temperature/temperature_home/widgets/temperature_stat_card.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/user/user_home/controllers/user_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TemperatureStatCard extends StatelessWidget {
  TemperatureStatCard(
      {required this.text, required this.value, this.isHighlighted = false});
  TabsController tabsController = Get.find();
  // UserHomeController userHomeController = Get.find();
  final String text;
  final double value;
  // 新增的 bool 参数，默认值为 false
  final bool isHighlighted;

  @override
  Widget build(BuildContext context) {
    print(value);
    // 根据 isHighlighted 的值设置颜色
    final textColor = isHighlighted ? Color(0xFFC293FF) : AppColors.Color999;
    return Container(
      // width: ScreenAdapter.width(41),
      // height: ScreenAdapter.height(39),
      // margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(58)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(text,
              style: TextStyle(
                  fontSize: ScreenAdapter.fontSize(12),
                  color: AppColors.Color666,
                  height: ScreenAdapter.fontSize(16.8 / 12))),
          Row(
            children: [
              Obx(
                () => Text(getText(),
                    style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(14),
                        
                        fontWeight: FontWeight.w500,
                        color: value.toInt() ==0 ?AppColors.Color999  : textColor,
                        height: 19.6 / 14)),
              ),
              Text('°C',
                  style: TextStyle(
                      fontSize: ScreenAdapter.fontSize(12),
                      color: value.toInt() ==0 ?AppColors.Color999  : textColor,
                      height: ScreenAdapter.fontSize(16.8 / 12))),
            ],
          )
        ],
      ),
    );
  }

  String getText() {
    if (tabsController.unitSetting[3][1].value&& value != 0) {
      return value.toStringAsFixed(1);
    } else if (value == 0) {
      return "--";
    } else {
      return (value * 1.8 + 32).toStringAsFixed(1);
    }
  }
}
