/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-13 14:46:46
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-04 14:21:25
 * @FilePath: /rpmappmaster/lib/app/modules/temperature/temperature_home/widgets/temperature_pointer.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/widgets.dart';

class TemperaturePointer extends CustomPainter {
  final double rotation; // 旋转参数，范围在 34 到 40 之间
  final double maxWidth;
  final double minWidth;
  final double height;

  TemperaturePointer(
     this.rotation, this.maxWidth, this.minWidth, this.height);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = AppColors.temperaturePointColor // 设置指针颜色
      ..style = PaintingStyle.fill; // 填充样式

    final double width = size.width;
    final double height = size.height;

    final double topWidth = minWidth;
    final double bottomWidth = maxWidth;
    final double pointerHeight = height;

    // 根据旋转参数计算旋转角度
    final double minRotation = 34.0;
    // final double maxRotation = 40.5;
    final double maxRotation = 40;
    final double minAngle = -130;
    final double maxAngle = 130;
    final double angle = minAngle + ((rotation - minRotation) / (maxRotation - minRotation)) * (maxAngle - minAngle);
    // print("最小度数：$minAngle");
    // print("最大度数：$maxAngle");
    // print("最小参数：$minRotation");
    // print("最大参数：$maxRotation");
    // print("传入参数: $rotation");
    
    // print('Calculated angle: $angle');


    // 旋转中心点
    final Offset center = Offset(bottomWidth / 2, height);

    // 保存当前状态
    canvas.save();

    // 应用旋转变换
    canvas.translate(center.dx, center.dy);
    canvas.rotate(angle * 3.1415927 / 180); // 角度转弧度
    canvas.translate(-center.dx, -center.dy);

    // 绘制指针
    final Path path = Path()
      ..moveTo(0, height) // 底部左端
      ..lineTo(bottomWidth, height) // 底部右端
      ..lineTo((width / 2) + (topWidth / 2), height - pointerHeight) // 顶部右端
      ..lineTo((width / 2) - (topWidth / 2), height - pointerHeight)
      ..close(); // 关闭路径

    canvas.drawPath(path, paint);

    // 恢复之前保存的状态
    canvas.restore();
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

