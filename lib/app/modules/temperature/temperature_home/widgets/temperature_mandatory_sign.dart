/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-13 14:56:17
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-08-23 15:50:53
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/temperature/temperature_home/widgets/temperature_mandatory_sign.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/model/position_top_left.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/widgets.dart';

class TemperatureMandatorySign extends StatelessWidget {
  final bool show;
   TemperatureMandatorySign({super.key, required this.show});
  final List<PositionTopLeft> bigList = [
    PositionTopLeft(top: ScreenAdapter.height(175), left: ScreenAdapter.width(26)),
    PositionTopLeft(top: ScreenAdapter.height(95), left: ScreenAdapter.width(0)),
    PositionTopLeft(top: ScreenAdapter.height(30), left: ScreenAdapter.width(33)),
    PositionTopLeft(top: ScreenAdapter.height(0), left: ScreenAdapter.width(104)),
    PositionTopLeft(top: ScreenAdapter.height(30), left: ScreenAdapter.width(176)),
    PositionTopLeft(top: ScreenAdapter.height(95), left: ScreenAdapter.width(209)),
    PositionTopLeft(top: ScreenAdapter.height(175), left: ScreenAdapter.width(183)),
  ];

    final List<PositionTopLeft> smallList = [
    PositionTopLeft(top: ScreenAdapter.height(80), left: ScreenAdapter.width(11)),
    PositionTopLeft(top: ScreenAdapter.height(47), left: ScreenAdapter.width(0)),
    PositionTopLeft(top: ScreenAdapter.height(16), left: ScreenAdapter.width(12)),
    PositionTopLeft(top: ScreenAdapter.height(0), left: ScreenAdapter.width(53)),
    PositionTopLeft(top: ScreenAdapter.height(16), left: ScreenAdapter.width(95)),
    PositionTopLeft(top: ScreenAdapter.height(47), left: ScreenAdapter.width(109)),
    PositionTopLeft(top: ScreenAdapter.height(80), left: ScreenAdapter.width(96)),
  ];

  @override
  Widget build(BuildContext context) {
    List<PositionTopLeft> index = show? bigList :smallList;
    TextStyle textStyle = show? normalF14H19C333 : normalF12H17C333;
    return Stack(
      children: [
        Positioned(
            top: index[0].top,
            left: index[0].left,
            child: Text(
              "34",
              style: textStyle,
            )),
        Positioned(
            top: index[1].top,
            left: index[1].left,
            child: Text(
              "35",
              style: textStyle,
            )),
        Positioned(
            top: index[2].top,
            left: index[2].left,
            child: Text(
              "36",
              style: textStyle,
            )),
        Positioned(
            top: index[3].top,
            left: index[3].left,
            child: Text(
              "37",
              style: textStyle,
            )),
        Positioned(
            top: index[4].top,
            left: index[4].left,
            child: Text(
              "38",
              style: textStyle,
            )),
        Positioned(
            top: index[5].top,
            left: index[5].left,
            child: Text(
              "39",
              style: textStyle,
            )),
        Positioned(
            top: index[6].top,
            left: index[6].left,
            child: Text(
              "40",
              style: textStyle,
            )),
      ],
    );
  }
}
