/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-09-10 11:51:37
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-12 15:30:03
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/manual_input/controllers/manual_input_dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/loading.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/blood_oxygen/oxygen_home/controllers/blood_oxygen_controller.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/modules/heart_rate/rate_home/controllers/rate_home_controller.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/controllers/temperature_home_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/app/services/toastHelper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:string_validator/string_validator.dart';

class ManualInputController extends BaseController {
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  int index = 0;
  List<String> titleList = [];
  List<List<String>> unitList = [];
  List<TextEditingController> textControllers = [];
  List<FocusNode> focusNodes = [];
  RxList<List<bool>> onclick = [
    [true]
  ].obs;
  // late RxList<FixedExtentScrollController> pickerControllerList; //
  RxList<FixedExtentScrollController> pickerControllerList =
      <FixedExtentScrollController>[].obs;
  RxList<int> selectIndexList = [0, 0, 0, 0, 0].obs;
  DateTime currentDate = DateTime.now(); // 当前时间
  RxString selectDateString = "".obs;
  // GlobalKey submitKey = GlobalKey();

  @override
  void onInit() {
    super.onInit();
    // 获取传递的参数
    index = Get.arguments['index'];
    logger.d("当前获取的index${index}");
    toSwitch();
    initList();
  }

  @override
  void onClose() {
    // 释放所有文本控制器
    textControllers.forEach((controller) => dispose());
    super.onClose();
  }

  void toSwitch() {
    switch (index) {
      case 0:
        titleList = [
          T.wordsTime.tr,
          T.bloodOxygen.tr,
          // appLocalization.wordsHeight,
        ];
        unitList = [
          [T.wordsPercentage.tr],
          // [appLocalization.wordsCm, appLocalization.wordsInch],
        ];
        // onclick.value = [
        //   [true],
        //   // [true, false]
        // ];
        // onclick.refresh();

        textControllers =
            List.generate(titleList.length - 1, (i) => TextEditingController());
        focusNodes =
            List.generate(titleList.length - 1, (i) => FocusNode()); // 焦点节点

        return;
      case 1:
        titleList = [
          T.wordsTime.tr,
          T.temperature.tr,
        ];
        unitList = [
          ["℃", "℉"],
        ];

        onclick.value = [
          [true, false],
        ];

        textControllers =
            List.generate(titleList.length - 1, (i) => TextEditingController());
        focusNodes =
            List.generate(titleList.length - 1, (i) => FocusNode()); // 焦点节点
        return;

      case 3:
        titleList = [
          T.wordsTime.tr,
          T.wordsWeight.tr,
          T.wordsHeight.tr,
        ];
        unitList = [
          [T.wordsKg.tr, T.wordsLb.tr],
          [T.wordsCm.tr, T.wordsInch.tr],
        ];
        onclick.value = [
          [true, false],
          [true, false]
        ];
        onclick.refresh();

        textControllers =
            List.generate(titleList.length - 1, (i) => TextEditingController());
        focusNodes =
            List.generate(titleList.length - 1, (i) => FocusNode()); // 焦点节点

        return;

      // case 2:
      //   titleList = [
      //     appLocalization.wordsTime,
      //     appLocalization.wordsSys,
      //     appLocalization.wordsDia,
      //     appLocalization.commonHeartRate
      //   ];
      //   unitList = [
      //     [appLocalization.wordsMmHg],
      //     [appLocalization.wordsMmHg],
      //     [appLocalization.commonTimesMinuteBig]
      //   ];
      //   onclick.value=[
      //     [true],
      //     [true],
      //     [true]
      //   ];

      //   textControllers =
      //       List.generate(titleList.length - 1, (i) => TextEditingController());
      //   focusNodes =
      //       List.generate(titleList.length - 1, (i) => FocusNode()); // 焦点节点
      //   return;

      // case 3:
      //   titleList = [
      //     appLocalization.wordsTime,
      //     appLocalization.wordsWeight,
      //     appLocalization.wordsHeight,
      //   ];
      //   unitList = [
      //     [appLocalization.wordsKg, appLocalization.wordsLb],
      //     [appLocalization.wordsCm, appLocalization.wordsInch],
      //   ];
      //   onclick.value = [
      //     [true, false],
      //     [true, false]
      //   ];
      //   onclick.refresh();

      //   textControllers =
      //       List.generate(titleList.length - 1, (i) => TextEditingController());
      //   focusNodes =
      //       List.generate(titleList.length - 1, (i) => FocusNode()); // 焦点节点

      //   return;

      case 2:
        titleList = [
          T.wordsTime.tr,
          T.heartRate.tr,
          // appLocalization.wordsHeight,
        ];
        unitList = [
          [T.wordsBPM.tr],
          // [appLocalization.wordsCm, appLocalization.wordsInch],
        ];
        // onclick.value = [
        //   [true],
        //   // [true, false]
        // ];
        // onclick.refresh();

        textControllers =
            List.generate(titleList.length - 1, (i) => TextEditingController());
        focusNodes =
            List.generate(titleList.length - 1, (i) => FocusNode()); // 焦点节点

        return;
      default:
        // logger.d("运行了没有？");
        // logger.d(titleList.length);
        return;
    }
  }

  onTap(int i, BuildContext context) {
    logger.d(i);
    if (i == 0) {
      logger.d("跳转到时间选择");
      showBottomSheet(context);
    } else {
      logger.d("跳转到控制器");
      // 获取焦点并移动光标
      focusNodes[i - 1].requestFocus();
      // 将光标定位到 i-1 的文本控制器上
      textControllers[i - 1].selection = TextSelection.fromPosition(
        TextPosition(offset: textControllers[i - 1].text.length),
      );
    }
  }

  showBottomSheet(BuildContext context) {
    initList();
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(
              horizontal: ScreenAdapter.width(16),
              vertical: ScreenAdapter.height(16)),
          height: ScreenAdapter.height(342),
          width: ScreenAdapter.width(375),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              //顶部操作栏
              Flexible(
                  child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Text(
                      T.wordsCancel.tr,
                      style: normalF16H22C666,
                    ),
                  ),
                  Text(
                    T.wordsTime.tr,
                    style: normalF16H22C666.copyWith(
                        color: AppColors.Color333, fontWeight: FontWeight.w600),
                  ),
                  InkWell(
                    onTap: () {
                      saveTime();
                    },
                    child: Text(
                      T.wordsSave.tr,
                      style:
                          normalF16H22C666.copyWith(color: AppColors.lightBlue),
                    ),
                  )
                ],
              )),
              SizedBox(
                height: ScreenAdapter.height(32),
              ),
              // Text('选择时间', style: TextStyle(fontSize: 18)),
              //动态 Picker 列表
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: List.generate(selectIndexList.length, (i) {
                  // logger.d("i:$i");
                  return Obx(() => Container(
                        width: ScreenAdapter.width(52),
                        height: ScreenAdapter.height(192),
                        child: CupertinoPicker(
                          scrollController: pickerControllerList[i],
                          selectionOverlay: Container(
                            color: Colors.transparent, // 设置中间选中行的背景颜色为透明
                          ),
                          itemExtent: ScreenAdapter.height(48), // 设置行高
                          // 根据 i 值动态生成年月日时分的内容
                          onSelectedItemChanged: (int value) {
                            switchPickerController(i, value);
                          },
                          children:
                              List.generate(getPickerItemCount(i), (int index) {
                            bool isSelected = index == selectIndexList.value[i];
                            // logger.d("ii:$i");
                            // if(i == 0 ){
                            //   isSelected = index +100 == selectIndexList.value[i] ;
                            //   logger.d("isSelected:index:$index,selectIndexList.value[i]:${selectIndexList.value[i]}");
                            // }
                            if (i == 0) {
                              isSelected = selectIndexList.value[i] == index;
                            }

                            String text = getPickerItemText(i, index);

                            return Center(
                              child: Text(
                                text,
                                style: TextStyle(
                                  color: isSelected
                                      ? AppColors.Color333
                                      : AppColors.Color999,
                                  fontSize: ScreenAdapter.fontSize(20),
                                  height: 28 / 20,
                                  fontWeight: isSelected
                                      ? FontWeight.w500
                                      : FontWeight.w400,
                                ),
                              ),
                            );
                          }),
                        ),
                      ));
                }),
              )
            ],
          ),
        );
      },
    );
  }

  unitSwitch(int index, int j) {
    logger.d("单位切换");
    for (int i = 0; i < onclick.value[index].length; i++) {
      // 如果是下标为 j 的元素，设置为 true，否则为 false
      onclick.value[index][i] = (i == j) ? true : false;
    }
    onclick.refresh();
  }

  // void initList() {
  void initList() {
    DateTime now = DateTime.now(); // 获取当前日期时间

    // 赋值：月份 -> 第二个参数，日 -> 第三个参数，分钟 -> 第四和第五个参数
    selectIndexList.value[0] = 99;
    selectIndexList.value[1] = now.month - 1; // 月份
    selectIndexList.value[2] = now.day - 1; // 日
    selectIndexList.value[3] = now.hour; // 时间分钟
    selectIndexList.value[4] = now.minute; // 时间分钟（第二次赋值）
    logger.d("查看值");
    logger.d(selectIndexList.value);
    pickerControllerList.value = List.generate(
      selectIndexList.length,
      (i) => FixedExtentScrollController(initialItem: selectIndexList[i]),
    );
    // pickerControllerList.refresh();
  }
  // }

  void switchPickerController(int index, int value) {
    logger.d("切换$index,$value");
    // 调用 jumpToItem 更新 picker 选中的项目
    // pickerControllerList[index].jumpToItem(value);

    // 更新选中的索引
    selectIndexList[index] = value;

    // 刷新观察到的列表以触发 UI 更新
    // pickerControllerList.refresh();
    selectIndexList.refresh();
  }

  int getPickerItemCount(int i) {
    switch (i) {
      case 0:
        return 100;
      case 1:
        return _getMonthInYear();
      case 2:
        return _getDaysInMonth(); // 动态获取天数
      case 3:
        // return 24;
        return _getHoursInDay(); // 动态获取小时数
      case 4:
        // return 60;
        return _getMinutesInHour(); // 动态获取分钟数
      default:
        return 10;
    }
  }

  int _getMonthInYear() {
    DateTime now = DateTime.now();
    int selectedYear = DateTime.now().year - 100 + selectIndexList[0] + 1;

    // 如果年月日等于当前日期，则限制小时数
    if (selectedYear == now.year) {
      return now.month;
    }

    return 12; // 默认返回24小时
  }

  // 动态获取当前选中年份和月份的天数
  int _getDaysInMonth() {
    DateTime now = DateTime.now();
    int selectedYear = DateTime.now().year - 100 + selectIndexList[0] + 1;
    int selectedMonth = selectIndexList[1] + 1;
    int day = DateTime(selectedYear, selectedMonth + 1, 0).day;
    if (day < (selectIndexList.value[2] + 1)) {
      selectIndexList.value[2] = day - 1;
    }
    if (selectedYear == now.year && selectedMonth == now.month) {
      return now.day;
    }
    // logger.d("${selectedMonth}的天数为$day,当前选中的day为${(selectIndexList.value[2]+1)}");

    // 当前月份的天数
    return day;
  }

  // 动态获取当前日期的小时数
  int _getHoursInDay() {
    DateTime now = DateTime.now();
    int selectedYear = DateTime.now().year - 100 + selectIndexList[0] + 1;
    int selectedMonth = selectIndexList[1] + 1;
    int selectedDay = selectIndexList[2] + 1;

    // 如果年月日等于当前日期，则限制小时数
    if (selectedYear == now.year &&
        selectedMonth == now.month &&
        selectedDay == now.day) {
      return now.hour + 1;
    }

    return 24; // 默认返回24小时
  }

// 动态获取当前小时的分钟数
  int _getMinutesInHour() {
    DateTime now = DateTime.now();
    int selectedYear = DateTime.now().year - 100 + selectIndexList[0] + 1;
    int selectedMonth = selectIndexList[1] + 1;
    int selectedDay = selectIndexList[2] + 1;
    int selectedHour = selectIndexList[3];

    // 如果年月日时等于当前时间，则限制分钟数
    if (selectedYear == now.year &&
        selectedMonth == now.month &&
        selectedDay == now.day &&
        selectedHour == now.hour) {
      return now.minute + 1;
    }
    logger.d(
        "$selectedYear,$selectedMonth,$selectedDay,$selectedHour,${now.hour}");

    return 60; // 默认返回60分钟
  }

  String getPickerItemText(int i, int index) {
    switch (i) {
      case 0:
        return (currentDate.year - 100 + index + 1).toString();
      case 1:
      case 2:
        return index + 1 < 10
            ? "0" + (index + 1).toString()
            : (index + 1).toString();
      case 3:
      case 4:
        return index < 10 ? "0" + index.toString() : index.toString();
      default:
        return "报错";
    }
  }

  saveTime() {
    List<int> selectedItems = [];

    // 循环遍历 pickerControllerList，获取每个选择器的选中项
    for (int i = 0; i < pickerControllerList.length; i++) {
      int selectedItem = pickerControllerList[i].selectedItem;
      selectedItems.add(selectedItem);
    }

    // 输出或进一步处理这些选中的选项
    logger.d('选中的选项为: $selectedItems');
    // 格式化成合法的日期时间字符串
    String template = "${currentDate.year - 100 + selectedItems[0] + 1}"
        "-${(selectedItems[1] + 1).toString().padLeft(2, '0')}"
        "-${(selectedItems[2] + 1).toString().padLeft(2, '0')} "
        "${(selectedItems[3]).toString().padLeft(2, '0')}:"
        "${(selectedItems[4]).toString().padLeft(2, '0')}";
    // 使用 DateTime.parse 或 DateFormat 解析日期
    try {
      if (isValidDateTime(template)) {
        selectDateString.value = template;

        Get.back();
        initList();
      } else {
        logger.d("日期不合法");
        ToastUtil.showError(Get.context!, T.promptDateTimeillegal.tr);
      }
    } catch (e) {
      logger.d("日期不合法，解析失败: $e");
    }
  }

  bool isValidDateTime(String dateTime) {
    // 正则表达式匹配日期时间格式
    RegExp regExp = RegExp(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$');
    if (!regExp.hasMatch(dateTime)) {
      return false; // 格式不符合
    }

    // 分离日期和时间部分
    List<String> parts = dateTime.split(' ');
    String datePart = parts[0];
    String timePart = parts[1];

    // 获取年、月、日、小时和分钟
    List<String> dateParts = datePart.split('-');
    int year = int.parse(dateParts[0]);
    int month = int.parse(dateParts[1]);
    int day = int.parse(dateParts[2]);

    List<String> timeParts = timePart.split(':');
    int hour = int.parse(timeParts[0]);
    int minute = int.parse(timeParts[1]);

    // 校验日期合法性
    if (!isValidDate(year, month, day)) {
      return false; // 日期不合法
    }

    // 校验小时和分钟合法性
    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
      return false; // 小时或分钟不合法
    }

    return true; // 日期和时间都合法
  }

// 辅助函数，校验日期
  bool isValidDate(int year, int month, int day) {
    if (year < 1) return false;
    if (month < 1 || month > 12) return false;
    if (day < 1 || day > 31) return false;

    // 二月的天数校验
    if (month == 2) {
      bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
      return day <= (isLeapYear ? 29 : 28);
    }

    // 30天的月份
    if (month == 4 || month == 6 || month == 9 || month == 11) {
      return day <= 30;
    }

    // 31天的月份
    return day <= 31;
  }

  bool isNotEmpty() {
    bool textNotNull =
        textControllers.every((controller) => controller.text.isNotEmpty);
    if (textNotNull && selectDateString.value.isNotEmpty) return true;
    return false;
  }

  String isValid() {
    if (index == 0) {
      String input = textControllers[0].value.text;
      // 检查是否包含小数点或是否可以转换为整数
      if (int.tryParse(input) == null ||
          input.contains('.') ||
          int.parse(input) > 100 ||
          int.parse(input) < 0) {
        return T.promptInt0TO100.tr;
      }
    }
    if (index == 1) {
      // logger.d(onclick.value);
      // logger.d(onclick.value[0][0]);
      if (onclick.value[0][0]) {
        // 摄氏度范围检查
        double celsiusValue =
            double.tryParse(textControllers[0].value.text) ?? -1;
        if (celsiusValue < 35.0 || celsiusValue > 42.0) {
          return T.promptCelsius35TO42.tr; // 假设你有对应的提示文本
        }
      } else {
        // 华氏度范围检查
        double fahrenheitValue =
            double.tryParse(textControllers[0].value.text) ?? -1;
        if (fahrenheitValue < 95.0 || fahrenheitValue > 107.6) {
          return T.promptFahren95TO107.tr; // 假设你有对应的提示文本
        }
      }
    }

    if (index == 2) {
      String input = textControllers[0].value.text;
      // 检查是否包含小数点或是否可以转换为整数
      if (int.tryParse(input) == null ||
          input.contains('.') ||
          int.parse(input) > 200 ||
          int.parse(input) < 0) {
        return T.promptInt0TO200.tr;
      }
    }
    return "";
  }

  void toSubmit() {
    // logger.d(selectDateString.value);
    // Loading();
    if (!isNotEmpty()) {
      ToastUtil.showError(Get.context!, T.promptFillAll.tr);
      return;
    }
    String validString = isValid();
    if (validString.isNotEmpty) {
      ToastUtil.showError(Get.context!, validString);
      return;
    }
    showLoading();
    if (index == 0) {
      // logger.d(textControllers[0].value.text.toInt());
      int percentage = int.parse(textControllers[0].value.text);
      OxygenData data = OxygenData(
          dataSource: 1,
          percentage: percentage < 60 ? 60 : percentage,
          date: selectDateString.value.toDate());
         callDataService(defaultRepositoryImpl.postOxygen(data: data))
      .then((result) {
        ToastUtil.showSuccess(Get.context!, T.promptManualUploadTrue.tr);
        Get.back();
        BloodOxygenController bloodOxygenController = Get.find();
        bloodOxygenController.requestParam.value = true;
        bloodOxygenController.getData();
        bloodOxygenController.refreshData();
        HomeController homeController = Get.find();
        homeController.fetchAllData();
        // p
      });
    } else if (index == 1) {
      double degree = 0;

      if (onclick.value[0][0]) {
        degree = textControllers[0].value.text.toDouble();
      } else {
        degree = (textControllers[0].value.text.toDouble() - 32) * 5 / 9;
      }
      TemperatureData data = TemperatureData(
          dataSource: 1, data: degree, date: selectDateString.value.toDate());
      defaultRepositoryImpl.postTemperature(data: data).then((result) {
        ToastUtil.showSuccess(Get.context!, T.promptManualUploadTrue.tr);
        Get.back();
        TemperatureHomeController temperatureHomeController = Get.find();
        temperatureHomeController.getData();
        temperatureHomeController.refreshData();
        HomeController homeController = Get.find();
        homeController.fetchAllData();
        // p
      }).catchError((error) {
        ToastUtil.showError(Get.context!, T.promptManualUploadFalse.tr);
      });
    } else if (index == 2) {
      HeartRateData data = HeartRateData(
          dataSource: 1,
          data: int.parse(textControllers[0].value.text),
          date: selectDateString.value.toDate());
      defaultRepositoryImpl.postHeartRate(data: data).then((result) {
       ToastUtil.showSuccess(Get.context!, T.promptManualUploadTrue.tr);
        RateHomeController rateHomeController = Get.find();
        Get.back();
        rateHomeController.refreshData();
        rateHomeController.getData();
        rateHomeController.heartLineKey.currentState?.loadData();
        

        HomeController homeController = Get.find();
        homeController.fetchAllData();
        // p
      }).catchError((error) {
        logger.d("what's error");
        logger.d(error);
        ToastUtil.showError(Get.context!, T.promptManualUploadFalse.tr);
      });
    } else {
      logger.d("tuSubmit");
    }
    Future.delayed(Duration(seconds: 1), () {
      // 延迟 1 秒后执行的代码
      hideLoading();
    });
  }
}
