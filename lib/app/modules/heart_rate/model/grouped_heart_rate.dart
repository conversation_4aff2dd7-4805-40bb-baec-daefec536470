/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-12 15:06:17
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-12 15:06:25
 * @FilePath: /rpmappmaster/lib/app/modules/heart_rate/model/grouped_heart_rate.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class GroupedHeartRateData {
  final double minY;
  final double maxY;
  final DateTime startTime;
  final DateTime endTime;

  GroupedHeartRateData({
    required this.minY,
    required this.maxY,
    required this.startTime,
    required this.endTime,
  });

  @override
  String toString() {
    return 'GroupedHeartRateData(minY: $minY, maxY: $maxY, startTime: $startTime, endTime: $endTime)';
  }
}