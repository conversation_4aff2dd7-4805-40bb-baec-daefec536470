/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-04-28 15:20:39
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-06 14:50:54
 * @FilePath: /RPM-APP/lib/app/modules/blood_oxygen/model/oxyge_page_data.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';

class HeartRatePageData {
  final List<HeartRateData> data;
  final int page;
  final int pageSize;
  final int total;

  HeartRatePageData({
    required this.data,
    required this.page,
    required this.pageSize,
    required this.total,
  });

    // 带有默认值的构造函数
  HeartRatePageData.withDefaults({
    List<HeartRateData> data = const [],
    int page = 1,
    int pageSize = 10,
    int total = 0,
  })  : data = data,
        page = page,
        pageSize = pageSize,
        total = total;

  // 将对象转换为 JSON 格式的 Map
  Map<String, dynamic> toJson() {
    return {
      'data': data.map((e) => e.toJson()).toList(),
      'page': page,
      'page_size': pageSize,
      'total': total,
    };
  }

  // 从 JSON 创建对象
  factory HeartRatePageData.fromJson(Map<String, dynamic> json) {
    return HeartRatePageData(
      data: (json['data'] as List)
          .map((e) => HeartRateData.fromJsonToAPI(e))
          .toList(),
      page: json['page'] as int,
      pageSize: json['page_size'] as int,
      total: json['total'] as int,
    );
  }

  @override
  String toString() {
    return 'HeartRatePageData(data: ${data.length} items, page: $page, total: $total)';
  }
}
