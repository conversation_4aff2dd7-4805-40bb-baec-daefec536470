/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-16 17:22:33
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-04-29 14:10:17
 * @FilePath: /rpmappmaster/lib/app/modules/blood_oxygen/model/oxygen_data.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class HeartRateData {
  int? data; // 百分比
  DateTime? date; // 时间
  int? dataSource; // 数据来源

  // 构造函数
  HeartRateData({
    this.dataSource,
    this.data,
    this.date,
  });

  // 将对象转换为 JSON 格式的 Map
  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'date': date?.toIso8601String(), // 将 DateTime 转换为字符串，处理 null 值
      'dataSource': dataSource,
    };
  }

  // 从 JSON 创建对象
  factory HeartRateData.fromJson(Map<String, dynamic> json) {
    return HeartRateData(
      dataSource: json['dataSource'] as int?,
      data: (json['data'] as int?)?.toInt(),
      date:
          json['date_time'] != null ? DateTime.parse(json['date_time']) : null,
    );
  }

  // 从 JSON 创建对象
  factory HeartRateData.fromJsonToAPI(Map<String, dynamic> json) {
    return HeartRateData(
      dataSource: json['type'] as int? ?? 1, // 默认值为 1
      data: (json['value'] as num?)?.toInt(), // saturation 对应 percentage
      date: json['date_time'] != null
          ? DateTime.parse(json['date_time'])
          : null, // 解析时间字符串
    );
  }

  @override
  String toString() {
    return 'HeartRateData(percentage: $data, date: $date, dataSource: $dataSource)';
  }

  int getTotalLevel() {
    if (data == null) {
      return 3; // 默认返回 3，如果数据为空
    } else if (data! < 60) {
      return 0; // 偏低
    } else if (data! <= 100) {
      return 1; // 正常
    } else if (data! <= 130) {
      return 2; // 偏高
    } else {
      return 4; // 过高
    }
  }

  bool isNull() {
    if (data == null)
      return true;
    else
      return false;
  }
}
