class HeartRateDays {
  List<List<HeartRateChild>> firstDay;  // 第一日的数据，每小时的数据列表
  List<List<HeartRateChild>> secondDay; // 第二日的数据
  List<List<HeartRateChild>> thirdDay;  // 第三日的数据

  HeartRateDays({
    required this.firstDay,
    required this.secondDay,
    required this.thirdDay,
  });

  // 装填数据的方法
  factory HeartRateDays.fromJson(List<dynamic> jsonData) {
    return HeartRateDays(
      firstDay: _parseHeartRateChildList(jsonData[0]),  // 第一日数据
      secondDay: _parseHeartRateChildList(jsonData[1]), // 第二日数据
      thirdDay: _parseHeartRateChildList(jsonData[2]),  // 第三日数据
    );
  }

  // 将一天的数据转换成 List<HeartRateChild>
  static List<List<HeartRateChild>> _parseHeartRateChildList(List<dynamic>data) {
  List<List<HeartRateChild>> HeartRateChildren = [];

    // 遍历每个小时的数据，确保每个小时的数据都有返回
    for (var hourData in data) {
      if (hourData.isEmpty) {
        // 如果小时数据为空，返回空列表
        HeartRateChildren.add([]);
      } else {
        // 如果小时数据不为空，解析数据并返回
        List<HeartRateChild> hourDataChildren = [];
        for (var item in hourData) {
          hourDataChildren.add(HeartRateChild(
            minY: (item['minY'] as num).toDouble(),
            maxY: (item['maxY'] as num).toDouble(),
            startTime: item['startTime'] as String,
            endTime: item['endTime'] as String,
          ));
        }
        HeartRateChildren.add(hourDataChildren);
      }
    }

    return HeartRateChildren;
  }

  @override
  String toString() {
    return 'HeartRateDays(firstDay: $firstDay, secondDay: $secondDay, thirdDay: $thirdDay)';
  }
}

class HeartRateChild {
  double minY;
  double maxY;
  String startTime;
  String endTime;

  HeartRateChild({
    required this.minY,
    required this.maxY,
    required this.startTime,
    required this.endTime,
  });

  @override
  String toString() {
    return 'HeartRateChild(minY: $minY, maxY: $maxY, startTime: $startTime, endTime: $endTime)';
  }
}
