/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-05 14:15:30
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-12 15:34:28
 * @FilePath: /rpmappmaster/lib/app/modules/heart_rate/rate_home/widgets/rate_pie_chart.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/bluetooth/custom_bluetooth_icon.dart';
import 'package:aiCare/app/core/widget/custom_rate_pie.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/modules/heart_rate/rate_home/controllers/rate_home_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class RatePieChart extends StatelessWidget with BaseWidgetMixin {
  RateHomeController controller = Get.find();
  RatePieChart({super.key});

  @override
  Widget body(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
      width: ScreenAdapter.width(343),
      height: ScreenAdapter.height(288+25+20+8),
      // color: Colors.transparent,
      decoration: BoxDecoration(
        borderRadius:
            BorderRadius.circular(ScreenAdapter.width(8)), // 设置 8px 圆角
        color: AppColors.colorWhite,
      ),
      child: Stack(
        children: [
          CustomBluetoothIcon(),
          Positioned(
            left: 0,
            right: 0,
            child: Obx(()=>CustomRatePie(
            width: ScreenAdapter.width(213),
            rateData:controller.homeController.heartRateData.value,
            rightPadding: ScreenAdapter.width(122.5),
          )),),
          SizedBox(
            height: ScreenAdapter.height(6),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: ScreenAdapter.height(14),
            child: Center(child: Obx(() => Text(
                !controller.homeController.heartRateData.value.isNull() ? getFormatTime() : " ",
                style: TextStyle(
                    fontSize: ScreenAdapter.fontSize(12),
                    height: 16.8 / 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.Color666),
              )),)),

                              //测量按钮
          Positioned(
            bottom: ScreenAdapter.height(14+25+8),
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  onTap: (){
                    controller.measure();
                  },
                  child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
                  height: ScreenAdapter.height(25),
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(Assets.images.measureButton.path),
                      fit: BoxFit.cover,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    T.measure.tr,
                    style: normalF12H17C999.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                ),
              ],
            ),
          ),

        ],
      ),
    );
  }

  getFormatTime() {
    final date = controller.homeController.heartRateData.value.date;
    if (date == null) return " ";
    return DateFormat("yyyy-MM-dd  HH:mm").format(date.toLocal());
  }
}
