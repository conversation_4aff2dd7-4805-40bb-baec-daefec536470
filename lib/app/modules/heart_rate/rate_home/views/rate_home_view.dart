/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-05 13:15:54
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-09 14:40:28
 * @FilePath: /rpmappmaster/lib/app/modules/heart_rate/rate_home/views/rate_home_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/widget/custom_domain.dart';
import 'package:aiCare/app/core/widget/custom_home_bar.dart';
import 'package:aiCare/app/modules/heart_rate/rate_home/widgets/rate_line_chart.dart';
import 'package:aiCare/app/modules/heart_rate/rate_home/widgets/rate_pie_chart.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/rate_home_controller.dart';

class RateHomeView extends BaseView<RateHomeController> {
  RateHomeView({
    super.key,
  }) : super(
          bgColor: AppColors.homeBgColor,
          statusBarColor: Colors.white,
        );

  @override
  Widget? appBar(BuildContext context) {
    return null;
  }

@override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: ScreenAdapter.height(812-44), // Set the minimum height
        ),
        child: Column(
          children: [
            CustomHomeBar(),
            RatePieChart(),
            RateLineChart(),
            CustomDomain(list: controller.domainList),
            // OxygenDomain(),
          ],
        ),
      ),
    );
  }
}
