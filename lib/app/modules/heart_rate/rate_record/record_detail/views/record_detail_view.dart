/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-05 13:19:29
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-07-31 16:03:50
 * @FilePath: /rpmappmaster/lib/app/modules/heart_rate/rate_record/record_detail/views/record_detail_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_decoration.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_shadow.dart';

import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/core/widget/custom_rate_pie.dart';

import 'package:aiCare/app/modules/blood_oxygen/oxygen_record/record_detail/widgets/oxygen_detail_sub.dart';
import 'package:aiCare/app/services/screenAdapter.dart';

import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../controllers/record_detail_controller.dart';

class RecordDetailView extends BaseView<RecordDetailController> {
  RecordDetailView({
    super.key,
  }) : super(
          bgColor: AppColors.homeBgColor,
          statusBarColor: Colors.white,
        );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: "",
    );
  }

  @override
  Widget body(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      child: Column(
        children: [
          Container(
            child: CustomRatePie(
              width: ScreenAdapter.width(213),
              // rateData:controller.lastValue.value
              rateData: controller.model!, // Add ! to handle nullable type
              rightPadding: ScreenAdapter.width(57.5),
            ),
          ),
          SizedBox(
            height: ScreenAdapter.height(36),
          ),
          _heartRateDetailTitle(),
          SizedBox(
            height: ScreenAdapter.height(12),
          ),
          OxygenDetailSub(),
          Expanded(child: SizedBox()),
          _heartRateDetailButton(),
          SizedBox(
            height: ScreenAdapter.height(20),
          )
        ],
      ),
    );
  }

  _heartRateDetailTitle() {

    return          Center(
      child: SizedBox(
        height: ScreenAdapter.height(46),
        // width: ScreenAdapter.width(242),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  T.measurementResult.tr+"：",
                  style: TextStyle(
                      color: AppColors.Color333,
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(16),
                      height: ScreenAdapter.fontSize(22.4 / 16)),
                ),
                Text(
                  "${controller.model?.data?.toInt()}",
                  style: TextStyle(
                      color: AppColors.recordList[controller.model?.getTotalLevel() ?? 0],
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(16),
                      height: ScreenAdapter.fontSize(22.4 / 16)),
                ),
                Text(
                  T.wordsBPM.tr,
                  style: normalF12H17C999.copyWith(color: AppColors.Color333, fontWeight: FontWeight.w500),),
                SizedBox(
                  width: ScreenAdapter.width(12),
                ),
                Container(
                  // width: ScreenAdapter.width(50),
                  height: ScreenAdapter.height(20),
                  padding: EdgeInsets.symmetric(
                      horizontal: ScreenAdapter.width(14),
                      vertical: ScreenAdapter.height(3)),
                  decoration: BoxDecoration(
                    color: AppColors.recordList[controller.model?.getTotalLevel() ?? 0],
                    borderRadius:
                        BorderRadius.circular(ScreenAdapter.width(16)),
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromRGBO(255, 255, 255, 0.45),
                        offset: Offset(0, 2),
                        blurRadius: ScreenAdapter.width(4),
                        spreadRadius: 0.0,
                        inset: true,
                      ),
                      BoxShadow(
                        color: Color.fromRGBO(255, 255, 255, 0.45),
                        offset: Offset(0, -2),
                        blurRadius: ScreenAdapter.width(4),
                        spreadRadius: 0.0,
                        inset: true,
                      ),
                    ],
                  ),
                  child: Text(
                    getSeverityString(controller.model?.getTotalLevel() ?? 0),
                    style: TextStyle(
                        color: AppColors.colorWhite,
                        fontSize: ScreenAdapter.fontSize(10),
                        height: ScreenAdapter.fontSize(14 / 10),
                        fontWeight: FontWeight.w500),
                  ),
                )
              ],
            ),
            
            Text(
              "${T.measurementTime.tr}: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(controller.model!.date!.toLocal())}",
              style: TextStyle(
                  color: AppColors.Color999,
                  fontWeight: FontWeight.w500,
                  fontSize: ScreenAdapter.fontSize(10),
                  height: ScreenAdapter.fontSize(14 / 10)),
            ),


          ],
        ),
      ),
    );
  
  }

  _heartRateDetailButton() {
        return InkWell(
      onTap: (){
        controller.toDelete();
      },
      child: Container(
        width: ScreenAdapter.width(208),
        height: ScreenAdapter.height(38),
        decoration: BoxDecoration(
            color: AppColors.searchButton,
            border: Border.all(
                color: AppColors.lightBlue, width: ScreenAdapter.width(1)),
            borderRadius: BorderRadius.circular(ScreenAdapter.width(24))),
        child: Center(
          child: Text(
            T.commonDeleteRecord.tr,
            style: TextStyle(
                color: AppColors.lightBlue,
                fontSize: ScreenAdapter.fontSize(16),
                fontWeight: FontWeight.w500,
                height: ScreenAdapter.fontSize(22.4 / 16)),
          ),
        )),
    );
  
  }

  _heartRateDetailSub() {}

    String getSeverityString(int value) {
    switch (value) {
      case 0:
        return T.wordsLow.tr;
      case 1:
        return T.wordsNormal.tr;
      case 2:
        return T.wordsMild.tr;
      case 3:
        return T.wordsSerious.tr;
      default:
        return T.wordsSerious.tr;
    }
  }
}
