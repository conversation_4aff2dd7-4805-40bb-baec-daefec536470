/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-06-18 16:19:34
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-15 17:10:38
 * @FilePath: /RPM-APP/lib/app/modules/heart_rate/rate_record/record_detail/controllers/record_detail_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */


import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/modules/heart_rate/rate_record/record_home/controllers/record_home_controller.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:get/get.dart';

class RecordDetailController extends BaseController {
  final recordHomeController = Get.find<RecordHomeController>();
  HeartRateData? model;
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  HomeController homeController = Get.find();

  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    logger.d("获得的model:$model");
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void toDelete() async {
    // showLoading();
    logger.d("删除的元素");
    logger.d(model!.date);
    await callDataService(defaultRepositoryImpl.deleteHeartRate(model!.date!));
    if (model!.date == homeController.heartRateData.value.date) {
      var result = await defaultRepositoryImpl.getHeartRateLast();
      if (result != null) {
        homeController.heartRateData.value = result;
      } else {
        homeController.heartRateData.value = HeartRateData();
      }
    }
    // hideLoading();
    
    // Remove the item using the new method
    recordHomeController.removeItemByDate(model!.date!);
    
    Get.back();
  }
}
