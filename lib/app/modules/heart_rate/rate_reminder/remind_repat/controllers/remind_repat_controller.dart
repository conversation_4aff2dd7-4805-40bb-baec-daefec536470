// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-12-05 13:18:31
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-12-09 14:04:07
//  * @FilePath: /rpmappmaster/lib/app/modules/heart_rate/rate_reminder/remind_repat/controllers/remind_repat_controller.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/controller/base_controller.dart';
// import 'package:aiCare/app/data/model/remind_data.dart';
// import 'package:get/get.dart';

// class RemindRepatController extends BaseController {
//   late int selectIndex;
//   RemindData? data;
//   late List<String> dateList;
//   RxList<bool> checkList = [
//     true,
//     true,
//     true,
//     true,
//     true,
//     true,
//     true,
//   ].obs;

//   @override
//   void onInit() {
//     super.onInit();
//     // 获取传递的参数
//     final arguments = Get.arguments;
//     selectIndex = arguments['index'] ?? -1;
//     checkList.value = arguments['repeat'];
//     checkList.refresh();

//     dateList = [
//       appLocalization.dateEverySunday,
//       appLocalization.dateEveryMonday,
//       appLocalization.dateEveryTuesday,
//       appLocalization.dateEveryWednesday,
//       appLocalization.dateEveryThursday,
//       appLocalization.dateEveryFriday,
//       appLocalization.dateEverySaturday,
//     ];
//   }

//   @override
//   void onClose() {
//     super.onClose();
//   }

//   void check(int index) {
//     checkList.value[index] = !checkList.value[index];
//     checkList.refresh();
//   }
// }
