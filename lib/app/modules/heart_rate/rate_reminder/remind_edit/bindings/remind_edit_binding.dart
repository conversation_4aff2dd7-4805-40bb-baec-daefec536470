// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-12-05 13:18:22
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-12-09 14:15:46
//  * @FilePath: /rpmappmaster/lib/app/modules/heart_rate/rate_reminder/remind_edit/bindings/remind_edit_binding.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/modules/heart_rate/rate_reminder/remind_edit/controllers/remind_edit_controller.dart';
// import 'package:get/get.dart';

// // import '../controllers/remind_edit_controller.dart';

// class RemindEditBinding extends Bindings {
//   @override
//   void dependencies() {
//     Get.lazyPut<RemindEditController>(
//       () => RemindEditController(),
//     );
//   }
// }
