// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-03 18:54:49
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-04-25 13:41:47
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/guide/guide_home/views/guide_home_view.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'package:aiCare/app/core/base/view/base_view.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/modules/guide/guide_home/widgets/jpush.dart';
// import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:flutter/material.dart';

// import 'package:get/get.dart';
// // import 'package:jpush_flutter/jpush_flutter.dart';

// import '../controllers/guide_home_controller.dart';

// class GuideHomeView extends BaseView<GuideHomeController> {
//     GuideHomeView({
//     super.key,
//   }) : super(parentPaddings: [
//           ScreenAdapter.width(16),
//           0,
//           ScreenAdapter.width(16),
//           0
//         ], bgColor: AppColors.homeBgColor, banner: HomeBanner());
  
//   @override
//   PreferredSizeWidget? appBar(BuildContext context) {
//     return null;
//   }


  
//   @override
//   Widget body(BuildContext context) {
//     return JpushState();
//   }
// }
