// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-09-03 18:57:05
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2025-04-25 13:42:09
//  * @FilePath: /RPM-APP-MASTER/lib/app/modules/guide/guide_home/widgets/jpush.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
// import 'dart:io';

// import 'package:flutter/widgets.dart';
// // import 'package:jpush_flutter/jpush_flutter.dart';

// class JpushState extends StatefulWidget {
//   const JpushState({super.key});

//   @override
//   State<JpushState> createState() => _JpushStateState();
// }

// class _JpushStateState extends State<JpushState> {
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     // this.initJpush();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container();
//   }

// //   void initJpush() {
// //     JPush jpush = new JPush();
// //     print("为什么不出现啊？");

// //     //初始化
// //     jpush.setup(
// //       appKey: "3e3e1930538d23b7dbdec74b",
// //       channel: "theChannel1",
// //       production: false,
// //       debug: false,
// //     );

// //     //设置别名，实现指定用户推送
// //     jpush.setAlias("jgid123456").then((map) {
// //       print("设置别名成功");
// //     }).catchError((error){
// //       print("设置别名时发生错误: $error");
// //     });

// //     //获取注册的id
// //     jpush.getRegistrationID().then((rid) {
// //       print("获取注册的id:$rid");
// //     }).catchError((error){
// //       print("设置别名时发生错误: $error");
// //     });

// //     if (Platform.isIOS) {
// //       // iOS 平台的推送配置
// //       print("设置ios平台推送");
// //       jpush.applyPushAuthority(
// //         NotificationSettingsIOS(sound: true, alert: true, badge: true),
// //       );
// //     }

// //     try {
// //       jpush.addEventHandler(
// //         onReceiveNotification: (Map<String, dynamic> message) async {
// //           print("flutter onReceiveNotification: $message");
// //         },
// //         onOpenNotification: (Map<String, dynamic> message) async {
// //           print("flutter onOpenNotification: $message");
// //         },
// //         onReceiveMessage: (Map<String, dynamic> message) async {
// //           print("flutter onReceiveMessage: $message");
// //         },
// //       );
// //     } on Exception {
// //       print("Failed to get platform version");
//     // }
//   // }
// }
