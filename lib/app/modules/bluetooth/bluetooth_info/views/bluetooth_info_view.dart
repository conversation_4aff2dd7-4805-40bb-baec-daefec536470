import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/bluetooth_info_controller.dart';

class BluetoothInfoView extends BaseView<BluetoothInfoController> {
  BluetoothInfoView({super.key})
      : super(
          // bgColor: AppColors.homeBgColor,
          bgColor: AppColors.homeBgColor,
          statusBarColor: Colors.white,
          // bgColor: Colors.red,
          // statusBarColor: Colors.red
        );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: T.wordsAizoRing.tr,
      // backgroundColor: Colors.red
    );
  }

  @override
  Widget body(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(12)),
        child: Column(
          children: [
            Container(
              width: double.infinity,
              height: ScreenAdapter.height(40),
              padding:
                  EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
              margin: EdgeInsets.only(
                  bottom: ScreenAdapter.height(8),
                  top: ScreenAdapter.height(12)),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6), color: Colors.white),
              child: Center(
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        T.wordsName.tr,
                        style: normalF14H19C333.copyWith(
                            fontWeight: FontWeight.w600),
                      ),
                      Text(
                        controller.aizoRing.advName != ""
                            ? controller.aizoRing.advName!
                            : T.wordsAizoRing.tr,
                        style: normalF12H17C999.copyWith(
                            fontWeight: FontWeight.w500,
                            color: AppColors.Color666),
                      )
                    ]),
              ),
            ),
            Container(
              width: double.infinity,
              height: ScreenAdapter.height(40),
              padding:
                  EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
              margin: EdgeInsets.only(bottom: ScreenAdapter.height(8)),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6), color: Colors.white),
              child: Center(
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        T.wordsID.tr,
                        style: normalF14H19C333.copyWith(
                            fontWeight: FontWeight.w600),
                      ),
                      Text(
                        controller.aizoRing.remoteId.str != null
                            ? controller.aizoRing.remoteId.str!
                            : "null",
                        style: normalF12H17C999.copyWith(
                            fontWeight: FontWeight.w500,
                            color: AppColors.Color666),
                      )
                    ]),
              ),
            ),
            Container(
              width: double.infinity,
              // height: ScreenAdapter.height(40),
              padding: EdgeInsets.symmetric(
                  horizontal: ScreenAdapter.width(10),
                  vertical: ScreenAdapter.height(2)),
              // padding: EdgeInsets.only(top: ScreenAdapter.height(2),),
              margin: EdgeInsets.only(bottom: ScreenAdapter.height(8)),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6), color: Colors.white),

              child: Column(
                  // mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(
                      () => InkWell(
                        onTap: () {
                          logger.d("连接或断开设备");
                          controller.disOrConnect();
                        },
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(
                              vertical: ScreenAdapter.height(8)),
                          child: Text(
                            //判断条件改成lastDevice是否等于aizoRing的remoteId
                            controller.bluetoothController.lastConnectedDevice
                                        ?.remoteId.str ==
                                    controller.aizoRing.remoteId.str
                                ? T.bluetoothDisConnected.tr
                                : T.bluetoothIsConnected.tr,
                            style: normalF14H19C333.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppColors.lightBlue),
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      height: ScreenAdapter.height(1),
                      color: AppColors.greyF6Color,
                    ),
                    InkWell(
                      onTap: () {
                        logger.d("跳转到设备并忽略设备");
                        controller.openSystemBluetoothSettings();
                      },
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                            vertical: ScreenAdapter.height(8)),
                        child: Text(
                          T.bluetoothIgnoreDevice.tr,
                          style: normalF14H19C333.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.lightBlue),
                        ),
                      ),
                    ),
                  ]),
            ),
          ],
        ));
  }
}
