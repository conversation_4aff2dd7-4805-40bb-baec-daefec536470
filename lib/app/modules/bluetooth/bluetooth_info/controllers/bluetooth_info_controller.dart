/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-03-07 13:15:23
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-14 15:19:34
 * @FilePath: /RPM-APP/lib/app/modules/bluetooth/bluetooth_info/controllers/bluetooth_info_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/services/toastHelper.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class BluetoothInfoController extends BaseController {
  //TODO: Implement BluetoothInfoController
  // 获取传递过来的 AizoRing 对象
  MyBluetoothDevice aizoRing = Get.arguments as MyBluetoothDevice;
  // TabsController tabsController = Get.find();
  BluetoothController bluetoothController = Get.find();

  final count = 0.obs;
  @override
  void onInit() {
    super.onInit();
    logger.d("aizoRing");
    logger.d(aizoRing.advName);
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void increment() => count.value++;

  void disOrConnect() {
    if (bluetoothController.lastConnectedDevice?.remoteId.str == aizoRing.remoteId.str) {
      bluetoothController.aizoDisconnect();
    } else {
      bluetoothController.aizoConnect(aizoRing);
    }
    // if (tabsController.bluetoothConnect.value == true) {
    //   tabsController.aizoUnbind();
    //   // ToastHelper.MyToast("disConnecting....");
    // } else {
    //   tabsController.aizoRingStatus();
    //   tabsController.aizoBatteryStatus();
    //   tabsController.aizoConnect(0, aizoRing);
    //   // ToastHelper.MyToast("connecting....");
    // }
  }

  void openSystemBluetoothSettings() async {
    // if (GetPlatform.isIOS) {
    // const url = 'App-Prefs:root=Bluetooth';
    // if (await canLaunchUrl(Uri.parse(url))) {
    //   await launchUrl(Uri.parse(url));
    // } else {
    //   logger.e('无法打开系统蓝牙设置页面');
    // }
    //  ScaffoldMessenger.of(Get.context!).showSnackBar(
    //     SnackBar(content: Text("无法打开蓝牙设置，请手动操作")),
    //   );
    ToastUtil.showError(Get.context!, T.unableToOpenBluetoothSettings.tr);
    // ToastHelper.Failed(T.unableToOpenBluetoothSettings.tr);
  }
}
