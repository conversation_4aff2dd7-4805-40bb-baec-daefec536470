/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-07 13:00:21
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-07 14:40:47
 * @FilePath: /rpmappmaster/lib/app/modules/bluetooth/bluetooth_first/bindings/bluetooth_first_binding.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:get/get.dart';

import '../controllers/bluetooth_first_controller.dart';

class BluetoothFirstBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<BluetoothFirstController>(
      () => BluetoothFirstController(),
    );
  }
}
