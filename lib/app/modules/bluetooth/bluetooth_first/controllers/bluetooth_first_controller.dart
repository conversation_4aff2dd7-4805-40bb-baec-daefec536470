/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-07 13:00:21
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-07 14:39:31
 * @FilePath: /rpmappmaster/lib/app/modules/bluetooth/bluetooth_first/controllers/bluetooth_first_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:get/get.dart';

class BluetoothFirstController extends BaseController {

  @override
  void onInit() {
    super.onInit();
  }


  @override
  void onClose() {
    super.onClose();
  }

}
