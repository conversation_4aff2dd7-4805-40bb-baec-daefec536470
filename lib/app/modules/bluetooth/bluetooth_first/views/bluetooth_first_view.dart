/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-07 13:00:21
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-07 15:01:07
 * @FilePath: /rpmappmaster/lib/app/modules/bluetooth/bluetooth_first/views/bluetooth_bluetooth_first_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/modules/bluetooth/bluetooth_first/controllers/bluetooth_first_controller.dart';
import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';


class BluetoothFirstView
    extends BaseView<BluetoothFirstController> {
  BluetoothFirstView({
    super.key,
  }) : super(
          bgColor: AppColors.homeBgColor,
          // bgIcon: _notionIcon()
        );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: "",
      leading: _leftBack(),
    );
  }

  @override
  Widget body(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: ScreenAdapter.height(10),
        ),
        _BluetoothIcon(),
        SizedBox(
          height: ScreenAdapter.height(130),
        ),
        _Text(),
        Expanded(child: SizedBox()),
        _Button(),
      ],
    );
  }
}

class _banner extends StatelessWidget {
  const _banner({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
        top: 0,
        child: IgnorePointer(
          child: Container(
            // width: double.infinity,
            width: ScreenAdapter.width(375),
            height: ScreenAdapter.height(350),

            decoration: BoxDecoration(
                color: Colors.transparent,
                image: DecorationImage(
                    image: AssetImage(Assets.images.bluetoothBackCircle1.path),
                    fit: BoxFit.fitWidth)),
          ),
        ));
  }
}

class _leftBack extends StatelessWidget {
  const _leftBack({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        print("点击返回");
        Get.back();
      },
      splashColor: Colors.transparent, // 去除水波纹效果
      highlightColor: Colors.transparent, // 去除高亮效果
      child: Row(
        children: [
          Container(
            width: ScreenAdapter.width(8.97),
            height: ScreenAdapter.height(16.26),
            margin: EdgeInsets.only(left: ScreenAdapter.width(21.51)),
            // color: Colors.red,
            child: Center(
              // 使用 Center 小部件使 SVG 图片居中
              child: Image.asset(
                Assets.images.backIconWhite.path,
                fit: BoxFit.contain, // 确保图片按比例显示
                // color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _BluetoothIcon extends StatelessWidget {
  const _BluetoothIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenAdapter.width(172),
      height: ScreenAdapter.width(172),
      child: Image.asset(Assets.images.bluetoothSearch1.path),
    );
  }
}

class _Text extends StatelessWidget with BaseWidgetMixin {
  _Text({super.key});

  @override
  Widget body(BuildContext context) {
    return Container(
      width: ScreenAdapter.width(319),
      child: Column(
        children: [
          Text(
            T.bluetoothConYouRing,
            style: TextStyle(
                fontSize: ScreenAdapter.fontSize(24),
                fontWeight: FontWeight.w500,
                height: 33.6 / 24,
                color: AppColors.Color666),
          ),
          SizedBox(
            height: ScreenAdapter.height(10),
          ),
          Text(
            T.bluetoothConYouRingDetail,
            // textAlign: TextAlign.justify,
            style: TextStyle(
                fontSize: ScreenAdapter.fontSize(15),
                height: 21 / 15.0,
                color: AppColors.Color999.withOpacity(0.6)),
          )
        ],
      ),
    );
  }
}

class _Button extends StatelessWidget with BaseWidgetMixin {
  _Button({super.key});

  @override
  Widget body(BuildContext context) {
    return InkWell(
      onTap: (){
        Get.toNamed(Routes.BLUETOOTH_CONNECT);
      },
      child: Container(
      width: ScreenAdapter.width(249),
      height: ScreenAdapter.height(38),
      margin: EdgeInsets.only(bottom: ScreenAdapter.height(20)),
      decoration: BoxDecoration(
          color: AppColors.lightBlue05,
          border: Border.all(
            color: AppColors.lightBlue,
            width: ScreenAdapter.width(1)
          ),
          borderRadius: BorderRadius.all(Radius.circular(ScreenAdapter.width(24)))
      ),
      child: Center(
              child: Text(
                T.bluetoothAddDevice,
                style: normalF16H22C666.copyWith(
                    color: AppColors.lightBlue),
              ),
            ),
    ),
    );
  }
}
