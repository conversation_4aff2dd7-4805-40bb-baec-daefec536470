/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-03-07 13:15:03
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-11 15:57:05
 * @FilePath: /RPM-APP/lib/app/modules/bluetooth/bluetooth_devices/views/bluetooth_devices_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:convert';

import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/model/page_background.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/modules/bluetooth/bluetooth_connect/views/bluetooth_connect_view.dart';
import 'package:aiCare/app/modules/bluetooth/bluetooth_devices/controllers/bluetooth_devices_controller.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:aiCare/app/core/utils/logger_singleton.dart';
import 'package:logger/logger.dart';

class BluetoothDevicesView extends BaseView<BluetoothDevicesController> {
  BluetoothDevicesView({super.key})
      : super(
                  bgColor: Colors.transparent,
          bgImage: PageBackground(
            imagePath: Assets.images.bluetoothBackCircle1.path,
            width: ScreenAdapter.width(375), // 宽度375
            height: ScreenAdapter.height(320+50), // 高度320
            fit: BoxFit.fitWidth, // 填充方式
            left: 0,
            top: 0,
          ),
          statusBarColor: AppColors.homeBgColor,
          // statusBarIconBrightness: Brightness.light,
        );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: "",
      leading: _leftBack(),
      backgroundColor: Colors.transparent,
    );
  }

  @override
  Widget body(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: ScreenAdapter.height(10),
        ),
        _BluetoothIcon(),
        SizedBox(
          height: ScreenAdapter.height(130),
        ),
        // _Text(),
        _List(),
        Expanded(child: SizedBox()),
        _Button(),
      ],
    );
  }
}

class _banner extends StatelessWidget {
  const _banner({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
        top: 0,
        child: IgnorePointer(
          child: Container(
            // width: double.infinity,
            width: ScreenAdapter.width(375),
            height: ScreenAdapter.height(350),

            decoration: BoxDecoration(
                color: Colors.transparent,
                image: DecorationImage(
                    image: AssetImage(Assets.images.bluetoothBackCircle1.path),
                    fit: BoxFit.fitWidth)),
          ),
        ));
  }
}

class _leftBack extends StatelessWidget {
  const _leftBack({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        print("点击返回");
        Get.back();
      },
      splashColor: Colors.transparent, // 去除水波纹效果
      highlightColor: Colors.transparent, // 去除高亮效果
      child: Row(
        children: [
          Container(
            width: ScreenAdapter.width(8.97),
            height: ScreenAdapter.height(16.26),
            margin: EdgeInsets.only(left: ScreenAdapter.width(21.51)),
            // color: Colors.red,
            child: Center(
              // 使用 Center 小部件使 SVG 图片居中
              child: Image.asset(
                Assets.images.backIconWhite.path,
                fit: BoxFit.contain, // 确保图片按比例显示
                // color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _BluetoothIcon extends StatelessWidget {
  const _BluetoothIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenAdapter.width(172),
      height: ScreenAdapter.width(172),
      child: Image.asset(Assets.images.bluetoothSearch1.path),
    );
  }
}

class _Button extends StatelessWidget with BaseWidgetMixin {
  _Button({super.key});

  @override
  Widget body(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.toNamed(Routes.BLUETOOTH_CONNECT);
      },
      child: Container(
        width: ScreenAdapter.width(249),
        height: ScreenAdapter.height(38),
        margin: EdgeInsets.only(bottom: ScreenAdapter.height(20)),
        decoration: BoxDecoration(
            color: AppColors.lightBlue05,
            border: Border.all(
                color: AppColors.lightBlue, width: ScreenAdapter.width(1)),
            borderRadius:
                BorderRadius.all(Radius.circular(ScreenAdapter.width(24)))),
        child: Center(
          child: Text(
            T.bluetoothAddDevice.tr,
            style: normalF16H22C666.copyWith(color: AppColors.lightBlue),
          ),
        ),
      ),
    );
  }
}

class _List extends StatelessWidget with BaseWidgetMixin {
  BluetoothController bluetoothController = Get.find();

  _List({super.key});

  @override
  Widget body(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            T.bluetoothMyDevice.tr,
            style: TextStyle(
                fontSize: ScreenAdapter.fontSize(13),
                height: 18.2 / 13,
                color: AppColors.Color666,
                fontWeight: FontWeight.w500),
          ),
          Gap(ScreenAdapter.height(8)),
          Obx(() {
            //获取一个lastdevice，如果有，则显示，否则不显示
            final devices = bluetoothController.lastConnectedDevice != null
                ? [bluetoothController.lastConnectedDevice!]
                : [];
            if (devices.isEmpty) {
              return Container();
            }
            return ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: devices.length,
              itemBuilder: (context, index) {
                final device = devices[index];
                final isLast = index == devices.length - 1;
                return buildDeviceItem(
                  device: device,
                  isLast: isLast,
                  index: index,
                  devicesView: true,
                );
              },
            );
          })
        ],
      ),
    );
  }
}
