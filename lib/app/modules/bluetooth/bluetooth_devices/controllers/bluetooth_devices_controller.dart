import 'dart:convert';

import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:get/get.dart';

class BluetoothDevicesController extends BaseController {
  //TODO: Implement BluetoothDevicesController
  RxList<AizoRing> devices = <AizoRing>[].obs;
  TabsController tabsController = Get.find();

  final count = 0.obs;

  @override
  void onInit() {
    super.onInit();
    getDevices();
    // 监听 tabsController.bluetoothConnect 的变化
    ever(tabsController.bluetoothConnect, (bool isConnected) {
      if (!isConnected) {
        setAllDevicesDisconnected(false);
      }else{
 setAllDevicesDisconnected(true);
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void increment() => count.value++;

  getDevices() async {
    var jsonData = await storage.getAizoDevicesLast();
    if (jsonData != null) {
      try {
        // 解析JSON数据为AizoRing对象
        // final decoded = jsonDecode(jsonData);
        // List<AizoRing> newDevices = [];
        // if (decoded is List) {
        //   // 如果存储的是列表，遍历每个元素
        //   newDevices = decoded.map((e) => AizoRing.fromJson(e)).toList();
        // } else if (decoded is Map) {
        //   // 如果存储的是单个对象，转换为列表
        //   newDevices = [AizoRing.fromJson(decoded)];
        // }
        // // 使用 assignAll 方法更新列表内容
        // devices.assignAll(newDevices);
        // // 获取设备数据后检查蓝牙连接状态
        // if (!tabsController.bluetoothConnect.value) {
        //   setAllDevicesDisconnected(false);
        // }
      } catch (e) {
        logger.e("解析设备数据失败: $e");
        // return Center(child: Text("数据解析失败"));
      }
      logger.d("有设备历史连接");
    } else {
      logger.d("无设备历史连接");
    }
  }

  // 将所有设备的 isSystemConnected 设置为 false
  void setAllDevicesDisconnected(bool value) {
    for (var device in devices) {
      device.isSystemConnected =  value;
    }
    devices.refresh(); // 刷新列表以更新 UI
  }
}