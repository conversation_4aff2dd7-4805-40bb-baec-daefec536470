/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-07 14:38:33
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-21 16:21:22
 * @FilePath: /rpmappmaster/lib/app/modules/bluetooth/bluetooth_connect/views/bluetooth_connect_view.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEB
 */
import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/model/page_background.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/modules/bluetooth/bluetooth_connect/controllers/bluetooth_connect_controller.dart';
import 'package:aiCare/app/modules/bluetooth/bluetooth_connect/widgets/circle_loading_painter.dart';
import 'package:aiCare/app/modules/home/<USER>/home_banner.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/user/user_home/views/user_view.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/app/services/toastHelper.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';

import '../controllers/bluetooth_connect_controller.dart';
// import 'package:aiCare/app/core/utils/string_utils.dart';

class BluetoothConnectView extends BaseView<BluetoothConnectController> {
  BluetoothConnectView({
    super.key,
  }) : super(
          // bgColor: AppColors.,
          // statusBarColor: AppColors.homeBgColor,
          bgColor: Colors.transparent,
          bgImage: PageBackground(
            imagePath: Assets.images.backBlueBlur.path,
            width: ScreenAdapter.width(375), // 宽度375
            height: ScreenAdapter.height(320), // 高度320
            fit: BoxFit.fitHeight, // 填充方式
            left: 0,
            top: 0,
          ),
        );
  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: T.wordsBluetooth.tr,
      // title: "nihaoasdasd",
      backgroundColor: Colors.transparent,
    );
  }

  @override
  Widget body(BuildContext context) {
    return Column(
      children: [
        _header(),
        Expanded(
          child: _body(),
        ),
      ],
    );
  }
}

class _header extends StatelessWidget with BaseWidgetMixin {
  final BluetoothConnectController controller = Get.find();
  _header({super.key});

  @override
  Widget body(BuildContext context) {
    return Obx(() => Container(
          width: ScreenAdapter.width(351),
          height: ScreenAdapter.height(71),
          padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
          margin: EdgeInsets.only(top: ScreenAdapter.height(12)),
          decoration: BoxDecoration(
              color: AppColors.colorWhite,
              borderRadius: BorderRadius.circular(8)),
          child: Row(
            children: [
              controller.show.value
                  ? AnimatedBuilder(
                      animation: controller.animationController,
                      builder: (context, child) {
                        return CustomPaint(
                          painter: RectangularLoadingPainter(
                              controller.animationController.value),
                          size: Size(ScreenAdapter.width(24),
                              ScreenAdapter.width(24)), // 可以调整大小
                        );
                      },
                    )
                  : SizedBox(
                      width: ScreenAdapter.width(22),
                      height: ScreenAdapter.height(16),
                      child: Image.asset(Assets.images.bluetoothCircle.path),
                    ),
              Container(
                margin:
                    EdgeInsets.symmetric(horizontal: ScreenAdapter.width(8)),
                child: Text(
                  T.bluetoothSearching.tr,
                  style: normalF16H22C666.copyWith(color: AppColors.Color999),
                ),
              ),
              Expanded(child: SizedBox()),
              InkWell(
                onTap: () {
                  logger.d("点击按钮");
                  controller.handleClick(context);
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: ScreenAdapter.width(14),
                      vertical: ScreenAdapter.height(4)),
                  decoration: BoxDecoration(
                      color: controller.show.value
                          ? AppColors.lightBlue05
                          : AppColors.Color666.withOpacity(0.05),
                      border: Border.all(
                          color: controller.show.value
                              ? AppColors.lightBlue
                              : AppColors.Color666.withOpacity(0.45)),
                      borderRadius: BorderRadius.circular(16)),
                  child: Text(
                    controller.show.value
                        ? T.wordsStop.tr
                        : T.wordsSearch.tr,
                    style: normalF12H17C333.copyWith(
                        color: controller.show.value
                            ? AppColors.lightBlue
                            : AppColors.Color999),
                  ),
                ),
              )
            ],
          ),
        ));
  }
}

// class _card extends StatelessWidget with BaseWidgetMixin {
//   final BluetoothConnectController controller = Get.find();
//   _card({super.key});

//   @override
//   Widget body(BuildContext context) {
//     return Obx((){
//       return
//     })
//   }
// }

class _body extends StatelessWidget with BaseWidgetMixin {
  BluetoothController controller = Get.find();
  _body({super.key});

  @override
  Widget body(BuildContext context) {
    return Obx(() {
        // 计算总的itemCount，确保最少生成5个小部件
        final itemCount = controller.scanResults.length < 5
            ? 5
            : controller.scanResults.length;

        return Container(
          width: ScreenAdapter.width(351),
          // color: Colors.red,
          // margin: EdgeInsets.only(bottom: ScreenAdapter.height(102)),
          child: ListView.builder(
            itemCount: itemCount,
            itemBuilder: (context, index) {
              final isLast = index == controller.scanResults.length - 1;
              if(index >= controller.scanResults.length){
                return Container();
              }
              return buildDeviceItem(
                device: controller.scanResults[index],
                index: index,
                isLast: isLast,
              );

            },
          ),
        );
      });
  }
}



class buildDeviceItem extends StatelessWidget with BaseWidgetMixin {
  final MyBluetoothDevice device; // 这是设备对象
  final int index; // 这是设备对象
  final bool isLast; // 这是一个布尔值，表示是否是最后一个项
  BluetoothController controller = Get.find();
  bool userView;
  bool devicesView;

  buildDeviceItem({
    required this.device,
    required this.index,
    required this.isLast,
    this.userView = false,
    this.devicesView = false,
    Key? key,
  }) : super(key: key);

  @override
  Widget body(BuildContext context) {
    logger.d("lastConnectedDevice: ${controller.lastConnectedDevice?.remoteId.str}");
    
    return InkWell(
      onTap: () {
        logger.d("点击链接或断开绑定");

        if (userView) {
          // ToastHelper.MyToast("Please connect the device and click this button to disconnect");
          Get.toNamed(Routes.BLUETOOTH_DEVICES);
          return;
        }
        if (devicesView) {
          Get.toNamed(Routes.BLUETOOTH_INFO, arguments: device);
          return;
        }
        //有问题，待修改
        final isConnected = controller.lastConnectedDevice != null &&
            controller.lastConnectedDevice!.remoteId.str == device.remoteId.str &&
            controller.isConnected.value;
        
        if (isConnected) {
          // ToastUtil.showWarning(message: "disConnecting....");
          // ToastUtil.showError(Get.context!, "disConnecting....");
          controller.aizoDisconnect();
        } else {
          // ToastUtil.showError(Get.context!, "connecting....");
          controller.aizoConnect(device);
        }
      },
      child: Container(
        width: ScreenAdapter.width(351),
        height: ScreenAdapter.height(81),
        margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
        padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(10)),
        decoration: BoxDecoration(
            color: AppColors.colorWhite,
            borderRadius: BorderRadius.circular(8)),
        child: Row(
          children: [
            Container(
              width: ScreenAdapter.width(41),
              height: ScreenAdapter.height(49),
              child: Image.asset(Assets.images.aizoRing.path),
            ),
            Container(
              margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        T.wordsAizoRing.tr,
                        style: normalF16H22C666.copyWith(
                            color: AppColors.Color333,
                            fontWeight: FontWeight.w500),
                      ),
                      SizedBox(
                        width: ScreenAdapter.width(8),
                      ),
                      Obx(() {
                        final isConnected = controller.lastConnectedDevice != null &&
                            controller.lastConnectedDevice!.remoteId.str == device.remoteId.str &&
                            controller.isConnected.value;
                        
                        return SizedBox(
                          width: ScreenAdapter.width(20),
                          height: ScreenAdapter.width(20),
                          child: SvgPicture.asset(isConnected
                              ? Assets.images.bluetoothOn
                              : Assets.images.bluetoothNo),
                        );
                      })
                    ],
                  ),
                  SizedBox(
                    height: ScreenAdapter.height(4),
                  ),
                  Text(
                    (userView == true && 
                     !(controller.lastConnectedDevice != null &&
                       controller.lastConnectedDevice!.remoteId.str == device.remoteId.str &&
                       controller.isConnected.value))
                        ? T.bluetoothPleaseConnect.tr
                        : "${T.bluetoothMacAddress.tr}：${device.remoteId.str ?? ""}",
                    style: normalF12H17C999,
                  )
                ],
              ),
            ),
            Expanded(child: SizedBox()),
            InkWell(
              onTap: () {
                logger.d("点击搜索按钮");
              },
              child: Container(
                width: ScreenAdapter.width(6),
                child: SvgPicture.asset(
                  Assets.images.rightArrow48,
                  fit: BoxFit.fitWidth,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
