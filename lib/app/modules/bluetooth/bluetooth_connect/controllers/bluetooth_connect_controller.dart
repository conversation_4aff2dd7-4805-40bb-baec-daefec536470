/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-07 14:38:33
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-26 18:52:42
 * @FilePath: /rpmappmaster/lib/app/modules/bluetooth/bluetooth_connect/controllers/bluetooth_connect_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:async'; // 添加定时器导入
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BluetoothConnectController extends BaseController with GetSingleTickerProviderStateMixin {
  BluetoothController bluetoothController = Get.find();
  RxBool show = true.obs;
  late AnimationController animationController;
  Timer? _timer; // 添加定时器变量

  final count = 0.obs;

  @override
  void onInit() {
    super.onInit();
    
    animationController = AnimationController(
      vsync: this,
      duration: Duration(seconds: 1),
    )..repeat();
    
    myInit();
    _startTimer(); // 刚进来时启动定时器
  }

  // 启动定时器方法
  void _startTimer() {
    _timer?.cancel(); // 取消之前的定时器
    _timer = Timer(Duration(seconds: 3), () {
      animationController.stop(); // 停止动画
      show.value = false; // 设置show为false
    });
  }

  myInit() async{
    bool result = await bluetoothController.initialize();
    if(!result){
      ToastUtil.showError(Get.context!,T.permissionRequestContent.tr);
    }
    bluetoothController.scanDevicesTask();
  }

  @override
  void onClose() {
    _timer?.cancel(); // 取消定时器
    animationController.dispose();
    super.onClose();
  }

  handleClick(BuildContext context){
    if(show.value == false){
      bluetoothController.scanDevicesTask();
    }
    show.value = !show.value;
    
    // 点击时重新启动定时器
    if(show.value) {
      animationController.repeat(); // 重新开始动画
      _startTimer(); // 重新启动定时器
    }
  }
}