/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-07 14:38:33
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-07 15:03:47
 * @FilePath: /rpmappmaster/lib/app/modules/bluetooth/bluetooth_connect/bindings/bluetooth_bluetooth_connect_binding.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:get/get.dart';

import '../controllers/bluetooth_connect_controller.dart';

class BluetoothConnectBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<BluetoothConnectController>(
      () => BluetoothConnectController(),
    );
  }
}
