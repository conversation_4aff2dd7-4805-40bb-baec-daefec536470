import 'dart:math';

import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

class RectangularLoadingPainter extends CustomPainter {
  final double progress;

  RectangularLoadingPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, size.height / 2);
    final int segments = 12; // 圆圈上的段数
    final double segmentWidth = ScreenAdapter.width(6); // 长方形的宽度
    final double segmentHeight = ScreenAdapter.height(2); // 长方形的高度

    for (int i = 0; i < segments; i++) {
      // 计算当前段的透明度
      double opacity = (i + progress * segments) % segments / segments;
      opacity = 1 * opacity; // 控制透明度的范围（0.3 到 1.0）

      paint.color = AppColors.lightBlue.withOpacity(opacity);

      // 计算每段的位置和旋转
      double angle = 2 * pi * i / segments;
      double dx = cos(angle) * (size.width / 2 - segmentHeight / 2);
      double dy = sin(angle) * (size.height / 2 - segmentHeight / 2);

      canvas.save();
      canvas.translate(center.dx + dx, center.dy + dy);
      canvas.rotate(angle);
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromCenter(
            center: Offset(0, 0),
            width: segmentWidth,
            height: segmentHeight,
          ),
          Radius.circular(4),
        ),
        paint,
      );
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}