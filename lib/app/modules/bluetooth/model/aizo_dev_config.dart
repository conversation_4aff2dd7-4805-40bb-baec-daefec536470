class AizoDevConfig {
  final bool bloodOxygenMonitoring;
  final bool bloodPressureMonitoring;
  final bool bloodSugarMonitoring;
  final bool breatheMonitoring;
  final bool ecgMonitoring;
  final bool heartRateMonitoring;
  final bool isHeartRateSupport;
  final bool isTouchSet;
  final bool pressureMonitoring;
  final bool sleepMonitoring;
  final int sosTriggerMode;
  final bool supportSos;
  final bool supportWakeupByGesture;
  final bool temperatureMonitoring;

  AizoDevConfig({
    required this.bloodOxygenMonitoring,
    required this.bloodPressureMonitoring,
    required this.bloodSugarMonitoring,
    required this.breatheMonitoring,
    required this.ecgMonitoring,
    required this.heartRateMonitoring,
    required this.isHeartRateSupport,
    required this.isTouchSet,
    required this.pressureMonitoring,
    required this.sleepMonitoring,
    required this.sosTriggerMode,
    required this.supportSos,
    required this.supportWakeupByGesture,
    required this.temperatureMonitoring,
  });

  factory AizoDevConfig.fromJson(Map<String, dynamic> json) {
    return AizoDevConfig(
      bloodOxygenMonitoring: json['bloodOxygenMonitoring'] ?? false,
      bloodPressureMonitoring: json['bloodPressureMonitoring'] ?? false,
      bloodSugarMonitoring: json['bloodSugarMonitoring'] ?? false,
      breatheMonitoring: json['breatheMonitoring'] ?? false,
      ecgMonitoring: json['ecgMonitoring'] ?? false,
      heartRateMonitoring: json['heartRateMonitoring'] ?? false,
      isHeartRateSupport: json['isHeartRateSupport'] ?? false,
      isTouchSet: json['isTouchSet'] ?? false,
      pressureMonitoring: json['pressureMonitoring'] ?? false,
      sleepMonitoring: json['sleepMonitoring'] ?? false,
      sosTriggerMode: json['sosTriggerMode'] ?? 0,
      supportSos: json['supportSos'] ?? false,
      supportWakeupByGesture: json['supportWakeupByGesture'] ?? false,
      temperatureMonitoring: json['temperatureMonitoring'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bloodOxygenMonitoring': bloodOxygenMonitoring,
      'bloodPressureMonitoring': bloodPressureMonitoring,
      'bloodSugarMonitoring': bloodSugarMonitoring,
      'breatheMonitoring': breatheMonitoring,
      'ecgMonitoring': ecgMonitoring,
      'heartRateMonitoring': heartRateMonitoring,
      'isHeartRateSupport': isHeartRateSupport,
      'isTouchSet': isTouchSet,
      'pressureMonitoring': pressureMonitoring,
      'sleepMonitoring': sleepMonitoring,
      'sosTriggerMode': sosTriggerMode,
      'supportSos': supportSos,
      'supportWakeupByGesture': supportWakeupByGesture,
      'temperatureMonitoring': temperatureMonitoring,
    };
  }
}