import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/core/base/view/base_view.dart';
import 'package:aiCare/app/core/enum/aizo_battery_status.dart';
import 'package:aiCare/app/core/model/page_background.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/core/widget/custom_app_bar.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';

import '../controllers/bluetooth_function_controller.dart';

class BluetoothFunctionView extends BaseView<BluetoothFunctionController> {
  BluetoothFunctionView({
    super.key,
  }) : super(
          // bgColor: AppColors.,
          // statusBarColor: AppColors.homeBgColor,
          bgColor: Colors.transparent,
          bgImage: PageBackground(
            imagePath: Assets.images.backBlueBlur.path,
            width: ScreenAdapter.width(375), // 宽度375
            height: ScreenAdapter.height(320), // 高度320
            fit: BoxFit.fitHeight, // 填充方式
            left: 0,
            top: 0,
          ),
        );

  @override
  Widget? appBar(BuildContext context) {
    return CustomAppBar(
      title: T.bluetoothFunction.tr,
      backgroundColor: Colors.transparent,
    );
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _header(),
          _bluetoothStatus(),
          _equipmentStatus(),
          Gap(ScreenAdapter.height(24)),
          _measurementControl(),
          Gap(ScreenAdapter.height(24)),
          _measurementInterval(),
          Gap(ScreenAdapter.height(24)),
          _dataRetrieval(),
        ],
      ),
    );
  }

  Widget _header() {
    return Container(
        margin: EdgeInsets.only(
            top: ScreenAdapter.height(12), bottom: ScreenAdapter.height(24)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(child: SizedBox()),
            Image.asset(
              Assets.images.airingFunction.path,
              // width: ScreenAdapter.width(144),
              height: ScreenAdapter.height(144),
              fit: BoxFit.fitHeight,
            ),
            Gap(ScreenAdapter.width(50)),
            // Expanded(child: SizedBox()),
            Obx(
              () => Image.asset(
                Assets.images.bluetoothFunction.path,
                // width: ScreenAdapter.width(144),
                height: ScreenAdapter.height(24),
                fit: BoxFit.fitHeight,
                color: controller.bluetoothController.isConnected.value
                    ? AppColors.lightBlue
                    : AppColors.Color666,
              ),
            ),
            Gap(ScreenAdapter.width(32)),
          ],
        ));
  }

  Widget _bluetoothStatus() {
    return Container(
      margin: EdgeInsets.symmetric(
          horizontal: ScreenAdapter.width(16),
          vertical: ScreenAdapter.height(24)),
      padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
      height: ScreenAdapter.height(52),
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.greyBlue03,
        borderRadius: BorderRadius.circular(ScreenAdapter.width(8)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(T.bluetooth.tr, style: normalF14H19C333),

          InkWell(
            onTap: () {
              controller.bluetoothController.aizoDisconnect();
            },
            child: Container(
              // width: ScreenAdapter.width(100),
              decoration: BoxDecoration(
                // color: AppColors.lightBlue,
                borderRadius: BorderRadius.circular(ScreenAdapter.width(20)),
                
                
                image: DecorationImage(
                  image: Image.asset(Assets.images.measureButton.path).image,
                  fit: BoxFit.fitHeight,
                ),
              ),
              
              padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16),vertical: ScreenAdapter.height(4)),
              child: Text(T.bluetoothUnbind.tr, style: normalF14H19C333.copyWith(color: AppColors.colorWhite,fontWeight: FontWeight.w500)),
            )
          ),
          // CupertinoSwitch(value: true, onChanged: (value) {}),
          // Transform.scale(
          //   scale: ScreenAdapter.width(42) /
          //       ScreenAdapter.width(51), // 51 是 CupertinoSwitch 的默认宽度
          //   child: Obx(() => CupertinoSwitch(
          //         value: controller.bluetoothController.isConnected.value
          //             ? true
          //             : false,
          //         onChanged: (v) {
          //           // logger.d("查看蓝牙控制器里面的设备:");
          //           // logger.d(controller.bluetoothController.lastConnectedDevice);
          //           if (controller.device.value == null) return;
          //           if (v) {
          //             //从内存中获取设备
          //             // MyBluetoothDevice device = controller.bluetoothController.device.value;

          //             controller.bluetoothController
          //                 .aizoConnect(controller.device.value!);
          //           } else {
          //             controller.bluetoothController.aizoDisconnect();
          //           }
          //         },
          //       )),
          // )
        ],
      ),
    );
  }

  Widget _equipmentStatus() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: ScreenAdapter.width(32)),
          child: Text(T.equipmentStatus.tr, style: normalF14H19C333),
        ),
        Gap(ScreenAdapter.height(4)),
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
          padding: EdgeInsets.symmetric(
              horizontal: ScreenAdapter.width(16),
              vertical: ScreenAdapter.height(16)),
          decoration: BoxDecoration(
            color: AppColors.greyBlue03,
            borderRadius: BorderRadius.circular(ScreenAdapter.width(8)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(T.macAddress.tr, style: normalF14H19C333),
                  Obx(() => Text(
                      controller.device.value == null
                          ? T.wordsNone.tr
                          : controller.device.value!.remoteId.toString(),
                      style: normalF14H19C999)),
                ],
              ),
              Container(
                margin:
                    EdgeInsets.symmetric(vertical: ScreenAdapter.height(12)),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.recordBdColor,
                    width: 1,
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(T.aiRingConnectionStatus.tr, style: normalF14H19C333),
                  Obx(() => controller.bluetoothController.isConnected.value
                      ? Text(T.bluetoothIsConnected.tr, style: normalF14H19C999)
                      : Text(T.bluetoothNotConnected.tr,
                          style: normalF14H19C999)),
                ],
              ),
              Container(
                margin:
                    EdgeInsets.symmetric(vertical: ScreenAdapter.height(12)),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.recordBdColor,
                    width: 1,
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(T.airingBatteryStatus.tr, style: normalF14H19C333),
                  //这里有3中状态，充电中，充电完成，未充电
                  //用switch完成

                  Obx(() {
                    logger.d(controller.bluetoothController.aizoBatteryStatus);
                    return controller.bluetoothController.aizoBatteryStatus ==
                            AizoBatteryStatus.CHARGING
                        ? Text(T.bluetoothCharging.tr, style: normalF14H19C999)
                        : controller.bluetoothController.aizoBatteryStatus ==
                                AizoBatteryStatus.FULL_CHARGED
                            ? Text(T.bluetoothCharged.tr,
                                style: normalF14H19C999)
                            : Text(T.bluetoothNotCharged.tr,
                                style: normalF14H19C999);
                  })
                ],
              ),
              Container(
                margin:
                    EdgeInsets.symmetric(vertical: ScreenAdapter.height(12)),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.recordBdColor,
                    width: 1,
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(T.airingBatteryLevel.tr, style: normalF14H19C333),
                  Obx(() => Text(
                      "${controller.bluetoothController.aizoBatteryLevel}%",
                      style: normalF14H19C999)),
                ],
              ),
              Container(
                margin:
                    EdgeInsets.symmetric(vertical: ScreenAdapter.height(12)),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.recordBdColor,
                    width: 1,
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(T.measurementResult.tr, style: normalF14H19C333),
                  Obx(() => Text(controller.measurementResult.value,
                      style: normalF14H19C999)),
                ],
              ),
              Container(
                margin:
                    EdgeInsets.symmetric(vertical: ScreenAdapter.height(12)),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.recordBdColor,
                    width: 1,
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(T.currentMeasurementStatus.tr, style: normalF14H19C333),
                  Obx(() => Text(controller.measurementStatus.value,
                      style: normalF14H19C999)),
                ],
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _measurementControl() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: ScreenAdapter.width(32)),
          child: Text(T.measurementControl.tr, style: normalF14H19C333),
        ),
        Gap(ScreenAdapter.height(4)),
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
          padding: EdgeInsets.symmetric(
              // horizontal: ScreenAdapter.width(16),
              // vertical: ScreenAdapter.height(16)
              ),
          decoration: BoxDecoration(
            color: AppColors.greyBlue03,
            borderRadius: BorderRadius.circular(ScreenAdapter.width(8)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Obx(
                () => InkWell(
                  onTap: () {
                    controller.measure(0);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: controller.selectedIndex.value == 0
                          ? AppColors.lightBlue.withOpacity(0.12)
                          : Colors.transparent,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(ScreenAdapter.width(8)),
                        topRight: Radius.circular(ScreenAdapter.width(8)),
                      ),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreenAdapter.width(16)),
                    height: ScreenAdapter.height(48),
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child: Text(T.measureHeartRate.tr, style: normalF14H19C333),
                  ),
                ),
              ),
              _divider(),
              Obx(
                () => InkWell(
                  onTap: () {
                    controller.measure(1);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: controller.selectedIndex.value == 1
                          ? AppColors.lightBlue.withOpacity(0.12)
                          : Colors.transparent,
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreenAdapter.width(16)),
                    height: ScreenAdapter.height(48),
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child:
                        Text(T.measureBloodOxygen.tr, style: normalF14H19C333),
                  ),
                ),
              ),
              _divider(),
              Obx(
                () => InkWell(
                  onTap: () {
                    controller.measure(2);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: controller.selectedIndex.value == 2
                          ? AppColors.lightBlue.withOpacity(0.12)
                          : Colors.transparent,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(ScreenAdapter.width(8)),
                        bottomRight: Radius.circular(ScreenAdapter.width(8)),
                      ),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreenAdapter.width(16)),
                    height: ScreenAdapter.height(48),
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child:
                        Text(T.measureTemperature.tr, style: normalF14H19C333),
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _measurementInterval() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: ScreenAdapter.width(32)),
          child: Text(T.measurementInterval.tr, style: normalF14H19C333),
        ),
        Gap(ScreenAdapter.height(4)),
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
          padding: EdgeInsets.symmetric(
              // horizontal: ScreenAdapter.width(16),
              // vertical: ScreenAdapter.height(4)
              ),
          decoration: BoxDecoration(
            color: AppColors.greyBlue03,
            borderRadius: BorderRadius.circular(ScreenAdapter.width(8)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                  onTap: () {
                    controller.setMeasureInterval(10);
                    // controller.bluetoothController.aizoSetMeasureInterval(10);
                  },
                  child: Obx(
                    () => Container(
                      height: ScreenAdapter.height(44),
                      decoration: BoxDecoration(
                        color: controller.bluetoothController
                                    .aizoCurrentInterval.value ==
                                10
                            ? AppColors.lightBlue.withOpacity(0.12)
                            : Colors.transparent,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(ScreenAdapter.width(8)),
                          topRight: Radius.circular(ScreenAdapter.width(8)),
                        ),
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: ScreenAdapter.width(16)),
                      alignment: Alignment.centerLeft,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(T.set10MinutesInterval.tr,
                              style: normalF14H19C333),
                          controller.bluetoothController.aizoCurrentInterval
                                      .value ==
                                  10
                              ? Image.asset(
                                  Assets.images.selectedIcon.path,
                                  height: ScreenAdapter.height(20),
                                  fit: BoxFit.fitHeight,
                                )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  )),
              _divider(),
              InkWell(
                  onTap: () {
                    controller.setMeasureInterval(20);
                  },
                  child: Obx(
                    () => Container(
                      height: ScreenAdapter.height(44),
                      decoration: BoxDecoration(
                        color: controller.bluetoothController
                                    .aizoCurrentInterval.value ==
                                20
                            ? AppColors.lightBlue.withOpacity(0.12)
                            : Colors.transparent,
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: ScreenAdapter.width(16)),
                      alignment: Alignment.centerLeft,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(T.set20MinutesInterval.tr,
                              style: normalF14H19C333),
                          controller.bluetoothController.aizoCurrentInterval
                                      .value ==
                                  20
                              ? Image.asset(
                                  Assets.images.selectedIcon.path,
                                  height: ScreenAdapter.height(20),
                                  fit: BoxFit.fitHeight,
                                )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  )),
              _divider(),
              InkWell(
                  onTap: () {
                    controller.setMeasureInterval(30);
                  },
                  child: Obx(
                    () => Container(
                      height: ScreenAdapter.height(44),
                      decoration: BoxDecoration(
                        color: controller.bluetoothController
                                    .aizoCurrentInterval.value ==
                                30
                            ? AppColors.lightBlue.withOpacity(0.12)
                            : Colors.transparent,
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(ScreenAdapter.width(8)),
                          bottomRight: Radius.circular(ScreenAdapter.width(8)),
                        ),
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: ScreenAdapter.width(16)),
                      alignment: Alignment.centerLeft,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(T.set30MinutesInterval.tr,
                              style: normalF14H19C333),
                          controller.bluetoothController.aizoCurrentInterval
                                      .value ==
                                  30
                              ? Image.asset(
                                  Assets.images.selectedIcon.path,
                                  height: ScreenAdapter.height(20),
                                  fit: BoxFit.fitHeight,
                                )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  )),
            ],
          ),
        )
      ],
    );
  }

  Widget _dataRetrieval() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: ScreenAdapter.width(32)),
          child: Text(T.dataRetrieval.tr, style: normalF14H19C333),
        ),
        Gap(ScreenAdapter.height(4)),
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
          // padding: EdgeInsets.symmetric(
          //     horizontal: ScreenAdapter.width(16),
          //     vertical: ScreenAdapter.height(16)),
          decoration: BoxDecoration(
            color: AppColors.greyBlue03,
            borderRadius: BorderRadius.circular(ScreenAdapter.width(8)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Obx(
                () => InkWell(
                  onTap: () {
                    controller.aizoGetHealthData();
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: controller.selectedIndex.value == 0
                          ? AppColors.lightBlue.withOpacity(0.12)
                          : Colors.transparent,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(ScreenAdapter.width(8)),
                        topRight: Radius.circular(ScreenAdapter.width(8)),
                      ),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreenAdapter.width(16)),
                    height: ScreenAdapter.height(48),
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child: Text(T.getHealthData.tr, style: normalF14H19C333),
                  ),
                ),
              ),
              _divider(),
              Obx(
                () => InkWell(
                  onTap: () {
                    controller.aizoGetSleepData();
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: controller.selectedIndex.value == 0
                          ? AppColors.lightBlue.withOpacity(0.12)
                          : Colors.transparent,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(ScreenAdapter.width(8)),
                        topRight: Radius.circular(ScreenAdapter.width(8)),
                      ),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreenAdapter.width(16)),
                    height: ScreenAdapter.height(48),
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child: Text(T.getSleepData.tr, style: normalF14H19C333),
                  ),
                ),
              ),
              _divider(),
              Obx(
                () => InkWell(
                  onTap: () {
                    // controller.measure(0);
                    // controller.bluetoothController.aizoGetHardwareInfo();
                    controller.aizoGetHardwareInfo();
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: controller.selectedIndex.value == 0
                          ? AppColors.lightBlue.withOpacity(0.12)
                          : Colors.transparent,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(ScreenAdapter.width(8)),
                        topRight: Radius.circular(ScreenAdapter.width(8)),
                      ),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreenAdapter.width(16)),
                    height: ScreenAdapter.height(48),
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child: Text(T.getHardwareInfo.tr, style: normalF14H19C333),
                  ),
                ),
              ),
              // _divider(),
              // Obx(
              //   () => 
              InkWell(
                  onTap: () {
                    // controller.measure(0);
                    // controller.bluetoothController.aizoGetHardwareInfo();
                    controller.test();
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: controller.selectedIndex.value == 0
                          ? AppColors.lightBlue.withOpacity(0.12)
                          : Colors.transparent,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(ScreenAdapter.width(8)),
                        topRight: Radius.circular(ScreenAdapter.width(8)),
                      ),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreenAdapter.width(16)),
                    height: ScreenAdapter.height(48),
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child: Text(T.wordsAll.tr, style: normalF14H19C333),
                  ),
                ),
              // ),
          
            ],
          ),
        )
      ],
    );
  }

  Widget _divider() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: ScreenAdapter.height(16)),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.recordBdColor,
          width: 1,
        ),
      ),
    );
  }
}
