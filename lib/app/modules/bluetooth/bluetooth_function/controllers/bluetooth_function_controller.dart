import 'dart:io';
import 'dart:ui';

import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

class BluetoothFunctionController extends BaseController {
  BluetoothController bluetoothController = Get.find();
  Rx<MyBluetoothDevice?> device = Rx<MyBluetoothDevice?>(null);
  RxInt selectedIndex = 99.obs;
  RxString measurementResult = "".obs;
  RxString measurementStatus = "".obs;

  @override
  void onInit() {
    super.onInit();
    device.value = bluetoothController.lastConnectedDevice;
    measurementResult.value = T.commonNoData.tr;
    measurementStatus.value = T.wordsIdle.tr;
     bluetoothController.setupMeasurementTimer();
    logger.d("蓝牙当前测量间隔: ${bluetoothController.aizoCurrentInterval}");
    ever(bluetoothController.isConnected, (connected) {
      if (!connected) {
        hideLoading();

      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void measure(int i) {
    selectedIndex.value = i;
    showLoading();
    switch (i) {
      case 0:
        bluetoothController.aizoMeasureHeartRate(
            setMeasurement: setMeasurement);
        break;
      case 1:
        bluetoothController.aizoMeasureSpO2(setMeasurement: setMeasurement);
        break;
      case 2:
        bluetoothController.aizoMeasureTemperature(
            setMeasurement: setMeasurement);
        break;
      default:
        break;
    }
    //延时0.2s
    Future.delayed(Duration(milliseconds: 500), () {
      selectedIndex.value = 99;
      hideLoading();
      if(Platform.isAndroid){
        measurementStatus.value = T.wordsStart.tr;
      }
    });
    // selectedColor.value = Colors.transparent;
  }

  void setMeasurement(String value) {
    logger.d("查看setMeasurement: $value");
    if(Platform.isIOS){
      if (value.contains("in progress")) {
      measurementStatus.value = T.measurementInProgress.tr;
    } else if (value.contains("started")) {
      measurementStatus.value = T.measurementStarted.tr;
    } else if (value.contains("successful")) {

        final match = RegExp(r'successful:\s*\{(.+)\}').firstMatch(value);
      if (match != null) {
        final content = match.group(1)!;
        final fields = content.split(',').map((e) => e.trim()).toList();
        final Map<String, dynamic> resultMap = {};
        for (var field in fields) {
          final kv = field.split(':');
          if (kv.length == 2) {
            final key = kv[0].trim();
            final val = kv[1].trim();
            resultMap[key] = num.tryParse(val) ?? val;
          }
        }
        // 你可以用 resultMap['measurementResult']、resultMap['measurementTime']、resultMap['measureType']
        //设置1位小数
        measurementResult.value =
            resultMap['measurementResult'].toStringAsFixed(1);
        measurementStatus.value = T.wordsFinish.tr;
        HomeController homeController = Get.find();
        print("设置为全 true");
        setStorage();
         homeController.fetchAllData();
        

    } 
    }else if (value.contains("error")) {
      measurementStatus.value = T.measurementError.tr;
    }
    }else if(Platform.isAndroid){
      logger.d("查看Android的value: $value");
      if (value.contains("successful")) {
        // Android: value is like 'successful: 70'
        final match = RegExp(r'successful:\s*(.+)').firstMatch(value);
        if (match != null) {
          final raw = match.group(1)!.trim();
          final numValue = num.tryParse(raw);
          final result = numValue != null ? numValue.toStringAsFixed(1) : raw;
          measurementResult.value = result;
          measurementStatus.value = T.wordsFinish.tr;
        }
        HomeController homeController = Get.find();
        print("设置为全 true");
        setStorage();
         homeController.fetchAllData();
      } else if (value.contains("error")) {
        measurementStatus.value = T.measurementError.tr;
      } else if (value.contains("in progress")) {
        measurementStatus.value = T.measurementInProgress.tr;
      } else if (value.contains("started")) {
        measurementStatus.value = T.measurementStarted.tr;
      }
    }
    
  }

  setStorage(){
    storage.setBool(AppValues.heartRateLineDaysRefresh, true);
    storage.setBool(AppValues.heartRateLineWeeksRefresh, true);
    storage.setBool(AppValues.heartRateLineMonthsRefresh, true);
  }

  Future<void> setMeasureInterval(int interval) async {
    showLoading();
    //得到值，如果是 false ，就弹出提示窗，设置失败
    final bool result =
        await bluetoothController.aizoSetMeasureInterval(interval);
    if (!result) {
      ToastUtil.showError(Get.context!, T.setMeasureIntervalFailed.tr);
    }
    hideLoading();
  }

  void aizoGetHealthData() async {
    if (!bluetoothController.isConnected.value) {
      ToastUtil.showError(Get.context!, T.bluetoothNotConnected.tr);
      return;
    }
    showLoading();

    //获取结果
    final result = await bluetoothController.bluetoothRepository
        .aizoGetHealthData(DateTime.now());
    hideLoading();
    _customBottomSheet(result.toString());
  }

  void aizoGetSleepData() async {
    if (!bluetoothController.isConnected.value) {
      ToastUtil.showError(Get.context!, T.bluetoothNotConnected.tr);
      return;
      
    }
    showLoading();

    //获取结果
    final result = await bluetoothController.bluetoothRepository
        .aizoGetSleepData(DateTime.now());
    hideLoading();

    _customBottomSheet(result.toString());
  }

  void aizoGetHardwareInfo() async {
    if (!bluetoothController.isConnected.value) {
      ToastUtil.showError(Get.context!, T.bluetoothNotConnected.tr);
      return;
    }
    showLoading();

    //获取结果
    final result =
        await bluetoothController.bluetoothRepository.aizoGetHardwareData();
    hideLoading();
    _customBottomSheet(result);
  }

  void _customBottomSheet(String result) {
    double statusBarHeight = MediaQuery.of(Get.context!).padding.top;
    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true, // 关键点：允许内容撑满屏幕
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          expand: false, // 不强制全屏，可拖动
          initialChildSize: 1, // 初始高度占满
          minChildSize: 0.3, // 最小高度
          maxChildSize: 1, // 最大高度
          builder: (context, scrollController) {
            return Container(
              padding: EdgeInsets.only(top: statusBarHeight),
              width: double.infinity,
              child: SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  children: [
                    Text(result.toString()),
                    // 这里可以放你想展示的内容
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void test() async{
    if (bluetoothController.isConnected.value) {
      showLoading();
      // bluetoothController.bluetoothRepository.aizoGetAllData();
      await bluetoothController.aizoMeasureHeartRate(
          setMeasurement: testSetMeasurement);

      hideLoading();
    } else {
      ToastUtil.showError(Get.context!, T.bluetoothNotConnected.tr);
    }
  }

  void testSetMeasurement(String value) {
    logger.d("查看setMeasurement: $value");
    if (value.contains("in progress")) {
      // measurementStatus.value = T.measurementInProgress.tr;
      // return;
    }else
    if (value.contains("started")) {
      // measurementStatus.value = T.measurementStarted.tr;
    } else if (value.contains("successful")) {
      // final match = RegExp(r'successful:\s*\{(.+)\}').firstMatch(value);
      // if (match != null) {
      //   final content = match.group(1)!;
      //   final fields = content.split(',').map((e) => e.trim()).toList();
      //   final Map<String, dynamic> resultMap = {};
      //   for (var field in fields) {
      //     final kv = field.split(':');
      //     if (kv.length == 2) {
      //       final key = kv[0].trim();
      //       final val = kv[1].trim();
      //       resultMap[key] = num.tryParse(val) ?? val;
      //     }
      //   }
      //   // 你可以用 resultMap['measurementResult']、resultMap['measurementTime']、resultMap['measureType']
      //   //设置1位小数
      //   measurementResult.value =
      //       resultMap['measurementResult'].toStringAsFixed(1);
      //   measurementStatus.value = T.wordsFinish.tr;

      // }
      
      //获取最新值
      HomeController homeController = Get.find();
      homeController.fetchAllData();
      ToastUtil.showSuccess(Get.context!, T.measurementSuccess.tr);
    } else if (value.contains("error")) {
      // measurementStatus.value = T.measurementError.tr;
      ToastUtil.showError(Get.context!, T.measurementError.tr);
    }
  }

}
