/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-31 10:54:14
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-05-08 07:52:47
 * @FilePath: /RPM-APP-MASTER/lib/app/routes/app_routes.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();

  static const TEST_COMPONENT = _Paths.TEST_COMPONENT;

  static const TABS = _Paths.TABS;
  static const LOGIN = _Paths.LOGIN;
  static const SIGNUP = _Paths.SIGNUP;
  static const RELATED_INFOR = _Paths.RELATED_INFOR;
  static const GUIDE_HOME = _Paths.GUIDE_HOME;
  static const MANUAL_INPUT = _Paths.MANUAL_INPUT;

  static const OXYGEN_HOME = _Paths.OXYGEN_HOME;
  static const OXYGEN_RECORD_HOME = _Paths.OXYGEN_HOME + _Paths.RECORD_HOME;
  static const OXYGEN_RECORD_DETAIL = _Paths.OXYGEN_HOME + _Paths.RECORD_DETAIL;
  static const OXYGEN_REMIND_HOME = _Paths.OXYGEN_HOME + _Paths.REMIND_HOME;
  static const OXYGEN_REMIND_REPEAT = _Paths.OXYGEN_HOME + _Paths.REMIND_REPEAT;
  static const OXYGEN_REMIND_EDIT = _Paths.OXYGEN_HOME + _Paths.REMIND_EDIT;

  static const PRESSURE_HOME = _Paths.PRESSURE_HOME;
  static const PRESSURE_REMIND_HOME = _Paths.PRESSURE_HOME + _Paths.REMIND_HOME;
  static const PRESSURE_REMIND_EDIT =
      _Paths.PRESSURE_HOME + _Paths.REMIND_EDIT; // 添加这一行
  static const PRESSURE_REMIND_REPEAT =
      _Paths.PRESSURE_HOME + _Paths.REMIND_REPEAT;
  static const PRESSURE_RECORD_HOME = _Paths.PRESSURE_HOME + _Paths.RECORD_HOME;
  static const PRESSURE_RECORD_DETAIL =
      _Paths.PRESSURE_HOME + _Paths.RECORD_DETAIL;
  static const PRESSURE_TARGET = _Paths.PRESSURE_HOME + _Paths.TARGET_HOME;

  static const TEMPERATURE_HOME = _Paths.TEMPERATURE_HOME;
  static const TEMPERATURE_RECORD_HOME =
      _Paths.TEMPERATURE_HOME + _Paths.RECORD_HOME;
  static const TEMPERATURE_REMIND_HOME =
      _Paths.TEMPERATURE_HOME + _Paths.REMIND_HOME;
  static const TEMPERATURE_REMIND_EDIT =
      _Paths.TEMPERATURE_HOME + _Paths.REMIND_EDIT;
  static const TEMPERATURE_RECORD_DETAIL =
      _Paths.TEMPERATURE_HOME + _Paths.RECORD_DETAIL;

  static const WEIGHT_HOME = _Paths.WEIGHT_HOME;
  static const WEIGHT_TARGET = _Paths.WEIGHT_HOME + _Paths.TARGET_HOME;
  static const WEIGHT_REMIND_HOME = _Paths.WEIGHT_HOME + _Paths.REMIND_HOME;
  static const WEIGHT_REMIND_EDIT = _Paths.WEIGHT_HOME + _Paths.REMIND_EDIT;
  static const WEIGHT_REMIND_REPEAT = _Paths.WEIGHT_HOME + _Paths.REMIND_REPEAT;

  static const SUGAR_HOME = _Paths.SUGAR_HOME;
  static const SUGAR_RECORD = _Paths.SUGAR_HOME + _Paths.SUGAR_RECORD;
  static const SUGAR_REMINDER = _Paths.SUGAR_HOME + _Paths.SUGAR_REMINDER;
  static const SUGAR_TARGET = _Paths.SUGAR_HOME + _Paths.SUGAR_TARGET;

  static const RATE_HOME = _Paths.RATE_HOME;
  static const RATE_REMIND_HOME = _Paths.RATE_HOME + _Paths.REMIND_HOME;
  static const RATE_REMIND_EDIT = _Paths.RATE_HOME + _Paths.REMIND_EDIT;
  static const RATE_REMIND_REPAT = _Paths.RATE_HOME + _Paths.REMIND_REPEAT;
  static const RATE_RECORD_HOME = _Paths.RATE_HOME + _Paths.RECORD_HOME;
  static const RATE_RECORD_DETAIL = _Paths.RATE_HOME + _Paths.RECORD_DETAIL;

  static const BLUETOOTH_FIRST = _Paths.BLUETOOTH_FIRST;
  static const BLUETOOTH_CONNECT = _Paths.BLUETOOTH_CONNECT;
  static const ABNORMAL_REMIND = _Paths.ABNORMAL_REMIND;

  static const USER_INFO = _Paths.TABS + _Paths.USER_INFO;
  static const USER_MODIF = _Paths.TABS + _Paths.USER_MODIF;
  static const ACCOUNT_SECURITY = _Paths.ACCOUNT_SECURITY;
  static const GOAL_SETTING = _Paths.GOAL_SETTING;
  static const UNIT_SETTING = _Paths.UNIT_SETTING;
  static const BLUETOOTH_DEVICES = _Paths.BLUETOOTH_DEVICES;
  static const BLUETOOTH_INFO = _Paths.BLUETOOTH_INFO;
  static const HEALTH_INFO = _Paths.HEALTH_INFO;
  static const HEALTH_REPORT = _Paths.HEALTH_REPORT;
  static const STEP_HOME = _Paths.STEP_HOME;
  static const SLEEP_HOME = _Paths.SLEEP_HOME;
  static const FITNESS_HOME = _Paths.FITNESS_HOME;
  static const BLUETOOTH_FUNCTION = _Paths.BLUETOOTH_FUNCTION;
}

abstract class _Paths {
  _Paths._();
  static const TABS = '/tabs';
  static const LOGIN = '/login';
  static const SIGNUP = '/signup';
  static const RELATED_INFOR = '/related-infor';
  static const MANUAL_INPUT = '/manual-input';
  static const TEST_COMPONENT = '/test-component';
  static const GUIDE_HOME = '/guide-home';

  static const OXYGEN_HOME = '/oxygen-home';
  static const OXYGEN_RECORD_HOME = '/oxygen-record-home';

  static const PRESSURE_HOME = '/pressure-home';
  static const PRESSURE_RECORD_HOME = '/pressure-record-home';
  static const PRESSURE_REMINDER_HOME = '/pressure-remind-home';
  static const PRESSURE_REMINDER_EDIT = '/remind-edit'; // 添加这一行
  static const PRESSURE_REMINDER_REPEAT = '/remind-repeat';
  static const PRESSURE_TARGET = '/pressure-target';

  static const TEMPERATURE_HOME = '/temperature-home';
  static const TEMPERATURE_RECORD_HOME = '/temperature-record-home';
  static const TEMPERATURE_REMINDER_HOME = '/temperature-remind-home';
  static const TEMPERATURE_REMINDER_EDIT = '/remind-edit';

  static const WEIGHT_HOME = '/weight-home';

  static const WEIGHT_REMINDER_HOME = '/weight-reminder-home';
  static const WEIGHT_REMINDER_EDIT = '/remind-edit'; // 添加这一行
  static const WEIGHT_REMINDER_REPEAT = '/remind-repeat';

  static const BLUETOOTH = '/bluetooth';
  static const BLUETOOTH_FIRST = '/bluetooth-first';
  static const BLUETOOTH_CONNECT = '/bluetooth-connect';

  static const SUGAR_HOME = '/sugar-home';
  static const SUGAR_RECORD = '/sugar-record';
  static const SUGAR_REMINDER = '/sugar-reminder';
  static const SUGAR_TARGET = '/sugar-target';

  static const RATE_HOME = '/rate-home';

  static const RECORD_HOME = "/record-home";

  static const RECORD_DETAIL = '/record-detail';

  static const REMIND_HOME = '/remind-home';
  static const REMIND_EDIT = '/remind-edit';

  static const TARGET_HOME = '/target-home';

  static const REMIND_REPEAT = '/remind-repeat';

  static const ABNORMAL_REMIND = '/abnormal-remind';

  static const USER_INFO = '/user-info';
  static const USER_MODIF = '/user-modif';

  static const ACCOUNT_SECURITY = '/account-security';
  static const GOAL_SETTING = '/goal-setting';
  static const UNIT_SETTING = '/unit-setting';
  static const BLUETOOTH_DEVICES = '/bluetooth-devices';
  static const BLUETOOTH_INFO = '/bluetooth-info';
  static const HEALTH_INFO = '/health-info';
  static const HEALTH_REPORT = '/health-report';
  static const STEP_HOME = '/step-home';
  static const SLEEP_HOME = '/sleep-home';
  static const FITNESS_HOME = '/fitness-home';
  static const BLUETOOTH_FUNCTION = '/bluetooth-function';
}
