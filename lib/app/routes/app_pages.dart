import 'package:get/get.dart';

import '../core/service/storage_service.dart';
import '../modules/account_security/bindings/account_security_binding.dart';
import '../modules/account_security/views/account_security_view.dart';
import '../modules/blood_oxygen/oxygen_home/bindings/blood_oxygen_bindind.dart';
import '../modules/blood_oxygen/oxygen_home/views/blood_oxygen_view.dart';
import '../modules/blood_oxygen/oxygen_record/record_detail/bindings/oxygen_detail_binding.dart';
import '../modules/blood_oxygen/oxygen_record/record_detail/views/oxygen_detail_view.dart';
import '../modules/bluetooth/bluetooth_connect/bindings/bluetooth_connect_binding.dart';
import '../modules/bluetooth/bluetooth_connect/views/bluetooth_connect_view.dart';
import '../modules/bluetooth/bluetooth_devices/bindings/bluetooth_devices_binding.dart';
import '../modules/bluetooth/bluetooth_devices/views/bluetooth_devices_view.dart';
import '../modules/bluetooth/bluetooth_first/bindings/bluetooth_first_binding.dart';
import '../modules/bluetooth/bluetooth_first/views/bluetooth_first_view.dart';
import '../modules/bluetooth/bluetooth_function/bindings/bluetooth_function_binding.dart';
import '../modules/bluetooth/bluetooth_function/views/bluetooth_function_view.dart';
import '../modules/bluetooth/bluetooth_info/bindings/bluetooth_info_binding.dart';
import '../modules/bluetooth/bluetooth_info/views/bluetooth_info_view.dart';
import '../modules/fitness/fitness_home/bindings/fitness_home_binding.dart';
import '../modules/fitness/fitness_home/views/fitness_home_view.dart';
import '../modules/goal_setting/bindings/goal_setting_binding.dart';
import '../modules/goal_setting/views/goal_setting_view.dart';
import '../modules/heart_rate/rate_home/bindings/rate_home_binding.dart';
import '../modules/heart_rate/rate_home/views/rate_home_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/manual_input/bindings/manual_input_binding.dart';
import '../modules/manual_input/views/manual_input_view.dart';
import '../modules/related_infor/bindings/related_infor_binding.dart';
import '../modules/related_infor/views/related_infor_view.dart';
import '../modules/signup/bindings/signup_binding.dart';
import '../modules/signup/views/signup_view.dart';
import '../modules/sleep/sleep_home/bindings/sleep_home_binding.dart';
import '../modules/sleep/sleep_home/views/sleep_home_view.dart';
import '../modules/step/step_home/bindings/step_home_binding.dart';
import '../modules/step/step_home/views/step_home_view.dart';
import '../modules/tabs/bindings/tabs_binding.dart';
import '../modules/tabs/views/tabs_view.dart';
import '../modules/temperature/temperature_home/bindings/temperature_home_binding.dart';
import '../modules/temperature/temperature_home/views/temperature_home_view.dart';
import '../modules/temperature/temperature_record/temperature_detail/bindings/temperature_detail_binding.dart';
import '../modules/temperature/temperature_record/temperature_detail/views/temperature_detail_view.dart';
import '../modules/test_component/bindings/test_component_binding.dart';
import '../modules/test_component/views/test_component_view.dart';
import '../modules/unit_setting/bindings/unit_setting_binding.dart';
import '../modules/unit_setting/views/unit_setting_view.dart';
import '../modules/user/user_info/bindings/user_info_binding.dart';
import '../modules/user/user_info/views/user_info_view.dart';
import '../modules/user/user_modif/bindings/user_modif_binding.dart';
import '../modules/user/user_modif/views/user_modif_view.dart';
import '../modules/weight/weight_home/bindings/weight_home_binding.dart';
import '../modules/weight/weight_home/views/weight_home_view.dart';
import '../modules/weight/weight_target/bindings/weight_target_binding.dart';
import '../modules/weight/weight_target/views/weight_target_view.dart';

import '../modules/blood_oxygen/oxygen_record/record_home/bindings/record_home_binding.dart'
    as oxygen;
import '../modules/blood_oxygen/oxygen_record/record_home/views/record_home_view.dart'
    as oxygen;
import '../modules/blood_oxygen/oxygen_remind/remind_repeat/bindings/remind_repeat_binding.dart'
    as oxygen;
import '../modules/blood_oxygen/oxygen_remind/remind_repeat/views/remind_repeat_view.dart'
    as oxygen;
import '../modules/blood_oxygen/oxygen_remind/remind_home/bindings/remind_home_binding.dart'
    as oxygen;
import '../modules/blood_oxygen/oxygen_remind/remind_home/views/remind_home_view.dart'
    as oxygen;
import '../modules/blood_oxygen/oxygen_remind/remind_edit/bindings/remind_edit_binding.dart'
    as oxygen;
import '../modules/blood_oxygen/oxygen_remind/remind_edit/views/remind_edit_view.dart'
    as oxygen;
import '../modules/blood_pressure/pressure_record/record_home/bindings/record_home_binding.dart'
    as pressure;
import '../modules/blood_pressure/pressure_record/record_home/views/record_home_view.dart'
    as pressure;
import '../modules/blood_pressure/pressure_reminder/remind_repeat/bindings/remind_repeat_binding.dart'
    as pressure;
import '../modules/blood_pressure/pressure_reminder/remind_repeat/views/remind_repeat_view.dart'
    as pressure;
import '../modules/temperature/temperature_record/record_home/bindings/record_home_binding.dart'
    as temperature;
import '../modules/temperature/temperature_record/record_home/views/record_home_view.dart'
    as temperature;
import '../modules/blood_pressure/pressure_reminder/remind_home/bindings/remind_home_binding.dart'
    as pressure;
import '../modules/blood_pressure/pressure_reminder/remind_home/views/remind_home_view.dart'
    as pressure;
import '../modules/temperature/temperature_reminder/remind_home/bindings/remind_home_binding.dart'
    as temperature;
import '../modules/temperature/temperature_reminder/remind_home/views/remind_home_view.dart'
    as temperature;
import '../modules/blood_pressure/pressure_reminder/remind_edit/bindings/remind_edit_binding.dart'
    as pressure;
import '../modules/blood_pressure/pressure_reminder/remind_edit/views/remind_edit_view.dart'
    as pressure;
import '../modules/temperature/temperature_reminder/remind_edit/bindings/remind_edit_binding.dart'
    as temperature;
import '../modules/temperature/temperature_reminder/remind_edit/views/remind_edit_view.dart'
    as temperature;
import '../modules/weight/weight_reminder/reminder_home/bindings/reminder_home_binding.dart'
    as weight;
import '../modules/weight/weight_reminder/reminder_home/views/reminder_home_view.dart'
    as weight;
import '../modules/weight/weight_reminder/remind_edit/bindings/remind_edit_binding.dart'
    as weight;
import '../modules/weight/weight_reminder/remind_edit/views/remind_edit_view.dart'
    as weight;
import '../modules/weight/weight_reminder/remind_repeat/bindings/remind_repeat_binding.dart'
    as weight;
import '../modules/weight/weight_reminder/remind_repeat/views/remind_repeat_view.dart'
    as weight;

import '../modules/heart_rate/rate_record/record_detail/bindings/record_detail_binding.dart'
    as rate;
import '../modules/heart_rate/rate_record/record_detail/views/record_detail_view.dart'
    as rate;
import '../modules/heart_rate/rate_reminder/remind_home/bindings/remind_home_binding.dart'
    as rate;
import '../modules/heart_rate/rate_reminder/remind_home/views/remind_home_view.dart'
    as rate;
import '../modules/heart_rate/rate_reminder/remind_edit/bindings/remind_edit_binding.dart'
    as rate;
import '../modules/heart_rate/rate_reminder/remind_edit/views/remind_edit_view.dart'
    as rate;

import '../modules/heart_rate/rate_reminder/remind_repat/bindings/remind_repat_binding.dart'
    as rate;
import '../modules/heart_rate/rate_reminder/remind_repat/views/remind_repat_view.dart'
    as rate;
import '../modules/heart_rate/rate_record/record_home/bindings/record_home_binding.dart'
    as rate;
import '../modules/heart_rate/rate_record/record_home/views/record_home_view.dart'
    as rate;

/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-31 10:54:14
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-08-23 10:15:21
 * @FilePath: /RPM-APP-MASTER/lib/app/routes/app_pages.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static String INITIAL = Routes.LOGIN;

  static Future<void> initialize() async {
    final idToken = await SecureStorageService.instance.getIDToken();
    INITIAL = idToken != null ? Routes.TABS : Routes.LOGIN;
  }

  static final List<GetPage> routes = [
    GetPage(
        name: _Paths.TABS,
        page: () => TabsView(),
        binding: TabsBinding(),
        children: [
          GetPage(
            name: _Paths.USER_INFO,
            page: () => UserInfoView(),
            binding: UserInfoBinding(),
          ),
          GetPage(
            name: _Paths.USER_MODIF,
            page: () => UserModifView(),
            binding: UserModifBinding(),
          ),
        ]),
    GetPage(
      name: _Paths.LOGIN,
      page: () => LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.SIGNUP,
      page: () => SignUpView(),
      binding: SignupBinding(),
    ),
    // GetPage(
    //   name: _Paths.GUIDE_HOME,
    //   page: () => GuideHomeView(),
    //   binding: GuideHomeBinding(),
    // ),
    GetPage(
      name: _Paths.RELATED_INFOR,
      page: () => RelatedInforView(),
      binding: RelatedInforBinding(),
    ),
    GetPage(
      name: _Paths.TEST_COMPONENT,
      page: () => TestComponentView(),
      binding: TestComponentBinding(),
    ),
    GetPage(
      name: _Paths.MANUAL_INPUT,
      page: () => ManualInputView(),
      binding: ManualInputBinding(),
    ),
    //血氧
    GetPage(
        name: _Paths.OXYGEN_HOME,
        page: () => BloodOxygenView(),
        binding: BloodOxygenBinding(),
        children: [
          GetPage(
            name: _Paths.RECORD_HOME,
            page: () => oxygen.RecordHomeView(),
            binding: oxygen.RecordHomeBinding(),
          ),
          GetPage(
            name: _Paths.RECORD_DETAIL,
            page: () => OxygenDetailView(),
            binding: OxygenDetailBinding(),
          ),
          // GetPage(
          //   name: _Paths.REMIND_REPEAT,
          //   page: () => oxygen.RemindRepeatView(),
          //   binding: oxygen.RemindRepeatBinding(),
          // ),
          // GetPage(
          //   name: _Paths.REMIND_HOME,
          //   page: () => oxygen.RemindHomeView(),
          //   binding: oxygen.RemindHomeBinding(),
          // ),
          // GetPage(
          //     name: _Paths.REMIND_EDIT,
          //     page: () => oxygen.RemindEditView(),
          //     binding: oxygen.RemindEditBinding(),
          //     transition: Transition.downToUp),
        ]),
    // //血压
    // GetPage(
    //   name: _Paths.PRESSURE_HOME,
    //   page: () => PressureHomeView(),
    //   binding: PressureHomeBinding(),
    //   children: [
    //     GetPage(
    //         name: _Paths.REMIND_EDIT,
    //         page: () => pressure.RemindEditView(),
    //         binding: pressure.RemindEditBinding(),
    //         transition: Transition.downToUp),
    //     GetPage(
    //         name: _Paths.REMIND_HOME,
    //         page: () => pressure.RemindHomeView(),
    //         binding: pressure.RemindHomeBinding(),
    //         transition: Transition.downToUp),
    //     GetPage(
    //       name: _Paths.REMIND_REPEAT,
    //       page: () => pressure.RemindRepeatView(),
    //       binding: pressure.RemindRepeatBinding(),
    //     ),
    //     GetPage(
    //       name: _Paths.REMIND_HOME,
    //       page: () => pressure.RemindHomeView(),
    //       binding: pressure.RemindHomeBinding(),
    //     ),
    //     GetPage(
    //       name: _Paths.REMIND_EDIT,
    //       page: () => pressure.RemindEditView(),
    //       binding: pressure.RemindEditBinding(),
    //     ),
    //     GetPage(
    //       name: _Paths.TARGET_HOME,
    //       page: () => PressureTargetView(),
    //       binding: PressureTargetBinding(),
    //     ),
    //     GetPage(
    //       name: _Paths.RECORD_DETAIL,
    //       page: () => RecordDetailView(),
    //       binding: RecordDetailBinding(),
    //     ),
    //     GetPage(
    //       name: _Paths.RECORD_HOME,
    //       page: () => pressure.RecordHomeView(),
    //       binding: pressure.RecordHomeBinding(),
    //     ),
    //   ],
    // ),

    // //体温
    GetPage(
        name: _Paths.TEMPERATURE_HOME,
        page: () => TemperatureHomeView(),
        binding: TemperatureHomeBinding(),
        children: [
          GetPage(
            name: _Paths.RECORD_HOME,
            page: () => temperature.RecordHomeView(),
            binding: temperature.RecordHomeBinding(),
            children: [],
          ),
          GetPage(
            name: _Paths.RECORD_DETAIL,
            page: () => TemperatureDetailView(),
            binding: TemperatureDetailBinding(),
          ),
          // GetPage(
          //     name: _Paths.REMIND_EDIT,
          //     page: () => temperature.RemindEditView(),
          //     binding: temperature.RemindEditBinding(),
          //     transition: Transition.downToUp),
          // GetPage(
          //   name: _Paths.REMIND_HOME,
          //   page: () => temperature.RemindHomeView(),
          //   binding: temperature.RemindHomeBinding(),
          // ),
        ]),
    // //体重
    // GetPage(
    //     name: _Paths.WEIGHT_HOME,
    //     page: () => WeightHomeView(),
    //     binding: WeightHomeBinding(),
    //     children: [
    //       GetPage(
    //         name: _Paths.TARGET_HOME,
    //         page: () => WeightTargetView(),
    //         binding: WeightTargetBinding(),
    //       ),
    //       GetPage(
    //           name: _Paths.REMIND_EDIT,
    //           page: () => weight.RemindEditView(),
    //           binding: weight.RemindEditBinding(),
    //           transition: Transition.downToUp),
    //       GetPage(
    //         name: _Paths.REMIND_REPEAT,
    //         page: () => weight.RemindRepeatView(),
    //         binding: weight.RemindRepeatBinding(),
    //       ),
    //       GetPage(
    //         name: _Paths.WEIGHT_REMINDER_HOME,
    //         page: () => weight.ReminderHomeView(),
    //         binding: weight.ReminderHomeBinding(),
    //       ),
    //     ]),

    // //蓝牙
    GetPage(
      name: _Paths.BLUETOOTH_FIRST,
      page: () => BluetoothFirstView(),
      binding: BluetoothFirstBinding(),
    ),
    GetPage(
      name: _Paths.BLUETOOTH_CONNECT,
      page: () => BluetoothConnectView(),
      binding: BluetoothConnectBinding(),
    ),

    GetPage(
      name: _Paths.BLUETOOTH_DEVICES,
      page: () => BluetoothDevicesView(),
      binding: BluetoothDevicesBinding(),
    ),
    GetPage(
      name: _Paths.BLUETOOTH_INFO,
      page: () => BluetoothInfoView(),
      binding: BluetoothInfoBinding(),
    ),
    // //血糖
    // GetPage(
    //   name: _Paths.SUGAR_HOME,
    //   page: () => SugarHomeView(),
    //   binding: SugarHomeBinding(),
    //   children: [
    //     GetPage(
    //       name: _Paths.SUGAR_RECORD,
    //       page: () => const SugarRecordView(),
    //       binding: SugarRecordBinding(),
    //     ),
    //     GetPage(
    //       name: _Paths.SUGAR_REMINDER,
    //       page: () => const SugarReminderView(),
    //       binding: SugarReminderBinding(),
    //     ),
    //     GetPage(
    //       name: _Paths.SUGAR_TARGET,
    //       page: () => const SugarTargetView(),
    //       binding: SugarTargetBinding(),
    //     ),
    //   ],
    // ),
    //心率
    GetPage(
        name: _Paths.RATE_HOME,
        page: () => RateHomeView(),
        binding: RateHomeBinding(),
        children: [
          // GetPage(
          //   name: _Paths.REMIND_HOME,
          //   page: () => rate.RemindHomeView(),
          //   binding: rate.RemindHomeBinding(),
          // ),
          // GetPage(
          //     name: _Paths.REMIND_EDIT,
          //     page: () => rate.RemindEditView(),
          //     binding: rate.RemindEditBinding(),
          //     transition: Transition.downToUp),
          // GetPage(
          //   name: _Paths.REMIND_REPEAT,
          //   page: () => rate.RemindRepeatView(),
          //   binding: rate.RemindRepatBinding(),
          // ),
          GetPage(
            name: _Paths.RECORD_HOME,
            page: () => rate.RecordHomeView(),
            binding: rate.RecordHomeBinding(),
          ),
          GetPage(
            name: _Paths.RECORD_DETAIL,
            page: () => rate.RecordDetailView(),
            binding: rate.RecordDetailBinding(),
          ),
        ]),
    // GetPage(
    //   name: _Paths.ABNORMAL_REMIND,
    //   page: () => AbnormalRemindView(),
    //   binding: AbnormalRemindBinding(),
    // ),
    GetPage(
      name: _Paths.ACCOUNT_SECURITY,
      page: () => AccountSecurityView(),
      binding: AccountSecurityBinding(),
    ),
    GetPage(
      name: _Paths.GOAL_SETTING,
      page: () => GoalSettingView(),
      binding: GoalSettingBinding(),
    ),
    GetPage(
      name: _Paths.UNIT_SETTING,
      page: () => UnitSettingView(),
      binding: UnitSettingBinding(),
    ),

    // GetPage(
    //   name: _Paths.HEALTH_INFO,
    //   page: () => HealthInfoView(),
    //   binding: HealthInfoBinding(),
    // ),
    // GetPage(
    //   name: _Paths.HEALTH_REPORT,
    //   page: () => HealthReportView(),
    //   binding: HealthReportBinding(),
    // ),
    // GetPage(
    //   name: _Paths.STEP_HOME,
    //   page: () => StepHomeView(),
    //   binding: StepHomeBinding(),
    // ),
    GetPage(
      name: _Paths.SLEEP_HOME,
      page: () => SleepHomeView(),
      binding: SleepHomeBinding(),
    ),
    GetPage(
      name: _Paths.FITNESS_HOME,
      page: () => FitnessHomeView(),
      binding: FitnessHomeBinding(),
    ),

    GetPage(
      name: _Paths.BLUETOOTH_FUNCTION,
      page: () =>  BluetoothFunctionView(),
      binding: BluetoothFunctionBinding(),
    ),
  ];
}

// List<GetPage> addGlobalMiddleware(List<GetPage> pages) {
//   return pages.map((page) {
//     return GetPage(
//       name: page.name,
//       page: page.page,
//       binding: page.binding,
//       children: page.children,
//       middlewares: [GlobalMiddleware()], // 统一添加中间件
//     );
//   }).toList();
// }
