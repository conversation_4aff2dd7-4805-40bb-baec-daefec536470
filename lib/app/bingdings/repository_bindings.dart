import 'package:get/get.dart';
import 'package:aiCare/app/data/repository/auth_repository.dart';
import 'package:aiCare/app/data/repository/auth_repository_impl.dart';
import 'package:aiCare/app/data/repository/default_repository.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';

class RepositoryBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut<DefaultRepository>(
      () => DefaultRepositoryImpl(),
      tag: (DefaultRepository).toString(),
    );
    Get.lazyPut<AuthRepository>(
      () => AuthRepositoryImpl(),
      tag: (AuthRepository).toString(),
    );
  }
}
