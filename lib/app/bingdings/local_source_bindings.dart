import 'package:get/get.dart';

import '/app/data/local/preference/preference_manager.dart';
import '/app/data/local/preference/preference_manager_impl.dart';

class LocalSourceBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut<PreferenceManager>(
      () => PreferenceManagerImpl(),
      tag: (PreferenceManager).toString(),//使用 PreferenceManager 类名作为标记，确保依赖项在不同上下文中唯一。
      fenix: true,//设置为 true，确保依赖项在被释放后仍然可以重新创建（GetX 会保留它的定义）。
    );
  }
}
