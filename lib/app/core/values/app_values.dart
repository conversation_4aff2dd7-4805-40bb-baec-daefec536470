abstract class AppValues {
  static const String userInfo = "USER_INFO";
  static const String accessToken = "ACCESS_TOKEN";
  static const String idToken = "ID_TOKEN";
  static const String id = "ID";
  static const String phoneCode = "PHONE_CODE";
  static const String domestic = "DOMESTIC"; // 表明是否国内

  static const String aizoConnectionStatus = "AIZO_CONNECTION_STATUS";
  static const String aizoDevicesLast = "AIZO_DEVICES_LAST";

  static const String bluetoothFirst = "BLUETOOTH_FIRST";
  static const String bluetoothConnect = "BLUETOOTH_CONNECT";
  static const String bluetoothDataUpdate = "BLUETOOTH_DATA_UPDATE";
  static const String bluetoothList = "BLUETOOTH_LIST";
  static const String androidDeviceID = "ANDROID_DEVICE_ID";
  static const String temperatureUnit = "TEMPERATURE_UNIT";

  static const String bloodOxygenList = "BLOOD_OXYGEN_LIST";
  static const String bloodOxygenAverage = "BLOOD_OXYGEN_AVERAGE";
  static const String bloodOxygenLineDaysRefresh = "BLOOD_OXYGEN_LINE_DAYS_REFRESH";
  static const String bloodOxygenLineWeeksRefresh = "BLOOD_OXYGEN_LINE_WEEKS_REFRESH";
  static const String bloodOxygenLineMonthsRefresh = "BLOOD_OXYGEN_LINE_MONTHS_REFRESH";
  static const String bloodOxygenLineDays = "BLOOD_OXYGEN_LINE_DAYS";
  static const String bloodOxygenLineWeeks = "BLOOD_OXYGEN_LINE_WEEKS";
  static const String bloodOxygenLineMonths = "BLOOD_OXYGEN_LINE_MONTHS";

  static const String bodyTemperatureList = "BODY_TEMPERATURE_LIST";
  static const String bodyTemperatureAverage = "BODY_TEMPERATURE_AVERAGE";
  static const String bodyTemperatureLineDaysRefresh = "BODY_TEMPERATURE_LINE_DAYS_REFRESH";
  static const String bodyTemperatureLineWeeksRefresh = "BODY_TEMPERATURE_LINE_WEEKS_REFRESH";
  static const String bodyTemperatureLineMonthsRefresh = "BODY_TEMPERATURE_LINE_MONTHS_REFRESH";
  static const String bodyTemperatureLineDays = "BODY_TEMPERATURE_LINE_DAYS";
  static const String bodyTemperatureLineWeeks = "BODY_TEMPERATURE_LINE_WEEKS";
  static const String bodyTemperatureLineMonths = "BODY_TEMPERATURE_LINE_MONTHS";
   

  static const String heartRateList = "HEART_RATE_LIST";
  static const String heartRateLineDays = "HEART_RATE_LINE_DAYS";
  static const String heartRateLineDaysDate = "HEART_RATE_LINE_DAYS_DATE";
  static const String heartRateLineDaysRefresh = "HEART_RATE_LINE_DAYS_REFRESH";
  static const String heartRateLineWeeks = "HEART_RATE_LINE_WEEKS";
  static const String heartRateLineWeeksDate = "HEART_RATE_LINE_WEEKS_DATE";
  static const String heartRateLineWeeksRefresh = "HEART_RATE_LINE_WEEKS_REFRESH";
  static const String heartRateLineMonths = "HEART_RATE_LINE_MONTHS";
  static const String heartRateLineMonthsDate = "HEART_RATE_LINE_MONTHS_DATE";
  static const String heartRateLineMonthsRefresh = "HEART_RATE_LINE_MONTHS_REFRESH";
  static const String heartRateLineDateList = "HEART_RATE_LINE_DATE_LIST";
  

  static const String avatarLocal = "AVATAR_LOCAL";

  static const String locale="LOCALE";

  static const String unitSetting = "UNIT_SETTING";
  static const String goalSetting = "GOAL_SETTING";

  //aiRing
  static const String aiRingPairringRemoved = "(Pairing Removed)";  //ios:表示未进行取消系统配对
  static const String aiRingConnecting = "(Connecting)";        //连接中
  static const String aiRingNoPairDevice = "(No Pair device)";  //ios：表示配对已删除，就是刚断开连接，但是未进行取消系统配对
  static const String aiRingConneted = "(Connected)";        //已成功连接

  //aizoSleepData
  static const String aizoSleepLast = "AIZO_SLEEP_LAST";
  


  static const double padding = 16;
  static const double padding_zero = 0;
  static const double halfPadding = 8;
  static const double smallPadding = 10;
  static const double extraSmallPadding = 6;
  static const double largePadding = 24;
  static const double extraLargePadding = 32;
  static const double padding_4 = 4;
  static const double padding_2 = 2;
  static const double padding_3 = 3;
  static const double buttonVerticalPadding = 12;

  static const double margin = 16;
  static const double margin_zero = 0;
  static const double smallMargin = 8;
  static const double extraSmallMargin = 6;
  static const double largeMargin = 24;
  static const double margin_40 = 40;
  static const double margin_32 = 32;
  static const double margin_18 = 18;
  static const double margin_2 = 2;
  static const double margin_4 = 4;
  static const double margin_6 = 6;
  static const double margin_12 = 12;
  static const double margin_10 = 10;
  static const double margin_30 = 30;
  static const double margin_20 = 20;
  static const double extraLargeMargin = 36;
  static const double marginBelowVerticalLine = 64;
  static const double extraLargeSpacing = 96;

  static const double radius = 16;
  static const double radius_zero = 0;
  static const double smallRadius = 8;
  static const double radius_6 = 6;
  static const double radius_12 = 12;
  static const double largeRadius = 24;
  static const double roundedButtonRadius = 24;
  static const double extraLargeRadius = 36;

  static const double elevation = 16;
  static const double smallElevation = 8;
  static const double extraSmallElevation = 4;
  static const double largeElevation = 24;

  static const double circularImageDefaultSize = 90;
  static const double circularImageSize_30 = 30;
  static const double circularImageDefaultBorderSize = 0;
  static const double circularImageDefaultElevation = 0;
  static const double momentThumbnailDefaultSize = 80;
  static const double momentSmallThumbnailDefaultSize = 32;
  static const double collectionThumbnailDefaultSize = 150;
  static const double defaultViewPortFraction = 0.9;
  static const int defaultAnimationDuration = 300;
  static const double listBottomEmptySpace = 200;
  static const double maxButtonWidth = 496;
  static const double stackedImageDefaultBorderSize = 4;
  static const double stackedImageDefaultSpaceFactor = 0.4;
  static const double stackedImageDefaultSize = 30;

  static const double iconDefaultSize = 24;
  static const double emoticonDefaultSize = 22;
  static const double iconSize_20 = 20;
  static const double iconSize_22 = 22;
  static const double iconSize_18 = 18;
  static const double iconSmallSize = 16;
  static const double iconSmallerSize = 12;
  static const double iconSize_14 = 14;
  static const double iconSize_28 = 28;
  static const double iconLargeSize = 36;
  static const double iconExtraLargerSize = 96;
  static const double appBarIconSize = 32;

  static const double customAppBarSize = 144.0;
  static const double collapsedAppBarSize = 70.0;

  //logger配置 开始
  static const int loggerLineLength = 2000;
  static const int loggerErrorMethodCount = 8;
  static const int loggerMethodCount = 2;
  //logger配置 结束

  static const double fullViewPort = 1;
  static const double indicatorDefaultSize = 8;
  static const double indicatorShadowBlurRadius = 1;
  static const double indicatorShadowSpreadRadius = 0;
  static const double appbarActionRippleRadius = 50;
  static const double activeIndicatorSize = 8;
  static const double inactiveIndicatorSize = 10;
  static const double datePickerHeightOnIos = 270;
  static const int maxCharacterCountOfQuote = 108;
  static const double barrierColorOpacity = 0.4;

  static const int defaultPageSize = 10;
  static const int defaultPageNumber = 1;
  static const int defaultDebounceTimeInMilliSeconds = 1000;
  static const int defaultThrottleTimeInMilliSeconds = 500;

  static const double height_16 = 16;

  static const String apiRegionKey = "API_REGION_KEY";
  static const String isAutoUpdate = "IS_AUTO_UPDATE";
  static const String lastMeasurementKey = "LAST_MEASUREMENT_KEY";

  static const String aizoSleepLastUpdateTime = "AIZO_SLEEP_LAST_UPDATE_TIME";
  static const String aizoHealthLastUpdateTime = "AIZO_HEALTH_LAST_UPDATE_TIME";
}
