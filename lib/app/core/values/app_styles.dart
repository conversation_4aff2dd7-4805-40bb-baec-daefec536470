import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

// 定义全局变量
final PreferredSizeWidget lineLightGreey = PreferredSize(
  preferredSize: Size.fromHeight(ScreenAdapter.height(0.5)), // 设置边框的高度
  child: Container(
    color: AppColors.greyBgColor, // 设置边框颜色
    height: ScreenAdapter.height(0.5), // 设置边框高度
  ),
);

