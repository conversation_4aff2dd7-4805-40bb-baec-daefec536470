import 'package:flutter/material.dart';

abstract class AppColors {
  //login
  static const Color loginTitleColor = Color(0XFF181C22);
  static const Color loginSubTitleColor = Color(0XFF63A79E);
  static const Color loginInputBgColor = Color(0XFFF3F3F5);
  static const Color loginHintColor = Color(0XFF5B5E66);
  static const Color loginPrimaryColor = Color(0XFFF7F7F7);
  static const Color loginButtonBgColor = Color(0XFF14A5A5);
  static const Color loginOtherTextColor = Color(0XFF8E9099);
  static const Color loginRegionColor = Color(0XFF333333);
  static const Color loginRegionBorderColor = Color(0XFFEEEEEE);

  //error
  static const Color errorTextColor = Color(0XFFE74C4C);
  static const Color errorPoint = Color(0XFFFF5B54);

  //warning
  static const Color warningTextColor = Color(0XFFFFC672);
  static const Color warningPoint = Color(0XFFFFC672);

  //home
  static const Color homeBgColor = Color(0XFFFAFAFA);

  static const Color ColorF5 = Color(0XFFF5F5F5);
  static const Color Coloreee = Color(0XFFeeeeee);
  static const Color Colore2 = Color(0XFFe2e2e2);
  static const Color Color333 = Color(0XFF333333);
  static const Color Color444 = Color(0XFF444444);
  static const Color Color666 = Color(0XFF666666);
  static const Color Color999 = Color(0XFF999999);
  static const Color ColorEF = Color(0XFFEFEFEF);
  static const Color Color999S15 = Color(0X26999999);
  static const Color ColorA4S20 = Color(0X33A4A4A4);
  static const Color ColorA4S15 = Color(0X26A4A4A4);

  static const Color searchBdColor = Color(0xFFDDDDDD);
  static const Color recordBdColor = Color(0xFFF2F2F2);

  static const Color temperatureBgColor = Color(0xFFF1FFF7);
  static const Color temperaturePointColor = Color(0xFFD2D2D2);
  static const Color temperatureBdHaveColor = Color(0xFFBFFFE0);
  static const Color temperatureBdNormalColor = Color(0xFFE6E6E6);
  static const Color temperatureStart = Color(0xFF09E16D);
  static const Color temperatureStartS15 = Color(0x2609E16D);
  static const List<Color> temperatureList = [
    Color(0xFF00D269),
  ];

  static  Color topBarSuccessful = Color.fromRGBO(213, 243, 211,1);
  static  Color topBarTextSuccessful = Color(0xFF22c614);
  static  Color topBarFailed = Color.fromRGBO(251, 222, 222,1);
  static  Color topBarTextFailed = Color(0xFFFF5959);

  // list
  static const List<Color> recordList = [
    Color.fromARGB(255, 225, 209, 61), //low
    Color(0xFF1CE10A), // 绿色
    Color(0xFFFFBE36), // 黄色
    Color(0xFFC9A0FF), // 紫色
    Color(0xFFFF574F), // 红色
  ];
  static const List<Color> detailList = [
    Color(0xFF25E514), // 绿色
    Color(0xFFFFBE36), // 黄色
    Color(0xFFC9A0FF), // 紫色
    Color(0xFFFF574F), // 红色
  ];
  static const List<Color> pressureBgList = [
    Color.fromRGBO(247, 255, 158, 0.15),
    Color.fromRGBO(12, 247, 130, 0.15),
    Color.fromRGBO(206, 167, 255, 0.15),
    Color.fromRGBO(255, 223, 54, 0.15),
    Color.fromRGBO(255, 84, 75, 0.15),
    Color.fromRGBO(230, 230, 230, 0.15),
  ];
  static const List<Color> pressureBdList = [
    Color.fromRGBO(247, 255, 158, 0.45),
    Color.fromRGBO(12, 247, 130, 0.45),
    Color.fromRGBO(206, 167, 255, 0.45),
    Color.fromRGBO(255, 223, 54, 0.45),
    Color.fromRGBO(255, 84, 75, 0.45),
    Color.fromRGBO(230, 230, 230, 0.45),
  ];
  static const List<Color> pressureTextList = [
    Color.fromRGBO(247, 255, 158, 1),
    Color.fromRGBO(12, 247, 130, 1),
    Color.fromRGBO(206, 167, 255, 1),
    Color.fromRGBO(255, 223, 54, 1),
    Color.fromRGBO(255, 84, 75, 1),
    Color.fromRGBO(230, 230, 230, 1),
  ];

  static const List<Color> pressureTargetTextList = [
    Color(0xFFF7FF9E),
    Color(0xFF0CF782),
    Color(0xFFCEA7FF),
    Color(0xFFFFDF36),
    Color(0xFFFF544B),
  ];
  static const List<Color> weightTargetTextList = [
    Color(0xFFF7FF9E),
    Color(0xFF0CF782),
    Color(0xFFFBD66B),
    Color(0xFFFFDF36),
  ];

  static const List<Color> heartRateList=[
    Color(0xFFF8DA00),
    Color(0xFF06C659),
    Color(0xFFFC3031),
  ];

  static const Color pressureButton = Color(0xFFF4F4F4);
  static const Color pressureSys = Color(0xFFFFCE49);
  static const Color pressureDia = Color(0xFF3AC055);

  static const Color pressureRemindBg = Color.fromRGBO(25, 106, 255, 0.03);

  //search
  static const Color searchButton = Color.fromRGBO(27, 107, 255, 0.05);
  static const Color searchBgColor = Color(0xFFD9D9D9);

  static const Color recordBorder = Color(0XFFDCDCDC);

  static const Color lightBlue = Color(0XFF1B6BFF);
  static const Color lightBlueText = Color(0XFF4870F3);
  static const Color lightBlue05 = Color(0x0D1B6BFF);
  static  Color lightBlue07 = Color(0XFF1B6BFF).withOpacity(0.07);
  static const Color lightBlue15 = Color(0X261B6BFF);
  // static const Color lightBlue80 = Color(0XFF1B6BFF);
  // static const Color lightBlue80 = Color(0xCC1B6BFF);
  static const Color lightBlue80 = Color(0xFF5987f7);
  static  Color greyBlue03 = Color(0XFF196AFF).withOpacity(0.03);

  static const Color deleteColor = Color(0XFFFF5656);

  static const Color greyLineColor = Color(0xFFE0E0E0);
  static const Color greyF9Color = Color(0xFFF9F9F9);
  static const Color greyF6Color = Color(0xFFF6F6F6);

  //pie chat
  static const Color normalColor = Color(0XFF3AC055);
  static const Color mildColor = Color(0XFFFFD058);
  static const Color moderateColor = Color(0XFFC293FF);

  static const Color minuentColor = Color(0XFFF24242);

  //currency
  static const Color greyBgColor = Color(0XFFEEEEEE);

  // static const Color pageBackground = Color(0xFFFAFBFD);
  static const Color pageBackground = Color(0xFFFFFFFF);
  // static const Color statusBarColor = Color(0xFF38686A);
  static const Color appBarColor = Color(0xFF38686A);
  static const Color appBarIconColor = Color(0xFF000000);

  static const Color appBarTextColor = Color(0xFF000000);

  static const Color centerTextColor = Colors.grey;
  static const MaterialColor colorPrimarySwatch = Colors.cyan;
  static const Color colorPrimary = Color(0xFF38686A);
  static const Color colorAccent = Color(0xFF38686A);
  static const Color colorLightGreen = Color(0xFF00EFA7);
  static const Color colorWhite = Color(0xFFFFFFFF);
  static const Color lightGreyColor = Color(0xFFC4C4C4);
  static const Color errorColor = Color(0xFFAB0B0B);
  static const Color colorDark = Color(0xFF323232);

  static const Color buttonBgColor = colorPrimary;
  static const Color disabledButtonBgColor = Color(0xFFBFBFC0);
  static const Color defaultRippleColor = Color(0x0338686A);

  static const Color textColorPrimary = Color(0xFF323232);
  static const Color textColorSecondary = Color(0xFF9FA4B0);
  static const Color textColorTag = colorPrimary;
  static const Color textColorGreyLight = Color(0xFFABABAB);
  static const Color textColorGreyDark = Color(0xFF979797);
  static const Color textColorBlueGreyDark = Color(0xFF939699);
  static const Color textColorCyan = Color(0xFF38686A);
  static const Color textColorWhite = Color(0xFFFFFFFF);
  static Color searchFieldTextColor = const Color(0xFF323232).withOpacity(0.5);

  static const Color iconColorDefault = Colors.grey;

  static Color barrierColor = const Color(0xFF000000).withOpacity(0.5);

  static Color timelineDividerColor = const Color(0x5438686A);

  static const Color gradientStartColor = Colors.black87;
  static const Color gradientEndColor = Colors.transparent;
  static const Color silverAppBarOverlayColor = Color(0x80323232);

  static const Color switchActiveColor = colorPrimary;
  static const Color switchInactiveColor = Color(0xFFABABAB);
  static Color elevatedContainerColorOpacity = Colors.grey.withOpacity(0.5);
  static const Color suffixImageColor = Colors.grey;
}
