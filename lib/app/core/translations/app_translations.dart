import 'package:get/get.dart';
import 'translation_keys.dart';

/// 应用翻译类
/// 
/// 使用GetX的翻译功能管理多语言支持
/// 包含英文和中文两种语言的翻译
class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'en_US': {
      // 通用
      T.appName: 'RPM',
      T.loading: 'Loading...',
      T.success: 'Success',
      T.error: 'Error',
      T.confirm: 'Confirm',
      T.cancel: 'Cancel',
    

      // 权限
      T.permissionRequest: 'Permission Request',
      T.permissionRequestContent: 'We need these permissions to provide you with the best experience',
      T.goToSettings: 'Go to Settings',

      // 权限名称
      T.camera: 'Camera',
      T.photos: 'Photos',
      T.location: 'Location',
      T.notification: 'Notification',
      T.bluetooth: 'Bluetooth',

      // 登录相关
      T.login: 'Login',
      T.loginEmail: 'Login with Email',
      T.loginPhone: 'Login with Phone',
      T.loginOther: 'Other Login Methods',
      T.loginOut: 'Logout',
      T.signUp: 'Sign Up',
      T.signUpError: 'Sign Up Failed',
      T.signUpSuccess: 'Sign Up Success',
      T.loginSuccess: 'Login Successful',
      T.loginFailed: 'Login Failed',

      // 通用检查策略
      T.commonCheckPolicy: 'Check Policy',
      T.commonUserReadPolicy1: 'I have read and agree to the ',
      T.commonUserReadPolicy2: 'User Agreement',
      T.commonUserReadPolicy3: ' and ',
      T.commonUserReadPolicy4: 'Privacy Policy',
      T.commonDeleteRemind: 'Delete Reminder',
      T.commonDeleteRecord: 'Delete Record',
      T.commonAddRemind: 'Add Reminder',
      T.commonDomain: 'Domain',
      T.commonDataSourceOnFDA: 'Data Source on FDA',
      T.commonDataSourceNoFDA: 'Data Source not on FDA',
      T.commonDataSourceOnHand: 'Data Source on Hand',
      T.commonTimeValue: 'Time Value',
      T.commonNumericalRange: 'Numerical Range',
      T.commonScreeningTime: 'Screening Time',
      T.commonTimeInterval: 'Time Interval',
      T.commonAddRecord: 'Add Record',
      T.commonHeartRate: 'Heart Rate',
      T.commonTimesMinuteSmall: 'times/minute',
      T.commonTimesMinuteBig: 'Times/Minute',
      T.commonHighTemperature: 'High Temperature',
      T.commonHypothermia: 'Hypothermia',
      T.commonTimedAlarmLock: 'Timed Alarm Lock',
      T.commonHeightInch: 'Height (inch)',
      T.commonMgDl: 'mg/dL',
      T.commonProfilePicture: 'Profile Picture',
      T.commonDateOfBirth: 'Date of Birth',
      T.commonShootNow: 'Shoot Now',
      T.commonPhotoUpload: 'Photo Upload',
      T.commonModifyTarget: 'Modify Target',
      T.commonGoCycling: 'Go Cycling',
      T.commonGoWakl: 'Go Walk',
      T.commonGoRunning: 'Go Running',
      T.commonNoData: 'No Data',

      // 邮箱相关
      T.email: 'Email',
      T.emailHintText: 'Please enter your email',
      T.emailError: 'Please enter a valid email',

      // 手机号相关
      T.phone: 'Phone',
      T.phoneHintText: 'Please enter your phone number',
      T.phoneError: 'Please enter a valid phone number',
      T.phoneCode: 'Verification Code',
      T.phoneCodeHintText: 'Enter the code',
      T.phoneCodeError: 'Please enter a valid verification code',
      T.phoneSendCode: 'Send Code',
      T.phoneResend: 'Resend',

      // 密码相关
      T.password: 'Password',
      T.passwordHintText: 'Please enter your password',
      T.passwordConfirmHintText: 'Please confirm your password',
      T.passwordRegular: 'Password must contain at least 8 characters',
      T.passwordForget: 'Forgot Password?',
      T.passwordError: 'Please enter a valid password',
      T.passwordLengthError: 'Password must be at least 8 characters',
      T.passwordConfirmError: 'Please confirm your password',
      T.passwordNotMatch: 'Passwords do not match',

      // 地区相关
      T.region: 'Region',
      T.regionChina: 'China',
      T.regionAmerica: 'America',

      // 错误信息
      T.errorMessagesPlatformIosError: 'Platform iOS Error',
      T.errorNetwork: 'Network Error',

      // 血氧相关
      T.bloodOxygen: 'Blood Oxygen',
      T.bloodOxygenSaturationLevel: 'Blood Oxygen Saturation Level',
      T.bloodOxygenNormalLevel: 'Normal Level',
      T.bloodOxygenMildLevel: 'Mild Level',
      T.bloodOxygenModerateLevel: 'Moderate Level',
      T.bloodOxygenSeriousLevel: 'Serious Level',
      T.bloodOxygenRecords: 'Blood Oxygen Records',
      T.bloodOxygenTrend: 'Blood Oxygen Trend',

      // 血压相关
      T.bloodPressure: 'Blood Pressure',
      T.bloodPressureTrend: 'Blood Pressure Trend',
      T.bloodPressureSystolic: 'Systolic',
      T.bloodPressureDiastolic: 'Diastolic',
      T.bloodPressureSetGoals: 'Set Goals',
      T.bloodPressureRemind: 'Blood Pressure Reminder',
      T.bloodPressureRemindNoting: 'Blood Pressure Reminder Note',
      T.bloodPressureRemindEdit: 'Edit Blood Pressure Reminder',

      // 体温相关
      T.temperature: 'Temperature',
      T.temperatureTrend: 'Temperature Trend',

      // 蓝牙相关
      T.bluetoothNotConnected: 'Not Connected',
      T.bluetoothIsConnected: 'Connected',
      T.bluetoothDisConnected: 'Disconnected',
      T.bluetoothSetting: 'Bluetooth Settings',
      T.bluetoothConYouRing: 'Connect Your Ring',
      T.bluetoothConYouRingDetail: 'Connect Your Ring Detail',
      T.bluetoothAddDevice: 'Add Device',
      T.bluetoothSearching: 'Searching...',
      T.bluetoothEquipmentNo: 'No Equipment',
      T.bluetoothPleaseConnect: 'Please Connect',
      T.bluetoothMyDevice: 'My Device',
      T.bluetoothIgnoreDevice: 'Ignore Device',
      T.bluetoothSystemPairing: 'System Pairing',
      T.bluetoothConnecting: 'Connecting...',
      T.bluetoothPairingDelete: 'Delete Pairing',
      T.bluetoothPairingDeleteAndroid: 'Delete Pairing (Android)',
      T.bluetoothConnected: 'Connected',
      T.bluetoothMacAddress: 'MAC Address',
      T.bluetoothConnectFailed: 'Connection Failed',
      T.bluetoothAuthRefused: 'Device Refused Connection',
      T.bluetoothAuthSuccess: 'Authentication Successful',
      T.bluetoothAuthBound: 'Device Already Bound to Another Phone',
      T.bluetoothAuthIllegal: 'Illegal Manufacturer',
      T.bluetoothVendorIdDifferent: 'Vendor ID Mismatch',
      T.bluetoothAppIdDifferent: 'App ID Mismatch',
      T.bluetoothDeviceOff: 'Device is Off',
      T.bluetoothReconnectSuccess: 'Reconnect Success',
      T.unableToOpenBluetoothSettings: 'Unable to open Bluetooth settings, please manually operate.',
      T.bluetoothNotCharged:"Not charged",
      T.bluetoothCharged:"Charged",
      T.bluetoothCharging:"Charging",
      T.bluetoothUnbind:"Unbind",
      T.bluetoothAlreadyBound:"The device is already bound. Please unbind it or place it on the charging pad before connecting it again.",
      // 血糖相关
      T.bloodSugar: 'Blood Sugar',

      // 单词
      T.wordsGood: 'Good',
      T.wordsPayAttention: 'Pay Attention',
      T.wordsHrs: 'hrs',
      T.wordsAverage: 'Average',
      T.wordsMaximum: 'Maximum',
      T.wordsMinimum: 'Minimum',
      T.wordsAll: 'All',
      T.wordsComprehensiveLevel: 'Comprehensive Level',
      T.wordsSystolic: 'Systolic',
      T.wordsDiastolic: 'Diastolic',
      T.wordsPulse: 'Pulse',
      T.wordsMinute: 'Minute',
      T.wordsUnit: 'Unit',
      T.wordsMmHg: 'mmHg',
      T.wordsKpa: 'kPa',
      T.wordsSys: 'SYS',
      T.wordsDia: 'DIA',
      T.wordsOk: 'OK',
      T.wordsLow: 'Low',
      T.wordsNormal: 'Normal',
      T.wordsMild: 'Mild',
      T.wordsModerate: 'Moderate',
      T.wordsSerious: 'Serious',
      T.wordsBack: 'Back',
      T.wordsCancel: 'Cancel',
      T.wordsYes: 'Yes',
      T.wordsSave: 'Save',
      T.wordsRepeat: 'Repeat',
      T.wordsLabel: 'Label',
      T.wordsSnooze: 'Snooze',
      T.wordsDelete: 'Delete',
      T.wordsTime: 'Time',
      T.wordsStart: 'Start',
      T.wordsHours: 'Hours',
      T.wordsMin: 'min',
      T.wordsSec: 'Sec',
      T.wordsWeight: 'Weight',
      T.wordsHeight: 'Height',
      T.wordsKg: 'kg',
      T.wordsLb: 'lb',
      T.wordsBMI: 'BMI',
      T.wordsCm: 'cm',
      T.wordsInch: 'inch',
      T.wordsBluetooth: 'Bluetooth',
      T.wordsStop: 'Stop',
      T.wordsAizoRing: 'aiRing',
      T.wordsSearch: 'Search',
      T.wordsBPM: 'BPM',
      T.wordsD: 'D',
      T.wordsW: 'W',
      T.wordsM: 'M',
      T.wordsPercentage: '%',
      T.wordsName: 'Name',
      T.wordsID: 'ID',
      T.wordsGender: 'Gender',
      T.wordsMale: 'Male',
      T.wordsFemale: 'Female',
      T.wordsOther: 'Other',
      T.wordsPrompt: 'Prompt',
      T.wordsWakeUp: 'Wake Up',
      T.wordsBreakfast: 'Breakfast',
      T.wordsLunch: 'Lunch',
      T.wordsDinner: 'Dinner',
      T.wordsBedtime: 'Bedtime',
      T.wordsMidnight: 'Midnight',
      T.wordsBeforeMeal: 'Before Meal',
      T.wordsAfterMeal: 'After Meal',
      T.wordsFeet: 'Feet',
      T.wordsInches: 'Inches',
      T.wordsMmolL: 'mmol/L',
      T.wordsHighest: 'Highest',
      T.wordsLowest: 'Lowest',
      T.wordsOverSpeed: 'Over Speed',
      T.wordsProposal: 'Proposal',
      T.wordsSlow: 'Slow',
      T.wordsMax: 'Max',
      T.wordsUnnormal: 'Unnormal',
      T.wordsAttention: 'Attention',
      T.wordsKcal: 'kcal',
      T.wordsKm: 'km',
      T.wordsCm: 'cm',
      T.wordsDeep: 'Deep',
      T.wordsLight: 'Light',
      T.wordsRem: 'REM',
      T.wordsAwake: 'Awake',
      T.wordsData: 'Data',
      T.wordsGo: 'Go',
      T.wordsScore: 'Score',
      T.wordsH: 'h',
      T.wordsDay: 'Day',
      T.wordsWeek: 'Week',
      T.wordsMonth: 'Month',
      T.wordsIdle: 'Idle',
      T.wordsFinish: 'Finish',
      T.wordsNone:"None",

      T.userAlreadyExists: 'This email is already registered',
      T.emailVerificationRequired: 'Please verify your email address first, then try to login again', // 新增
      // 日期相关
      T.dateDay: 'Day',
      T.dateWeek: 'Week',
      T.dateMonths: 'Months',
      T.dateToday: 'Today',
      T.dateLastWeek: 'Last Week',
      T.dateLastMonth: 'Last Month',
      T.dateLastYear: 'Last Year',
      T.dateEveryMonday: 'Every Monday',
      T.dateEveryTuesday: 'Every Tuesday',
      T.dateEveryWednesday: 'Every Wednesday',
      T.dateEveryThursday: 'Every Thursday',
      T.dateEveryFriday: 'Every Friday',
      T.dateEverySaturday: 'Every Saturday',
      T.dateEverySunday: 'Every Sunday',
      T.dateEveryday: 'Everyday',
      T.dateMondayToFriday: 'Monday to Friday',
      T.dateMonday: 'Monday',
      T.dateTuesday: 'Tuesday',
      T.dateWednesday: 'Wednesday',
      T.dateThursday: 'Thursday',
      T.dateFriday: 'Friday',
      T.dateSaturday: 'Saturday',
      T.dateSunday: 'Sunday',
      T.dateMon: 'Mon',
      T.dateTue: 'Tue',
      T.dateWed: 'Wed',
      T.dateThu: 'Thu',
      T.dateFri: 'Fri',
      T.dateSat: 'Sat',
      T.dateSun: 'Sun',

      // 详情相关
      T.detailButton: 'Details',

      // 测量相关
      T.measurementResult: 'Measurement Result',
      T.measurementSettings: 'Measurement Settings',
      T.measurementRemind: 'Measurement Reminder',
      T.measurementRecord: 'Measurement Record',
      T.measurementResultNormal: 'Tip: Your measurement results are normal. Please continue to maintain good lifestyle habits to keep the values normal and your body healthy.',
      T.measurementTime: 'Measurement Time',
      T.measurementStarted: 'Started',
      T.measurementError: 'Error',
      T.setMeasureIntervalFailed: 'Set Measurement Interval Failed',
      T.measurementInProgress: 'Preparing...',
      T.measurementSuccess: "Measurement successful",
      T.measure:"Measure",
      // 健康相关
      T.healthTitle: 'Health',
      T.healthInformation: 'Health Information',
      T.healthRecoveryReport: 'Recovery Report',
      T.healthAllRecord: 'All Records',
      T.healthScore: 'Health Score',
      T.healthSleepLast: 'Last Sleep',
      T.healthCaloriesDay: 'Calories Today',
      T.healthSleepTitle: 'Sleep',
      T.healthBodyScoreTitle: 'Body Score',
      T.healthRingRemind: 'Ring Reminder',
      T.healthAdvice: 'Health Advice',
      T.healthAdviceInfor1: 'Health Advice Information 1',
      T.healthSleepScoreTitle: 'Sleep Score',
      T.healthActivityScoreTitle: 'Activity Score',
      T.healthHeartRateScoreTitle: 'Heart Rate Score',
      T.healthBloodOxygenTitle: 'Blood Oxygen',
      T.healthTemperatureTitle: 'Temperature',
      T.healthHolistic: 'Holistic',
      T.healthTimeAsleep: 'Time Asleep',
      T.healthSleepEffi: 'Sleep Efficiency',
      T.healthFallAsleep: 'Fall Asleep',
      T.healthSleepScore: 'Sleep Score',
      T.healthActivityScore: 'Activity Score',
      T.healthDurationExer: 'Exercise Duration',
      T.healthNumberExer: 'Exercise Number',
      T.healthNumberActi: 'Activity Number',
      T.healthActivityCon: 'Activity Continuity',
      T.healthResting: 'Resting',
      T.healthProposalNo: 'No Proposal',
      T.healthProposalAchieved: 'You almost achieved your sleep goal last night.',
      T.healthChanged: 'Changed',
      T.healthAverageSleepTemperature: 'Average Sleep Temperature',

      // 底部导航栏
      T.bottomNavHome: 'Home',
      T.bottomNavHealth: 'Health',
      T.bottomNavMy: 'Me',

      // 知识相关
      T.knowledge: 'Knowledge',
      T.knowledgeManuallyEnterData: 'Manually Enter Data',
      T.knowledUnderstandRelevant: 'Understand Relevant',
      T.bloodOxygenTitle: 'Blood Oxygen',
      T.bloodOxygenTitle1: 'Blood Oxygen Saturation',
      T.bloodOxygenTitle2: 'Low Blood Oxygen',
      T.bloodOxygenValue1: '    Blood oxygen saturation is the percentage of hemoglobin in the blood that is oxygenated, reflecting the concentration of oxygen in the blood. It is an important physiological parameter of the respiratory cycle. Ideally, blood oxygen saturation should be 95%-100%, but this can vary depending on personal health status, altitude, certain diseases, etc.',
      T.bloodOxygenValue2: '    If your blood oxygen saturation is detected to be below the set reminder value for a period of time, you will be reminded to pay attention to your current health status and record the data. If you also experience shortness of breath, headaches, fatigue, or decreased concentration, it is recommended to seek help from professional medical personnel. Special note: The blood oxygen data obtained from wearable devices is for reference only. If you feel any discomfort, please visit a hospital for professional medical consultation.',
      T.bloodPressureTitle: 'Blood pressure Knowledge',
      T.bloodPressureTitle1: 'Blood pressure',
      T.bloodPressureTitle2: 'Classification of blood pressure levels',
      T.bloodPressureValue1: '    Blood pressure is the pressure exerted by the blood circulation on the arterial walls of the body. Composed of two numerical values, systolic blood pressure displays the vascular pressure when the heart contracts or beats, commonly known as \"high pressure\"; diastolic blood pressure displays the vascular pressure when the heart relaxes between two beats, commonly known as \"low pressure\". According to the diagnostic criteria for hypertension in the \"Chinese Guidelines for the Prevention and Treatment of Hypertension\" (2018 revised edition), the diagnosis of hypertension can be made by measuring high blood pressure (systolic blood pressure)>140mmHg and low blood pressure (diastolic blood pressure)>90mmHg three times on the same day without taking antihypertensive drugs. If currently taking antihypertensive medication and blood pressure<140/90mmHg, the diagnosis is still hypertension.',
      T.bloodPressureValue2: '    At present, China classifies blood pressure levels based on normal blood pressure, normal high value, and hypertension. This classification applies to adults aged 18 and above of any age. Normal blood pressure: high pressure (systolic blood pressure)<120mmHg and low pressure (diastolic blood pressure)<80mmHg Normal high value: high pressure (systolic blood pressure) 120-139mmHg and/or low pressure (diastolic blood pressure) 80-89mmHg. Hypertension: high blood pressure (systolic blood pressure)>140mmHg and/or low blood pressure (diastolic blood pressure)>90mmHg. The above definition of hypertension and classification of blood pressure levels are derived from the \"Chinese Guidelines for the Prevention and Treatment of Hypertension (Revised in 2018)\". Hypotension has not yet been clearly defined. It is generally believed that adults with high blood pressure (systolic blood pressure<90mmHg, low blood pressure (diastolic blood pressure)<60mmHg) have low blood pressure The above content is for reference only and cannot be used as a basis for diagnosis or treatment',
      T.temperatureTitle1: 'Body temperature',
      T.temperatureValue1: '    The normal body temperature of the human body is related to the measurement method, and the axillary temperature of 36–37 °C (96.8–98.6 °F) is generally used as the normal value. The body temperature of a normal person is maintained within a constant range, with fluctuations generally not exceeding 1 °C (1.8 °F).\n\n    There are currently three commonly used methods for measuring body temperature:\n    1. Axillary Temperature: A normal temperature range of 36–37 °C (96.8–98.6 °F).\n    2. Oral Temperature: A normal temperature range of 36.3–37.2 °C (97.3–99.0 °F).\n    3. Anal Temperature: A normal temperature range of 36.3–37.5 °C (97.3–99.5 °F).\n\n    Infrared Thermometers and Other Methods:\n    Infrared forehead thermometers and ear thermometers are widely used for measuring body temperature. The normal temperature range is between 36–37 °C (96.8–98.6 °F). However, external factors, such as ambient temperature or improper usage, may affect the accuracy. If the deviation is too large, it is advisable to remeasure or use an axillary thermometer.\n\n    Normal Variations: \n    he normal human body temperature fluctuates and is influenced by internal and external factors:\n    - Morning vs. Afternoon: Body temperature is usually slightly lower in the morning and higher in the afternoon.\n    - Physical Activity and Meals: After intense exercise or a full meal, body temperature may slightly increase, but the fluctuation typically does not exceed 1 °C (1.8 °F).\n\n    Quick Conversion:\n    - To convert °C to °F: Multiply by 1.8 and add 32.\n    - To convert °F to °C: Subtract 32 and divide by 1.8.',
      T.bloodSugarTitle1: 'What is blood sugar？',
      T.bloodSugarTitle2: 'The clinical guidance significance of blood glucose',
      T.bloodSugarTitle3: 'Distribution of blood glucose',
      T.bloodSugarValue1: '    The glucose in the blood is called blood glucose (Glu). Glucose is an important component of the human body and a significant source of energy. The normal human body needs a lot of sugar every day to provide energy and power for the normal operation of various tissues and organs. So blood sugar must be maintained at a certain level to meet the needs of various organs and tissues in the body.',
      T.bloodSugarValue2: '    Blood glucose monitoring is an important part of diabetes management, which can reflect the degree of glucose metabolism disorder in diabetes patients, and is used to develop a reasonable hypoglycemic program, evaluate the effect of hypoglycemic treatment, and guide the adjustment of treatment programs',
      T.bloodSugarValue3: '    Glucose levels>13.9mmol/L belong to level 2 hyperglycemia. Glucose levels between 10.1 and 13.9mmol/L belong to level 1 hyperglycemia. Glucose levels between 3.9 and 10.0mmol/L belong to normal glucose levels. Glucose levels between 3.0 and 3.8 mmol/L belong to level 1 hypoglycemia. Glucose level<3.0mmol/L belongs to grade 2 hypoglycemia',
      T.heartRateTitle1: 'What is heart rate？',
      T.heartRateValue1: "    Heart rate refers to the number of heartbeats per minute in a normal person's quiet state, also known as quiet heart rate. It is generally 60-100 beats per minute and may vary among individuals due to age, gender, or other physiological factors. Generally speaking, the younger the age, the faster the heart rate. Older people have slower heart rates than younger people, and women have faster heart rates than men of the same age. These are all normal physiological phenomena. In a quiet state, the normal heart rate for adults is 60-100 beats per minute, and the ideal heart rate should be 55-70 beats per minute (athletes have a slower heart rate than ordinary adults, usually around 50 beats per minute).",

      // 体重相关
      T.weightTrend: 'Weight Trend',

      // 心率相关
      T.heartRate: 'Heart Rate',

      // 异常提醒
      T.abnormalRemind: 'Abnormal Reminder',
      T.abnormalRemindLowRate: 'Low Rate Reminder',
      T.abnormalRemindHighRate: 'High Rate Reminder',

      // 提示词
      T.promptManualUploadTrue: 'Manual Upload True',
      T.promptManualUploadFalse: 'Manual Upload False',
      T.promptFillAll: 'Please Fill All Fields',
      T.promptInt0TO100: 'Please enter a number between 0 and 100',
      T.promptInt0TO200: 'Please enter a number between 0 and 200',
      T.promptDateTimeillegal: 'Date Time Illegal',
      T.promptFahren95TO107: 'Please enter a temperature between 95 and 107°F',
      T.promptCelsius35TO42: 'Please enter a temperature between 35 and 42°C',
      T.promptModifSuccess: 'Modification Success',
      T.promptModifFailed: 'Modification Failed',
      T.promptCameraNo: 'Camera Not Available',

      // 用户相关
      T.userHealthGoal: 'Health Goal',
      T.userUnitSetting: 'Unit Setting',
      T.userAccountSecurity: 'Account Security',
      T.userPersonInfo: 'Personal Information',
      T.userSetName: 'Set Name',
      T.userSetID: 'Set ID',
      T.userPicture: 'Profile Picture',

      // 账号与安全
      T.asDeleteAccount: 'Delete Account',
      T.asDeleteAccountP: 'Are you sure you want to delete all the data of the current account? (Irrevocable)',
      T.asLogOutP: 'Are you sure to log out?',

      // 记录相关
      T.recordDropMore: 'Drop More',
      T.recordNoMore: 'No More Records',

      // 健康目标设置
      T.hgsTitle: 'Health Goal Setting',
      T.hgsStepTarget: 'Step Target',
      T.hgsBloodPressureGoal: 'Blood Pressure Goal',
      T.hgsBloodGlucoseGoal: 'Blood Glucose Goal',
      T.hgsReminderHeartRate: 'Heart Rate Reminder',
      T.hgsDailyGoal: 'Daily Goal',
      T.hgsHighReminderHeartRate: 'High Heart Rate Reminder',
      T.hgsLowReminderHeartRate: 'Low Heart Rate Reminder',

      // 单位设置
      T.unitSTitle: 'Unit Setting',
      T.unitSHeightUnit: 'Height Unit',
      T.unitSWeightUnit: 'Weight Unit',
      T.unitSTemperatureUnit: 'Temperature Unit',
      T.unitSBloodPressureUnit: 'Blood Pressure Unit',
      T.unitSBloodGlucoseUnits: 'Blood Glucose Units',
      T.unitSMetricSystem: 'Metric System',
      T.unitSImperialSystem: 'Imperial System',
      T.unitSAutomatic: 'Automatic',
      T.unitSLowBloodReminder: 'Low Blood Reminder',
      T.unitSCentigrade: 'Centigrade',
      T.unitSFahrenheit: 'Fahrenheit',

      // 步数相关
      T.steps: 'Steps',
      T.stepsDistance: 'Distance',
      T.stepsRecord: 'Steps Record',
      T.stepsTargetProgress: 'Target Progress',
      T.stepsCaloBurned: 'Calories Burned',
      T.stepsCaloCheeseburger: 'Calories (Cheeseburger)',
      T.stepsFourStadium: 'Four Stadium',

      // 运动相关
      T.fitness: 'Fitness',
      T.fitnessDailyAct: 'Daily Activity',
      T.fitnessDistanceM: 'Distance (m)',
      T.fitnessConsumKcal: 'Calories Consumed',
      T.fitnessAdjustGoals: 'Adjust Goals',
      T.fitnessPhysicalexercise: 'Physical Exercise',
      T.fitnessExeRecords: 'Exercise Records',
      T.fitnessStayAct: 'Stay Active',
      T.fitnessBeneficialAct: 'Beneficial Activity',
      T.fitnessExerciseVol: 'Exercise Volume',
      T.fitnessExerciseFre: 'Exercise Frequency',
      T.fitnessPromptNo: 'No Prompt',

      // 睡眠相关
      T.sleep: 'Sleep',
      T.sleepNaps: 'Naps',
      T.sleepTotalNap: 'Total Nap',
      T.sleepNapNote1: 'Note: Our sleep evaluation require at least 3 hours of sleep dataanything less than that counts as a nap.',
      T.sleepBreathQuality: 'Breath Quality',
      T.sleepOxygenDuring: 'Oxygen During Sleep',
      T.sleepManagement: 'Sleep Management',
      T.sleepTotalDurationNight: 'Total Night Duration',

      // AOJ温度计相关
      T.aojThermometer: 'AOJ Thermometer',
      T.scanAndClassifyDevices: 'Scan and Classify Devices',
      T.noDevicesFound: 'No devices found',
      T.unknownDevice: 'Unknown Device',
      T.macAddress: 'MAC Address',
      T.connectAojThermometer: 'Connect',
      T.disconnectAojThermometer: 'Disconnect',
      T.getBatteryLevel: 'Get Battery',
      T.currentTemperature: 'Current Temperature',
      T.lastMeasurementTime: 'Last Measurement Time',
      T.getLastTemperature: 'Get Last Temperature',
      T.setTime: 'Set Time',

      // 错误相关
      T.errorServer: 'Server Error',
      T.errorNotFound: 'Not Found',
      T.errorForbidden: 'Forbidden',
      T.bluetoothOff: 'Bluetooth Off,Please turn on the bluetooth',

      // Test Component View
      T.bluetoothFunction: 'Bluetooth Function',
      T.deviceStatus: 'Device Status',
      T.connectionStatus: 'Connection Status',
      T.batteryStatus: 'Battery Status',
      T.batteryLevel: 'Battery Level',
      T.connectionControl: 'Connection Control',
      T.connectDevice: 'Connect Device',
      T.disconnectDevice: 'Disconnect Device',
      T.measurementInterval: 'Measurement Interval',
      T.getCurrentInterval: 'Get Current Interval',
      T.set10MinutesInterval: 'Set 10 Minutes Interval',
      T.set20MinutesInterval: 'Set 20 Minutes Interval',
      T.set30MinutesInterval: 'Set 30 Minutes Interval',
      T.measurementControl: 'Measurement Control',
      T.measureHeartRate: 'Measure Heart Rate',
      T.measureBloodOxygen: 'Measure Blood Oxygen',
      T.measureTemperature: 'Measure Temperature',
      T.dataRetrieval: 'Data Retrieval',
      T.getHealthData: 'Get Health Data',
      T.getSleepData: 'Get Sleep Data',
      T.getHardwareInfo: 'Get Hardware Info',
      T.permissions: 'Permissions',
      T.requestBluetoothPermission: 'Request Bluetooth Permission',
      T.requestLocationPermission: 'Request Location Permission',
      T.requestNotificationPermission: 'Request Notification Permission',
      T.currentMeasurementInterval: 'Current Measurement Interval',
      T.currentMeasurementStatus: 'Current Measurement Status',
      
      // 蓝牙控制器相关
      T.deviceDisconnectedRetry: 'Device disconnected, please reconnect and try again',
      T.measurementFailedRetry: 'Measurement failed, please try again',
      T.operationFailedRetry: 'Operation failed, please try again',
      T.disconnectFailedRetry: 'Disconnect failed, please try again',
      T.bluetoothInitFailedRetry: 'Bluetooth initialization failed, please try again',
      T.aojDeviceConnectSuccess: 'AOJ device connected successfully',
      T.aojDeviceConnectFailed: 'AOJ device connection failed',
      T.aojDeviceDisconnectSuccess: 'AOJ device disconnected successfully',
      T.aojDeviceDisconnectFailed: 'Failed to disconnect AOJ device',
      T.bluetoothStatusUnknown: 'Unknown Bluetooth status',
      T.dataParseFailedRetry: 'Data parsing failed, please try again',
      T.requestFailed: 'Request failed',
      T.serverError: 'Server error',
      T.invalidResponseFormat: 'Invalid response format',
      T.requestCancelled: 'Request cancelled',
      T.equipmentStatus: 'Equipment status',
      T.aiRingConnectionStatus: 'aiRing Connection Status',
      T.airingBatteryStatus: 'aiRing Battery Status',
      T.airingBatteryLevel: 'aiRing Battery Level',
      T.measurementResults: 'Measurement Results',
      T.currentMeasurement: 'Current Measurement',
      T.measurementControl: 'Measurement Control',
    },
    'zh_CN': {
      // 通用
      T.appName: 'RPM',
      T.loading: '加载中...',
      T.success: '成功',
      T.error: '错误',
      T.confirm: '确认',
      T.cancel: '取消',

      // 权限
      T.permissionRequest: '权限请求',
      T.permissionRequestContent: '我们需要这些权限来为您提供最佳体验',
      T.goToSettings: '前往设置',

      // 权限名称
      T.camera: '相机',
      T.photos: '相册',
      T.location: '位置',
      T.notification: '通知',
      T.bluetooth: '蓝牙',
      T.equipmentStatus: '设备状态',  
      // 登录相关
      T.login: '登录',
      T.loginEmail: '邮箱登录',
      T.loginPhone: '手机登录',
      T.loginOther: '其他登录方式',
      T.loginOut: '退出登录',
      T.signUp: '注册',
      T.signUpError: '注册失败',
      T.signUpSuccess: '注册成功',
      T.loginSuccess: '登录成功',
      T.loginFailed: '登录失败',

      // 通用检查策略
      T.commonCheckPolicy: '检查策略',
      T.commonUserReadPolicy1: '我已阅读并同意',
      T.commonUserReadPolicy2: '用户协议',
      T.commonUserReadPolicy3: '和',
      T.commonUserReadPolicy4: '隐私政策',
      T.commonDeleteRemind: '删除提醒',
      T.commonDeleteRecord: '删除记录',
      T.commonAddRemind: '添加提醒',
      T.commonDomain: '域名',
      T.commonDataSourceOnFDA: 'FDA数据源',
      T.commonDataSourceNoFDA: '非FDA数据源',
      T.commonDataSourceOnHand: '手持数据源',
      T.commonTimeValue: '时间值',
      T.commonNumericalRange: '数值范围',
      T.commonScreeningTime: '筛查时间',
      T.commonTimeInterval: '时间间隔',
      T.commonAddRecord: '添加记录',
      T.commonHeartRate: '心率',
      T.commonTimesMinuteSmall: '次/分钟',
      T.commonTimesMinuteBig: '次/分钟',
      T.commonHighTemperature: '高温',
      T.commonHypothermia: '低温',
      T.commonTimedAlarmLock: '定时闹钟锁定',
      T.commonHeightInch: '身高(英寸)',
      T.commonMgDl: '毫克/分升',
      T.commonProfilePicture: '头像',
      T.commonDateOfBirth: '出生日期',
      T.commonShootNow: '立即拍摄',
      T.commonPhotoUpload: '照片上传',
      T.commonModifyTarget: '修改目标',
      T.commonGoCycling: '去骑行',
      T.commonGoWakl: '去步行',
      T.commonGoRunning: '去跑步',
      T.commonNoData: '暂无数据',

      // 邮箱相关
      T.email: '邮箱',
      T.emailHintText: '请输入邮箱',
      T.emailError: '请输入有效的邮箱',

      // 手机号相关
      T.phone: '手机号',
      T.phoneHintText: '请输入手机号',
      T.phoneError: '请输入有效的手机号',
      T.phoneCode: '验证码',
      T.phoneCodeHintText: '请输入验证码',
      T.phoneCodeError: '请输入有效的验证码',
      T.phoneSendCode: '发送验证码',
      T.phoneResend: '重新发送',

      // 密码相关
      T.password: '密码',
      T.passwordHintText: '请输入密码',
      T.passwordConfirmHintText: '请确认密码',
      T.passwordRegular: '密码至少包含8个字符',
      T.passwordForget: '忘记密码？',
      T.passwordError: '请输入有效的密码',
      T.passwordLengthError: '密码至少8个字符',
      T.passwordConfirmError: '请确认密码',
      T.passwordNotMatch: '密码不匹配',

      // 地区相关
      T.region: '地区',
      T.regionChina: '中国',
      T.regionAmerica: '美国',

      // 错误信息
      T.errorMessagesPlatformIosError: 'iOS平台错误',
      T.errorNetwork: '网络错误',

      // 血氧相关
      T.bloodOxygen: '血氧',
      T.bloodOxygenSaturationLevel: '血氧饱和度水平',
      T.bloodOxygenNormalLevel: '正常水平',
      T.bloodOxygenMildLevel: '轻度水平',
      T.bloodOxygenModerateLevel: '中度水平',
      T.bloodOxygenSeriousLevel: '严重水平',
      T.bloodOxygenRecords: '血氧记录',
      T.bloodOxygenTrend: '血氧趋势',

      // 血压相关
      T.bloodPressure: '血压',
      T.bloodPressureTrend: '血压趋势',
      T.bloodPressureSystolic: '收缩压',
      T.bloodPressureDiastolic: '舒张压',
      T.bloodPressureSetGoals: '设置目标',
      T.bloodPressureRemind: '血压提醒',
      T.bloodPressureRemindNoting: '血压提醒说明',
      T.bloodPressureRemindEdit: '编辑血压提醒',

      // 体温相关
      T.temperature: '体温',
      T.temperatureTrend: '体温趋势',

      // 蓝牙相关
      T.bluetoothNotConnected: '未连接',
      T.bluetoothIsConnected: '已连接',
      T.bluetoothDisConnected: '已断开',
      T.bluetoothSetting: '蓝牙设置',
      T.bluetoothConYouRing: '连接您的戒指',
      T.bluetoothConYouRingDetail: '连接您的戒指详情',
      T.bluetoothAddDevice: '添加设备',
      T.bluetoothSearching: '搜索中...',
      T.bluetoothEquipmentNo: '无设备',
      T.bluetoothPleaseConnect: '请连接',
      T.bluetoothMyDevice: '我的设备',
      T.bluetoothIgnoreDevice: '忽略设备',
      T.bluetoothSystemPairing: '系统配对',
      T.bluetoothConnecting: '连接中...',
      T.bluetoothPairingDelete: '删除配对',
      T.bluetoothPairingDeleteAndroid: '删除配对(安卓)',
      T.bluetoothConnected: '已连接',
      T.bluetoothMacAddress: 'MAC地址',
      T.bluetoothConnectFailed: '连接失败',
      T.bluetoothAuthRefused: '设备拒绝连接',
      T.bluetoothAuthSuccess: '认证成功',
      T.bluetoothAuthBound: '设备已绑定到其他手机',
      T.bluetoothAuthIllegal: '非法制造商',
      T.bluetoothVendorIdDifferent: '供应商ID不匹配',
      T.bluetoothAppIdDifferent: '应用程序ID不匹配',
      T.bluetoothDeviceOff: '设备已关闭',
      T.unableToOpenBluetoothSettings: '无法打开蓝牙设置，请手动操作',    

      // 血糖相关
      T.bloodSugar: '血糖',

      // 单词
      T.wordsGood: '良好',
      T.wordsPayAttention: '注意',
      T.wordsHrs: '小时',
      T.wordsAverage: '平均',
      T.wordsMaximum: '最大',
      T.wordsMinimum: '最小',
      T.wordsAll: '全部',
      T.wordsComprehensiveLevel: '综合水平',
      T.wordsSystolic: '收缩压',
      T.wordsDiastolic: '舒张压',
      T.wordsPulse: '脉搏',
      T.wordsMinute: '分钟',
      T.wordsUnit: '单位',
      T.wordsMmHg: '毫米汞柱',
      T.wordsKpa: '千帕',
      T.wordsSys: '收缩压',
      T.wordsDia: '舒张压',
      T.wordsOk: '确定',
      T.wordsLow: '低',
      T.wordsNormal: '正常',
      T.wordsMild: '轻度',
      T.wordsModerate: '中度',
      T.wordsSerious: '严重',
      T.wordsBack: '返回',
      T.wordsCancel: '取消',
      T.wordsYes: '是',
      T.wordsSave: '保存',
      T.wordsRepeat: '重复',
      T.wordsLabel: '标签',
      T.wordsSnooze: '小睡',
      T.wordsDelete: '删除',
      T.wordsTime: '时间',
      T.wordsStart: '开始',
      T.wordsHours: '小时',
      T.wordsMin: '分钟',
      T.wordsSec: '秒',
      T.wordsWeight: '体重',
      T.wordsHeight: '身高',
      T.wordsKg: '千克',
      T.wordsLb: '磅',
      T.wordsBMI: '体重指数',
      T.wordsCm: '厘米',
      T.wordsInch: '英寸',
      T.wordsBluetooth: '蓝牙',
      T.wordsStop: '停止',
      T.wordsAizoRing: '智能戒指',
      T.wordsSearch: '搜索',
      T.wordsBPM: '每分钟心跳',
      T.wordsD: '日',
      T.wordsW: '周',
      T.wordsM: '月',
      T.wordsPercentage: '%',
      T.wordsName: '姓名',
      T.wordsID: 'ID',
      T.wordsGender: '性别',
      T.wordsMale: '男',
      T.wordsFemale: '女',
      T.wordsOther: '其他',
      T.wordsPrompt: '提示',
      T.wordsWakeUp: '起床',
      T.wordsBreakfast: '早餐',
      T.wordsLunch: '午餐',
      T.wordsDinner: '晚餐',
      T.wordsBedtime: '睡前',
      T.wordsMidnight: '午夜',
      T.wordsBeforeMeal: '餐前',
      T.wordsAfterMeal: '餐后',
      T.wordsFeet: '英尺',
      T.wordsInches: '英寸',
      T.wordsMmolL: '毫摩尔/升',
      T.wordsHighest: '最高',
      T.wordsLowest: '最低',
      T.wordsOverSpeed: '超速',
      T.wordsProposal: '建议',
      T.wordsSlow: '慢',
      T.wordsMax: '最大',
      T.wordsUnnormal: '异常',
      T.wordsAttention: '注意',
      T.wordsKcal: '千卡',
      T.wordsKm: '公里',
      T.wordsDeep: '深度',
      T.wordsLight: '轻度',
      T.wordsRem: '快速眼动',
      T.wordsAwake: '清醒',
      T.wordsData: '数据',
      T.wordsGo: '去',
      T.wordsScore: '分数',
      T.wordsH: '小时',
      T.wordsNone: '无',  
      // 日期相关
      T.dateDay: '日',
      T.dateWeek: '周',
      T.dateMonths: '月',
      T.dateToday: '今天',
      T.dateLastWeek: '上周',
      T.dateLastMonth: '上月',
      T.dateLastYear: '去年',
      T.dateEveryMonday: '每周一',
      T.dateEveryTuesday: '每周二',
      T.dateEveryWednesday: '每周三',
      T.dateEveryThursday: '每周四',
      T.dateEveryFriday: '每周五',
      T.dateEverySaturday: '每周六',
      T.dateEverySunday: '每周日',
      T.dateEveryday: '每天',
      T.dateMondayToFriday: '周一至周五',
      T.dateMonday: '周一',
      T.dateTuesday: '周二',
      T.dateWednesday: '周三',
      T.dateThursday: '周四',
      T.dateFriday: '周五',
      T.dateSaturday: '周六',
      T.dateSunday: '周日',
      T.dateMon: '周一',
      T.dateTue: '周二',
      T.dateWed: '周三',
      T.dateThu: '周四',
      T.dateFri: '周五',
      T.dateSat: '周六',
      T.dateSun: '周日',

      // 详情相关
      T.detailButton: '详情',

      // 测量相关
      T.measurementResult: '测量结果',
      T.measurementSettings: '测量设置',
      T.measurementRemind: '测量提醒',
      T.measurementRecord: '测量记录',
      T.measurementResultNormal: '正常',
      T.measurementTime: '测量时间',

      // 健康相关
      T.healthTitle: '健康',
      T.healthInformation: '健康信息',
      T.healthRecoveryReport: '恢复报告',
      T.healthAllRecord: '所有记录',
      T.healthScore: '健康分数',
      T.healthSleepLast: '最近睡眠',
      T.healthCaloriesDay: '今日卡路里',
      T.healthSleepTitle: '睡眠',
      T.healthBodyScoreTitle: '身体分数',
      T.healthRingRemind: '戒指提醒',
      T.healthAdvice: '健康建议',
      T.healthAdviceInfor1: '健康建议信息1',
      T.healthSleepScoreTitle: '睡眠分数',
      T.healthActivityScoreTitle: '活动分数',
      T.healthHeartRateScoreTitle: '心率分数',
      T.healthBloodOxygenTitle: '血氧',
      T.healthTemperatureTitle: '体温',
      T.healthHolistic: '整体',
      T.healthTimeAsleep: '睡眠时间',
      T.healthSleepEffi: '睡眠效率',
      T.healthFallAsleep: '入睡',
      T.healthSleepScore: '睡眠分数',
      T.healthActivityScore: '活动分数',
      T.healthDurationExer: '运动时长',
      T.healthNumberExer: '运动次数',
      T.healthNumberActi: '活动次数',
      T.healthActivityCon: '活动连续性',
      T.healthResting: '休息',
      T.healthProposalNo: '无建议',
      T.healthProposalAchieved: '建议已达成',
      T.healthChanged: '已更改',
      T.healthAverageSleepTemperature: '平均睡眠体温',

      // 底部导航栏
      T.bottomNavHome: '首页',
      T.bottomNavHealth: '健康',
      T.bottomNavMy: '我的',

      // 知识相关
      T.knowledge: '知识',
      T.knowledgeManuallyEnterData: '手动输入数据',
      T.knowledUnderstandRelevant: '了解相关',
      T.bloodOxygenTitle: '血氧',
      T.bloodOxygenTitle1: '血氧标题1',
      T.bloodOxygenTitle2: '血氧标题2',
      T.bloodOxygenValue1: '血氧值1',
      T.bloodOxygenValue2: '血氧值2',
      T.bloodPressureTitle: '血压',
      T.bloodPressureTitle1: '血压标题1',
      T.bloodPressureTitle2: '血压标题2',
      T.bloodPressureValue1: '血压值1',
      T.bloodPressureValue2: '血压值2',
      T.temperatureTitle1: '体温标题1',
      T.temperatureValue1: '体温值1',
      T.bloodSugarTitle1: '血糖标题1',
      T.bloodSugarTitle2: '血糖标题2',
      T.bloodSugarTitle3: '血糖标题3',
      T.bloodSugarValue1: '血糖值1',
      T.bloodSugarValue2: '血糖值2',
      T.bloodSugarValue3: '血糖值3',
      T.heartRateTitle1: '心率标题1',
      T.heartRateValue1: '心率值1',

      // 体重相关
      T.weightTrend: '体重趋势',

      // 心率相关
      T.heartRate: '心率',

      // 异常提醒
      T.abnormalRemind: '异常提醒',
      T.abnormalRemindLowRate: '低心率提醒',
      T.abnormalRemindHighRate: '高心率提醒',

      // 提示词
      T.promptManualUploadTrue: '手动上传成功',
      T.promptManualUploadFalse: '手动上传失败',
      T.promptFillAll: '请填写所有字段',
      T.promptInt0TO100: '请输入0到100之间的数字',
      T.promptInt0TO200: '请输入0到200之间的数字',
      T.promptDateTimeillegal: '日期时间非法',
      T.promptFahren95TO107: '请输入95到107°F之间的温度',
      T.promptCelsius35TO42: '请输入35到42°C之间的温度',
      T.promptModifSuccess: '修改成功',
      T.promptModifFailed: '修改失败',
      T.promptCameraNo: '相机不可用',

      // 用户相关
      T.userHealthGoal: '健康目标',
      T.userUnitSetting: '单位设置',
      T.userAccountSecurity: '账号安全',
      T.userPersonInfo: '个人信息',
      T.userSetName: '设置姓名',
      T.userSetID: '设置ID',
      T.userPicture: '头像',

      // 账号与安全
      T.asDeleteAccount: '删除账号',
      T.asDeleteAccountP: '删除账号P',
      T.asLogOutP: '退出登录P',

      // 记录相关
      T.recordDropMore: '下拉更多',
      T.recordNoMore: '没有更多记录',

      // 健康目标设置
      T.hgsTitle: '健康目标设置',
      T.hgsStepTarget: '步数目标',
      T.hgsBloodPressureGoal: '血压目标',
      T.hgsBloodGlucoseGoal: '血糖目标',
      T.hgsReminderHeartRate: '心率提醒',
      T.hgsDailyGoal: '每日目标',
      T.hgsHighReminderHeartRate: '高心率提醒',
      T.hgsLowReminderHeartRate: '低心率提醒',

      // 单位设置
      T.unitSTitle: '单位设置',
      T.unitSHeightUnit: '身高单位',
      T.unitSWeightUnit: '体重单位',
      T.unitSTemperatureUnit: '温度单位',
      T.unitSBloodPressureUnit: '血压单位',
      T.unitSBloodGlucoseUnits: '血糖单位',
      T.unitSMetricSystem: '公制',
      T.unitSImperialSystem: '英制',
      T.unitSAutomatic: '自动',
      T.unitSLowBloodReminder: '低血压提醒',
      T.unitSCentigrade: '摄氏度',
      T.unitSFahrenheit: '华氏度',

      // 步数相关
      T.steps: '步数',
      T.stepsDistance: '距离',
      T.stepsRecord: '步数记录',
      T.stepsTargetProgress: '目标进度',
      T.stepsCaloBurned: '消耗卡路里',
      T.stepsCaloCheeseburger: '卡路里(芝士汉堡)',
      T.stepsFourStadium: '四个体育场',

      // 运动相关
      T.fitness: '健身',
      T.fitnessDailyAct: '每日活动',
      T.fitnessDistanceM: '距离(米)',
      T.fitnessConsumKcal: '消耗卡路里',
      T.fitnessAdjustGoals: '调整目标',
      T.fitnessPhysicalexercise: '体育锻炼',
      T.fitnessExeRecords: '运动记录',
      T.fitnessStayAct: '保持活跃',
      T.fitnessBeneficialAct: '有益活动',
      T.fitnessExerciseVol: '运动量',
      T.fitnessExerciseFre: '运动频率',
      T.fitnessPromptNo: '无提示',

      // 睡眠相关
      T.sleep: '睡眠',
      T.sleepNaps: '小睡',
      T.sleepTotalNap: '总小睡时间',
      T.sleepNapNote1: '小睡说明1',
      T.sleepBreathQuality: '呼吸质量',
      T.sleepOxygenDuring: '睡眠期间血氧',
      T.sleepManagement: '睡眠管理',
      T.sleepTotalDurationNight: '夜间总时长',

      // AOJ温度计相关
      T.aojThermometer: 'AOJ温度计',
      T.scanAndClassifyDevices: '扫描并分类设备',
      T.noDevicesFound: '未发现设备',
      T.unknownDevice: '未知设备',
      T.macAddress: 'MAC地址',
      T.connectAojThermometer: '连接',
      T.disconnectAojThermometer: '断开',
      T.getBatteryLevel: '获取电量',
      T.currentTemperature: '当前温度',
      T.lastMeasurementTime: '上次测量时间',
      T.getLastTemperature: '获取最后温度',
      T.setTime: '设置时间',

      // Test Component View
      T.bluetoothFunction: '蓝牙功能',
      T.deviceStatus: '设备状态',
      T.connectionStatus: '连接状态',
      T.batteryStatus: '电池状态',
      T.batteryLevel: '电池电量',
      T.connectionControl: '连接控制',
      T.connectDevice: '连接设备',
      T.disconnectDevice: '断开设备',
      T.measurementInterval: '测量间隔',
      T.getCurrentInterval: '获取当前间隔',
      T.set10MinutesInterval: '设置10分钟间隔',
      T.set20MinutesInterval: '设置20分钟间隔',
      T.set30MinutesInterval: '设置30分钟间隔',
      T.measurementControl: '测量控制',
      T.measureHeartRate: '测量心率',
      T.measureBloodOxygen: '测量血氧',
      T.measureTemperature: '测量体温',
      T.dataRetrieval: '数据获取',
      T.getHealthData: '获取健康数据',
      T.getSleepData: '获取睡眠数据',
      T.getHardwareInfo: '获取硬件信息',
      T.permissions: '权限',
      T.requestBluetoothPermission: '请求蓝牙权限',
      T.requestLocationPermission: '请求定位权限',
      T.requestNotificationPermission: '请求通知权限',
      T.currentMeasurementInterval: '当前测量间隔',
      T.currentMeasurementStatus: '当前测量状态',
      
      // 蓝牙控制器相关
      T.deviceDisconnectedRetry: '设备已断开连接，请重新连接设备后重试',
      T.measurementFailedRetry: '测量失败，请重试',
      T.operationFailedRetry: '操作失败，请重试',
      T.disconnectFailedRetry: '断开连接失败，请重试',
      T.bluetoothInitFailedRetry: '蓝牙初始化失败，请重试',
      T.aojDeviceConnectSuccess: 'AOJ设备连接成功',
      T.aojDeviceConnectFailed: 'AOJ设备连接失败',
      T.aojDeviceDisconnectSuccess: 'AOJ设备已断开连接',
      T.aojDeviceDisconnectFailed: '断开AOJ设备失败',
      T.bluetoothStatusUnknown: '蓝牙连接状态未知',
      T.dataParseFailedRetry: '数据解析失败，请重试',
      T.requestFailed: '请求失败',
      T.serverError: '服务器错误',
      T.invalidResponseFormat: '无效的响应格式',
      T.requestCancelled: '请求已取消',
    },
  };
} 
