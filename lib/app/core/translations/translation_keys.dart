/// 翻译键值类
/// 
/// 定义了应用中所有需要翻译的文本键值
/// 使用静态常量管理所有翻译键，便于统一管理和维护
/// 按功能模块分类组织翻译键
abstract class T {
  // 通用
  static const String appName = 'appName';
  static const String loading = 'loading';
  static const String success = 'success';
  static const String error = 'error';
  static const String confirm = 'confirm';
  static const String cancel = 'cancel';

  // 权限
  static const String permissionRequest = 'permissionRequest';
  static const String permissionRequestContent = 'permissionRequestContent';
  static const String goToSettings = 'goToSettings';

  static const String userAlreadyExists = 'userAlreadyExists';
  static const String emailVerificationRequired = 'emailVerificationRequired'; // 新增

  // 权限名称
  static const String camera = 'camera';
  static const String photos = 'photos';
  static const String location = 'location';
  static const String notification = 'notification';
  static const String bluetooth = 'bluetooth';
  static const String equipmentStatus = 'equipmentStatus';
  // 登录相关
  static const String login = 'login';
  static const String loginEmail = 'loginEmail';
  static const String loginPhone = 'loginPhone';
  static const String loginOther = 'loginOther';
  static const String loginOut = 'loginOut';
  static const String signUp = 'signUp';
  static const String signUpError = 'signUpError';
  static const String signUpSuccess = 'signUpSuccess';
  static const String loginSuccess = 'loginSuccess';
  static const String loginFailed = 'loginFailed';

  // 通用检查策略
  static const String commonCheckPolicy = 'commonCheckPolicy';
  static const String commonUserReadPolicy1 = 'commonUserReadPolicy1';
  static const String commonUserReadPolicy2 = 'commonUserReadPolicy2';
  static const String commonUserReadPolicy3 = 'commonUserReadPolicy3';
  static const String commonUserReadPolicy4 = 'commonUserReadPolicy4';
  static const String commonDeleteRemind = 'commonDeleteRemind';
  static const String commonDeleteRecord = 'commonDeleteRecord';
  static const String commonAddRemind = 'commonAddRemind';
  static const String commonDomain = 'commonDomain';
  static const String commonDataSourceOnFDA = 'commonDataSourceOnFDA';
  static const String commonDataSourceNoFDA = 'commonDataSourceNoFDA';
  static const String commonDataSourceOnHand = 'commonDataSourceOnHand';
  static const String commonTimeValue = 'commonTimeValue';
  static const String commonNumericalRange = 'commonNumericalRange';
  static const String commonScreeningTime = 'commonScreeningTime';
  static const String commonTimeInterval = 'commonTimeInterval';
  static const String commonAddRecord = 'commonAddRecord';
  static const String commonHeartRate = 'commonHeartRate';
  static const String commonTimesMinuteSmall = 'commonTimesMinuteSmall';
  static const String commonTimesMinuteBig = 'commonTimesMinuteBig';
  static const String commonHighTemperature = 'commonHighTemperature';
  static const String commonHypothermia = 'commonHypothermia';
  static const String commonTimedAlarmLock = 'commonTimedAlarmLock';
  static const String commonHeightInch = 'commonHeightInch';
  static const String commonMgDl = 'commonMgDl';
  static const String commonProfilePicture = 'commonProfilePicture';
  static const String commonDateOfBirth = 'commonDateOfBirth';
  static const String commonShootNow = 'commonShootNow';
  static const String commonPhotoUpload = 'commonPhotoUpload';
  static const String commonModifyTarget = 'commonModifyTarget';
  static const String commonGoCycling = 'commonGoCycling';
  static const String commonGoWakl = 'commonGoWakl';
  static const String commonGoRunning = 'commonGoRunning';
  static const String commonNoData = 'commonNoData';

  // 邮箱相关
  static const String email = 'email';
  static const String emailHintText = 'emailHintText';
  static const String emailError = 'emailError';

  // 手机号相关
  static const String phone = 'phone';
  static const String phoneHintText = 'phoneHintText';
  static const String phoneError = 'phoneError';
  static const String phoneCode = 'phoneCode';
  static const String phoneCodeHintText = 'phoneCodeHintText';
  static const String phoneCodeError = 'phoneCodeError';
  static const String phoneSendCode = 'phoneSendCode';
  static const String phoneResend = 'phoneResend';

  // 密码相关
  static const String password = 'password';
  static const String passwordHintText = 'passwordHintText';
  static const String passwordConfirmHintText = 'passwordConfirmHintText';
  static const String passwordRegular = 'passwordRegular';
  static const String passwordForget = 'passwordForget';
  static const String passwordError = 'passwordError';
  static const String passwordLengthError = 'passwordLengthError';
  static const String passwordConfirmError = 'passwordConfirmError';
  static const String passwordNotMatch = 'passwordNotMatch';

  // 地区相关
  static const String region = 'region';
  static const String regionChina = 'regionChina';
  static const String regionAmerica = 'regionAmerica';

  // 错误信息
  static const String errorMessagesPlatformIosError = 'errorMessagesPlatformIosError';
  static const String errorNetwork = 'errorNetwork';

  // 血氧相关
  static const String bloodOxygen = 'bloodOxygen';
  static const String bloodOxygenSaturationLevel = 'bloodOxygenSaturationLevel';
  static const String bloodOxygenNormalLevel = 'bloodOxygenNormalLevel';
  static const String bloodOxygenMildLevel = 'bloodOxygenMildLevel';
  static const String bloodOxygenModerateLevel = 'bloodOxygenModerateLevel';
  static const String bloodOxygenSeriousLevel = 'bloodOxygenSeriousLevel';
  static const String bloodOxygenRecords = 'bloodOxygenRecords';
  static const String bloodOxygenTrend = 'bloodOxygenTrend';

  // 血压相关
  static const String bloodPressure = 'bloodPressure';
  static const String bloodPressureTrend = 'bloodPressureTrend';
  static const String bloodPressureSystolic = 'bloodPressureSystolic';
  static const String bloodPressureDiastolic = 'bloodPressureDiastolic';
  static const String bloodPressureSetGoals = 'bloodPressureSetGoals';
  static const String bloodPressureRemind = 'bloodPressureRemind';
  static const String bloodPressureRemindNoting = 'bloodPressureRemindNoting';
  static const String bloodPressureRemindEdit = 'bloodPressureRemindEdit';

  // 体温相关
  static const String temperature = 'temperature';
  static const String temperatureTrend = 'temperatureTrend';

  // 蓝牙相关
  static const String bluetoothNotConnected = 'bluetoothNotConnected';
  static const String bluetoothIsConnected = 'bluetoothIsConnected';
  static const String bluetoothDisConnected = 'bluetoothDisConnected';
  static const String bluetoothAlreadyBound = 'bluetoothAlreadyBound';
  static const String bluetoothSetting = 'bluetoothSetting';
  static const String bluetoothConYouRing = 'bluetoothConYouRing';
  static const String bluetoothConYouRingDetail = 'bluetoothConYouRingDetail';
  static const String bluetoothAddDevice = 'bluetoothAddDevice';
  static const String bluetoothSearching = 'bluetoothSearching';
  static const String bluetoothEquipmentNo = 'bluetoothEquipmentNo';
  static const String bluetoothPleaseConnect = 'bluetoothPleaseConnect';
  static const String bluetoothMyDevice = 'bluetoothMyDevice';
  static const String bluetoothIgnoreDevice = 'bluetoothIgnoreDevice';
  static const String bluetoothSystemPairing = 'bluetoothSystemPairing';
  static const String bluetoothConnecting = 'bluetoothConnecting';
  static const String bluetoothPairingDelete = 'bluetoothPairingDelete';
  static const String bluetoothPairingDeleteAndroid = 'bluetoothPairingDeleteAndroid';
  static const String bluetoothConnected = 'bluetoothConnected';
  static const String bluetoothMacAddress = 'bluetoothMacAddress';
  static const String bluetoothConnectFailed = 'bluetoothConnectFailed';
  static const String bluetoothAuthRefused = 'bluetoothAuthRefused';
  static const String bluetoothAuthSuccess = 'bluetoothAuthSuccess';
  static const String bluetoothAuthBound = 'bluetoothAuthBound';
  static const String bluetoothAuthIllegal = 'bluetoothAuthIllegal';
  static const String bluetoothVendorIdDifferent = 'bluetoothVendorIdDifferent';
  static const String bluetoothAppIdDifferent = 'bluetoothAppIdDifferent';
  static const String bluetoothDeviceOff = 'bluetoothDeviceOff';
  static const String bluetoothReconnectSuccess = 'bluetoothReconnectSuccess';
  static const String unableToOpenBluetoothSettings = 'unableToOpenBluetoothSettings';
  static const String bluetoothOff = 'bluetoothOff';
  static const String bluetoothUnbind = 'bluetoothUnbind';
  // 血糖相关
  static const String bloodSugar = 'bloodSugar';

  // 单词
  static const String wordsGood = 'wordsGood';
  static const String wordsPayAttention = 'wordsPayAttention';
  static const String wordsHrs = 'wordsHrs';
  static const String wordsAverage = 'wordsAverage';
  static const String wordsMaximum = 'wordsMaximum';
  static const String wordsMinimum = 'wordsMinimum';
  static const String wordsAll = 'wordsAll';
  static const String wordsComprehensiveLevel = 'wordsComprehensiveLevel';
  static const String wordsSystolic = 'wordsSystolic';
  static const String wordsDiastolic = 'wordsDiastolic';
  static const String wordsPulse = 'wordsPulse';
  static const String wordsMinute = 'wordsMinute';
  static const String wordsUnit = 'wordsUnit';
  static const String wordsMmHg = 'wordsMmHg';
  static const String wordsKpa = 'wordsKpa';
  static const String wordsSys = 'wordsSys';
  static const String wordsDia = 'wordsDia';
  static const String wordsOk = 'wordsOk';
  static const String wordsLow = 'wordsLow';
  static const String wordsNormal = 'wordsNormal';
  static const String wordsMild = 'wordsMild';
  static const String wordsModerate = 'wordsModerate';
  static const String wordsSerious = 'wordsSerious';
  static const String wordsBack = 'wordsBack';
  static const String wordsCancel = 'wordsCancel';
  static const String wordsYes = 'wordsYes';
  static const String wordsSave = 'wordsSave';
  static const String wordsRepeat = 'wordsRepeat';
  static const String wordsLabel = 'wordsLabel';
  static const String wordsSnooze = 'wordsSnooze';
  static const String wordsDelete = 'wordsDelete';
  static const String wordsTime = 'wordsTime';
  static const String wordsStart = 'wordsStart';
  static const String wordsHours = 'wordsHours';
  static const String wordsMin = 'wordsMin';
  static const String wordsSec = 'wordsSec';
  static const String wordsWeight = 'wordsWeight';
  static const String wordsHeight = 'wordsHeight';
  static const String wordsKg = 'wordsKg';
  static const String wordsLb = 'wordsLb';
  static const String wordsBMI = 'wordsBMI';
  static const String wordsCm = 'wordsCm';
  static const String wordsInch = 'wordsInch';
  static const String wordsBluetooth = 'wordsBluetooth';
  static const String wordsStop = 'wordsStop';
  static const String wordsAizoRing = 'wordsAizoRing';
  static const String wordsSearch = 'wordsSearch';
  static const String wordsBPM = 'wordsBPM';
  static const String wordsD = 'wordsD';
  static const String wordsW = 'wordsW';
  static const String wordsM = 'wordsM';
  static const String wordsPercentage = 'wordsPercentage';
  static const String wordsName = 'wordsName';
  static const String wordsID = 'wordsID';
  static const String wordsGender = 'wordsGender';
  static const String wordsMale = 'wordsMale';
  static const String wordsFemale = 'wordsFemale';
  static const String wordsOther = 'wordsOther';
  static const String wordsPrompt = 'wordsPrompt';
  static const String wordsWakeUp = 'wordsWakeUp';
  static const String wordsBreakfast = 'wordsBreakfast';
  static const String wordsLunch = 'wordsLunch';
  static const String wordsDinner = 'wordsDinner';
  static const String wordsBedtime = 'wordsBedtime';
  static const String wordsMidnight = 'wordsMidnight';
  static const String wordsBeforeMeal = 'wordsBeforeMeal';
  static const String wordsAfterMeal = 'wordsAfterMeal';
  static const String wordsFeet = 'wordsFeet';
  static const String wordsInches = 'wordsInches';
  static const String wordsMmolL = 'wordsMmolL';
  static const String wordsHighest = 'wordsHighest';
  static const String wordsLowest = 'wordsLowest';
  static const String wordsOverSpeed = 'wordsOverSpeed';
  static const String wordsProposal = 'wordsProposal';
  static const String wordsSlow = 'wordsSlow';
  static const String wordsMax = 'wordsMax';
  static const String wordsUnnormal = 'wordsUnnormal';
  static const String wordsAttention = 'wordsAttention';
  static const String wordsKcal = 'wordsKcal';
  static const String wordsKm = 'wordsKm';
  static const String wordsDeep = 'wordsDeep';
  static const String wordsLight = 'wordsLight';
  static const String wordsRem = 'wordsRem';
  static const String wordsAwake = 'wordsAwake';
  static const String wordsData = 'wordsData';
  static const String wordsGo = 'wordsGo';
  static const String wordsScore = 'wordsScore';
  static const String wordsH = 'wordsH';
  static const String wordsDay = 'wordsDay';
  static const String wordsWeek = 'wordsWeek';
  static const String wordsMonth = 'wordsMonth';
  static const String wordsNone = 'wordsNone';
  static const String wordsIdle = 'wordsIdle';
  static const String wordsFinish = 'wordsFinish';
  // 日期相关
  static const String dateDay = 'dateDay';
  static const String dateWeek = 'dateWeek';
  static const String dateMonths = 'dateMonths';
  static const String dateToday = 'dateToday';
  static const String dateLastWeek = 'dateLastWeek';
  static const String dateLastMonth = 'dateLastMonth';
  static const String dateLastYear = 'dateLastYear';
  static const String dateEveryMonday = 'dateEveryMonday';
  static const String dateEveryTuesday = 'dateEveryTuesday';
  static const String dateEveryWednesday = 'dateEveryWednesday';
  static const String dateEveryThursday = 'dateEveryThursday';
  static const String dateEveryFriday = 'dateEveryFriday';
  static const String dateEverySaturday = 'dateEverySaturday';
  static const String dateEverySunday = 'dateEverySunday';
  static const String dateEveryday = 'dateEveryday';
  static const String dateMondayToFriday = 'dateMondayToFriday';
  static const String dateMonday = 'dateMonday';
  static const String dateTuesday = 'dateTuesday';
  static const String dateWednesday = 'dateWednesday';
  static const String dateThursday = 'dateThursday';
  static const String dateFriday = 'dateFriday';
  static const String dateSaturday = 'dateSaturday';
  static const String dateSunday = 'dateSunday';
  static const String dateMon = 'dateMon';
  static const String dateTue = 'dateTue';
  static const String dateWed = 'dateWed';
  static const String dateThu = 'dateThu';
  static const String dateFri = 'dateFri';
  static const String dateSat = 'dateSat';
  static const String dateSun = 'dateSun';

  // 详情相关
  static const String detailButton = 'detailButton';

  // 测量相关
  static const String measurementResult = 'measurementResult';
  static const String measurementSettings = 'measurementSettings';
  static const String measurementRemind = 'measurementRemind';
  static const String measurementRecord = 'measurementRecord';
  static const String measurementResultNormal = 'measurementResultNormal';
  static const String measurementTime = 'measurementTime';
  static const String measurementStarted = 'measurementStarted';
  static const String measurementError = 'measurementError';
  static const String setMeasureIntervalFailed = 'setMeasureIntervalFailed';
  static const String measurementInProgress = 'measurementInProgress';
  static const String measurementSuccess = "measurementSuccess";

  // 健康相关
  static const String healthTitle = 'healthTitle';
  static const String healthInformation = 'healthInformation';
  static const String healthRecoveryReport = 'healthRecoveryReport';
  static const String healthAllRecord = 'healthAllRecord';
  static const String healthScore = 'healthScore';
  static const String healthSleepLast = 'healthSleepLast';
  static const String healthCaloriesDay = 'healthCaloriesDay';
  static const String healthSleepTitle = 'healthSleepTitle';
  static const String healthBodyScoreTitle = 'healthBodyScoreTitle';
  static const String healthRingRemind = 'healthRingRemind';
  static const String healthAdvice = 'healthAdvice';
  static const String healthAdviceInfor1 = 'healthAdviceInfor1';
  static const String healthSleepScoreTitle = 'healthSleepScoreTitle';
  static const String healthActivityScoreTitle = 'healthActivityScoreTitle';
  static const String healthHeartRateScoreTitle = 'healthHeartRateScoreTitle';
  static const String healthBloodOxygenTitle = 'healthBloodOxygenTitle';
  static const String healthTemperatureTitle = 'healthTemperatureTitle';
  static const String healthHolistic = 'healthHolistic';
  static const String healthTimeAsleep = 'healthTimeAsleep';
  static const String healthSleepEffi = 'healthSleepEffi';
  static const String healthFallAsleep = 'healthFallAsleep';
  static const String healthSleepScore = 'healthSleepScore';
  static const String healthActivityScore = 'healthActivityScore';
  static const String healthDurationExer = 'healthDurationExer';
  static const String healthNumberExer = 'healthNumberExer';
  static const String healthNumberActi = 'healthNumberActi';
  static const String healthActivityCon = 'healthActivityCon';
  static const String healthResting = 'healthResting';
  static const String healthProposalNo = 'healthProposalNo';
  static const String healthProposalAchieved = 'healthProposalAchieved';
  static const String healthChanged = 'healthChanged';
  static const String healthAverageSleepTemperature = 'healthAverageSleepTemperature';

  // 底部导航栏
  static const String bottomNavHome = 'bottomNavHome';
  static const String bottomNavHealth = 'bottomNavHealth';
  static const String bottomNavMy = 'bottomNavMy';

  // 知识相关
  static const String knowledge = 'knowledge';
  static const String knowledgeManuallyEnterData = 'knowledgeManuallyEnterData';
  static const String knowledUnderstandRelevant = 'knowledUnderstandRelevant';
  static const String bloodOxygenTitle = 'bloodOxygenTitle';
  static const String bloodOxygenTitle1 = 'bloodOxygenTitle1';
  static const String bloodOxygenTitle2 = 'bloodOxygenTitle2';
  static const String bloodOxygenValue1 = 'bloodOxygenValue1';
  static const String bloodOxygenValue2 = 'bloodOxygenValue2';
  static const String bloodPressureTitle = 'bloodPressureTitle';
  static const String bloodPressureTitle1 = 'bloodPressureTitle1';
  static const String bloodPressureTitle2 = 'bloodPressureTitle2';
  static const String bloodPressureValue1 = 'bloodPressureValue1';
  static const String bloodPressureValue2 = 'bloodPressureValue2';
  static const String temperatureTitle1 = 'temperatureTitle1';
  static const String temperatureValue1 = 'temperatureValue1';
  static const String bloodSugarTitle1 = 'bloodSugarTitle1';
  static const String bloodSugarTitle2 = 'bloodSugarTitle2';
  static const String bloodSugarTitle3 = 'bloodSugarTitle3';
  static const String bloodSugarValue1 = 'bloodSugarValue1';
  static const String bloodSugarValue2 = 'bloodSugarValue2';
  static const String bloodSugarValue3 = 'bloodSugarValue3';
  static const String heartRateTitle1 = 'heartRateTitle1';
  static const String heartRateValue1 = 'heartRateValue1';

  static const String getLastTemperature = 'getLastTemperature';
  static const String setTime = 'setTime';

  // 体重相关
  static const String weightTrend = 'weightTrend';

  // 心率相关
  static const String heartRate = 'heartRate';

  // 异常提醒
  static const String abnormalRemind = 'abnormalRemind';
  static const String abnormalRemindLowRate = 'abnormalRemindLowRate';
  static const String abnormalRemindHighRate = 'abnormalRemindHighRate';

  // 提示词
  static const String promptManualUploadTrue = 'promptManualUploadTrue';
  static const String promptManualUploadFalse = 'promptManualUploadFalse';
  static const String promptFillAll = 'promptFillAll';
  static const String promptInt0TO100 = 'promptInt0TO100';
  static const String promptInt0TO200 = 'promptInt0TO200';
  static const String promptDateTimeillegal = 'promptDateTimeillegal';
  static const String promptFahren95TO107 = 'promptFahren95TO107';
  static const String promptCelsius35TO42 = 'promptCelsius35TO42';
  static const String promptModifSuccess = 'promptModifSuccess';
  static const String promptModifFailed = 'promptModifFailed';
  static const String promptCameraNo = 'promptCameraNo';

  // 用户相关
  static const String userHealthGoal = 'userHealthGoal';
  static const String userUnitSetting = 'userUnitSetting';
  static const String userAccountSecurity = 'userAccountSecurity';
  static const String userPersonInfo = 'userPersonInfo';
  static const String userSetName = 'userSetName';
  static const String userSetID = 'userSetID';
  static const String userPicture = 'userPicture';

  // 账号与安全
  static const String asDeleteAccount = 'asDeleteAccount';
  static const String asDeleteAccountP = 'asDeleteAccountP';
  static const String asLogOutP = 'asLogOutP';

  // 记录相关
  static const String recordDropMore = 'recordDropMore';
  static const String recordNoMore = 'recordNoMore';

  // 健康目标设置
  static const String hgsTitle = 'hgsTitle';
  static const String hgsStepTarget = 'hgsStepTarget';
  static const String hgsBloodPressureGoal = 'hgsBloodPressureGoal';
  static const String hgsBloodGlucoseGoal = 'hgsBloodGlucoseGoal';
  static const String hgsReminderHeartRate = 'hgsReminderHeartRate';
  static const String hgsDailyGoal = 'hgsDailyGoal';
  static const String hgsHighReminderHeartRate = 'hgsHighReminderHeartRate';
  static const String hgsLowReminderHeartRate = 'hgsLowReminderHeartRate';

  // 单位设置
  static const String unitSTitle = 'unitSTitle';
  static const String unitSHeightUnit = 'unitSHeightUnit';
  static const String unitSWeightUnit = 'unitSWeightUnit';
  static const String unitSTemperatureUnit = 'unitSTemperatureUnit';
  static const String unitSBloodPressureUnit = 'unitSBloodPressureUnit';
  static const String unitSBloodGlucoseUnits = 'unitSBloodGlucoseUnits';
  static const String unitSMetricSystem = 'unitSMetricSystem';
  static const String unitSImperialSystem = 'unitSImperialSystem';
  static const String unitSAutomatic = 'unitSAutomatic';
  static const String unitSLowBloodReminder = 'unitSLowBloodReminder';
  static const String unitSCentigrade = 'unitSCentigrade';
  static const String unitSFahrenheit = 'unitSFahrenheit';

  // 步数相关
  static const String steps = 'steps';
  static const String stepsDistance = 'stepsDistance';
  static const String stepsRecord = 'stepsRecord';
  static const String stepsTargetProgress = 'stepsTargetProgress';
  static const String stepsCaloBurned = 'stepsCaloBurned';
  static const String stepsCaloCheeseburger = 'stepsCaloCheeseburger';
  static const String stepsFourStadium = 'stepsFourStadium';

  // 运动相关
  static const String fitness = 'fitness';
  static const String fitnessDailyAct = 'fitnessDailyAct';
  static const String fitnessDistanceM = 'fitnessDistanceM';
  static const String fitnessConsumKcal = 'fitnessConsumKcal';
  static const String fitnessAdjustGoals = 'fitnessAdjustGoals';
  static const String fitnessPhysicalexercise = 'fitnessPhysicalexercise';
  static const String fitnessExeRecords = 'fitnessExeRecords';
  static const String fitnessStayAct = 'fitnessStayAct';
  static const String fitnessBeneficialAct = 'fitnessBeneficialAct';
  static const String fitnessExerciseVol = 'fitnessExerciseVol';
  static const String fitnessExerciseFre = 'fitnessExerciseFre';
  static const String fitnessPromptNo = 'fitnessPromptNo';

  // 睡眠相关
  static const String sleep = 'sleep';
  static const String sleepNaps = 'sleepNaps';
  static const String sleepTotalNap = 'sleepTotalNap';
  static const String sleepNapNote1 = 'sleepNapNote1';
  static const String sleepBreathQuality = 'sleepBreathQuality';
  static const String sleepOxygenDuring = 'sleepOxygenDuring';
  static const String sleepManagement = 'sleepManagement';
  static const String sleepTotalDurationNight = 'sleepTotalDurationNight';

  // Test Component View
  static const String bluetoothFunction = 'bluetoothFunction';
  static const String deviceStatus = 'deviceStatus';
  static const String connectionStatus = 'connectionStatus';
  static const String batteryStatus = 'batteryStatus';
  static const String batteryLevel = 'batteryLevel';
  static const String connectionControl = 'connectionControl';
  static const String connectDevice = 'connectDevice';
  static const String disconnectDevice = 'disconnectDevice';
  static const String measurementInterval = 'measurementInterval';
  static const String getCurrentInterval = 'getCurrentInterval';
  static const String set10MinutesInterval = 'set10MinutesInterval';
  static const String set20MinutesInterval = 'set20MinutesInterval';
  static const String set30MinutesInterval = 'set30MinutesInterval';
  static const String measurementControl = 'measurementControl';
  static const String measureHeartRate = 'measureHeartRate';
  static const String measureBloodOxygen = 'measureBloodOxygen';
  static const String measure = "measure";
  static const String measureTemperature = 'measureTemperature';
  static const String dataRetrieval = 'dataRetrieval';
  static const String getHealthData = 'getHealthData';
  static const String getSleepData = 'getSleepData';
  static const String getHardwareInfo = 'getHardwareInfo';
  static const String permissions = 'permissions';
  static const String requestBluetoothPermission = 'requestBluetoothPermission';
  static const String requestLocationPermission = 'requestLocationPermission';
  static const String requestNotificationPermission = 'requestNotificationPermission';

  // AOJ温度计相关
  static const String aojThermometer = 'aojThermometer';
  static const String scanAndClassifyDevices = 'scanAndClassifyDevices';
  static const String noDevicesFound = 'noDevicesFound';
  static const String unknownDevice = 'unknownDevice';
  static const String macAddress = 'macAddress';
  static const String connectAojThermometer = 'connectAojThermometer';
  static const String disconnectAojThermometer = 'disconnectAojThermometer';
  static const String getBatteryLevel = 'getBatteryLevel';
  static const String currentTemperature = 'currentTemperature';
  static const String lastMeasurementTime = 'lastMeasurementTime';

  static const String currentMeasurementInterval = 'currentMeasurementInterval';
  static const String currentMeasurementStatus = 'currentMeasurementStatus';

  // 错误相关
  static const String errorServer = 'errorServer';
  static const String errorNotFound = 'errorNotFound';
  static const String errorForbidden = 'errorForbidden';
  
  // 蓝牙控制器相关
  static const String deviceDisconnectedRetry = 'deviceDisconnectedRetry';
  static const String measurementFailedRetry = 'measurementFailedRetry';
  static const String operationFailedRetry = 'operationFailedRetry';
  static const String disconnectFailedRetry = 'disconnectFailedRetry';
  static const String bluetoothInitFailedRetry = 'bluetoothInitFailedRetry';
  static const String aojDeviceConnectSuccess = 'aojDeviceConnectSuccess';
  static const String aojDeviceConnectFailed = 'aojDeviceConnectFailed';
  static const String aojDeviceDisconnectSuccess = 'aojDeviceDisconnectSuccess';
  static const String aojDeviceDisconnectFailed = 'aojDeviceDisconnectFailed';
  static const String bluetoothStatusUnknown = 'bluetoothStatusUnknown';
  static const String dataParseFailedRetry = 'dataParseFailedRetry';
  static const String requestFailed = 'requestFailed';
  static const String serverError = 'serverError';
  static const String invalidResponseFormat = 'invalidResponseFormat';
  static const String requestCancelled = 'requestCancelled';


  // 设备相关
  static const String aiRingConnectionStatus = 'aiRingConnectionStatus';
  static const String airingBatteryStatus = 'airingBatteryStatus';
  static const String airingBatteryLevel = 'airingBatteryLevel';
  static const String measurementResults = 'measurementResults';
  static const String currentMeasurement = 'currentMeasurement';
  static const String bluetoothNotCharged = 'bluetoothNotCharged';
  static const String bluetoothCharged = 'bluetoothCharged';
  static const String bluetoothCharging = 'bluetoothCharging';

  
  
  
  
}