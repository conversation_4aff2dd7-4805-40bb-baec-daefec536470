/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-03-13 15:48:20
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-13 15:50:13
 * @FilePath: /flutter-template-getx/lib/app/core/model/page_state.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

/// 页面状态枚举
/// 
/// 用于表示页面在不同阶段的状态，便于统一管理UI展示和状态处理
/// 在GetX架构中，常用于控制页面的加载、错误、成功等状态的展示
enum PageState {
  /// 页面的初始默认状态
  /// 
  /// 通常用于页面初始化时，显示默认的UI布局
  DEFAULT,      //页面的初始默认状态。

  /// 页面正在加载数据
  /// 
  /// 用于显示加载中的提示，如加载进度条、骨架屏等
  /// 通常在网络请求或数据加载过程中使用
  LOADING,      //页面正在加载数据，可用于显示加载中的提示，如加载进度条。

  /// 数据加载成功
  /// 
  /// 表示数据已成功加载，可以显示正常的页面内容
  /// 通常用于网络请求成功后的状态
  SUCCESS,      //数据加载成功，可用于显示正常的页面内容

  /// 数据加载失败
  /// 
  /// 表示数据加载过程中出现错误
  /// 通常用于显示错误提示、重试按钮等
  FAILED,       //数据加载失败，可用于显示错误提示。

  /// 数据更新成功
  /// 
  /// 表示数据已成功更新
  /// 通常用于显示更新成功的提示信息
  UPDATED,      //数据更新成功，可用于提示用户数据已更新。

  /// 数据创建成功
  /// 
  /// 表示新数据已成功创建
  /// 通常用于显示创建成功的提示信息
  CREATED,      //数据创建成功，可用于提示用户新数据已创建

  /// 网络连接失败
  /// 
  /// 表示设备无法连接到网络
  /// 通常用于提示用户检查网络连接
  NO_INTERNET,  //网络连接失败，可用于提示用户检查网络连接

  /// 一般性消息提示
  /// 
  /// 用于显示普通的消息提示
  /// 通常用于显示操作结果、提示信息等
  MESSAGE,      //用于显示一般性的消息提示。

  /// 用户未授权
  /// 
  /// 表示用户未登录或未获得必要权限
  /// 通常用于引导用户进行登录或授权操作。
  UNAUTHORIZED, //用户未授权，可用于引导用户进行登录或授权操作。
}

