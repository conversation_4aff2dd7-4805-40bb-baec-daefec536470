enum AizoConnectStatus {
  /// 已断开。disconnected
  DISCONNECTED,

  /// 终端移除配对信息。Terminal removed pairing info.
  PAIRING_REMOVED,

  /// 设备关闭。Device off.
  OFF,

  /// 未绑定设备。Unbound device.
  NO_PAIR,

  /// 连接中。Connecting.
  CONNECTING,

  /// 已连接。Connected.
  CONNECTED,

  /// 连接失败。Connection failed.
  CONNECT_FAILED,

  /// 戒指端点击拒绝。Watch side refused.
  AUTH_REFUSED,

  /// 鉴权成功。Authentication successful.
  AUTH_SUCCESS,

  /// 失败已被其他手机绑定。Failed, already bound to another phone.
  AUTH_BOUND,

  /// 非法厂商。Illegal manufacturer.
  AUTH_ILLEGAL,

  /// 厂商ID不一致。Vendor ID mismatch.
  VENDOR_ID_DIFFERENT,

  /// App的ID不一致。App ID mismatch.
  APP_ID_DIFFERENT
}
