import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/dialog_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart';

class PermissionUtil {
  /// 显示权限设置对话框
  static Future<void> _showPermissionDialog(
      BuildContext context, String permissionName) async {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('权限申请'),
          content: Text('需要$permissionName权限才能使用此功能，是否前往设置页面开启权限？'),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('去设置'),
              onPressed: () async {
                Navigator.of(context).pop();
                await _openSettings();
              },
            ),
          ],
        );
      },
    );
  }

  /// 请求相机权限
  static Future<bool> requestCamera() async {
    print("requestCamera");
    var status = await Permission.camera.status;
    if (status.isGranted) {
      print("相机权限已授权");

      return true;
    }

    status = await Permission.camera.request();
    if (status.isGranted) {
      print("相机权限请求成功");

      return true;
    }

    if (status.isDenied || status.isPermanentlyDenied) {
      bool? result =
          await DialogUtil.showPermissionDialog(Get.context!, T.camera.tr);
      if (result == true) {
        await _openSettings();
      }
    }
    print("requestCamera false");
    return false;
  }

  /// 请求相册权限
  static Future<bool> requestPhotos() async {
    print("requestPhotos");
    var status = await Permission.photos.status;
    if (status.isGranted) {
      print("相册权限已授权");
      return true;
    }
    
    status = await Permission.photos.request();
    if (status.isGranted) {
      print("相册权限请求成功");
      return true;
    }
    
    if (status.isDenied || status.isPermanentlyDenied) {
      bool? result =
          await DialogUtil.showPermissionDialog(Get.context!, T.photos.tr);
      if (result == true) {
        await _openSettings();
      }
    }
    print("requestPhotos false");
    return false;
  }

  /// 请求位置权限（用于蓝牙扫描）
  static Future<bool> requestLocation() async {
    print("requestLocation");
    // 请求精确位置权限
    var status = await Permission.locationWhenInUse.status;
    if (status.isGranted) {
      print("位置权限已授权");
      return true;
    }

    status = await Permission.locationWhenInUse.request();
    if (status.isGranted) {
      print("位置权限请求成功");

      return true;
    }

    if (status.isDenied || status.isPermanentlyDenied) {
      bool? result =
          await DialogUtil.showPermissionDialog(Get.context!, T.location.tr);
      if (result == true) {
        await _openSettings();
      }
    }
    print("requestLocation false");
    return false;
  }

  /// 请求通知权限
  static Future<bool> requestNotification() async {
    print("requestNotification");
    var status = await Permission.notification.status;
    if (status.isGranted) {
      print("通知权限已授权");
      return true;
    }
    
    status = await Permission.notification.request();
    if (status.isGranted) {
      print("通知权限请求成功");
      return true;
    }
    
    if (status.isDenied || status.isPermanentlyDenied) {
      bool? result = await DialogUtil.showPermissionDialog(
          Get.context!, T.notification.tr);
      if (result == true) {
        await _openSettings();
      }
    }
    print("requestNotification false");
    return false;
  }

  /// 请求蓝牙权限
  static Future<bool> requestBluetooth() async {
    print("requestBluetooth");

    if (Platform.isAndroid) {
      // Android 平台权限处理
      // 首先请求位置权限（Android 12以下需要）
      if (!await requestLocation()) {
        print("位置权限被拒绝，无法使用蓝牙功能");
        return false;
      }

      // 检查蓝牙权限状态
      var bluetoothStatus = await Permission.bluetooth.status;
      var bluetoothScanStatus = await Permission.bluetoothScan.status;
      var bluetoothConnectStatus = await Permission.bluetoothConnect.status;
      var bluetoothAdvertiseStatus = await Permission.bluetoothAdvertise.status;

      // 如果所有权限都已授权，直接返回true
      if (bluetoothStatus.isGranted && 
          bluetoothScanStatus.isGranted && 
          bluetoothConnectStatus.isGranted && 
          bluetoothAdvertiseStatus.isGranted) {
        print("所有蓝牙权限已授权");
        return true;
      }

      // 请求所有必要的蓝牙权限
      Map<Permission, PermissionStatus> statuses = await [
        Permission.bluetooth,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.bluetoothAdvertise,
      ].request();

      // 检查所有权限是否都已授权
      bool allGranted = true;
      statuses.forEach((permission, status) {
        if (!status.isGranted) {
          allGranted = false;
          print("${permission.toString()} 权限未授权");
        }
      });

      if (allGranted) {
        print("所有蓝牙权限已授权");
        return true;
      }

      // 如果有权限被拒绝，显示设置对话框
      if (statuses.values.any((status) => status.isDenied || status.isPermanentlyDenied)) {
        bool? result = await DialogUtil.showPermissionDialog(Get.context!, T.bluetooth.tr);
        if (result == true) {
          await _openSettings();
        }
      }
    } else if (Platform.isIOS) {

          // 首先请求位置权限（Android 12以下需要）
      if (!await requestLocation()) {
        print("位置权限被拒绝，无法使用蓝牙功能");
        return false;
      }
      // iOS 平台权限处理
      // iOS 只需要基本的蓝牙权限
      var bluetoothStatus = await Permission.bluetooth.status;
      
      if (bluetoothStatus.isGranted) {
        print("iOS蓝牙权限已授权");
        return true;
      }

      bluetoothStatus = await Permission.bluetooth.request();
      if (bluetoothStatus.isGranted) {
        print("iOS蓝牙权限请求成功");
        return true;
      }

      if (bluetoothStatus.isDenied || bluetoothStatus.isPermanentlyDenied) {
        bool? result = await DialogUtil.showPermissionDialog(Get.context!, T.bluetooth.tr);
        if (result == true) {
          await _openSettings();
        }
      }
    }

    print("requestBluetooth false");
    return false;
  }

  /// 检查权限状态
  static Future<bool> checkPermission(Permission permission) async {
    var status = await permission.status;
    return status.isGranted;
  }

  /// 打开应用设置页面
  static Future<bool> _openSettings() async {
    return await openAppSettings();
  }
}
