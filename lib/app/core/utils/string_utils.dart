/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-05 15:08:52
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-19 15:05:32
 * @FilePath: /rpmappmaster/lib/app/core/utils/string_utils.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// /*
//  * @Author: 张仕鹏 <EMAIL>
//  * @Date: 2024-11-05 15:08:52
//  * @LastEditors: 张仕鹏 <EMAIL>
//  * @LastEditTime: 2024-11-06 10:05:04
//  * @FilePath: /rpmappmaster/lib/app/core/utils/string_utils.dart
//  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
//  */
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

String capitalizeEachWord(String input) {
  if (input.isEmpty) {
    return input;
  }
  return input.split(' ').map((word) => word.capitalize()).join(' ');
}

extension StringExtension on String {
  String capitalize() {
    if (this.isEmpty) {
      return this;
    }
    return this[0].toUpperCase() + this.substring(1);
  }
}

extension ScanResultToAizoRing on ScanResult {
  AizoRing toAizoRing() {
    return AizoRing(
      name: advertisementData.advName ?? 'Unknown',
      // uuidString: advertisementData.u device.,
      // macAddress: device.id, // 在 Android 上可以使用 MAC 地址，在 iOS 上 UUID 作为标识符
      rssi: rssi,
      isSystemConnected: advertisementData.connectable,
    );
  }
}