import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/model/aizo_sleep_data.dart';
import 'package:aiCare/app/data/repository/bluetooth_repository_impl.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';

class BackendUtil with WidgetsBindingObserver {
  Timer? _monitorTimer;
  final storage = SecureStorageService.instance;
  final bluetoothRepository = BluetoothRepositoryImpl();
  final defaultRepositoryImpl = DefaultRepositoryImpl();
  int timeInterval = 10;

  final FlutterLocalNotificationsPlugin notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static final BackendUtil _instance = BackendUtil._internal();
  factory BackendUtil() => _instance;

  BackendUtil._internal() {
    WidgetsBinding.instance.addObserver(this);
    _init();
  }

  Future<void> _init() async {
    print('BackendUtil: 开始初始化');
    await initNotifications();
    showForegroundNotification();
    print('BackendUtil: Headless任务注册完成');
  }

  setTimeInterval(int timeInterval) {
    this.timeInterval = timeInterval;
  }


  // 处理 Headless Task 的静态方法
  static Future<void> handleHeadlessTask(String taskId) async {
    print('[HeadlessTask] 开始处理健康数据检查');
    final prefs = await SharedPreferences.getInstance();
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    prefs.setInt(AppValues.lastMeasurementKey, currentTime);
    prefs.setBool(AppValues.isAutoUpdate, true);
    print('[HeadlessTask] 已设置测量更新标志');
  }

  // 初始化通知
  Future<void> initNotifications() async {
    const AndroidInitializationSettings androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
        if(Platform.isAndroid){
              await notificationsPlugin.initialize(
      const InitializationSettings(android: androidSettings),
    );
        }


  }

  // 显示前台通知
  Future<void> showForegroundNotification() async {
    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'health_monitor',
      'Health Monitor',
      channelDescription: 'Health data tracking service',
      importance: Importance.low,
      priority: Priority.low,
      ongoing: true,
      playSound: false,
      enableVibration: false,
      showWhen: false,
    );

    await notificationsPlugin.show(
      1,
      'Health Monitor Active',
      'Monitoring your health data',
      const NotificationDetails(android: androidDetails),
    );
  }


  String _getStatusDescription(int status) {
    switch (status) {
      case 0:
        return 'BackgroundFetch.STATUS_AVAILABLE';
      case 1:
        return 'BackgroundFetch.STATUS_DENIED';
      case 2:
        return 'BackgroundFetch.STATUS_RESTRICTED';
      default:
        return '未知状态: $status';
    }
  }

  // 后台任务回调
  //1.获取上一次的测量时间,如果为空，则设置当前时间戳为上一次测量时间
  //2.获取当前时间戳
  //3.计算时间差
  //4.如果时间差大于timeInterval分钟，则设置更新标志
  //5.如果时间差小于timeInterval分钟，则不设置更新标志
  Future<void> _onBackgroundFetch(String taskId) async {
    if(storage.getID() == null){
      print("id为空，不进行测量");
      return;
    }else{
      print("id不为空，进行测量");
    }

    print('[BackgroundFetch] 触发健康数据检查：$taskId');
    //获取上一次的测量时间,如果为空，则设置当前时间戳为上一次测量时间
    int? lastMeasurementTime = storage.getInt(AppValues.lastMeasurementKey);
    //时间戳转时间

    if (lastMeasurementTime == null) {
      lastMeasurementTime = DateTime.now().millisecondsSinceEpoch;
      storage.setInt(AppValues.lastMeasurementKey, lastMeasurementTime);
    } else {
      print(DateTime.fromMillisecondsSinceEpoch(lastMeasurementTime));
    }
    //获取当前时间戳
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    //计算时间差
    int timeDiff = currentTime - lastMeasurementTime;
    int rage = timeDiff - timeInterval * 60 * 1000;
    // logger.d("")
    print("时间差为:"+rage.toString());
    print("是否触发监听${timeDiff > timeInterval * 60 * 1000}");
    //如果时间差大于timeInterval分钟，则设置更新标志
    if (timeDiff > timeInterval * 60 * 1000) {
      
      storage.setBool(AppValues.isAutoUpdate, true);
      storage.setInt(AppValues.lastMeasurementKey, currentTime);
    }
      //     storage.setBool(AppValues.isAutoUpdate, true);
      // storage.setInt(AppValues.lastMeasurementKey, currentTime);
    try {
      final needsUpdate = storage.getBool(AppValues.isAutoUpdate) ?? false;

      if (needsUpdate) {
        await _handleMeasurementLogic();
      }
      print('[BackgroundFetch] 任务完成：$taskId');
    } catch (e) {
      print('[BackgroundFetch] 任务执行出错：$e');
    } finally {
    }
  }

  // 应用状态变化回调
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print('应用状态变化: $state');

    if (state == AppLifecycleState.resumed) {
      _onBackgroundFetch("监听触发ID");
    }
    // else if (state == AppLifecycleState.paused) {
    //   _handleAppPaused();
    // }
  }

  // 应用回到前台
  // Future<void> _handleAppResumed() async {
  //   print("应用回到前台");
  //   try {
  //     // 检查是否有待处理的更新请求
  //     if (storage.getBool(AppValues.isAutoUpdate) == true) {
  //       print('有待处理的测量请求');
  //       // 通知 BluetoothController 处理更新
  //       try {
  //         final bluetoothController = Get.find<BluetoothController>();
  //         await bluetoothController.checkAndUpdateData();
  //       } catch (e) {
  //         print('获取 BluetoothController 失败: $e');
  //       }
  //     }

  //     // 检查是否需要初始化蓝牙和重连
  //     try {
  //       final bluetoothController = Get.find<BluetoothController>();
  //       if (bluetoothController.hasConnectedBefore) {
  //         await bluetoothController.initialize();
  //       }
  //     } catch (e) {
  //       print('初始化蓝牙和重连失败: $e');
  //     }
  //   } catch (e) {
  //     print('处理应用恢复状态失败: $e');
  //   }
  // }

  // 应用退到后台
  // Future<void> _handleAppPaused() async {
  //   print('应用进入后台');
  //   try {
  //     // 检查蓝牙控制器是否存在且已连接
  //     final bluetoothController = Get.find<BluetoothController>();
  //     if (bluetoothController.isConnected.value) {
  //       // 启动后台任务
  //       if (Platform.isAndroid) {
  //         // 在Android上启动前台服务
  //         await BackgroundFetch.start().then((_) async {
  //           await showForegroundNotification();
  //           // 设置一个标志，表示需要在下一次后台任务执行时更新数据
  //           final prefs = await SharedPreferences.getInstance();
  //           await prefs.setBool(_measurementRequestedKey, true);
  //         });
  //       } else if (Platform.isIOS) {
  //         // 在iOS上，我们只能依赖BackgroundFetch
  //         final prefs = await SharedPreferences.getInstance();
  //         await prefs.setBool(_measurementRequestedKey, true);
  //       }
  //     }
  //   } catch (e) {
  //     print('处理应用暂停状态失败: $e');
  //   }
  // }

  // 处理测量逻辑
  Future<void> _handleMeasurementLogic() async {
    try {
      var bearer = await storage.getIDToken();
      if(bearer == null){
        print("bearer为空，不进行测量");
        return;
      }
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      storage.setInt(AppValues.lastMeasurementKey, currentTime);



      // 检查应用状态
      final isAppActive =
          WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;

      if (isAppActive) {
        print('[状态检测] 应用在前台');

        // 尝试获取蓝牙控制器并更新数据
        try {
          final bluetoothController = Get.find<BluetoothController>();
          await bluetoothController.updateMeasurementData();
        storage.setBool(AppValues.isAutoUpdate, false);
          //
          
        } catch (e) {
          print('更新测量数据失败: $e');
        }
      } else {
        print('[状态检测] 应用在后台，设置更新标志');
        storage.setBool(AppValues.isAutoUpdate, true);
      }
    } catch (e) {
      print('处理测量逻辑失败: $e');
    }
  }

  // 在类销毁时清理
  void dispose() {
    _monitorTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
  }
}
