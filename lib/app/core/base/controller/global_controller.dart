import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/app/core/utils/logger_singleton.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

class GlobalController extends GetxController {
  // =============== 基础服务 ===============
  /// 安全存储服务实例
  final storage = SecureStorageService.instance;

  /// 日志服务实例
  final Logger logger = LoggerSingleton.getInstance();
  
  @override
  void onInit() {
    super.onInit();
    logger.d("Global`Controller初始化");
  }
}