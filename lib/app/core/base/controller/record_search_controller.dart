/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-09-04 12:20:53
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-11 16:53:12
 * @FilePath: /RPM-APP-MASTER/lib/app/core/base/controller/search_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

abstract class RecordSearchController<T> extends BaseController {
  RxBool searchBool = false.obs;

  //Precise time filtering of text box values
  Rx<List<String>> firstData = Rx<List<String>>(["0000", "00", "00"]);
  Rx<List<String>> lateData = Rx<List<String>>(["0000", "00", "00"]);

  //Accurate time interval
  Rx<List<DateTime?>> selectedDateRange = Rx<List<DateTime?>>([null, null]);

  //Enable filtering criteria
  RxBool enableFilter = false.obs;

  //Degree screening
  RxInt selectSeverity = 99.obs;
  RxList<String> severityList = [""].obs;

  //Fuzzy time filtering and precise time filtering are mutually exclusive
  //and can only have one value
  RxInt selectFuzzyTime = 99.obs;
  RxBool selectExactShow = false.obs;

  //Fuzzy time filtering interval
  RxList<String> dateList = [""].obs;

  //List of fake data
  List<T> maskData = List.empty();

  //Filtered list
  RxList<T> filterList = RxList<T>([]);

  /**
   * @description: open search dropdown menu
   * @return {*}
   */
  doOpen() {
    searchBool.value = true;
    update();
  }

    /**
   * @description: Click Finish to start processing filtering rules
   * @return null
   */
  doFinish() {
    
  }


  /**
   * @description: Select degree type
   * @return null
   */
  doSelectSeverity(int index) {
    if (selectSeverity.value != 99) {
      if (severityList.value[index] ==
          severityList.value[selectSeverity.value]) {
        selectSeverity.value = 99;
        update();
        return;
      }
    }

    switch (index) {
      case 0:
        selectSeverity.value = 0;
        break;
      case 1:
        selectSeverity.value = 1;
        break;
      case 2:
        selectSeverity.value = 2;
        break;
      case 3:
        selectSeverity.value = 3;
        break;
      case 4:
        selectSeverity.value = 4;
        break;
      default:
        selectSeverity.value = 99;
    }
    update();
  }

  /**
   * @description: Choose fuzzy time
   * @return null
   */
  doSelectDate(int index) {
    if (selectFuzzyTime.value != 99) {
      if (dateList.value[index] == dateList.value[selectFuzzyTime.value]) {
        selectFuzzyTime.value = 99;
        update();
        refreshExactDate();
        return;
      }
    }
    switch (index) {
      case 0:
        selectFuzzyTime.value = 0;
        break;
      case 1:
        selectFuzzyTime.value = 1;
        break;
      case 2:
        selectFuzzyTime.value = 2;
        break;
      case 3:
        selectFuzzyTime.value = 3;
        break;

      default:
        selectFuzzyTime.value = 99;
    }
    refreshExactDate();
    update();
  }

  /**
   * @description: Refresh Time Text Box
   * @return null
   */
  void refreshExactDate() {
    DateTime now = DateTime.now();

    switch (selectFuzzyTime.value) {
      case 0:
        setFirstAndLateData(now, now);
        selectExactShow.value = true;
        break;
      case 1:
        setFirstAndLateData(now.subtract(Duration(days: 7)), now);
        selectExactShow.value = true;
        break;
      case 2:
        setFirstAndLateData(DateTime(now.year, now.month - 1, now.day), now);
        selectExactShow.value = true;
        break;
      case 3:
        setFirstAndLateData(DateTime(now.year - 1, now.month, now.day), now);
        selectExactShow.value = true;
        break;
      case 99:
        firstData.value = ["0000", "00", "00"];
        lateData.value = ["0000", "00", "00"];
        selectExactShow.value = false;
        update();

      default:
        return;
    }
  }

  /**
   * @description: Set the time text box to display content
   * @return null
   */
  void setFirstAndLateData(DateTime first, DateTime late) {
    firstData.value[0] = (first.year).toString().padLeft(4, '0'); // 只保存后两位
    firstData.value[1] = first.month.toString().padLeft(2, '0');
    firstData.value[2] = first.day.toString().padLeft(2, '0');

    lateData.value[0] = (late.year).toString().padLeft(4, '0'); // 只保存后两位
    lateData.value[1] = late.month.toString().padLeft(2, '0');
    lateData.value[2] = late.day.toString().padLeft(2, '0');

    firstData.refresh();
    lateData.refresh();
    logger.d(firstData);
    logger.d(lateData);
    update(); // Assuming you have a GetX controller with an update() method to trigger UI refresh
  }

  /**
   * @description: Open the date selection box, and users can choose their own time interval
   * @return null
   */
  selectDateRange(BuildContext context) async {
    // if (!selectExactShow.value) {
    //   return;
    // }
    final picked = await showCalendarDatePicker2Dialog(
      context: context,
      config: CalendarDatePicker2WithActionButtonsConfig(
          calendarType: CalendarDatePicker2Type.range,
          firstDate: DateTime(2019), // 设置可选的最早日期
          lastDate: DateTime.now(), // 设置可选的最晚日期为当前日期
          todayTextStyle: TextStyle(),
          // todayButtonDecoration:
          selectedDayHighlightColor: AppColors.lightBlue80,
          selectedRangeHighlightColor: AppColors.lightBlue80,
          selectedRangeDayTextStyle: TextStyle(color: AppColors.colorWhite)),
      dialogSize: Size(ScreenAdapter.width(325), ScreenAdapter.height(400)),
      value: selectedDateRange.value,
      borderRadius: BorderRadius.circular(15),
    );
    logger.d(picked);

    if (picked != null && picked != selectedDateRange.value) {
      selectFuzzyTime.value = 99;
      selectedDateRange.value = picked;

      setFirstAndLateData(
          selectedDateRange.value[0]!, selectedDateRange.value[1]!);
      selectExactShow.value = true;

      update();
    }
  }

  /**
   * @description: Process user clicks on blank spaces and clear selection rules after closing, shrink dropdown menu
   * @return {*}
   */
  doClose() {
    //全部回归初始状态
    searchBool.value = false;
    //精确时间筛选
    firstData.value = ["00", "00", "00"];
    lateData.value = ["00", "00", "00"];
    //启用筛选条件
    enableFilter.value = false;
    //程度筛选
    selectSeverity.value = 99;
    //模糊时间筛选 和 精确时间筛选 互斥，只能存在一个值
    selectFuzzyTime.value = 99;

   

    update();
  }
}
