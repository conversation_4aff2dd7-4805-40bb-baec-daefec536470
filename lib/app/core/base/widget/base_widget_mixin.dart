/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-07 13:30:33
 * @FilePath: /RPM-APP-MASTER/lib/app/core/base/widget/base_widget_mixin.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// import 'package:aiCare/app/services/l10nService.dart';
import 'package:aiCare/app/core/utils/logger_singleton.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:aiCare/app/core/utils/string_utils.dart';
import '/flavors/build_config.dart';

mixin BaseWidgetMixin on StatelessWidget {
  // AppLocalizations get appLocalization => AppLocalizations.of(Get.context!)!;
  // final Logger logger = BuildConfig.instance.config.logger;
  final storage = SecureStorageService.instance;
  final Logger logger = LoggerSingleton.getInstance();
  // final l10nService l10n = l10nService();
  

  @override
  Widget build(BuildContext context) {
    return Container(
      child: body(context),
    );
  }

  Widget body(BuildContext context);
}
