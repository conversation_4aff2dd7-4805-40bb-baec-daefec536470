import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class GlowDotCirclePainter extends FlDotCirclePainter {
  final Color glowColor;
  final double glowRadius;

  GlowDotCirclePainter({
    Color color = Colors.green,
    double? radius,
    Color strokeColor = const Color.fromRGBO(76, 175, 80, 1),
    double strokeWidth = 0.0,
    required this.glowColor,
    required this.glowRadius,
  }) : super(color: color, radius: radius, strokeColor: strokeColor, strokeWidth: strokeWidth);

  @override
  void draw(Canvas canvas, FlSpot spot, Offset offsetInCanvas) {
    // 绘制光晕
    final glowPaint = Paint()
      ..color = glowColor.withOpacity(0.5) // 调整光晕的透明度
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, glowRadius);

    canvas.drawCircle(offsetInCanvas, glowRadius, glowPaint);

    // 调用父类的方法绘制圆点
    super.draw(canvas, spot, offsetInCanvas);
  }

  @override
  List<Object?> get props => super.props + [glowColor, glowRadius];
}
