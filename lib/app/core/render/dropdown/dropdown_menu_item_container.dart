// The container widget for a menu item created by a [DropdownButton]. It
// provides the default configuration for [DropdownMenuItem]s, as well as a
// [DropdownButton]'s hint and disabledHint widgets.
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

class DropdownMenuItemContainer extends StatelessWidget {
  /// Creates an item for a dropdown menu.
  ///
  /// The [child] argument is required.
  const DropdownMenuItemContainer({
    super.key,
    this.alignment = AlignmentDirectional.centerStart,
    required this.child, this.width, this.height,
  });

  /// The widget below this widget in the tree.
  ///
  /// Typically a [Text] widget.
  final Widget child;

  /// Defines how the item is positioned within the container.
  ///
  /// Defaults to [AlignmentDirectional.centerStart].
  ///
  /// See also:
  ///
  ///  * [Alignment], a class with convenient constants typically used to
  ///    specify an [AlignmentGeometry].
  ///  * [AlignmentDirectional], like [Alignment] for specifying alignments
  ///    relative to text direction.
  final AlignmentGeometry alignment;

  final double? width;
  final double? height;
  @override
  Widget build(BuildContext context) {
    return Container(
      constraints:  BoxConstraints(minHeight: ScreenAdapter.height(10)),
      alignment: alignment,
      width: width ?? ScreenAdapter.width(20),
      height: height ?? ScreenAdapter.height(20),
      child: child,
    );
  }
}
