import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

class CustomIconButton extends StatefulWidget {
  final bool isSelected;
  final Widget? icon;
  final Widget? selectedIcon;
  final VoidCallback? onPressed;

  const CustomIconButton({
    Key? key,
    required this.isSelected,
    this.icon,
    this.selectedIcon,
    this.onPressed,
  }) : super(key: key);

  @override
  _CustomIconButtonState createState() => _CustomIconButtonState();
}

class _CustomIconButtonState extends State<CustomIconButton> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: ScreenAdapter.width(12),
        child: widget.isSelected
            ? widget.selectedIcon ?? const Icon(Icons.arrow_drop_up)
            : widget.icon ?? const Icon(Icons.arrow_drop_down),
    );
  }
}