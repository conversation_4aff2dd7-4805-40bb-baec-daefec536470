class AojDevice {
  final String name;
  final String macAddress;
  final String broadcastId;
  final String deviceType;
  final int rssi;

  AojDevice({
    required this.name,
    required this.macAddress,
    required this.broadcastId,
    required this.deviceType,
    required this.rssi,
  });

  factory AojDevice.fromMap(Map<dynamic, dynamic> map) {
    return AojDevice(
      name: map['name'] ?? 'Unknown',
      macAddress: map['macAddress'] ?? '',
      broadcastId: map['broadcastId'] ?? '',
      deviceType: map['deviceType'] ?? 'Unknown',
      rssi: map['rssi'] ?? 0,
    );
  }

  @override
  String toString() {
    return 'AojDevice{name: $name, mac: $macAddress, type: $deviceType, rssi: $rssi}';
  }
}

class AojTempData {
  final String broadcastId;
  final double temperature;
  final int unit; // 0: Celsius, 1: Fahrenheit
  final DateTime timestamp;
  final String mode; // Adult, Children, Ear, Material

  AojTempData({
    required this.broadcastId,
    required this.temperature,
    required this.unit,
    required this.timestamp,
    required this.mode,
  });

  factory AojTempData.fromMap(Map<dynamic, dynamic> map) {
    return AojTempData(
      broadcastId: map['broadcastId'] ?? '',
      temperature: (map['temperature'] as num).toDouble(),
      unit: map['unit'] ?? 0,
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] * 1000),
      mode: map['mode'] ?? 'Unknown',
    );
  }
}

class AojBpmData {
  final String broadcastId;
  final int systolic; // 收缩压
  final int diastolic; // 舒张压
  final int pulse; // 脉搏
  final DateTime timestamp;
  final int user; // 用户ID

  AojBpmData({
    required this.broadcastId,
    required this.systolic,
    required this.diastolic,
    required this.pulse,
    required this.timestamp,
    required this.user,
  });

  factory AojBpmData.fromMap(Map<dynamic, dynamic> map) {
    return AojBpmData(
      broadcastId: map['broadcastId'] ?? '',
      systolic: map['systolic'] ?? 0,
      diastolic: map['diastolic'] ?? 0,
      pulse: map['pulse'] ?? 0,
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] * 1000),
      user: map['user'] ?? 1,
    );
  }
}