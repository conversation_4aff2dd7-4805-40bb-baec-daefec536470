/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-09-11 16:49:50
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-26 14:19:06
 * @FilePath: /RPM-APP-MASTER/lib/app/modules/test_component/views/custom_line.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';

import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:fl_chart/fl_chart.dart';
// import 'package:fl_chart/fl_chart.dart' hide LineTooltipItem,LineBarSpot,LineChartData,LineChartBarData,LineChartStepData,BarAreaData,BetweenBarsData,BarAreaSpotsLine,CheckToShowSpotLine,GetDotColorCallback,FlDotPainter,FlDotData,LineTouchData,LineTouchTooltipData;

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class CustomLine extends StatelessWidget with BaseWidgetMixin {
  final List<List<FlSpot>> spots;

  final List<Color> spotsColor;

  final double spotsWidth;

  final List<FlDotData> dotStyle;

  final List<String> listData;

  final double leftMin;
  final double leftMax;
  final double leftInterval;
  final TextStyle leftStyle;

  final TextStyle bottomStyle;

  final Color bgColor;

  final LineTouchData? lineTouchData;
  // 根据 spots 的长度动态生成 showingTooltipOnSpots
  final List<int> showingTooltipOnSpots;
  bool temperatureUnit;

  CustomLine(
      {super.key,
      required this.spots,
      required this.leftMin,
      required this.leftMax,
      required this.leftInterval,
      required this.leftStyle,
      required this.bgColor,
      required this.spotsColor,
      required this.spotsWidth,
      required this.dotStyle,
      required this.bottomStyle,
      required this.listData,
      this.temperatureUnit = true,
      this.lineTouchData})
      : showingTooltipOnSpots = List<int>.generate(
          spots.isNotEmpty ? spots[0].length : 0,
          (index) => index,
        );

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    // print(value);
    if (value.toInt() == 1) {
      return SideTitleWidget(
        axisSide: meta.axisSide,
        space: ScreenAdapter.height(8),
        // angle: ScreenAdapter.width(12),
        child: Text(listData[0], style: bottomStyle),
      );
    } else if (value == 7.0) {
      return SideTitleWidget(
        axisSide: meta.axisSide,
        space: ScreenAdapter.height(8),
        child: Text(listData[listData.length-1], style: bottomStyle),
      );
    }
    return SizedBox();
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    // print("获得的leftValue$value,$leftMin");
    if (leftMin.toInt() == 40) {
      if ([40, 70, 100, 130, 160, 190].contains(value.toInt())) {
        return SideTitleWidget(
          axisSide: meta.axisSide,
          space: ScreenAdapter.width(2),
          child: Text(meta.formattedValue, style: leftStyle),
        );
      } else {
        return SizedBox();
      }
    } else {
      if (generateYAxisValues(leftMin, leftMax - leftInterval, leftInterval)
          .contains(value.toInt())) {
           String  leftText ;
           if(temperatureUnit == false){
            leftText = (value * 1.8 +32).toStringAsFixed(0);
           }else{
            leftText = meta.formattedValue;
           }
           
          // logger.d("看看contains的leftText${leftText}");
        return SideTitleWidget(
          axisSide: meta.axisSide,
          space: ScreenAdapter.width(1),
          child: Text(leftText, style: leftStyle),
        );
      } else {
        return SizedBox();
      }
    }
  }

  List<double> generateYAxisValues(double min, double max, double interval) {
    List<double> yAxisValues = [];
    for (double value = min; value <= max; value += interval) {
      yAxisValues.add(value);
    }
    return yAxisValues;
  }

  @override
  Widget body(BuildContext context) {
    final lineBarsData = spots.asMap().entries.map((entry) {
      int index = entry.key; // 获取索引
      List<FlSpot> item = entry.value; // 获取元素
      
      return LineChartBarData(
        
        showingIndicators: showingTooltipOnSpots,
        
        // showingIndicators: showingTooltipOnSpots,
        spots: spots[index], //图表上点的列表
        // spots: [
        //   FlSpot(0, 1),
        //   FlSpot(1, -1),
        //   FlSpot(2, 1),
        //   FlSpot(3, -1),
        // ],
        show: true, //是否显示此数据线
        color: spotsColor[
            index], //数据线的颜色。如果未设置，且 gradient 为空，则默认为Colors.cyan。 AppColors.pressureDia,
        //gradient://数据线的渐变颜色
        barWidth: spotsWidth, //数据线的宽度 ScreenAdapter.width(2)
        isCurved: true, //数据线是否为曲线
        // curveSmoothness:0.9, //曲线的平滑度
        // preventCurveOverShooting:true,//是否防止曲线的过度超出
        // preventCurveOvershootingThreshold//防止曲线超出的阈值
        isStrokeCapRound: true, //是否将线条的末端设置为圆形
        // isStrokeJoinRound: true,//是否将线条的连接处设置为圆形
        //数据线以下的区域填充
        // belowBarData: BarAreaData(
        //   show: true,
        //   // color: AppColors.errorTextColor,
        //   gradient: LinearGradient(
        //     begin: Alignment.topCenter, // 渐变从顶部开始
        //     end: Alignment.bottomCenter, // 渐变在底部结束
        //     colors: [
        //       Color(0xFFFF7575)
        //           .withOpacity(0.1064), // 起始颜色，透明度为 10.64%
        //       Color(0xFFFE473B)
        //           .withOpacity(1), // 结束颜色，透明度为 100.31%
        //     ],
        //   ),
        // ),
        //数据线以上的内容填充
        //                     aboveBarData: BarAreaData(
        //   show: true,
        //   // color: AppColors.errorTextColor,
        //   gradient: LinearGradient(
        //     begin: Alignment.topCenter, // 渐变从顶部开始
        //     end: Alignment.bottomCenter, // 渐变在底部结束
        //     colors: [
        //       Color(0xFFFF7575)
        //           .withOpacity(0.1064), // 起始颜色，透明度为 10.64%
        //       Color(0xFFFE473B)
        //           .withOpacity(1), // 结束颜色，透明度为 100.31%
        //     ],
        //   ),
        // ),
        //数据点的样式
        dotData: dotStyle[index],

        // FlDotData(
        //     show: true,
        //     getDotPainter: (spot, perent, barData, index) {
        //       return FlDotCirclePainter(
        //         radius: ScreenAdapter.width(3), //点的半径
        //         color: AppColors.pressureDia, //点的颜色
        //         strokeWidth: ScreenAdapter.width(1), //点的边框宽度
        //         strokeColor: Colors.white, //点边框的颜色
        //       );
        //     })

        // showingIndicators: [1, 2, 3], // 高亮显示索引为1，2,3的数据点，没看出来
        // dashArray: [5,2]//设置虚线样式
        // shadow:Shadow() //数据线的阴影模式
        // isStepLineChart: true,//是否为阶梯线图
        // lineChartStepData: //阶梯线图的数据
      );
    
    }).toList();

    final tooltipsOnBar = lineBarsData[0];

    return Container(
      // width: ScreenAdapter.width(324),
      // height: ScreenAdapter.height(189),
      // color: Colors.red,
      child: LineChart(
        LineChartData(
          backgroundColor: bgColor, // 设置背景颜色 AppColors.pressureRemindBg,
          showingTooltipIndicators: showingTooltipOnSpots.map((index) {
  return ShowingTooltipIndicators(
    lineBarsData.map((lineBar) {
      return LineBarSpot(
        lineBar,
        lineBarsData.indexOf(lineBar),
        lineBar.spots[index],
      );
    }).toList(),
  );
}).toList(),


          //触摸交互行为
          lineTouchData: lineTouchData ?? LineTouchData(enabled: false),
          //图表中显示的线条数据
          lineBarsData: lineBarsData,

          //用于在不同线条之间绘制区域填充
          // betweenBarsData
          //Y轴的最小值
          minY: leftMin,
          //Y轴的最大值
          maxY: leftMax,
          minX: 0.5,
          maxX: 7.5, // 根据需要设置 X 轴的范围
          //配置图表的标题数据，包括轴标签、标题和样式
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                interval: leftInterval,
                showTitles: true,
                getTitlesWidget: (value, meta) => leftTitleWidgets(value, meta),
                reservedSize: ScreenAdapter.width(28),
              ),
              drawBelowEverything: true,
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) =>
                    bottomTitleWidgets(value, meta),
                reservedSize: 36,
                interval: 1,
              ),
              drawBelowEverything: true,
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),

          gridData: FlGridData(
            show: true,
            drawHorizontalLine: true,
            drawVerticalLine: false,
            horizontalInterval: leftInterval,
            verticalInterval: 1,
            checkToShowHorizontalLine: (value) {
              // print("checkValue：$value");
              if (leftMin.toInt() == 40) {
                // print("返回40了");
                return [70, 100, 130, 160, 190].contains(value.toInt());
              } else {
                return generateYAxisValues(
                        leftMin, leftMax - leftInterval, leftInterval)
                    .contains(value.toInt());
              }
              // 只显示在 Y 轴等于 40, 70, 100, 130, 160, 190 的位置
            },
            getDrawingHorizontalLine: (value) {
              // print("辅助线Value$value");
              // print("leftMin$leftMin");
              return FlLine(
                color: AppColors.Coloreee, // 其他虚线
                strokeWidth: ScreenAdapter.width(2),
                dashArray: [3, 3], // 设置虚线样式
              );
            },
            // getDrawingHorizontalLine: (_) => FlLine(
            //   color: Colors.blue.withOpacity(1),
            //   dashArray: [8, 2],
            //   strokeWidth: 0.8,
            // ),
            // getDrawingVerticalLine: (_) => FlLine(
            //   color: Colors.yellow.withOpacity(1),
            //   dashArray: [8, 2],
            //   strokeWidth: 0.8,
            // ),
            // checkToShowVerticalLine: (value) {
            //   return value.toInt() == 0;
            // },
          ),

          borderData: FlBorderData(
              show: true,
              border: Border(
                  bottom: BorderSide(
                      color: AppColors.Coloreee,
                      width: ScreenAdapter.width(2)))),
          //配置额外的线条数据，通常用于绘制基线、参考线等。
          // extraLinesData: ExtraLinesData(
          //   horizontalLines: [
          //     HorizontalLine(
          //       y: 0, // Y 轴线在 y=0 的位置
          //       color: Colors.red, // 设置线条颜色
          //       strokeWidth: 2, // 设置线条宽度
          //       dashArray: [10, 5], // 设置虚线样式
          //     ),
          //   ],
          // ),
          //置顶显示工具指示指示器的位置
          // showingTooltipIndicators:
        ),
        // duration: Duration(milliseconds: 10000),
      ),
    );
  }
}
