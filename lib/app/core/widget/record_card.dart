import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_decoration.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_shadow.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/blood_pressure/model/pressure_data.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class RecordCard extends StatelessWidget with BaseWidgetMixin {
  final TabsController tabsController = Get.find();
  final int index;
  final dynamic data;

  RecordCard({super.key, required this.index, required this.data});

  Widget getTitle() {
    switch (index) {
      case 0:
        return data is OxygenData
            ? Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21), // 以较大的文本基线对齐
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      // 转换为百分比形式
                      '${(data.percentage).toInt()}',
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: ScreenAdapter.fontSize(16),
                          height: ScreenAdapter.fontSize(21 / 16.0),
                          color: AppColors.recordList[getIndex()]),
                    ),
                  ),
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21), // 与上面文本对齐
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      "%",
                      style: normalF12H17C999,
                    ),
                  ),
                ],
              )
            : Text("数据出错");
      case 1:
        return data is PressureData
            ? Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21),
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      '${data.sysPressure}/${data.diaPressure}',
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: ScreenAdapter.fontSize(16),
                          height: ScreenAdapter.fontSize(21 / 16.0),
                          color: AppColors.recordList[getIndex()]),
                    ),
                  ),
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21),
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      "mmHg",
                      style: normalF12H17C999,
                    ),
                  ),
                  SizedBox(
                    width: ScreenAdapter.width(12),
                  ),
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21),
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      '${data.pulse}',
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: ScreenAdapter.fontSize(16),
                          height: ScreenAdapter.fontSize(21 / 16.0),
                          color: AppColors.recordList[getIndex()]),
                    ),
                  ),
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21),
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      T.commonTimesMinuteSmall.tr,
                      style: normalF12H17C999,
                    ),
                  ),
                ],
              )
            : Text("数据出错");
      case 2:
        return data is TemperatureData
            ? Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21),
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      getTemperatureText(),
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: ScreenAdapter.fontSize(16),
                          height: ScreenAdapter.fontSize(21 / 16.0),
                          color: AppColors.recordList[getIndex()]),
                    ),
                  ),
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21),
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      "℃",
                      style: normalF12H17C999,
                    ),
                  ),
                ],
              )
            : Text("数据出错");
                case 3:
        return data is HeartRateData
            ? Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21),
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      "${data.data.toInt()}",
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: ScreenAdapter.fontSize(16),
                          height: ScreenAdapter.fontSize(21 / 16.0),
                          color: AppColors.recordList[getIndex()]),
                    ),
                  ),
                  Baseline(
                    baseline: ScreenAdapter.fontSize(21),
                    baselineType: TextBaseline.alphabetic,
                    child: Text(
                      T.wordsBPM.tr,
                      style: normalF12H17C999,
                    ),
                  ),
                ],
              )
            : Text("数据出错");

      default:
        return Text("索引出错");
    }
  }

  String getSeverityString() {
    switch (getIndex()) {
      case 0:
        return T.wordsLow.tr;
      case 1:
        return T.wordsNormal.tr;
      case 2:
        return T.wordsMild.tr;
      case 3:
        return T.wordsModerate.tr;
      default:
        return T.wordsSerious.tr;
    }
  }

  int getIndex() {
    switch (index) {
      case 0: 
        return data.getTotalLevel() + 1;
      default:
        return data.getTotalLevel();
    }
  }

  String getDataSource() {
    switch (data.dataSource) {
      case 0:
        return T.commonDataSourceOnFDA.tr;
      case 1:
        return T.commonDataSourceNoFDA.tr;
      default:
        return T.commonDataSourceOnHand.tr;
    }
  }

  String getTime() {
    return "${data.date.toLocal().toString().split(' ')[0]} ${data.date.toLocal().toString().split(' ')[1].split('.')[0]}";
  }

  void toNamed() {
    logger.d(index);
    switch (index) {
      case 0:
        Get.toNamed(Routes.OXYGEN_RECORD_DETAIL, arguments: {'model': data});
      case 2:
        Get.toNamed(Routes.TEMPERATURE_RECORD_DETAIL, arguments: {'model': data});
      case 3:
        Get.toNamed(Routes.RATE_RECORD_DETAIL,arguments: {'model':data});
      
      default:
        logger.d("暂无跳转");
    }
  }

  @override
  Widget body(BuildContext context) {
    return InkWell(
      onTap: toNamed,
      child: Container(
          width: ScreenAdapter.width(375),
          height: ScreenAdapter.height(54),
          margin: EdgeInsets.only(bottom: ScreenAdapter.height(12)),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: AppColors.recordBorder, // 边框颜色
                width: ScreenAdapter.width(0.4), // 边框宽度
              ),
            ),
          ),
          child: Container(
            width: ScreenAdapter.width(343),
            margin: EdgeInsets.only(
                left: ScreenAdapter.width(16),
                right: ScreenAdapter.width(16),
                bottom: ScreenAdapter.height(8)),
            child: Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  height: ScreenAdapter.height(21),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        // width: ScreenAdapter.width(50),
                        height: double.infinity,
                        child: getTitle(),
                      ),
                      Container(
                        // width: ScreenAdapter.width(50),
                        height: ScreenAdapter.height(20),
                        padding: EdgeInsets.only(
                            left: ScreenAdapter.width(14),
                            right: ScreenAdapter.width(14),
                            top: ScreenAdapter.height(3),
                            bottom: ScreenAdapter.height(3)),
                        decoration: BoxDecoration(
                          color: AppColors.recordList[getIndex()],
                          borderRadius:
                              BorderRadius.circular(ScreenAdapter.width(16)),
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromRGBO(255, 255, 255, 0.45),
                              offset: Offset(0, 2),
                              blurRadius: ScreenAdapter.width(4),
                              spreadRadius: 0.0,
                              inset: true,
                            ),
                            BoxShadow(
                              color: Color.fromRGBO(255, 255, 255, 0.45),
                              offset: Offset(0, -2),
                              blurRadius: ScreenAdapter.width(4),
                              spreadRadius: 0.0,
                              inset: true,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            getSeverityString(), // 程度
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: AppColors.colorWhite,
                              fontSize: ScreenAdapter.fontSize(10),
                              fontWeight: FontWeight.w500,
                              height: 1.0,
                            ),
                          ),
                        ),
                      )
                   
                    ],
                  ),
                ),
                Expanded(child: SizedBox()),
                SizedBox(
                  width: double.infinity,
                  height: ScreenAdapter.height(17),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        getTime(), // 时间
                        style: TextStyle(
                          color: AppColors.Color999,
                          fontSize: ScreenAdapter.fontSize(12),
                          fontWeight: FontWeight.w300,
                          height: ScreenAdapter.fontSize(17 / 10),
                        ),
                      ),
                      SizedBox(
                        // width: ScreenAdapter.width(110),
                        child: Text(
                          getDataSource(), // 数据来源
                          textAlign: TextAlign.end,
                          style: TextStyle(
                            color: AppColors.Color999,
                            fontSize: ScreenAdapter.fontSize(12),
                            fontWeight: FontWeight.w300,
                            height: ScreenAdapter.fontSize(17 / 10),
                          ),
                          overflow: TextOverflow.ellipsis, // 超出显示省略号
                          maxLines: 1, // 限制为一行
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  String getTemperatureText(){
    //修改
    if(tabsController.unitSetting[3][1] == true){
      return data.data.toStringAsFixed(1);
    }else{
      return (data.data*1.8+32).toStringAsFixed(1);
    }
  }
}
