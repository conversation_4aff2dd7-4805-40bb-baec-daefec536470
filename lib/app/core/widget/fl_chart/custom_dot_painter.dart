/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-11 14:56:23
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-12-11 15:42:37
 * @FilePath: /rpmappmaster/lib/app/modules/test_component/widgets/custom_dot_painter.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:ui';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class CustomDotPainter extends FlDotPainter {
  final double fromY; // 矩形的起点
  final double toY;   // 矩形的终点
  final double width; // 矩形的宽度
  final Color color;  // 矩形的填充颜色
  final double borderRadius; // 顶点和底点的圆角半径

  const CustomDotPainter({
    required this.fromY,
    required this.toY,
    required this.width,
    required this.color,
    this.borderRadius = 8.0, // 默认圆角半径
  });

  /// 绘制柱状点
  @override
  void draw(Canvas canvas, FlSpot spot, Offset offsetInCanvas) {
    // 计算矩形的顶部和底部
    final double topY = offsetInCanvas.dy - (spot.y - fromY).abs();
    final double bottomY = offsetInCanvas.dy - (spot.y - toY).abs();

    // 定义带圆角的矩形区域
    final RRect rRect = RRect.fromRectAndCorners(
      Rect.fromLTRB(
        offsetInCanvas.dx - width / 2, // 左边
        bottomY,                      // 顶部
        offsetInCanvas.dx + width / 2, // 右边
        topY,                         // 底部
      ),
      topLeft: Radius.circular(borderRadius), // 左上角圆角
      topRight: Radius.circular(borderRadius), // 右上角圆角
      bottomLeft: Radius.circular(borderRadius), // 左下角圆角
      bottomRight: Radius.circular(borderRadius), // 右下角圆角
    );

    // 绘制带圆角的矩形
    final Paint paint = Paint()..color = color;
    canvas.drawRRect(rRect, paint);
  }

  /// 返回柱状点的大小
  @override
  Size getSize(FlSpot spot) {
    return Size(width, (toY - fromY).abs());
  }

  /// 返回主颜色
  @override
  Color get mainColor => color;

  /// 插值方法，用于动画或渐变过渡
  @override
  FlDotPainter lerp(FlDotPainter a, FlDotPainter b, double t) {
    if (a is CustomDotPainter && b is CustomDotPainter) {
      return CustomDotPainter(
        fromY: lerpDouble(a.fromY, b.fromY, t)!,
        toY: lerpDouble(a.toY, b.toY, t)!,
        width: lerpDouble(a.width, b.width, t)!,
        color: Color.lerp(a.color, b.color, t)!,
        borderRadius: lerpDouble(a.borderRadius, b.borderRadius, t)!,
      );
    }
    return this;
  }

  /// 属性用于比较
  @override
  List<Object?> get props => [fromY, toY, width, color, borderRadius];

  /// 自定义 `hitTest` 方法
  @override
  bool hitTest(
    FlSpot spot,
    Offset touched,
    Offset center,
    double extraThreshold,
  ) {
    // 获取矩形大小
    final size = getSize(spot);
    final rect = Rect.fromCenter(
      center: center,
      width: size.width,
      height: size.height,
    );
    final thresholdRect = rect.inflate(extraThreshold);
    return thresholdRect.contains(touched);
  }
}
