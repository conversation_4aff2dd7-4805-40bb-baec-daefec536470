// ignore_for_file: public_member_api_docs, sort_constructors_first
/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-12 15:19:32
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-12 14:41:35
 * @FilePath: /rpmappmaster/lib/app/core/widget/fl_chart/scatter_plot.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import 'dart:convert';

import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' hide TextDirection;

class MyScatterPlot extends StatefulWidget {
  // 整个图表的宽高
  final double width;
  final double height;
  // 当前图表时间类型（日/月/年）
  final int timeFrame;
  // 当前图表展示的类型（血氧、心跳、心率）
  final int index;
  // 控制是否采用接口请求数据
  List<RxBool> requestParamList;
  // 头部样式
  ScatterHeaderStyle headerStyle;
  // 图表样式
  ScatterChartStyle chartStyle;
  // AppLocalizations get appLocalization => AppLocalizations.of(Get.context!)!;
  GlobalKey topKey = GlobalKey();
  //用于修改requestParamList 里面的值
  Function(int index, bool value) requestParamListChange;
  // 单位符号
  final String unit;

  MyScatterPlot({
    Key? key,
    required this.requestParamList,
    required this.index,
    required this.width,
    required this.height,
    required this.headerStyle,
    required this.chartStyle,
    required this.timeFrame,
    required this.requestParamListChange,
    required this.unit,
  }) : super(key: key);

  @override
  State<MyScatterPlot> createState() => MyScatterPlotState();
}

class MyScatterPlotState extends State<MyScatterPlot> {
  // 接口请求
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  // 标题文字
  String mainTtitle = "——";
  // 时间文字
  String timeText = "——";
  // X轴最小值
  double minX = 48;
  // X轴最大值
  double maxX = 71;
  // Y轴最小值
  double minY = 0;
  // Y轴最大值
  double maxY = 125;
  // Y轴分割单位
  double interableY = 50;
  // X轴分割单位
  double interableX = 6;
  //
  String rateRange = "——";
  //
  String timeRange = "——";
  // x轴显示多少条数据
  double slipX = 23;
  // 顶部是否显示
  bool topShow = true;
  //
  double topX = 0;
  // 当前拖动的 x 坐标，为 null 表示未拖动
  double? dragX;
  // 对齐到最近 X 轴刻度的 x 坐标
  double? alignedDragX;
  // 数据集
  List<ChartData> dataList = <ChartData>[];
  //
  int nearestX = 0;
  // 存储
  final storage = SecureStorageService.instance;

  //初始化
  @override
  void initState() {
    super.initState();
    print("外面查看值：${widget.requestParamList[widget.timeFrame].value}");

    // 监听切换时间类型，加载对应数据
    widget.requestParamList[widget.timeFrame].listen((val) {
      print("里面查看值：$val");
      if (val == true) {
        print("监听到了");
        loadData();
      }
    });
    // 初始化的时候直接加载数据
    loadData();
  }

  //是否更新图表
  @override
  void didUpdateWidget(covariant MyScatterPlot oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当 timeFrame 发生变化时重新加载数据
    // print("判断是否更改！！！！！！！");
    if (widget.timeFrame != oldWidget.timeFrame) {
      // print("更改");
      loadData();
    }
  }

  //加载数据
  Future<void> loadData() async {
    // 等待数据加载完成
    List<ChartData> chartDataList = await dataInit();
    // 数据加载完成后更新 state
    setState(() {
      dataList = chartDataList;
    });
  }

  Future<List<ChartData>> dataInit() async {
    List<ChartData> chartDataList = [];
    //获取当前日期
    DateTime currentDay = DateTime.now()
        .toUtc()
        .subtract(Duration(days: 0))
        .copyWith(hour: 0, minute: 0, second: 0, millisecond: 0);
    //获取当前日期 - 固定为2025年7月30日
// DateTime currentDay = DateTime(2025, 7, 30)
//     .toUtc()
//     .copyWith(hour: 0, minute: 0, second: 0, millisecond: 0);
    //格式化日期
    String formattedDate =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(currentDay.toUtc());
    print("查看当前timeFrme 和 index ");
    print(widget.timeFrame);
    print(widget.index);

    // 如果是 日
    if (widget.timeFrame == 0) {
      //设置日列表通用规则
      setState(() {
        // X轴最小值
        minX = 48;
        // X轴最大值
        maxX = 72;
        // x轴显示的最多条数据
        slipX = 23;
        // x轴垂直线分割的单位
        interableX = 6;
      });
      // 当前类型是心率
      if (widget.index == 0) {
        // 设置心率日数据专属规则
        setState(() {
          maxY = 200;
          interableY = 50;
        });

        //判断是否使用内存中的数据
        if (await storage.haveHeartLineDays(formattedDate) &&
            widget.requestParamList[0].value != true) {
          String? chartDataString =
              storage.getString(AppValues.heartRateLineDays);

          List<dynamic> chartDataJson = jsonDecode(chartDataString!);
          chartDataList =
              chartDataJson.map((item) => ChartData.fromJson(item)).toList();
          // print("查看chartDataList：$chartDataList");
        } else {
          print("使用接口获取心率日期数据！！！！");
          List<dynamic> result = await defaultRepositoryImpl.getHeartRateDays(
              currentDay);

          // 安全转换为 List<List<List<dynamic>>> 类型
          List<List<List<dynamic>>> heartRateData =
              List<List<List<dynamic>>>.from(result.map((e) =>
                  List<List<dynamic>>.from(
                      e.map((f) => List<dynamic>.from(f)))));

          int dayOffset = 0; // 用于标记天数（例如：第1天，第2天）

          // 遍历天数
          for (var dayData in heartRateData) {
            // 如果某天没有数据，填充空的小时数据
            if (dayData.isEmpty) {
              for (int hour = 0; hour < 24; hour++) {
                // 计算当前小时的 x 坐标
                double x = (dayOffset * 24 + hour).toDouble();

                // 填充 ChartData 对象，并添加到 chartDataList
                chartDataList.add(ChartData(
                  x: x,
                  minY: 0,
                  maxY: 0,
                  startTime: "--",
                  endTime: "--",
                ));
              }
            } else {
              // 遍历该天的每小时数据
              for (int hour = 0; hour < dayData.length; hour++) {
                var hourData = dayData[hour];

                // 如果该小时有数据
                if (hourData.isNotEmpty) {
                  for (var data in hourData) {
                    // 解析每个数据点
                    double minY = data['minY'].toDouble();
                    double maxY = data['maxY'].toDouble();
                    String startTime = data['startTime'];
                    String endTime = data['endTime'];

                    // 当前小时的 x 坐标，确保相同小时的 x 坐标一致
                    // double x = (dayOffset * 24 + hour).toDouble();
                    double x = dayOffset * 24 +
                        DateTime.parse(startTime).toLocal().hour.toDouble();
                    // double x = DateTime.parse(startTime).toLocal().millisecondsSinceEpoch.toDouble();
                    // double x = DateTime.parse(startTime).toLocal().day.toDouble();
                    // double x = 48;

                    // 添加该数据点到 chartDataList
                    chartDataList.add(ChartData(
                      x: x,
                      minY: minY,
                      maxY: maxY,
                      startTime: startTime,
                      endTime: endTime,
                    ));
                  }
                } else {
                  // 如果该小时没有数据，填充默认值
                  double x = (dayOffset * 24 + hour).toDouble();
                  chartDataList.add(ChartData(
                    x: x,
                    minY: 0,
                    maxY: 0,
                    startTime: "--",
                    endTime: "--",
                  ));
                }
              }
            }

            dayOffset++; // 增加天数偏移
          }

          // storage.setString(AppValues.heartRateLineDaysDate, formattedDate);
          // storage.setString(
          //     AppValues.heartRateLineDays, jsonEncode(chartDataList));
          widget.requestParamListChange(0, false);
          widget.requestParamList[0].refresh();
        }
      } else if (widget.index == 1) {
        //血氧
        // print("设置00");
        setState(() {
          interableY = 50;
          maxY = 125;
        });
        //如果为true，则重新请求数据，否则使用内存中的数据
        if (await storage.haveBloodOxygenLineDays(formattedDate) &&
            widget.requestParamList[0].value != true) {
          // if (false) {
          // print("包含当前日期，用内存中的数据");

          String? chartDataString =
              storage.getString(AppValues.bloodOxygenLineDays);
          // String? chartDataString = storage.getString(AppValues.heartRateLineDays);
          List<dynamic> chartDataJson = jsonDecode(chartDataString!);
          chartDataList =
              chartDataJson.map((item) => ChartData.fromJson(item)).toList();
          // print("查看chartDataList：$chartDataList");
        } else {
          // print("获取心率日期数据！！！！");
          List<dynamic> result = await defaultRepositoryImpl.getOxygenDays(
              DateTime.now()
                  .toUtc()
                  .subtract(Duration(days: 0))
                  .copyWith(hour: 0, minute: 0, second: 0, millisecond: 0));

          // 安全转换为 List<List<List<dynamic>>> 类型
          List<List<List<dynamic>>> heartRateData =
              List<List<List<dynamic>>>.from(result.map((e) =>
                  List<List<dynamic>>.from(
                      e.map((f) => List<dynamic>.from(f)))));

          int dayOffset = 0; // 用于标记天数（例如：第1天，第2天）

          // 遍历天数
          for (var dayData in heartRateData) {
            // 如果某天没有数据，填充空的小时数据
            if (dayData.isEmpty) {
              for (int hour = 0; hour < 24; hour++) {
                // 计算当前小时的 x 坐标
                double x = (dayOffset * 24 + hour).toDouble();

                // 填充 ChartData 对象，并添加到 chartDataList
                chartDataList.add(ChartData(
                  x: x,
                  minY: 0,
                  maxY: 0,
                  startTime: "--",
                  endTime: "--",
                ));
              }
            } else {
              // 遍历该天的每小时数据
              for (int hour = 0; hour < dayData.length; hour++) {
                var hourData = dayData[hour];

                // 如果该小时有数据
                if (hourData.isNotEmpty) {
                  for (var data in hourData) {
                    // 解析每个数据点
                    double minY = data['minY'].toDouble();
                    double maxY = data['maxY'].toDouble();
                    String startTime = data['startTime'];
                    String endTime = data['endTime'];

                    // 当前小时的 x 坐标，确保相同小时的 x 坐标一致
                    // double x = (dayOffset * 24 + hour).toDouble();
                    double x = dayOffset * 24 +
                        DateTime.parse(startTime).toLocal().hour.toDouble();
                    // double x = DateTime.parse(startTime).toLocal().millisecondsSinceEpoch.toDouble();
                    // double x = DateTime.parse(startTime).toLocal().day.toDouble();
                    // double x = 48;

                    // 添加该数据点到 chartDataList
                    chartDataList.add(ChartData(
                      x: x,
                      minY: minY,
                      maxY: maxY,
                      startTime: startTime,
                      endTime: endTime,
                    ));
                  }
                } else {
                  // 如果该小时没有数据，填充默认值
                  double x = (dayOffset * 24 + hour).toDouble();
                  chartDataList.add(ChartData(
                    x: x,
                    minY: 0,
                    maxY: 0,
                    startTime: "--",
                    endTime: "--",
                  ));
                }
              }
            }

            dayOffset++; // 增加天数偏移
          }

          // storage.setString(AppValues.heartRateLineDaysDate, formattedDate);
          // storage.setString(
          //     AppValues.heartRateLineDays, jsonEncode(chartDataList));
          widget.requestParamListChange(0, false);
          widget.requestParamList[0].refresh();
        }
      } else if (widget.index == 2) {
        print("到体温了");
        TabsController tabsController = Get.find();
        if (tabsController.unitSetting[3][1].value == true) {
          //卡路里
          minY = 32;
          maxY = 40;
          interableY = 2;
        } else {
          //华氏摄氏度
          minY = 90;
          maxY = 110;
          interableY = 5;
        }

        if (await storage.haveTemperatureLineDays(formattedDate) &&
            widget.requestParamList[0].value != true) {
          // if (false) {
          // print("包含当前日期，用内存中的数据");

          String? chartDataString =
              storage.getString(AppValues.bodyTemperatureLineDays);
          List<dynamic> chartDataJson = jsonDecode(chartDataString!);
          chartDataList =
              chartDataJson.map((item) => ChartData.fromJson(item)).toList();
          // print("查看chartDataList：$chartDataList");
        } else {
          // print("获取心率日期数据！！！！");
          List<dynamic> result = await defaultRepositoryImpl.getTemperatureDays(
              DateTime.now()
                  .toUtc()
                  .subtract(Duration(days: 0))
                  .copyWith(hour: 0, minute: 0, second: 0, millisecond: 0));

          // 安全转换为 List<List<List<dynamic>>> 类型
          List<List<List<dynamic>>> heartRateData =
              List<List<List<dynamic>>>.from(result.map((e) =>
                  List<List<dynamic>>.from(
                      e.map((f) => List<dynamic>.from(f)))));

          int dayOffset = 0; // 用于标记天数（例如：第1天，第2天）

          // 遍历天数
          for (var dayData in heartRateData) {
            // 如果某天没有数据，填充空的小时数据
            if (dayData.isEmpty) {
              for (int hour = 0; hour < 24; hour++) {
                // 计算当前小时的 x 坐标
                double x = (dayOffset * 24 + hour).toDouble();

                // 填充 ChartData 对象，并添加到 chartDataList
                chartDataList.add(ChartData(
                  x: x,
                  minY: 0,
                  maxY: 0,
                  startTime: "--",
                  endTime: "--",
                ));
              }
            } else {
              // 遍历该天的每小时数据
              for (int hour = 0; hour < dayData.length; hour++) {
                var hourData = dayData[hour];

                // 如果该小时有数据
                if (hourData.isNotEmpty) {
                  for (var data in hourData) {
                    // 解析每个数据点
                    double minY = data['minY'].toDouble();
                    double maxY = data['maxY'].toDouble();
                    if (tabsController.unitSetting[3][1].value == false) {
                      maxY = maxY * 1.8 + 32;
                      minY = minY * 1.8 + 32;
                    }
                    String startTime = data['startTime'];
                    String endTime = data['endTime'];

                    // 当前小时的 x 坐标，确保相同小时的 x 坐标一致
                    // double x = (dayOffset * 24 + hour).toDouble();
                    double x = dayOffset * 24 +
                        DateTime.parse(startTime).toLocal().hour.toDouble();
                    // double x = DateTime.parse(startTime).toLocal().millisecondsSinceEpoch.toDouble();
                    // double x = DateTime.parse(startTime).toLocal().day.toDouble();
                    // double x = 48;

                    // 添加该数据点到 chartDataList
                    chartDataList.add(ChartData(
                      x: x,
                      minY: minY,
                      maxY: maxY,
                      startTime: startTime,
                      endTime: endTime,
                    ));
                  }
                } else {
                  // 如果该小时没有数据，填充默认值
                  double x = (dayOffset * 24 + hour).toDouble();
                  chartDataList.add(ChartData(
                    x: x,
                    minY: 0,
                    maxY: 0,
                    startTime: "--",
                    endTime: "--",
                  ));
                }
              }
            }

            dayOffset++; // 增加天数偏移
          }

          // storage.setString(AppValues.heartRateLineDaysDate, formattedDate);
          // storage.setString(
          //     AppValues.heartRateLineDays, jsonEncode(chartDataList));
          widget.requestParamListChange(0, false);
          widget.requestParamList[0].refresh();
        }
      }

      //计算标题
      double overallMinY = 999; // 全局最小值
      double overallMaxY = 0; // 全局最大值
      for (int i = 48; i < 72; i++) {
        // 筛选出 x 匹配的数据
        List<ChartData> filteredList = chartDataList
            .where((data) => data.x == i && data.minY > 0)
            .toList();

        // print(filteredList);

        if (filteredList.isNotEmpty) {
          // 计算当前 x 的 minY 和 maxY
          double currentMinY =
              filteredList.map((e) => e.minY).reduce((a, b) => a < b ? a : b);
          double currentMaxY =
              filteredList.map((e) => e.maxY).reduce((a, b) => a > b ? a : b);

          // 更新整体的最小值和最大值
          if (currentMinY < overallMinY) overallMinY = currentMinY;
          if (currentMaxY > overallMaxY) overallMaxY = currentMaxY;
        }
      }
      // print("查看当前最大最小值：${overallMaxY} ${overallMinY}");
      if (overallMinY == 999 || overallMaxY == 0) {
        // 如果没有有效数据
        setState(() {
          rateRange = "——";
          mainTtitle = rateRange;
          timeText = "——";
        });
      } else {
        setState(() {
          rateRange = "${overallMinY.toInt()}-${overallMaxY.toInt()}";
          mainTtitle = rateRange;
          timeText = T.dateToday.tr;
        });
      }
      // if()
    }
    // 如果是 周
    else if (widget.timeFrame == 1) {
      currentDay = currentDay.add(Duration(days: 1));
      setState(() {
        minX = 14; // X轴最小值
        maxX = 21; // X轴最大值
        slipX = 6;
        interableX = 1;
        interableY = 50;
      });
      if (widget.index == 0) {
        setState(() {
          maxY = 200;
          interableY = 50;
        });
        storage.delete(AppValues.heartRateLineWeeksDate);
        if (await storage.haveHeartLineWeeks(formattedDate) &&
            widget.requestParamList[1].value != true) {
          print("包含当前日期，用内存中的数据");

          String? chartDataString =
              await storage.getString(AppValues.heartRateLineWeeks);
          List<dynamic> chartDataJson = jsonDecode(chartDataString!);
          chartDataList =
              chartDataJson.map((item) => ChartData.fromJson(item)).toList();
        } else {
          List<dynamic> result =
              await defaultRepositoryImpl.getHeartRateWeeks(currentDay);

          // print(result);

          // 安全转换为 List<List<dynamic>> 类型
          List<List<dynamic>> heartRateData = List<List<dynamic>>.from(
              result.map((e) => List<dynamic>.from(e)));

          // 遍历每一周的数据，生成 ChartData 对象
          int x = 0; // 初始化 x 坐标
          for (var week in heartRateData) {
            for (var day in week) {
              // 解析每一天的数据，并生成 ChartData 对象
              double minY = day['minY'].toDouble();
              double maxY = day['maxY'].toDouble();
              String startTime = day['startTime'];
              String endTime = day['endTime'];

              // 生成一个新的 ChartData 对象并添加到 chartDataList
              chartDataList.add(ChartData(
                x: x.toDouble(),
                minY: minY,
                maxY: maxY,
                startTime: startTime,
                endTime: endTime,
              ));
              x++; // 更新 x 坐标
            }
          }
          // storage.setString(AppValues.heartRateLineWeeksDate, formattedDate);
          // storage.setString(
          //     AppValues.heartRateLineWeeks, jsonEncode(chartDataList));
          widget.requestParamListChange(1, false);
          widget.requestParamList[1].refresh();
        }
      } else if (widget.index == 1) {
        setState(() {
          interableY = 50;
          maxY = 125;
        });
        if (await storage.haveBloodOxygenLineWeeks(formattedDate) &&
            widget.requestParamList[1].value != true) {
          print("包含当前日期，用内存中的数据");

          String? chartDataString =
              await storage.getString(AppValues.bloodOxygenLineWeeks);
          List<dynamic> chartDataJson = jsonDecode(chartDataString!);
          chartDataList =
              chartDataJson.map((item) => ChartData.fromJson(item)).toList();
        } else {
          List<dynamic> result =
              await defaultRepositoryImpl.getOxygenWeeks(currentDay);

          print(result);

          // 安全转换为 List<List<dynamic>> 类型
          List<List<dynamic>> heartRateData = List<List<dynamic>>.from(
              result.map((e) => List<dynamic>.from(e)));

          // 遍历每一周的数据，生成 ChartData 对象
          int x = 0; // 初始化 x 坐标
          for (var week in heartRateData) {
            for (var day in week) {
              // 解析每一天的数据，并生成 ChartData 对象
              double minY = day['minY'].toDouble();
              double maxY = day['maxY'].toDouble();
              String startTime = day['startTime'];
              String endTime = day['endTime'];

              // 生成一个新的 ChartData 对象并添加到 chartDataList
              chartDataList.add(ChartData(
                x: x.toDouble(),
                minY: minY,
                maxY: maxY,
                startTime: startTime,
                endTime: endTime,
              ));
              x++; // 更新 x 坐标
            }
          }
          // storage.setString(AppValues.heartRateLineWeeksDate, formattedDate);
          // storage.setString(
          //     AppValues.heartRateLineWeeks, jsonEncode(chartDataList));
          widget.requestParamListChange(1, false);
          widget.requestParamList[1].refresh();
        }
      } else if (widget.index == 2) {
        TabsController tabsController = Get.find();
        if (tabsController.unitSetting[3][1].value == true) {
          //卡路里
          minY = 32;
          maxY = 40;
          interableY = 4;
        } else {
          //华氏摄氏度
          minY = 90;
          maxY = 110;
          interableY = 10;
        }
        if (await storage.haveTemperatureLineWeeks(formattedDate) &&
            widget.requestParamList[1].value != true) {
          print("包含当前日期，用内存中的数据");

          String? chartDataString =
              await storage.getString(AppValues.bodyTemperatureLineWeeks);
          List<dynamic> chartDataJson = jsonDecode(chartDataString!);
          chartDataList =
              chartDataJson.map((item) => ChartData.fromJson(item)).toList();
        } else {
          List<dynamic> result =
              await defaultRepositoryImpl.getTemperatureWeeks(currentDay);

          // print(result);

          // 安全转换为 List<List<dynamic>> 类型
          List<List<dynamic>> heartRateData = List<List<dynamic>>.from(
              result.map((e) => List<dynamic>.from(e)));

          // 遍历每一周的数据，生成 ChartData 对象
          int x = 0; // 初始化 x 坐标
          for (var week in heartRateData) {
            for (var day in week) {
              // 解析每一天的数据，并生成 ChartData 对象
              double minY = day['minY'].toDouble();
              double maxY = day['maxY'].toDouble();
              if (tabsController.unitSetting[3][1].value == false) {
                maxY = maxY * 1.8 + 32;
                minY = minY * 1.8 + 32;
              }

              String startTime = day['startTime'];
              String endTime = day['endTime'];

              // 生成一个新的 ChartData 对象并添加到 chartDataList
              chartDataList.add(ChartData(
                x: x.toDouble(),
                minY: minY,
                maxY: maxY,
                startTime: startTime,
                endTime: endTime,
              ));
              x++; // 更新 x 坐标
            }
          }
          // storage.setString(AppValues.heartRateLineWeeksDate, formattedDate);
          // storage.setString(
          //     AppValues.heartRateLineWeeks, jsonEncode(chartDataList));
          widget.requestParamListChange(1, false);
          widget.requestParamList[1].refresh();
        }
      }
      //计算标题
      double overallMinY = 999; // 全局最小值
      double overallMaxY = 0; // 全局最大值
      for (int i = 14; i < 21; i++) {
        // 筛选出 x 匹配的数据
        List<ChartData> filteredList = chartDataList
            .where((data) => data.x == i && data.minY > 0)
            .toList();

        // print(filteredList);

        if (filteredList.isNotEmpty) {
          // 计算当前 x 的 minY 和 maxY
          double currentMinY =
              filteredList.map((e) => e.minY).reduce((a, b) => a < b ? a : b);
          double currentMaxY =
              filteredList.map((e) => e.maxY).reduce((a, b) => a > b ? a : b);

          // 更新整体的最小值和最大值
          if (currentMinY < overallMinY) overallMinY = currentMinY;
          if (currentMaxY > overallMaxY) overallMaxY = currentMaxY;
        }
      }
      // print("查看当前最大最小值：${overallMaxY} ${overallMinY}");
      if (overallMinY == 999 || overallMaxY == 0) {
        // 如果没有有效数据
        setState(() {
          rateRange = "——";
          mainTtitle = rateRange;
          timeText = "——";
        });
      } else {
        setState(() {
          rateRange = "${overallMinY.toInt()}-${overallMaxY.toInt()}";
          mainTtitle = rateRange;
        });
      }
      DateTime now = DateTime.now();
      // 找到本周的开始和结束
      int weekday = now.weekday; // 1=Monday, 7=Sunday
      DateTime startDate = now.subtract(Duration(days: weekday - 1)); // 本周一
      DateTime endDate = startDate.add(Duration(days: 6)); // 本周日
      // DateTime endDate =
      //     DateTime.parse(chartDataList[chartDataList.length - 1].endTime);
      // // 计算开始日期（减去 7 天）
      // DateTime startDate = endDate.subtract(Duration(days: 7));

      // logger.d("")
      print("心率日期结算");
      print(endDate);
      print(startDate);

      // 格式化日期
      String startDay = DateFormat('d').format(startDate);
      String startMonth = DateFormat('MMM').format(startDate); // 开始日期的月份
      String endDay = DateFormat('d').format(endDate);
      String endMonth = DateFormat('MMM').format(endDate); // 结束日期的月份
      String year = DateFormat('y').format(endDate); // 使用结束日期的年份

      // 判断是否跨月
      if (startMonth == endMonth) {
        setState(() {
          timeRange = "$startDay-$endDay $endMonth $year"; // 同月情况下
          timeText = timeRange;
        });
      } else {
        setState(() {
          timeRange =
              "$startDay $startMonth - $endDay $endMonth $year"; // 跨月情况下
          timeText = timeRange;
        });
      }
    }
    //如果是月
    else if (widget.timeFrame == 2) {
      currentDay = currentDay.add(Duration(days: 1));
      setState(() {
        minX = 62; // X轴最小值
        maxX = 93; // X轴最大值
        slipX = 30;
        interableX = 6;
      });
      if (widget.index == 0) {
        setState(() {
          maxY = 200;
          interableY = 50;
        });
        //判断内存中是否有当前日期的心率日数据
        //如果为true，则重新请求数据，否则使用内存中的数据
        if (await storage.haveHeartLineMonths(formattedDate) &&
            widget.requestParamList[2].value != true) {
          print("包含当前日期，用内存中的数据");

          String? chartDataString =
              await storage.getString(AppValues.heartRateLineMonths);
          List<dynamic> chartDataJson = jsonDecode(chartDataString!);
          chartDataList =
              chartDataJson.map((item) => ChartData.fromJson(item)).toList();
        } else {
          List<dynamic> result =
              await defaultRepositoryImpl.getHeartRateMonths(currentDay);
// 安全转换为 List<List<dynamic>> 类型
          List<List<dynamic>> heartRateData = List<List<dynamic>>.from(
              result.map((e) => List<dynamic>.from(e)));

          // 遍历每一周的数据，生成 ChartData 对象
          // int x = 0; // 初始化 x 坐标

          int dayOffset = 0; // 用于标记天数（例如：第1天，第2天）

          for (var monthData in heartRateData) {
            // 如果某天没有数据，填充空的小时数据
            if (monthData.isEmpty) {
              for (int hour = 0; hour < 30; hour++) {
                // 计算当前小时的 x 坐标
                double x = (dayOffset++).toDouble();

                // 填充 ChartData 对象，并添加到 chartDataList
                chartDataList.add(ChartData(
                  x: x,
                  minY: 0,
                  maxY: 0,
                  startTime: "--",
                  endTime: "--",
                ));
              }
            } else {
              for (var week in heartRateData) {
                for (var day in week) {
                  double minY = day['minY'].toDouble();
                  double maxY = day['maxY'].toDouble();
                  String startTime = day['startTime'];
                  String endTime = day['endTime'];

                  chartDataList.add(ChartData(
                    x: dayOffset.toDouble(),
                    minY: minY,
                    maxY: maxY,
                    startTime: startTime,
                    endTime: endTime,
                  ));
                  dayOffset++;
                }
              }
            }

            dayOffset++; // 增加天数偏移
          }
          // storage.setString(AppValues.heartRateLineMonthsDate, formattedDate);
          // storage.setString(
          //     AppValues.heartRateLineMonths, jsonEncode(chartDataList));
          widget.requestParamListChange(2, false);
          widget.requestParamList[2].refresh();
        }
      } else if (widget.index == 1) {
        setState(() {
          interableY = 50;
          maxY = 125;
        });
        //判断内存中是否有当前日期的心率日数据
        //如果为true，则重新请求数据，否则使用内存中的数据
        if (await storage.haveBloodOxygenLineMonths(formattedDate) &&
            widget.requestParamList[2].value != true) {
          print("包含当前日期，用内存中的数据");

          String? chartDataString =
              await storage.getString(AppValues.bloodOxygenLineMonths);
          List<dynamic> chartDataJson = jsonDecode(chartDataString!);
          chartDataList =
              chartDataJson.map((item) => ChartData.fromJson(item)).toList();
        } else {
          List<dynamic> result =
              await defaultRepositoryImpl.getOxygenMonths(currentDay);
// 安全转换为 List<List<dynamic>> 类型
          List<List<dynamic>> heartRateData = List<List<dynamic>>.from(
              result.map((e) => List<dynamic>.from(e)));

          // 遍历每一周的数据，生成 ChartData 对象
          // int x = 0; // 初始化 x 坐标

          int dayOffset = 0; // 用于标记天数（例如：第1天，第2天）

          for (var monthData in heartRateData) {
            // 如果某天没有数据，填充空的小时数据
            if (monthData.isEmpty) {
              for (int hour = 0; hour < 30; hour++) {
                // 计算当前小时的 x 坐标
                double x = (dayOffset++).toDouble();

                // 填充 ChartData 对象，并添加到 chartDataList
                chartDataList.add(ChartData(
                  x: x,
                  minY: 0,
                  maxY: 0,
                  startTime: "--",
                  endTime: "--",
                ));
              }
            } else {
              for (var week in heartRateData) {
                for (var day in week) {
                  double minY = day['minY'].toDouble();
                  double maxY = day['maxY'].toDouble();
                  String startTime = day['startTime'];
                  String endTime = day['endTime'];

                  chartDataList.add(ChartData(
                    x: dayOffset.toDouble(),
                    minY: minY,
                    maxY: maxY,
                    startTime: startTime,
                    endTime: endTime,
                  ));
                  dayOffset++;
                }
              }
            }

            dayOffset++; // 增加天数偏移
          }
          storage.setString(AppValues.heartRateLineMonthsDate, formattedDate);
          storage.setString(
              AppValues.heartRateLineMonths, jsonEncode(chartDataList));
          widget.requestParamListChange(2, false);
          widget.requestParamList[2].refresh();
        }
      } else if (widget.index == 2) {
        TabsController tabsController = Get.find();
        if (tabsController.unitSetting[3][1].value == true) {
          //卡路里
          minY = 32;
          maxY = 40;
          interableY = 2;
        } else {
          //华氏摄氏度
          minY = 90;
          maxY = 110;
          interableY = 5;
        }

        //判断内存中是否有当前日期的心率日数据
        //如果为true，则重新请求数据，否则使用内存中的数据
        if (await storage.haveTemperatureLineMonths(formattedDate) &&
            widget.requestParamList[2].value != true) {
          print("包含当前日期，用内存中的数据");

          String? chartDataString =
              await storage.getString(AppValues.bodyTemperatureLineMonths);
          List<dynamic> chartDataJson = jsonDecode(chartDataString!);
          chartDataList =
              chartDataJson.map((item) => ChartData.fromJson(item)).toList();
        } else {
          List<dynamic> result =
              await defaultRepositoryImpl.getTemperatureMonths(currentDay);
// 安全转换为 List<List<dynamic>> 类型
          List<List<dynamic>> heartRateData = List<List<dynamic>>.from(
              result.map((e) => List<dynamic>.from(e)));

          // 遍历每一周的数据，生成 ChartData 对象
          // int x = 0; // 初始化 x 坐标

          int dayOffset = 0; // 用于标记天数（例如：第1天，第2天）

          for (var monthData in heartRateData) {
            // 如果某天没有数据，填充空的小时数据
            if (monthData.isEmpty) {
              for (int hour = 0; hour < 30; hour++) {
                // 计算当前小时的 x 坐标
                double x = (dayOffset++).toDouble();

                // 填充 ChartData 对象，并添加到 chartDataList
                chartDataList.add(ChartData(
                  x: x,
                  minY: 0,
                  maxY: 0,
                  startTime: "--",
                  endTime: "--",
                ));
              }
            } else {
              for (var week in heartRateData) {
                for (var day in week) {
                  double minY = day['minY'].toDouble();
                  double maxY = day['maxY'].toDouble();
                  if (tabsController.unitSetting[3][1].value == false) {
                    maxY = maxY * 1.8 + 32;
                    minY = minY * 1.8 + 32;
                  }

                  String startTime = day['startTime'];
                  String endTime = day['endTime'];

                  chartDataList.add(ChartData(
                    x: dayOffset.toDouble(),
                    minY: minY,
                    maxY: maxY,
                    startTime: startTime,
                    endTime: endTime,
                  ));
                  dayOffset++;
                }
              }
            }

            dayOffset++; // 增加天数偏移
          }
          // storage.setString(AppValues.heartRateLineMonthsDate, formattedDate);
          // storage.setString(
          //     AppValues.heartRateLineMonths, jsonEncode(chartDataList));
          widget.requestParamListChange(2, false);
          widget.requestParamList[2].refresh();
        }
      }
      //计算标题
      double overallMinY = 999; // 全局最小值
      double overallMaxY = 0; // 全局最大值
      for (int i = 62; i < 93; i++) {
        // 筛选出 x 匹配的数据
        List<ChartData> filteredList = chartDataList
            .where((data) => data.x == i && data.minY > 0)
            .toList();

        // print(filteredList);

        if (filteredList.isNotEmpty) {
          // 计算当前 x 的 minY 和 maxY
          double currentMinY =
              filteredList.map((e) => e.minY).reduce((a, b) => a < b ? a : b);
          double currentMaxY =
              filteredList.map((e) => e.maxY).reduce((a, b) => a > b ? a : b);

          // 更新整体的最小值和最大值
          if (currentMinY < overallMinY) overallMinY = currentMinY;
          if (currentMaxY > overallMaxY) overallMaxY = currentMaxY;
        }
      }
      // print("查看当前最大最小值：${overallMaxY} ${overallMinY}");
      if (overallMinY == 999 || overallMaxY == 0) {
        // 如果没有有效数据
        setState(() {
          rateRange = "——";
          mainTtitle = rateRange;
          timeText = "——";
        });
      } else {
        setState(() {
          rateRange = "${overallMinY.toInt()}-${overallMaxY.toInt()}";
          mainTtitle = rateRange;
          timeText = T.dateToday.tr;
        });
      }
      DateTime endDate =
          DateTime.parse(chartDataList[chartDataList.length - 1].endTime);
      // 计算开始日期（减去 7 天）
      // DateTime startDate = endDate.subtract(Duration(days: 7));

      // 格式化日期
      String endMonth = DateFormat('MMM').format(endDate); // 结束日期的月份
      String year = DateFormat('y').format(endDate); // 使用结束日期的年份
      setState(() {
        timeRange = "$endMonth $year"; // 同月情况下
        timeText = timeRange;
      });
    }

    // 打印输出以调试
    // print("这是最终的数据:");
    // print(chartDataList);

    return chartDataList;
  }

  findMinMaxByX(int x) {
    print("查看当前x:"+x.toString());
    // if (widget.timeFrame == 0) {
    // 筛选出 x 匹配的数据
    List<ChartData> filteredList =
        dataList.where((data) => data.x == x && data.minY > 0).toList();
    print(filteredList);
    // 添加空列表检查
    if (filteredList.isEmpty) {
      setState(() {
        mainTtitle = "——";
        timeText = "——";
      });
      return; // 提前返回，避免后续处理
    }
    if (filteredList[0].minY == 0) {
      setState(() {
        mainTtitle = "——";
        timeText = "——";
      });
      // return;
    } else {
// 计算 minY 和 maxY
      // print("当前过滤数据");
      // print(filteredList);
// 先过滤掉 minY == 0 的数据
      List<double> validMinY =
          filteredList.map((e) => e.minY).where((y) => y > 0).toList();
      double minY =
          validMinY.isNotEmpty ? validMinY.reduce((a, b) => a < b ? a : b) : 0;
      // double minY =
      //     filteredList.map((e) => e.minY).reduce((a, b){

      //       return  a < b ? a : b;
      //     });
      double maxY =
          filteredList.map((e) => e.maxY).reduce((a, b) => a > b ? a : b);
      // 获取 startTime（这里假设取第一个的 startTime）
      String startTime = filteredList.first.startTime;
      // 解析时间字符串为 DateTime 对象
      DateTime dateTime = DateTime.parse(startTime);

      // print("起始时间和结束时间");
      // print(startTime);

      // 提取日期的 日 和 月 和 年
      String day = dateTime.day.toString();
      String month = DateFormat('MMM').format(dateTime); // 英文月份缩写
      String year = dateTime.year.toString(); // 提取年

      // 提取小时段：例如 08:33 => "08-09"
      int hour = dateTime.hour;
      String hourRange =
          "${hour.toString().padLeft(2, '0')}-${(hour + 1).toString().padLeft(2, '0')}";
      // print(hourRange);
      setState(() {
        mainTtitle = "${minY.toInt()}-${maxY.toInt()}";
        if (widget.timeFrame == 0) {
          timeText = "$day $month $hourRange";
        } else if (widget.timeFrame == 1) {
          timeText = "$day $month $year";
        } else if (widget.timeFrame == 2) {
          timeText = "$day $month $year";
        }
      });
    }
    // } else {
    //   setState(() {
    //     mainTtitle = "————";
    //     timeText = "————";
    //   });
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      child: Stack(
        children: [
          
          _RightText(),
          _CenterChart(),
          _TopInformation(),
        ],
      ),
    );
  }
  _RightText(){
    return Container(
              width: double.infinity,
              height: double.infinity,
              // color: Colors.blue,
              child: CustomPaint(
                painter: ScatterChartRightPainter(
                    rightPadding: widget.chartStyle.horizontalTW, // 去除右侧的绘图区域填充
                    minY: minY,
                    maxY: maxY,
                    verticalTextStyle: widget.chartStyle.verticalTextStyle,
                    horizontalTextStyle: widget.chartStyle.horizontalTextStyle,
                    // horizontalPadding: ScreenAdapter.width(4),
                    timeFrame:widget.timeFrame,
                    bottomPadding: widget.chartStyle.verticalTH,
                    intervalY: interableY),
              ));
  }

  _TopInformation() {
    return Positioned(
      left:
          topX.clamp(0.0, widget.width - ScreenAdapter.width(100)), // 确保不会超出边界
      top: 0, // 距离顶部的固定位置
      child: Container(
        key: widget.topKey,
        alignment: Alignment.center,
        margin: widget.headerStyle.margin,
        // color: Colors.red,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                      text: mainTtitle,
                      style: widget.headerStyle.mainTitleStyle),
                  TextSpan(
                    text: " ${widget.unit}",
                    style: widget.headerStyle.unitStyle,
                  ),
                ],
              ),
            ),
            Text(
              timeText,
              style: widget.headerStyle.timeStyle,
            ),
          ],
        ),
      ),
    );
  }

  _CenterChart() {
    return Container(
        // width: ScreenAdapter.width(v),
        // color: Colors.blue,
        height: ScreenAdapter.height(216),
        margin: EdgeInsets.only(top: ScreenAdapter.height(42)),
        child: GestureDetector(
            onHorizontalDragStart: (details) {
              // setState(() {
              //   action = "水平拖动开始";
              // });
              // print("拖动开始");
            },
            onHorizontalDragUpdate: (details) {
              if (details.delta.dx > 0) {
                //   setState(() {
                //     dragDirection = "向右拖动";
                //   });
                // print("向右拖动");
                setState(() {
                  if (widget.timeFrame == 0 && minX > 0) {
                    minX--;
                    maxX--;
                  } else if (widget.timeFrame == 1 && minX > 0) {
                    minX -= 0.2;
                    maxX -= 0.2;
                  } else if (widget.timeFrame == 2 && minX > 0) {
                    minX -= 0.5;
                    maxX -= 0.5;
                  }
                });
              } else if (details.delta.dx < 0) {
                // print("向左拖动");
                // print(details.delta.dx > );
                setState(() {
                  if (widget.timeFrame == 0 && maxX < 72) {
                    minX++;
                    maxX++;
                  } else if (widget.timeFrame == 1 && maxX < 21) {
                    minX += 0.2;
                    maxX += 0.2;
                  } else if (widget.timeFrame == 2 && maxX < 93) {
                    minX += 0.5;
                    maxX += 0.5;
                  }
                });
              }
            },
            // 检测长按
            onLongPressStart: (details) {
              // setState(() {
              //   action = "长按触发";
              // });
              // print("长按触发,${details.localPosition}");
              setState(() {
                topShow = false;
                topX = details.localPosition.dx - 50; // 减去宽度的一半以居中
                dragX = details.localPosition.dx; // 更新当前拖动位置
              });
            },
            onLongPressMoveUpdate: (details) {
              // print("长按拖动中，${details.localPosition}");
              setState(() {
                // 更新 topX 值
                topX = details.localPosition.dx - 50; // 减去宽度的一半以居中
                dragX = details.localPosition.dx;
                // print(dragX);
                double leftPadding = 0;
                if(widget.timeFrame == 1){
                  leftPadding = ScreenAdapter.width(20);
                }else{
                  leftPadding = ScreenAdapter.width(8);
                }
                //获取当前容器宽度
                final usableWidth = widget.width;
                
                // print(widget.chartStyle.verticalTH);
                // print(ScreenAdapter.width(8)*2);
                print("查看总宽度"+usableWidth.toString());

                // final usableWidth = width - rightPadding; // 可用宽度
                
                final effectiveWidth = usableWidth - leftPadding *2 - 0; // 去除左右内边距后的宽度
                print("查看右边隔开的距离");
                print(widget.chartStyle.verticalTH);
                // print("查看最后的总宽度:${effectiveWidth}");
                // print(ri)

                // 将当前拖动位置映射到逻辑坐标
                final normalizedX =
                    ((dragX! - leftPadding) / effectiveWidth) * (maxX - minX) +
                        minX;
                // print(normalizedX);

                // 对齐到最近的X轴刻度
                nearestX = normalizedX.round().clamp(minX, maxX).toInt() == maxX
                    ? (maxX - 1).toInt()
                    : normalizedX.round().clamp(minX, maxX).toInt();
                print(nearestX%24);
                findMinMaxByX(nearestX);

                // 重新映射回像素位置
                // alignedDragX = leftPadding + (nearestX - minX) / (maxX - minX - 1) * usableWidth;
                // alignedDragX = nearestX.toDouble();
                  // alignedDragX = leftPadding + ((nearestX - minX) / (maxX - minX) * effectiveWidth) ;
                  // alignedDragX =  ((nearestX - minX) / (maxX - minX) * usableWidth);
                  // alignedDragX = 1/7 * usableWidth;
                alignedDragX = nearestX.toDouble();
                print("这是最后结算的align"+alignedDragX.toString());
                // alignedDragX =nearestX.toDouble();
                // print(alignedDragX);
              });
            },
            onLongPressEnd: (details) {
              // setState(() {
              //   dragDirection = "拖动结束";
              // });

              //  print("拖动结束");

              setState(() {
                topShow = true;
                dragX = null; // 重置拖动位置
                topX = 0;
                alignedDragX = null; // 重置对齐位置
                if (widget.timeFrame == 0) {
                  // mainTtitle = rateRange;
                  timeText = T.dateToday.tr;
                } else if (widget.timeFrame == 1) {
                  // mainTtitle = rateRange;
                  timeText = timeRange;
                } else if (widget.timeFrame == 2) {
                  // mainTtitle = rateRange;
                  timeText = timeRange;
                }
                mainTtitle = rateRange;
              });
            },

            // 防止手势冲突
            // behavior: HitTestBehavior.opaque,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: _ScatterCanvos(),
            )));
  }

  _ScatterCanvos() {
    return Container(
      // padding: EdgeInsets.only(right: widget.chartStyle.horizontalTW),
      // margin: EdgeInsets.only(right: widget.chartStyle.horizontalTW),

      // color: Colors.blue,
      // color: Colors.red,
      // color:Colors.yellow,
      child: ClipRect(
        child: RepaintBoundary(
          child: CustomPaint(
          painter: ScatterChartPainter(
              slipX: slipX,
              interableX: interableX,
              bottomPadding: widget.chartStyle.verticalTH,
              verticalTextStyle: widget.chartStyle.verticalTextStyle,
              horizontalTextStyle: widget.chartStyle.horizontalTextStyle,
              horizontalTW:widget.chartStyle.horizontalTW,
              horizontalPadding: ScreenAdapter.width(6),
              data: dataList,
              minX: minX,
              maxX: maxX,
              minY: minY,
              maxY: maxY,
              dragX: dragX,
              alignedDragX: alignedDragX,
              intervalY: interableY,
              timeFrame:widget.timeFrame,
              ),
        ),
        ),
      ),
    );
  }
}

// 头部样式
class ScatterHeaderStyle {
  // 外边距
  final EdgeInsets margin;

  // 主标题字体样式
  final TextStyle mainTitleStyle;

  // 单位字体样式
  final TextStyle unitStyle;

  // 时间字体样式
  final TextStyle timeStyle;

  // 构造函数
  ScatterHeaderStyle({
    required this.margin,
    required this.mainTitleStyle,
    required this.unitStyle,
    required this.timeStyle,
  });
}

// 图表样式
class ScatterChartStyle {
  // 垂直方向阈值
  final double verticalTH;
  // 水平方向阈值
  final double horizontalTW;
  // 背景颜色
  final Color backgroundColor;
  // 内边距
  final EdgeInsets padding;
  // 点颜色
  final Color dotColor;
  // 垂直字体样式
  final TextStyle verticalTextStyle;
  // 水平字体样式
  final TextStyle horizontalTextStyle;

  // 构造函数
  ScatterChartStyle({
    required this.dotColor,
    required this.verticalTH,
    required this.horizontalTW,
    required this.backgroundColor,
    required this.padding,
    required this.verticalTextStyle,
    required this.horizontalTextStyle,
  });
}

class ScatterChartPainter extends CustomPainter {
  final List<ChartData> data;
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final double bottomPadding; // 底部 X 轴标签空间
  // 垂直字体样式
  final TextStyle verticalTextStyle;

  // 水平字体样式
  final TextStyle horizontalTextStyle;

  final double horizontalPadding;

  final double? dragX; // 新增拖动位置
  final double? alignedDragX; // 新增对齐到 X 轴刻度的拖动位置
  final double slipX;
  final double interableX;
  final double intervalY;
  final double horizontalTW;
  final int timeFrame;

  ScatterChartPainter(
      {required this.bottomPadding,
      required this.data,
      required this.minX,
      required this.maxX,
      required this.minY,
      required this.maxY,
      required this.horizontalTextStyle,
      required this.verticalTextStyle,
      required this.horizontalPadding,
      this.dragX,
      this.alignedDragX,
      required this.slipX,
      required this.interableX, // 接收对齐后的拖动位置
      required this.intervalY,
      required this.horizontalTW,
      required this.timeFrame});

  @override
  void paint(Canvas canvas, Size size) {
    // print("查看宽高");
    // print(size.width);
    // print(dragX);
    final paint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    Size newSize = Size(size.width  - horizontalTW, size.height);

    // 绘制背景
    canvas.drawRect(
      Rect.fromLTWH(
          0, 0, size.width - horizontalTW, size.height - bottomPadding),
      Paint()
        ..color = Colors.lightBlue.withOpacity(0.03)
        // ..color = Colors.red
        ..style = PaintingStyle.fill,
    );

    // 绘制网格线
    _drawGridLines(canvas, newSize, intervalY);

    // 绘制拖动的竖线
    if (alignedDragX != null) {
      _drawVerticalLine(canvas, newSize, alignedDragX!);
    }

    // 绘制散点
    _drawScatterPoints(canvas, newSize);
  }

  void _drawGridLines(Canvas canvas, Size size, double intervalY) {
    final gridPaint = Paint()
      ..color = AppColors.Coloreee
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    final textPainter = TextPainter(
        textAlign: TextAlign.center, textDirection: TextDirection.ltr);

    // 绘制垂直网格线和 X 轴标签

    // final double intervalX = 6;
    for (double i = 0; i <= maxX; i += interableX) {
      final x = _mapXToPixel(i, size.width);
      // print(x);
      // if(i>=minX){
      //   print(x);
      // }

      // 垂直线
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height - bottomPadding),
        gridPaint,
      );
      // print(i);
      String text = "";
      double textSplace = ScreenAdapter.width(8);
      if (slipX.toInt() == 23) {
        text = (i.toInt() % 24) < 10
            ? "0${(i.toInt() % 24)}"
            : (i.toInt() % 24).toString();
      } else if (slipX.toInt() == 6) {
        textSplace = ScreenAdapter.width(12);
        switch (i.toInt() % 7) {
          case 0:
            text = T.dateMon.tr; // 星期一
            break;
          case 1:
            text = T.dateTue.tr; // 星期二
            break;
          case 2:
            text = T.dateWed.tr; // 星期三
            break;
          case 3:
            text = T.dateThu.tr; // 星期四
            break;
          case 4:
            text = T.dateFri.tr; // 星期五
            break;
          case 5:
            text = T.dateSat.tr; // 星期六
            break;
          case 6:
            text = T.dateSun.tr; // 星期日
            break;
          default:
            text = ""; // 默认值
        }
      } else if (slipX.toInt() == 30) {
        // print(i);
        text = (i.toInt() % 31).toString();
      }

      textPainter.text = TextSpan(
        text: text,
        style: verticalTextStyle,
      );
      textPainter.layout();
      // X 轴标签

      if (i != maxX) {
        textPainter.paint(
          canvas,
          Offset(x - textPainter.width / 2 + textSplace,
              size.height - bottomPadding + 4),
        );
      }
    }

    // 绘制水平网格线和 Y 轴标签
    // final double intervalY = 100;
    for (double i = minY; i <= maxY; i += intervalY) {
      final y = _mapYToPixel(i, size.height);

      // 水平线
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width , y),
        gridPaint,
      );

      // textPainter.paint(
      //   canvas,
      //   Offset(size.width - rightPadding + horizontalPadding,
      //       y - textPainter.height / 2 + 8),
      // );
    }
  }

  void _drawScatterPoints(Canvas canvas, Size size) {
    final pointPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;
    // print("查看宽高");
    // print(size.width - horizontalTW);

    double barWidth = ScreenAdapter.width(5); // 设置柱状图宽度

    for (final ChartData point in data) {
      
      final x = _mapXToPixelPadding(point.x, size.width);

      // 如果有区间，绘制带圆角的柱状图
      if (point.minY != 0 && point.maxY != 0 && point.minY != point.maxY) {
        final topY = _mapYToPixel(point.maxY!, size.height);
        double bottomY = _mapYToPixel(point.minY!, size.height);
      //   if(point.x == 48){
      //   print("查看第一个的x，用于解决长度问题");
      //   print(point.minY);
      //   print(point.maxY);
      //   print(topY);
      //   print(bottomY);
      //   print(topY - bottomY);
      // }
        //设置如果长度小于4，则设置bottomY为topY +4
        if ((bottomY - topY) < 4) {
          bottomY = topY + 8;
        }

        // 定义带圆角的矩形
        final RRect roundedRect = RRect.fromRectAndCorners(
          Rect.fromLTRB(
            x - barWidth / 2, // 左侧边界
            topY, // 顶部边界
            x + barWidth / 2, // 右侧边界
            bottomY, // 底部边界
          ),
          topLeft: Radius.circular(4), // 顶部左侧圆角
          topRight: Radius.circular(4), // 顶部右侧圆角
          bottomLeft: Radius.circular(4), // 底部左侧圆角
          bottomRight: Radius.circular(4), // 底部右侧圆角
        );

        // 绘制带圆角的竖柱
        canvas.drawRRect(roundedRect, pointPaint);
      } else if (point.minY != 0 && point.minY == point.maxY) {
        // 如果没有区间，则绘制一个简单的圆点
        final y = _mapYToPixel(point.minY, size.height);
        canvas.drawCircle(Offset(x, y), 4, pointPaint);
      }
      //
    }
  }

  void _drawVerticalLine(Canvas canvas, Size size, double alignedDragX) {
    final linePaint = Paint()
      ..color = Color(0XFFBBBBBB) // 竖线颜色
      // ..color = Colors.red
      ..strokeWidth = 1; // 竖线宽度
      // print("aligneDragX:"+alignedDragX.toString());
      // print("size.width:"+size.width.toString());
    //这里处理一下 alignedDragX;
    alignedDragX = _mapXToPixelPadding(alignedDragX,size.width);

    // 绘制竖线
    canvas.drawLine(
      Offset(alignedDragX, 0), // 起点
      Offset(alignedDragX, size.height - bottomPadding), // 终点
      linePaint,
    );
  }

  double _mapXToPixel(double x, double width) {
    return (x - minX) / (maxX - minX) * width;
  }

  double _mapXToPixelPadding(double x, double width) {
    // print("size.width"+width.toString());
    double usableWidth = 0;
    double leftPadding = 0;
    if(timeFrame == 1){
      //周
      leftPadding=ScreenAdapter.width(18);
    }else{
      leftPadding=ScreenAdapter.width(8);
    }
    usableWidth = width ;
    // print("查看可用宽度:${usableWidth}");
    final effectiveWidth = usableWidth - leftPadding * 2; // 去除左右内边距后的宽度
    // print("查看x的定位:${leftPadding + (x - minX) / (maxX - minX - 1) * effectiveWidth}");
    return leftPadding + (x - minX) / (maxX - minX - 1) * effectiveWidth;
    // return (x - minX) / (maxX - minX) * usableWidth;
  }

  double _mapYToPixel(double y, double height) {
    final usableHeight = height - bottomPadding; // 可用高度
    return height - bottomPadding - (y - minY) / (maxY - minY) * usableHeight;
  }

  // @override
  // bool shouldRepaint(covariant CustomPainter oldDelegate) {

  //   return true;
  // }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // 将 oldDelegate 转换为具体的类型以访问 alignedDragX
    // print("开始重绘");
    // print(alignedDragX);
    // if (oldDelegate is ScatterChartPainter) {
    //   // 如果 alignedDragX 不为空，并且与旧值不同，则重绘
    //   // if(dragX != null){
    //   //   return dragX != oldDelegate.dragX;
    //   // }
    //   if (alignedDragX != null) {
    //     // print(alignedDragX != oldDelegate.alignedDragX);

    //     return alignedDragX != oldDelegate.alignedDragX;
    //   }
      
    //   // 如果当前 alignedDragX 为空，但旧值不为空，也需要重绘

    //   return oldDelegate.alignedDragX != null;
    // }

    // 如果类型不匹配，默认重绘
    return true;
  }
}

class ScatterChartRightPainter extends CustomPainter {
  final double minY;
  final double maxY;
  final double rightPadding; // 右侧额外空间
  // final double horizontalPadding;
  // 垂直字体样式
  final TextStyle verticalTextStyle;
  final double bottomPadding; // 底部 X 轴标签空间
  final double intervalY;

  // 水平字体样式
  final TextStyle horizontalTextStyle;

  final int timeFrame;  

  ScatterChartRightPainter(
      {required this.rightPadding,
      required this.minY,
      required this.maxY,
      required this.horizontalTextStyle,
      required this.verticalTextStyle,
      // required this.horizontalPadding,
      required this.bottomPadding,
      required this.intervalY,
      required this.timeFrame,
      });  

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制右侧标签
    _drawGridLines(canvas, size);
  }

  void _drawGridLines(Canvas canvas, Size size) {
    final textPainter = TextPainter(
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    // 绘制水平网格线和 Y 轴标签
    // final double intervalY = 100;
    for (double i = minY; i <= maxY; i += intervalY) {
      final y = _mapYToPixel(i, size.height);
      // Y 轴标签
      textPainter.text = TextSpan(
        text: i.toInt().toString(),
        style: horizontalTextStyle,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(size.width - rightPadding + ScreenAdapter.width(4),
            y - textPainter.height / 2 + 8),
      );
    }
  }

  double _mapYToPixel(double y, double height) {
    final usableHeight =
        height - bottomPadding - ScreenAdapter.height(37); // 可用高度
    return height - bottomPadding - (y - minY) / (maxY - minY) * usableHeight;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class ChartData {
  final double x;
  final double minY;
  final double maxY;
  String _startTime;
  String _endTime;

  ChartData({
    required this.x,
    required this.minY,
    required this.maxY,
    required String startTime,
    required String endTime,
  })  : _startTime = startTime,
        _endTime = endTime;
  // Getter: 获取本地时间格式字符串
  String get localStartTime {
    if (_startTime == "--") return "--";
    return DateFormat('yyyy-MM-dd HH:mm')
        .format(DateTime.parse(_startTime).toLocal());
  }

  String get localEndTime {
    if (_endTime == "--") return "--";
    return DateFormat('yyyy-MM-dd HH:mm')
        .format(DateTime.parse(_endTime).toLocal());
  }

  // Getter: 获取原始UTC字符串
  String get startTime => localStartTime;
  String get endTime => localEndTime;

  // 从 Map 转换为 ChartData
  factory ChartData.fromJson(Map<String, dynamic> json) {
    return ChartData(
      x: json['x'].toDouble(),
      minY: json['minY'].toDouble(),
      maxY: json['maxY'].toDouble(),
      startTime: json['startTime'],
      endTime: json['endTime'],
    );
  }
  factory ChartData.fromJsonX(Map<String, dynamic> json, double xIndex) {
    return ChartData(
      x: xIndex,
      minY: json['minY'].toDouble(),
      maxY: json['maxY'].toDouble(),
      startTime: json['startTime'],
      endTime: json['endTime'],
    );
  }

  // 转换为 Map
  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'minY': minY,
      'maxY': maxY,
      'startTime': startTime,
      'endTime': endTime,
    };
  }

  // Setter: 传入本地时间字符串，自动转为UTC字符串存储
  set localStartTime(String localTime) {
    if (localTime == "--") {
      _startTime = "--";
    } else {
      final dt = DateTime.parse(localTime);
      _startTime = dt.toUtc().toIso8601String();
    }
  }

  set localEndTime(String localTime) {
    if (localTime == "--") {
      _endTime = "--";
    } else {
      final dt = DateTime.parse(localTime);
      _endTime = dt.toUtc().toIso8601String();
    }
  }

  @override
  String toString() {
    return 'ChartData(x: $x, minY: $minY, maxY: $maxY, startTime: $startTime, endTime: $endTime)';
  }
}
