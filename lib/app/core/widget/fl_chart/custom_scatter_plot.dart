// // /*
// //  * @Author: 张仕鹏 <EMAIL>
// //  * @Date: 2024-12-11 15:43:24
// //  * @LastEditors: 张仕鹏 <EMAIL>
// //  * @LastEditTime: 2024-12-12 15:42:39
// //  * @FilePath: /rpmappmaster/lib/app/core/widget/fl_chart/custom_scatter_plot.dart
// //  * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
// //  */
// // // import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';

// // // import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// // import 'package:aiCare/app/modules/heart_rate/model/grouped_heart_rate.dart';
// // import 'package:gap/gap.dart';
// // import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// // import 'package:aiCare/app/core/values/app_colors.dart';
// // import 'package:aiCare/app/core/values/text_styles.dart';
// // import 'package:aiCare/app/core/widget/fl_chart/custom_dot_painter.dart';
// // import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
// // import 'package:aiCare/app/services/screenAdapter.dart';
// // import 'package:fl_chart/fl_chart.dart';
// // import 'package:flutter/material.dart';
// // import 'package:get/get.dart';
// // import 'package:get/get_core/src/get_main.dart';
// // import 'package:flutter_gen/gen_l10n/app_localizations.dart';
// // import 'package:logger/logger.dart';

// // class CustomScatterPlot extends StatefulWidget {
// //   final int timeFrame; // 当前图表展示类型（日/月/年）
// //   final List<HeartRateData> data; // 数据列表
// //   final Color color; // 点的颜色

// //   AppLocalizations get appLocalization => AppLocalizations.of(Get.context!)!;

// //   CustomScatterPlot({
// //     super.key,
// //     required this.timeFrame,
// //     required this.data,
// //     required this.color,
// //   });

// //   @override
// //   _CustomScatterPlotState createState() => _CustomScatterPlotState();
// // }

// // class _CustomScatterPlotState extends State<CustomScatterPlot> {
// //   double minX = -1;
// //   double minY = 0;
// //   double maxX = 25;
// //   double maxY = 200;
// //   // List<> dataList =[];
// //   Map<String, Map<int, List<GroupedHeartRateData>>> list;

// //   @override
// //   void initState() {
// //     // TODO: implement initState
// //     super.initState();
// //     initData();
// //   }

// //   void initData() {
// //     //周
// //     if (widget.timeFrame == 1) {
// //       //月
// //     } else if (widget.timeFrame == 2) {
// //       //日
// //     } else {
// //       setState(() {
// //         minX = -1;
// //         minY = 0;
// //         maxX = 24;
// //         maxY = 200;
// //       });
// //           // 分组并融合数据
// //     final groupedData = groupAndMergeHeartRateData(valueList.value);
// //     // 打印结果
// //     groupedData.forEach((date, hourlyData) {
// //       print("Date: $date");
// //       hourlyData.forEach((hour, groupList) {
// //         print("  Hour: $hour");
// //         for (final group in groupList) {
// //           print("    $group");
// //         }
// //       });
// //     });
// //     }
// //   }

//     import 'package:aiCare/app/modules/heart_rate/model/grouped_heart_rate.dart';
// import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';

// /// 按日期和小时分组
//   Map<String, Map<int, List<HeartRateData>>> groupHeartRateDataByDateAndHour(
//       List<HeartRateData> data) {
//     // 创建一个 Map 用于分组：日期 -> 小时 -> 数据列表
//     final Map<String, Map<int, List<HeartRateData>>> groupedData = {};

//     for (final entry in data) {
//       // 格式化日期，保留 yyyy-MM-dd
//       final dateKey = entry.date!.toUtc().toIso8601String().split('T')[0];

//       // 提取小时
//       final hourKey = entry.date!.hour;

//       // 如果日期不存在，初始化为嵌套 Map
//       if (!groupedData.containsKey(dateKey)) {
//         groupedData[dateKey] = {};
//       }

//       // 如果小时不存在，初始化为列表
//       if (!groupedData[dateKey]!.containsKey(hourKey)) {
//         groupedData[dateKey]![hourKey] = [];
//       }

//       // 将数据添加到对应日期和小时的列表中
//       groupedData[dateKey]![hourKey]!.add(entry);
//     }

//     return groupedData;
//   }

//   /// 按日期和小时分组并对小时内数据融合
//   Map<String, Map<int, List<GroupedHeartRateData>>> groupAndMergeHeartRateData(
//       List<HeartRateData> data) {
//     // 日期 -> 小时 -> 分组数据
//     final Map<String, Map<int, List<GroupedHeartRateData>>> groupedData = {};

//     // 按日期和小时分组
//     final hourlyGroupedData = groupHeartRateDataByDateAndHour(data);

//     // 遍历日期和小时
//     hourlyGroupedData.forEach((date, hourlyData) {
//       groupedData[date] = {};

//       hourlyData.forEach((hour, dataList) {
//         // 按时间排序
//         dataList.sort((a, b) => a.date!.compareTo(b.date!));

//         // 分组逻辑
//         List<GroupedHeartRateData> groups = [];
//         double? currentMinY;
//         double? currentMaxY;
//         DateTime? currentStartTime;
//         DateTime? currentEndTime;

//         for (var entry in dataList) {
//           if (currentMinY == null) {
//             // 初始化新分组
//             currentMinY = entry.data!.toDouble();
//             currentMaxY = entry.data!.toDouble();
//             currentStartTime = entry.date!;
//             currentEndTime = entry.date!;
//           } else {
//             // 检查是否符合融合条件
//             final percentageDiff =
//                 (entry.data!.toDouble() - currentMaxY!).abs();
//             final timeDiff = entry.date!.difference(currentEndTime!).inMinutes;

//             if (percentageDiff > 20 || timeDiff > 20) {
//               // 不符合条件，保存当前分组并创建新分组
//               groups.add(GroupedHeartRateData(
//                 minY: currentMinY,
//                 maxY: currentMaxY,
//                 startTime: currentStartTime!,
//                 endTime: currentEndTime!,
//               ));

//               // 开始新分组
//               currentMinY = entry.data!.toDouble();
//               currentMaxY = entry.data!.toDouble();
//               currentStartTime = entry.date!;
//               currentEndTime = entry.date!;
//             } else {
//               // 符合条件，更新当前分组
//               currentMinY = currentMinY < entry.data!.toDouble()
//                   ? currentMinY
//                   : entry.data!.toDouble();
//               currentMaxY = currentMaxY > entry.data!.toDouble()
//                   ? currentMaxY
//                   : entry.data!.toDouble();
//               currentEndTime = entry.date!;
//             }
//           }
//         }

//         // 添加最后一组数据
//         if (currentMinY != null) {
//           groups.add(GroupedHeartRateData(
//             minY: currentMinY,
//             maxY: currentMaxY!,
//             startTime: currentStartTime!,
//             endTime: currentEndTime!,
//           ));
//         }

//         groupedData[date]![hour] = groups;
//       });
//     });

//     return groupedData;
//   }


  

// //   @override
// //   Widget build(BuildContext context) {
// //     return Container(
// //       width: ScreenAdapter.width(320),
// //       height: ScreenAdapter.height(253),
// //       margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
// //       child: Column(
// //         children: [
// //           _topCard(),
// //           // Expanded(
// //           //   child: ScatterChart(
// //           //     ScatterChartData(
// //           //       clipData: FlClipData.all(), // 开启裁剪
// //           //       scatterSpots: _generateScatterSpots(),
// //           //       minX: minX,
// //           //       maxX: maxX,
// //           //       minY: minY,
// //           //       maxY: maxY,
// //           //       gridData: FlGridData(
// //           //         show: true,
// //           //         drawHorizontalLine: true,
// //           //         drawVerticalLine: true,
// //           //         horizontalInterval: 100,
// //           //         verticalInterval: 1,
// //           //         getDrawingHorizontalLine: (value) {
// //           //           return FlLine(
// //           //             color: AppColors.Coloreee,
// //           //             strokeWidth: 1,
// //           //             dashArray: [5, 0],
// //           //           );
// //           //         },
// //           //         getDrawingVerticalLine: (value) {
// //           //           // print(value);
// //           //           return FlLine(
// //           //             color: AppColors.Coloreee,
// //           //             strokeWidth: (value.toInt() + 1) % 6 == 0 ? 1 : 0,
// //           //             dashArray: [5, 5],
// //           //           );
// //           //         },
// //           //       ),
// //           //       backgroundColor: AppColors.lightBlue.withOpacity(0.03),
// //           //       borderData: FlBorderData(
// //           //         show: true,
// //           //         border: Border.all(color: AppColors.Coloreee),
// //           //       ),
// //           //       titlesData: _customTitlesData(),
// //           //     ),
// //           //   ),
// //           // ),
        
// //         ],
// //       ),
// //     );
// //   }

// //   /// 根据 timeFrame 动态生成 ScatterSpot 列表
// //   List<ScatterSpot> _generateScatterSpots() {
// //     return widget.data
// //         .map((e) => ScatterSpot(
// //               _calculateXValue(e.date!),
// //               e.data!.toDouble(),
// //               dotPainter: CustomDotPainter(
// //                 color: widget.color,
// //                 width: 10.0,
// //                 fromY: 10,
// //                 toY: 40,
// //               ),
// //             ))
// //         .toList();
// //   }

// //   /// 格式化 X 轴的标签
// //   String _formatXAxisLabel(double value) {
// //     String result;
// //     switch (widget.timeFrame) {
// //       case 0: // 日视图
// //         int modValue = value.toInt() % 24;
// //         if (modValue == 12 || modValue == 18) return "${value.toInt()}";
// //         if (modValue == 0 || modValue == 6) return "0${modValue}";
// //         break;
// //       case 1: // 月视图
// //         return "${value.toInt()}d";
// //       case 2: // 年视图
// //         return "${value.toInt()}m";
// //       default:
// //         return value.toString();
// //     }
// //     return "";
// //   }

// //   /// 根据时间动态计算 X 坐标
// //   double _calculateXValue(DateTime date) {
// //     switch (widget.timeFrame) {
// //       case 0: // 日视图
// //         return date.hour.toDouble();
// //       case 1: // 月视图
// //         return date.day.toDouble();
// //       case 2: // 年视图
// //         return date.month.toDouble();
// //       default:
// //         return date.hour.toDouble();
// //     }
// //   }

// //   FlTitlesData _customTitlesData() {
// //     return FlTitlesData(
// //       bottomTitles: AxisTitles(
// //         sideTitles: SideTitles(
// //           showTitles: true,
// //           reservedSize: ScreenAdapter.height(15),
// //           interval: 1,
// //           getTitlesWidget: (value, meta) {
// //             return Text(
// //               _formatXAxisLabel(value),
// //               style: normalF12H17C666,
// //             );
// //           },
// //         ),
// //       ),
// //       leftTitles: AxisTitles(
// //         sideTitles: SideTitles(
// //           showTitles: false,
// //         ),
// //       ),
// //       topTitles: AxisTitles(
// //         sideTitles: SideTitles(
// //           showTitles: false,
// //         ),
// //       ),
// //       rightTitles: AxisTitles(
// //         sideTitles: SideTitles(
// //           showTitles: true,
// //           interval: 1,
// //           getTitlesWidget: (value, meta) {
// //             if (value == 100 || value == 200) {
// //               return Text(
// //                 value.toInt().toString(),
// //                 style: normalF12H17C666,
// //               );
// //             }
// //             return const SizedBox.shrink();
// //           },
// //         ),
// //       ),
// //     );
// //   }

// //   Widget _topCard() {
// //     return Container(
// //       alignment: Alignment.centerLeft,
// //       margin: EdgeInsets.only(bottom: ScreenAdapter.height(4)),
// //       child: Column(
// //         mainAxisAlignment: MainAxisAlignment.end,
// //         crossAxisAlignment: CrossAxisAlignment.start,
// //         children: [
// //           Text.rich(
// //             TextSpan(
// //               children: [
// //                 TextSpan(
// //                   text: "50-130",
//                   // style: normalF16H22C666.copyWith(
//                   //   color: AppColors.Color333,
//                   //   fontWeight: FontWeight.w600,
//                   // ),
// //                 ),
// //                 TextSpan(
// //                   text: " ${widget.appLocalization.wordsBPM}",
// //                   style: normalF12H17C999,
// //                 ),
// //               ],
// //             ),
// //           ),
// //           Text(
// //             "Today",
// //             style: normalF12H17C666.copyWith(fontWeight: FontWeight.w500),
// //           ),
// //         ],
// //       ),
// //     );
  
// //   }


// // }
