import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

class CustomHomeBar extends StatelessWidget with BaseWidgetMixin {
  // 创建静态实例
  static final CustomHomeBar _instance = CustomHomeBar._internal();

  //选中下标
  RxString selectedIndex = "".obs;

  // 私有构造函数
  CustomHomeBar._internal({Key? key});

  // 工厂构造函数返回唯一实例
  factory CustomHomeBar() {
    return _instance;
  }

  Rx<bool> isCenterDropdownOpen = false.obs; // 控制下拉菜单的显示
  Rx<bool> isRightDropdownOpen = false.obs; // 控制下拉菜单的显示
  // late OverlayEntry overlayCenterEntry;
  // late OverlayEntry overlayRightEntry;
  Rx<String> centerValue = "".obs;
  int index = 0;

  getAllCenterList() {
    return [
      T.bloodOxygen.tr,
      // T.bloodPressure,
      T.temperature.tr,
      // T.wordsWeight,
      // T.bloodSugar,
      T.heartRate.tr,
      T.fitness.tr,
      T.sleep.tr
    ];
  }

  // void getIndexOfCenterValue(String value) {
  //   List<String> centerList = getAllCenterList();
  //   switch (centerList.indexOf(value)) {
  //     case 0:
  //       if (centerValue.value != T.bloodOxygen) {
  //         CustomHomeBar().setCenterValue(0);
  //         index = 0;
  //         Get.offNamed(Routes.OXYGEN_HOME);
  //       }
  //       return;
  //     // case 1:
  //     //   if (centerValue.value != T.bloodPressure) {
  //     //     CustomHomeBar().setCenterValue(1);
  //     //     index = 1;
  //     //     Get.offNamed(Routes.PRESSURE_HOME);
  //     //   }
  //     //   return;

  //     case 1:
  //       if (centerValue.value != T.temperature) {
  //         CustomHomeBar().setCenterValue(1);
  //         index = 1;
  //         Get.offNamed(Routes.TEMPERATURE_HOME);
  //       }
  //       return;

  //     // case 3:
  //     //   if (centerValue.value != T.wordsWeight) {
  //     //     CustomHomeBar().setCenterValue(3);
  //     //     index = 3;
  //     //     Get.offNamed(Routes.WEIGHT_HOME);
  //     //   }
  //     //   return;
  //     // case 4:
  //     //   if (centerValue.value != T.bloodSugar) {
  //     //     CustomHomeBar().setCenterValue(4);
  //     //     index = 4;
  //     //     Get.offNamed(Routes.SUGAR_HOME);
  //     //   }
  //     //   return;

  //     case 2:
  //       if (centerValue.value != T.heartRate) {
  //         CustomHomeBar().setCenterValue(2);
  //         index = 2;
  //         Get.offNamed(Routes.RATE_HOME);
  //       }
  //       return;
  //     // case 0:
  //     //   if (centerValue.value == T.bloodOxygen)
  //     //     return;
  //     //   else
  //     //     Get.offAndToNamed(Routes.OXYGEN_HOME);
  //     //   return;
  //     // case 0:
  //     //   if (centerValue.value == T.bloodOxygen)
  //     //     return;
  //     //   else
  //     //     Get.offAndToNamed(Routes.OXYGEN_HOME);
  //     //   return;
  //     // case 0:
  //     //   if (centerValue.value == T.bloodOxygen)
  //     //     return;
  //     //   else
  //     //     Get.offAndToNamed(Routes.OXYGEN_HOME);
  //     //   return;
  //   }
  // }

  void getIndexOfCenterValue(String value) {
    switch (value) {
      case "0": // 对应血氧逻辑
        if (centerValue.value != T.bloodOxygen) {
          CustomHomeBar().setCenterValue(0);
          index = 0;
          Get.offNamed(Routes.OXYGEN_HOME);
        }
        break;

      case "1": // 对应温度逻辑
        if (centerValue.value != T.temperature) {
          CustomHomeBar().setCenterValue(1);
          index = 1;
          Get.offNamed(Routes.TEMPERATURE_HOME);
        }
        break;

      case "2": // 对应心率逻辑
        if (centerValue.value != T.heartRate) {
          CustomHomeBar().setCenterValue(2);
          index = 2;
          Get.offNamed(Routes.RATE_HOME);
        }
        break;
      case "3": // 对应心率逻辑
        if (centerValue.value != T.fitness) {
          CustomHomeBar().setCenterValue(3);
          index = 3;
          Get.offNamed(Routes.FITNESS_HOME);
        }
        break;
      case "4": // 对应心率逻辑
        if (centerValue.value != T.sleep) {
          CustomHomeBar().setCenterValue(4);
          index = 4;
          Get.offNamed(Routes.SLEEP_HOME);
        }
        break;

      default: // 处理不匹配的情况
        print("Invalid value: $value");
    }
  }

  void getIndexOfRightValue(String value) {
    //延时0.5s
    Future.delayed(Duration(milliseconds: 300), () {
      switch (value) {
        case "0": // 跳转到手动输入页面
          Get.toNamed(Routes.MANUAL_INPUT, arguments: {"index": index});
          break;

        case "1": // 跳转到相关信息页面
          logger.d("index: $index");
          Get.toNamed(Routes.RELATED_INFOR, arguments: {'index': index});
          break;

        case "2": // 跳转到测试组件页面
          Get.toNamed(Routes.BLUETOOTH_FUNCTION);
          break;

        default: // 处理无效值
          print("Invalid value: $value");
      }
      selectedIndex.value = "";
    });
  }

  setCenterValue(i) {
    return centerValue.value = getAllCenterList()[i];
  }

  getAllRightList() {
    return [
      T.knowledgeManuallyEnterData.tr,
      T.knowledUnderstandRelevant.tr,
      T.camera.tr,
    ];
  }

  @override
  Widget body(BuildContext context) {
    // final overlay = Overlay.of(context);
    return Container(
        width: double.infinity,
        color: Colors.white,
        height: kToolbarHeight,
        child: Center(
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      // Get.offAndToNamed(Routes.TABS);
                      Get.back();
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                          left: ScreenAdapter.width(16),
                          top: kToolbarHeight / 2 - ScreenAdapter.height(14),
                          bottom: kToolbarHeight / 2 - ScreenAdapter.height(14),
                          right: ScreenAdapter.width(16)),
                      width: ScreenAdapter.width(56),
                      height: kToolbarHeight,
                      child: Image.asset(
                        Assets.images.vectorLeft.path,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),

                  //中间
                  GestureDetector(
                    onTap: () {
                      //起始点
                      if (isCenterDropdownOpen.value) {
                        // overlayCenterEntry.remove();
                        isCenterDropdownOpen.value = false;
                      } else {
                        isCenterDropdownOpen.value = true;
                        showCenterLay(context);
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: ScreenAdapter.width(16)),
                      color: Colors.white,
                      child: Obx(
                        () => Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              centerValue.value,
                              style: TextStyle(
                                color: AppColors.Color444,
                                fontWeight: FontWeight.w500,
                                fontSize: ScreenAdapter.fontSize(20),
                                height: 28 / 20,
                              ),
                            ),
                            SizedBox(width: ScreenAdapter.width(6)),
                            Image.asset(
                              isCenterDropdownOpen.value
                                  ? Assets.images.vectorTopBlue.path
                                  : Assets.images.vectorBottom.path,
                              width: ScreenAdapter.width(12),
                              height: ScreenAdapter.height(24),
                              fit: BoxFit.contain,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  //右边
                  GestureDetector(
                    onTap: () {
                      if (isRightDropdownOpen.value) {
                        // overlayRightEntry.remove();
                        isRightDropdownOpen.value = false;
                      } else {
                        isRightDropdownOpen.value = true;
                        showRightLay(context);
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                          left: ScreenAdapter.width(16),
                          top: kToolbarHeight / 2 - ScreenAdapter.height(14),
                          bottom: kToolbarHeight / 2 - ScreenAdapter.height(14),
                          right: ScreenAdapter.width(16)),
                      width: ScreenAdapter.width(56),
                      height: kToolbarHeight,
                      child: Image.asset(
                        Assets.images.navigationRight.path,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  void showCenterLay(BuildContext context) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Size screenSize = renderBox.size;
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        screenSize.width / 2 - 50, // 屏幕宽度的一半减去菜单宽度的一半
        screenSize.height / 2 + ScreenAdapter.height(60), // 屏幕高度的一半减去菜单高度的一半
        screenSize.width / 2 - 50, // 同左边保持一致
        screenSize.height / 2 - 25, // 同顶部保持一致
      ),
      items: [
        // getAllCenterList(),
        PopupMenuItem<String>(
          value: '0',
          child: Text(
            T.bloodOxygen.tr,
          ),
        ),
        PopupMenuItem<String>(
          value: '1',
          child: Text(
            T.temperature.tr,
          ),
        ),
        PopupMenuItem<String>(
          value: '2',
          child: Text(
            T.heartRate.tr,
          ),
        ),
        PopupMenuItem<String>(
          value: '3',
          child: Text(
            T.fitness.tr,
          ),
        ),
        PopupMenuItem<String>(
          value: '4',
          child: Text(
            T.sleep.tr,
          ),
        ),
      ],
      elevation: 8.0,
    ).then((value) {
      isCenterDropdownOpen.value = false;
      if (value != null) {
        // if (value == "0") {
        //   // tabsController.temperatureUnit.value = true;
        //   // SecureStorageService.instance.setTemperatureUnit("true");
        //   centerValue.value = T.bloodOxygen;
        // } else if(value == "1"){
        //   // tabsController.temperatureUnit.value = false;
        //   // SecureStorageService.instance.setTemperatureUnit("false");
        //   centerValue.value = T.temperature;
        // }else if(value == "2"){
        //   centerValue.value = T.heartRate;
        //   // tabsController.temperatureUnit.value = false;
        //   // SecureStorageService.instance.setTemperatureUnit("false");
        // }
        getIndexOfCenterValue(value);
        // tabsController.update();
        // tabsController.temperatureUnit.value = value;
      }
    });
    // overlayCenterEntry = OverlayEntry(
    //   builder: (context) => Positioned(
    //     top: ScreenAdapter.height(88),
    //     left: 0,
    //     right: 0,
    //     child: Center(
    //       child: Material(
    //         color: Colors.transparent,
    //         child: IntrinsicWidth(
    //           child: buildDropdownContent(
    //             getAllCenterList(),
    //             onItemSelected: (value) {
    //               overlayCenterEntry.remove();
    //               isCenterDropdownOpen.value = false;
    //               logger.d(value);
    //               getIndexOfCenterValue(value);

    //               // controller.dropdownValue.value = value;
    //               // controller.onSelect(value);
    //             },
    //           ),
    //         ),
    //       ),
    //     ),
    //   ),
    // );
    // // overlay.insert(overlayCenterEntry);
  }

  void showRightLay(BuildContext context) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Size screenSize = renderBox.size;

    // 动态生成菜单项列表
    List<PopupMenuEntry<String>> menuItems = [];

    // 检查当前中间显示的文字是否为 sleep 或 fitness
    bool shouldHideDataSource =
        centerValue.value == T.sleep.tr || centerValue.value == T.fitness.tr;

    // 如果不是 sleep 或 fitness，则添加 "Data Source on hand" 菜单项
    if (!shouldHideDataSource) {
      menuItems.add(_buildPopupMenuItem(
          '0', T.commonDataSourceOnHand.tr, Assets.images.edit.path));
      menuItems.add(PopupMenuItem<String>(
        enabled: false,
        height: ScreenAdapter.height(4),
        child: Container(color: Colors.transparent),
      ));
    }
    // 添加其他菜单项
    menuItems.add(_buildPopupMenuItem(
        '1', T.knowledUnderstandRelevant.tr, Assets.images.knowledge.path));
    menuItems.add(PopupMenuItem<String>(
      enabled: false,
      height: ScreenAdapter.height(4),
      child: Container(color: Colors.transparent),
    ));
    menuItems.add(_buildPopupMenuItem(
        '2', T.bluetoothFunction.tr, Assets.images.bluetoothMenu.path));

    //设置高度为 154，宽度为 118
    showMenu<String>(
      context: context,
      menuPadding: EdgeInsets.all(0),
      position: RelativeRect.fromLTRB(
        screenSize.width / 2 + 50, // 屏幕宽度的一半减去菜单宽度的一半
        screenSize.height / 2 + ScreenAdapter.height(60), // 屏幕高度的一半减去菜单高度的一半
        // screenSize.width / 2 - 50, // 同左边保持一致
        // 20,
        0,
        screenSize.height / 2 - 25, // 同顶部保持一致
      ),

      items: menuItems, // 使用动态生成的菜单项列表
      elevation: 8.0,
    ).then((value) {
      isCenterDropdownOpen.value = false;
      if (value != null) {
        selectedIndex.value = value;
        // logger.d(selectedIndex.value);
        getIndexOfRightValue(value);
        // selectedIndex.value = "";
        // tabsController.update();
        // tabsController.temperatureUnit.value = value;
      }
    });
  }

  PopupMenuItem<String> _buildPopupMenuItem(
      String value, String title, String imagePath) {
    return PopupMenuItem<String>(
      value: value,
      // padding: EdgeInsets.all(2),
      height: ScreenAdapter.height(40),
      child: Obx(
        () => Container(
          width: double.infinity,
          height: ScreenAdapter.height(40),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(ScreenAdapter.width(4)),
            color: selectedIndex.value == value
                ? AppColors.lightBlue.withOpacity(0.12)
                : Colors.transparent,
            // color: Colors.red
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,

            children: [
              Gap(ScreenAdapter.width(6)),
              Image.asset(
                imagePath,
                width: ScreenAdapter.height(18),
                height: ScreenAdapter.height(18),
                color: selectedIndex.value == value
                    ? AppColors.lightBlue
                    : AppColors.Color666,
              ),
              Gap(ScreenAdapter.width(4)),
              Container(
                // width: 100,
                padding: EdgeInsets.all(0),
                // color: Colors.red,
                child: Text(
                title,
                style: TextStyle(
                  color: selectedIndex.value == value
                      ? AppColors.lightBlue
                      : Color(0xFF858585),
                  fontWeight: FontWeight.w400,
                  fontSize: ScreenAdapter.fontSize(12),
                  
                ),
              ),
              
              ),
            ],
          ),
        ),
      ),
    );
  }
}
