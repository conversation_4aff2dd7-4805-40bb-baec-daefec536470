/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-12-05 14:24:58
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-12 16:11:29
 * @FilePath: /rpmappmaster/lib/app/core/widget/custom_rate_pie.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_decoration.dart';
import 'package:aiCare/app/core/render/inset_box_shadow/lib/box_shadow.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';

import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';

import 'package:get/get.dart';
import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;



class CustomRatePie extends StatelessWidget with BaseWidgetMixin {
  final double width;
  final double rightPadding;
  final HeartRateData rateData;

  CustomRatePie({
    super.key,
    required this.rateData,
    required this.width,
    required this.rightPadding,
  });

  @override
  Widget body(BuildContext context) {
    bool show = rateData.data != null;
    int level = show ? (rateData.getTotalLevel() ?? 1) : 1; // 默认normal
    String severityText = show ? getSeverityString(level) : "--";
    Color severityColor = show ? getSeverityColor(level) : AppColors.Color999;

    return Container(
      width: width,
      height: width,
      margin: EdgeInsets.only(top: ScreenAdapter.height(38)),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 背景图片层
          Image.asset(
            show ? Assets.images.ratePie.path : Assets.images.ratePieNone.path,
            width: width,
            height: width,
            fit: BoxFit.cover,
          ),
          
          // 内容覆盖层
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // SizedBox(height: ScreenAdapter.width(56)),
              
              // 心率数值
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    show ? rateData.data.toString() : "--",
                    style: TextStyle(
                      color: severityColor,
                      fontSize: ScreenAdapter.fontSize(40),
                      height: 46.88 / 40,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: ScreenAdapter.width(4)),
                  Text(
                    T.wordsBPM.tr,
                    style: TextStyle(
                      fontSize: ScreenAdapter.fontSize(12),
                      height: 14.0 / 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.Color666,
                    ),
                  ),
                ],
              ),
              
              // 状态标签
              Container(
                height: ScreenAdapter.height(22),
                padding: EdgeInsets.symmetric(
                  horizontal: ScreenAdapter.width(14),
                  vertical: ScreenAdapter.height(3),
                ),
                decoration: BoxDecoration(
                  color: AppColors.recordList[rateData.getTotalLevel()],
                  borderRadius: BorderRadius.circular(ScreenAdapter.width(16)),
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromRGBO(255, 255, 255, 0.45),
                      offset: Offset(0, 2),
                      blurRadius: ScreenAdapter.width(4),
                      spreadRadius: 0.0,
                      inset: true,
                    ),
                    BoxShadow(
                      color: Color.fromRGBO(255, 255, 255, 0.45),
                      offset: Offset(0, -2),
                      blurRadius: ScreenAdapter.width(4),
                      spreadRadius: 0.0,
                      inset: true,
                    ),
                  ],
                ),
                child: Text(
                  severityText,
                  textAlign: TextAlign.center,
                  style: normalF12H17C333.copyWith(color: Colors.white),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  String getSeverityString(int value) {
    switch (value) {
      case 0:
        return T.wordsLow.tr;
      case 1:
        return T.wordsNormal.tr;
      case 2:
        return T.wordsMild.tr;
      case 3:
        return T.wordsSerious.tr;
      default:
        return T.wordsSerious.tr;
    }
  }

  Color getSeverityColor(int value) {
    return AppColors.recordList[value];
  }
}