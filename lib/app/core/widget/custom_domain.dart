


import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/model/domains.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class CustomDomain extends StatelessWidget with BaseWidgetMixin {
  final List<Domains> list;

  CustomDomain({super.key, required this.list});

  @override
  Widget body(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
          top: ScreenAdapter.height(8), bottom: ScreenAdapter.height(8)),
      width: ScreenAdapter.width(343),
      // height: ScreenAdapter.height(200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8), // 设置 8px 圆角
        color: AppColors.colorWhite,
      ),
      child: Column(
        children: [
          // Title
          Container(
            margin: EdgeInsets.only(
                left: ScreenAdapter.width(12), top: ScreenAdapter.height(12)),
            alignment: Alignment.topLeft,
            child: Text(
              T.commonDomain.tr,
              style: normalF12H17C999.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppColors.Color333
                ),
            ),
          ),
                   // Icons
          Container(
            margin: EdgeInsets.only(
              top: ScreenAdapter.height(16),
              left: ScreenAdapter.width(24),
              right: ScreenAdapter.width(24),
              bottom: ScreenAdapter.height(8)
            ),
            width: ScreenAdapter.width(295),
            child: Column(
              children: _buildIconsGrid(list),
            ),
          ),
        ],
      ),
    );
  }
}

List<Widget> _buildIconsGrid(List<Domains> list) {
    List<Widget> rows = [];
    for (int i = 0; i < list.length; i += 3) {
      // Get the sublist for the current row
      var sublist = list.sublist(
        i,
        i + 3 > list.length ? list.length : i + 3,
      );
      rows.add(
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: sublist
              .map((domain) => IconsButton(
                    images: domain.images,
                    text: domain.text,
                    tap: domain.tap,
                    
                  ))
              .toList(),
        ),
      );
    }
    return rows;
  }



class IconsButton extends StatelessWidget with BaseWidgetMixin {
  final String images;
  final String text;
  final VoidCallback tap;

  IconsButton({
    super.key,
    required this.images,
    required this.text,
    required this.tap,
  });

  @override
  Widget body(BuildContext context) {
    return InkWell(
      onTap: tap,
      child: SizedBox(
        width: ScreenAdapter.width(58),
        height: ScreenAdapter.height(58),
        child: Column(
          // mainAxisAlignment: MainAxisAlignment.end,
          // crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              images,
              width: ScreenAdapter.width(32),
              height: ScreenAdapter.height(32),
            ),
            Expanded(child: Container()),
            Container(
              width: ScreenAdapter.width(58),
              height: ScreenAdapter.height(20),
              alignment: Alignment.topCenter,
              child: Text(
                text,
                style: normalF12H17C999.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppColors.Color333
                ),
              ),
            )
          ],
        ),
      
      ),
    );
  }
}
