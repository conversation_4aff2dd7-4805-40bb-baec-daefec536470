/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-25 11:28:14
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-02-11 14:08:55
 * @FilePath: /RPM-APP-MASTER/lib/app/core/widget/user_agreement.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/controller/language_controller.dart';
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:url_launcher/url_launcher.dart';

class UserAgreement extends StatelessWidget with BaseWidgetMixin {
  final controller;
  final languageController = Get.find<LanguageController>();
  UserAgreement({super.key, required this.controller});

  @override
  Widget body(BuildContext context) {
    //图标
    return Container(
      margin: EdgeInsets.only(bottom: ScreenAdapter.height(12)),
      width: ScreenAdapter.width(285),
      height: ScreenAdapter.height(34),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Obx(() => InkWell(
                onTap: () {
                  controller.setUserReadPolic(!controller.userReadPolicy.value);
                },
                child: Container(
                  width: ScreenAdapter.width(16),
                  height: ScreenAdapter.height(16),
                  margin: EdgeInsets.all(ScreenAdapter.width(8)),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.loginButtonBgColor),
                    color: controller.userReadPolicy.value
                        ? AppColors.loginButtonBgColor
                        : Colors.transparent,
                  ),
                  child: controller.userReadPolicy.value
                      ? const Icon(
                          Icons.check,
                          size: 12,
                          color: Colors.white,
                        )
                      : null,
                ),
              )),
          SizedBox(
            width: ScreenAdapter.width(261 - 8),
            height: ScreenAdapter.height(34),
            child: RichText(
              text: TextSpan(
                  style: TextStyle(
                    color: AppColors.loginOtherTextColor,
                    fontSize: ScreenAdapter.fontSize(14),
                    height: ScreenAdapter.height(34) / ScreenAdapter.height(28),
                  ),
                  children: [
                    TextSpan(
                      text: T.commonUserReadPolicy1.tr,
                    ),
                    TextSpan(
                      text: T.commonUserReadPolicy2.tr,
                      style: TextStyle(color: Colors.blue),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          _launchURL(
                              'https://medical.aihnet.com/user-agreement-aicare/');
                        },
                    ),
                    TextSpan(
                      text: T.commonUserReadPolicy3.tr,
                    ),
                    TextSpan(
                      text: T.commonUserReadPolicy4.tr,
                      style: TextStyle(color: Colors.blue),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          _launchURL(
                              'https://medical.aihnet.com/privacy-policy-aicare/');
                        },
                    ),
                    TextSpan(
                      text: languageController.currentLocale.languageCode == 'en' ? '.' : '。',
                    ),
                  ]),
            ),
          )
        ],
      ),
    );
  }

  // 打开链接的函数
  void _launchURL(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }
}
