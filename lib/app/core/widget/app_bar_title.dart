import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';
import 'package:aiCare/app/core/values/app_colors.dart';

import '../values/text_styles.dart';

class AppBarTitle extends StatelessWidget {
  final String text;

  const AppBarTitle({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        color: AppColors.Color444,
        fontSize: ScreenAdapter.fontSize(22),
        height: ScreenAdapter.fontSize(31/22.0),
        fontWeight: FontWeight.w600
        ),
      textAlign: TextAlign.center,
    );
  }
}


