/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-23 14:38:04
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-04-16 15:06:36
 * @FilePath: /RPM-APP-MASTER/lib/app/core/widget/custom_temperature_chart.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/model/position_top_left.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/modules/blood_pressure/model/pressure_data.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/widgets/temperature_mandatory_sign.dart';
import 'package:aiCare/app/modules/temperature/temperature_home/widgets/temperature_pointer.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class CustomTemperatureChart extends StatelessWidget with BaseWidgetMixin {
  final TabsController tabsController = Get.find();
  final double width;
  final double height;
  final PositionTopLeft posList;
  TemperatureData? data; // 修改为可选类型
  TextStyle? numberStyle;
  TextStyle? unitStyle;
  bool big;
  final double pointWidth;
  final double pointHeight;
  bool text;

  CustomTemperatureChart(
      {super.key,
      this.data,
      required this.posList,
      this.numberStyle,
      this.unitStyle,
      required this.width,
      required this.height,
      this.big = true,
      required this.pointWidth,
      required this.pointHeight,
      this.text = false});

  String switchText(index) {
    switch (index) {
      case 0:
        return T.wordsLow.tr;
      case 1:
        return T.wordsNormal.tr;
      case 2:
        return T.wordsMild.tr;
      case 3:
        return T.wordsModerate.tr;
      case 4:
        return T.wordsSerious.tr;
      default:
        return "--";
    }
  }

  String switchPie(index) {
    switch (index) {
      case 0:
        return Assets.images.temperatureCenPieNone.path;
      case 1:
        return Assets.images.temperatureCenPie.path;
      case 2:
        return Assets.images.temperatureFahPieNone.path;
      case 3:
        return Assets.images.temperatureFahPie.path;
      default:
        return Assets.images.temperatureCenPieNone.path;
    }
  }

  @override
  Widget body(BuildContext context) {
    bool show = data?.data != null; // 当 data 或 data.data 为 null 时，show 为 false

    return IgnorePointer(
      child: SizedBox(
        width: width,
        height: height,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Obx(
              () => getTemperaturePie(),
            ),
            if (data?.data != null)
              Positioned(
                top: posList.top,
                left: posList.left,
                child: TweenAnimationBuilder(
                  tween: Tween(begin: 37, end: show ? (data?.data ?? 37) : 37),
                  duration: Duration(milliseconds: 350),
                  builder: (context, value, child) {
                    return RepaintBoundary(
                      child: CustomPaint(
                        size: Size(pointWidth, pointHeight), // 画布的宽度和高度
                        painter: TemperaturePointer(
                          value.toDouble(),
                          pointWidth,
                          big ? ScreenAdapter.width(2) : ScreenAdapter.width(1),
                          pointHeight,
                        ),
                      ),
                    );
                  },
                ),
              ),
            if (text)
              Positioned(
                left: 0,
                right: 0,
                bottom: ScreenAdapter.width(42),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Obx(()=>Text(
                      getTemperatureCenterData(),
                      style: TextStyle(
                          color: data?.data != null ? AppColors.temperatureList[0] : AppColors.Color999,
                          fontSize: ScreenAdapter.fontSize(24),
                          height: 33.6 / 24,
                          fontWeight: FontWeight.w500),
                    )),
                    Obx(()=>Text(
                      tabsController.unitSetting[3][1].value ? "℃" : "℉",
                      style: TextStyle(
                          color: data?.data != null ? AppColors.temperatureList[0] : AppColors.Color999,
                          fontSize: ScreenAdapter.fontSize(24),
                          height: 33.6 / 24,
                          fontWeight: FontWeight.w500),
                    ),),
                  ],
                ),
              )
          ],
        ),
      ),
    );
  }

  int getTemperaturePieIndex() {
    //判断有无数据
    bool show = data?.data != null; // 当 data 或 data.data 为 null 时，show 为 false
    //show == true 有数据
    //show == false 无数据

    //判断单位
    bool unit = tabsController.unitSetting[3][1].value;
    //unit == true 摄氏度
    //unit == false 华氏度

    int index = 0;
    //0是摄氏度无数据
    //1是摄氏度有数据
    //2是华氏度无数据
    //3是华氏度有数据
    if (unit == true && show == false) {
      return 0;
    } else if (unit == true && show == true) {
      return 1;
    } else if (unit == false && show == false) {
      return 2;
    } else {
      return 3;
    }
  }

  Widget getTemperaturePie() {
    // logger.d("获得的index${getTemperaturePieIndex()}");
    return Container(
      width: ScreenAdapter.width(width),
      height: ScreenAdapter.width(height),
      child: Image.asset(switchPie(getTemperaturePieIndex())),
    );
  }

  String getTemperatureCenterData() {
    //判断单位
    bool unit = tabsController.unitSetting[3][1].value;
    if (data != null) {
      if (data!.data != null) {
        
        if (unit == true) {
          return data!.data!.toStringAsFixed(1); // 使用 ?? 操作符确保返回类型始终是 String
        } else {
          return (data!.data! * 1.8 + 32).toStringAsFixed(1);
        }
      }
    }
    return "--";
  }
}
