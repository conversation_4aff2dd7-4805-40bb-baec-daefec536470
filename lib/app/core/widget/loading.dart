/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-02-28 16:57:04
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-13 16:22:15
 * @FilePath: /RPM-APP/lib/app/core/widget/loading.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/material.dart';

import '/app/core/values/app_colors.dart';
import '/app/core/values/app_values.dart';
import '/app/core/widget/elevated_container.dart';

class Loading extends StatelessWidget {
  final String myText;
  Loading({Key? key, this.myText = ''}) : super(key: key); // 默认文本为“加载中...”

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ElevatedContainer(
          padding: EdgeInsets.all(AppValues.margin),
          // decoration: BoxDecoration(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                color: AppColors.colorPrimary,
              ),
              Container(
                margin: EdgeInsets.only(top: ScreenAdapter.height(8)),
                child: Text(
                myText,
                style: normalF16H22C666,
              ),
              )
            ],
          )),
    );
  
  }
}
