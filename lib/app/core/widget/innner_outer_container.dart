/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-09-19 12:01:26
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-09-19 12:01:28
 * @FilePath: /RPM-APP-MASTER/lib/app/core/widget/innner_outer_container.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:flutter/widgets.dart';

class GradientBorderPainter extends CustomPainter {
  List<Color> paintColor;
  double width;

  GradientBorderPainter({required this.paintColor, required this.width});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: paintColor,
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.stroke
      ..strokeWidth = ScreenAdapter.width(width);

    final double radius = size.width / 2;
    canvas.drawCircle(Offset(radius, radius), radius - 2, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}