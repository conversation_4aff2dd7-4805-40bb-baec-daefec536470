/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-11-05 14:19:31
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2024-11-21 14:59:53
 * @FilePath: /rpmappmaster/lib/app/core/widget/custom_bluetooth_icon.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

class CustomBluetoothIcon extends StatelessWidget with BaseWidgetMixin {
  CustomBluetoothIcon({super.key});

  // final TabsController controller = Get.find();
  final BluetoothController controller = Get.find();

  @override
  Widget body(BuildContext context) {
    return //蓝牙图标
        Positioned(
      top: ScreenAdapter.height(12),
      right: ScreenAdapter.width(16),
      child: InkWell(
        onTap: () {
          controller.showBluetoothDialog();
        },
        child: SizedBox(
          // width: ScreenAdapter.width(55),
          height: ScreenAdapter.height(41),
          
          child: Column(
            children: [
              SizedBox(
                width: ScreenAdapter.width(24),
                height: ScreenAdapter.height(24),
                child: Obx(() => SvgPicture.asset(
                      controller.isConnected.value
                          ? Assets.images.bluetoothOn
                          : Assets.images.bluetoothNo,
                      width: ScreenAdapter.width(24),
                      height: ScreenAdapter.height(24),
                    )),
              ),
              Expanded(child: Container()),
              SizedBox(
                // width: ScreenAdapter.width(55),
                height: ScreenAdapter.height(14),
                child: Obx(() => Text(
                      controller.isConnected.value
                          ? T.bluetoothIsConnected.tr
                          : T.bluetoothNotConnected.tr,
                      // "显示文字",
                      style: TextStyle(
                          fontSize: ScreenAdapter.fontSize(10),
                          fontWeight: FontWeight.w400,
                          height: ScreenAdapter.height(14.0 / 10),
                          textBaseline: TextBaseline.alphabetic,
                          color: controller.isConnected.value
                              ? AppColors.lightBlue
                              : AppColors.Color666),
                    )),
              )
            ],
          ),
        ),
      ),
    );
  }
}
