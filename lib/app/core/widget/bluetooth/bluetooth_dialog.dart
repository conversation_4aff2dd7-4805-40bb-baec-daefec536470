import 'dart:core';
import 'dart:ffi';

import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/string_utils.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/modules/tabs/controllers/tabs_controller.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

class BluetoothDialog extends StatelessWidget with BaseWidgetMixin {
  final VoidCallback onSettingsTap;
  final BluetoothController controller = Get.find();

  BluetoothDialog({
    required this.onSettingsTap,
  });

  @override
  Widget body(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 当点击空白区域时关闭对话框
        Navigator.pop(context);
      },
      child: Scaffold(
        backgroundColor: Colors.black.withOpacity(0.5), // 背景半透明
        body: GestureDetector(
          onTap: () {}, // 阻止点击子组件关闭
          child: Center(
            child: Container(
              width: ScreenAdapter.width(286),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _header(),
                  _body(),
                  _footer(
                    onSettingsTap: onSettingsTap,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  _header() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          width: ScreenAdapter.width(34),
          height: ScreenAdapter.height(24),
          // color: Colors.blue,
          margin: EdgeInsets.only(
              top: ScreenAdapter.height(20), bottom: ScreenAdapter.height(8)),
          child: Image.asset(
            Assets.images.bluetoothFoundOpen.path,
          ),
        ),
        Text(
          capitalizeEachWord(T.wordsBluetooth.tr),
          style: normalF16H22C666.copyWith(
              fontWeight: FontWeight.w500, color: AppColors.lightBlue),
        )
      ],
    );
  }

  _body() {
    
    return Obx(() {
      return Container(
        width: ScreenAdapter.width(286),
        height: ScreenAdapter.height(212),
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    color: AppColors.greyF9Color,
                    width: ScreenAdapter.height(1)),
                top: BorderSide(
                    color: AppColors.greyF9Color,
                    width: ScreenAdapter.height(1)))),
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: controller.scanResults.length,
          padding: EdgeInsets.symmetric(
              horizontal: ScreenAdapter.width(16),
              vertical: ScreenAdapter.height(12)),
          itemBuilder: (context, index) {
            final isLast = index == controller.scanResults.length - 1;
            if (index >= controller.scanResults.length) {
              return _buildEmptyDeviceItem(
                isLast: isLast,
              );
            } else {
              final device = controller.scanResults[index];
              return _buildDeviceItem(
                device: device,
                index: index,
                isLast: isLast,
              );
            }
          },
        ),
      );
    });
  }
}

class _footer extends StatelessWidget with BaseWidgetMixin {
  final VoidCallback onSettingsTap;
  _footer({
    required this.onSettingsTap,
  });

  @override
  Widget body(BuildContext context) {
    return Container(
      width: ScreenAdapter.width(286),
      height: ScreenAdapter.height(53),
      child: InkWell(
        onTap: () {
          logger.d("点击了");
          onSettingsTap();
        },
        child: Center(
          child: Text(
            T.bluetoothSetting.tr,
            style: normalF16H22C666.copyWith(
                fontWeight: FontWeight.w500, color: AppColors.lightBlue),
          ),
        ),
      ),
    );
  }
}

class _buildDeviceItem extends StatelessWidget with BaseWidgetMixin {
  final MyBluetoothDevice device;
  final int index;
  final bool isLast;
  final BluetoothController controller = Get.find();

  _buildDeviceItem({
    required this.device,
    required this.index,
    required this.isLast,
    Key? key,
  }) : super(key: key);

  @override
  Widget body(BuildContext context) {
    return InkWell(
      onTap: () async {
        logger.d("点击链接或断开绑定");
        if (controller.lastConnectedDevice != null &&
            controller.lastConnectedDevice!.remoteId.str ==
                device.remoteId.str &&
                  controller.isConnected.value) {
          await controller.aizoDisconnect();
        } else {
          await controller.aizoConnect(device);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade300),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              device.platformName == "" ? "aiRing" : device.platformName,
              style: normalF14H19C333.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              "${T.bluetoothMacAddress.tr}: ${device.remoteId.str}",
              style: normalF12H17C999,
            ),
            Obx(() {
              if (controller.lastConnectedDevice != null &&
                  controller.lastConnectedDevice!.remoteId.str ==
                      device.remoteId.str &&
                  controller.isConnected.value) {
                return Text(
                  T.bluetoothIsConnected.tr,
                  style: normalF12H17C999,
                );
              } else {
                return Text(
                  T.bluetoothNotConnected.tr,
                  style: normalF12H17C999,
                );
              }
            }),
          ],
        ),
      ),
    );
  }
}

class _buildEmptyDeviceItem extends StatelessWidget with BaseWidgetMixin {
  final bool isLast; // 这是一个布尔值，表示是否是最后一个项

  _buildEmptyDeviceItem({
    required this.isLast,
    Key? key,
  }) : super(key: key);

  @override
  Widget body(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: isLast
              ? BorderSide.none
              : BorderSide(
                  color: AppColors.greyF6Color, width: ScreenAdapter.height(1)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            ' ',
            style: normalF14H19C333.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(" ", style: normalF12H17C999),
        ],
      ),
    );
  }
}
