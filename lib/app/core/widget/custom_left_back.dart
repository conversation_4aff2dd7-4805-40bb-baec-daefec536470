/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-08-28 15:00:57
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-19 15:55:01
 * @FilePath: /RPM-APP-MASTER/lib/app/core/widget/custom_left_back.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class CustomLeftBack extends StatelessWidget with BaseWidgetMixin {
  dynamic arguments;
  CustomLeftBack({super.key,this.arguments});

  @override
  Widget body(BuildContext context) {
    return InkWell(
      onTap: () {
        logger.d("左侧回退点击");
        Get.back(result: arguments);
      },
      splashColor: Colors.transparent, // 去除水波纹效果
      highlightColor: Colors.transparent, // 去除高亮效果
      child: Row(
        children: [
          Container(
            width: ScreenAdapter.width(4),
            height: ScreenAdapter.height(8),
            margin: EdgeInsets.only(right: ScreenAdapter.width(8)),
            child: Center(
              // 使用 Center 小部件使 SVG 图片居中
              child: SvgPicture.asset(
                Assets.images.backIcon,
                fit: BoxFit.contain, // 确保图片按比例显示
                color: AppColors.Color999,
                // color: Colors.red,
              ),
            ),
          ),
          Text(
            T.wordsBack,
            style: TextStyle(
                color: AppColors.Color999,
                fontWeight: FontWeight.w400,
                fontSize: ScreenAdapter.fontSize(16),
                height: ScreenAdapter.fontSize(22.4 / 16)),
          )
        ],
      ),
    );
  }
}
