// import 'package:aiCare/app/core/base/widget/base_widget_mixin.dart';
// import 'package:aiCare/app/core/model/position_top_left.dart';
// import 'package:aiCare/app/core/values/app_colors.dart';
// import 'package:aiCare/app/core/values/text_styles.dart';
// import 'package:aiCare/app/modules/blood_pressure/model/pressure_data.dart';
// import 'package:aiCare/app/services/l10nService.dart';
// import 'package:aiCare/app/services/screenAdapter.dart';
// import 'package:aiCare/gen/assets.gen.dart';
// import 'package:flutter/widgets.dart';

// class CustomPressureChart extends StatelessWidget with BaseWidgetMixin {
//   List<PositionTopLeft> posList;
//   final List<double> widthList;
//   final PressureData? data;
//   final double textHeight;
//   final TextStyle? titleTextStyle;
//   final TextStyle textTextStyle;
//   l10nService l10n = l10nService();
//   bool? titleShow;

//   List<String> innerCircle = [
//     Assets.images.pressureInnerLow.path,
//     Assets.images.pressureInnerNormal.path,
//     Assets.images.pressureInnerMild.path,
//     Assets.images.pressureInnerModerate.path,
//     Assets.images.pressureInnerSerious.path,
//     Assets.images.pressureInnerNothing.path,
//   ];

//   List<String> outterCircle = [
//     Assets.images.pressureOuterLow.path,
//     Assets.images.pressureOuterNormal.path,
//     Assets.images.pressureOuterMild.path,
//     Assets.images.pressureOuterModerate.path,
//     Assets.images.pressureOuterSerious.path,
//     Assets.images.pressureOuterNothing.path,
//   ];

//   CustomPressureChart(
//       {super.key,
//       this.data,
//       required this.posList,
//       required this.widthList,
//       required this.textHeight,
//       this.titleTextStyle,
//       required this.textTextStyle,
//       this.titleShow = true});

//   String switchText(index) {
//     switch (index) {
//       case 0:
//         return appLocalization.wordsLow;
//       case 1:
//         return appLocalization.wordsNormal;
//       case 2:
//         return appLocalization.wordsMild;
//       case 3:
//         return appLocalization.wordsModerate;
//       case 4:
//         return appLocalization.wordsSerious;
//       default:
//         return "--";
//     }
//   }

//   @override
//   Widget body(BuildContext context) {
//     int index;
//     if (data != null) {
//       index = data!.getTotalLevel();
//     } else {
//       index = 5;
//     }
//     // logger.e("获得的index${index}");
//     if (posList[4].left == ScreenAdapter.width(151.5) && l10n.isEnglish()) {
//       posList[4].left = ScreenAdapter.width(134.2);
//     }

//     // print(index);

//     return IgnorePointer(
//       child: Stack(
//         alignment: Alignment.center,
//         children: [
//           //外圈
//           Positioned(
//               top: posList[0].top,
//               left: posList[0].left,
//               child: Container(
//                 child: Image.asset(
//                   outterCircle[index],
//                   width: widthList[0],
//                   height: widthList[0],
//                   fit: BoxFit.fitWidth,
//                 ),
//               )),
//           //虚线
//           Positioned(
//             top: posList[1].top,
//             left: posList[1].left,
//             child: Container(
//               child: Image.asset(
//                 Assets.images.pressureDashed.path,
//                 width: widthList[1],
//                 height: widthList[1],
//                 fit: BoxFit.fitWidth,
//               ),
//             ),
//           ),
//           // 内圈 bug to be fixed
//           Positioned(
//             top: posList[2].top,
//             left: posList[2].left,
//             child: Container(
//               child: Image.asset(
//                 innerCircle[index],
//                 width: widthList[2],
//                 // height: widthList[2],
//                 fit: BoxFit.fitHeight,
//               ),
//             ),
//           ),
//           //显示
//           Positioned(
//             top: posList[3].top,
//             left: posList[3].left,
//             child: Container(
//                 // margin: EdgeInsets.only(top: posList[3].top),
//                 child: Center(
//               child: Container(
//                 width: widthList[3],
//                 height: widthList[3] / 2,
//                 decoration: BoxDecoration(
//                     color: AppColors.pressureBgList[index],
//                     borderRadius: BorderRadius.only(
//                       topLeft:
//                           Radius.circular(widthList[3] / 2), // 调整这个值以达到你想要的圆形效果
//                       topRight:
//                           Radius.circular(widthList[3] / 2), // 调整这个值以达到你想要的圆形效果
//                     ),
//                     border: Border.all(
//                         color: AppColors.pressureBdList[index],
//                         width: ScreenAdapter.width(1))),
//                 child: SizedBox(
//                   height: ScreenAdapter.height(45),
//                   child: Center(
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         titleShow!
//                             ? Text(appLocalization.wordsComprehensiveLevel,
//                                 style: titleTextStyle)
//                             : SizedBox(
//                                 height: 0,
//                               ),
//                         Text(switchText(index),
//                             style: textTextStyle.copyWith(
//                               color: AppColors.pressureTextList[index],
//                             ))
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             )),
//           ),
//         ],
//       ),
//     );
//   }
// }
