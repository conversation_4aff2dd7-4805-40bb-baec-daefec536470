import 'package:aiCare/app/core/base/controller/base_controller.dart';
import 'package:aiCare/app/core/base/controller/record_search_controller.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/values/text_styles.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:get/get.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

class RecordSearch extends StatefulWidget {
  RecordSearchController controller;
  RecordSearch({super.key, required this.controller});

  @override
  State<RecordSearch> createState() => _RecordSearchState();
}

class _RecordSearchState extends State<RecordSearch> {
  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
          width: ScreenAdapter.width(375),
          height: widget.controller.searchBool.value
              ? ScreenAdapter.height(368)
              : ScreenAdapter.height(64),
          padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
          decoration: BoxDecoration(
            color: AppColors.colorWhite,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(ScreenAdapter.width(12)),
              bottomRight: Radius.circular(ScreenAdapter.width(12)),
            ), // 只设置底部两个角为圆角
          ),
          child: Column(
            children: [
              InkWell(
                  onTap: widget.controller.doOpen,
                  child: Container(
                    width: double.infinity,
                    height: ScreenAdapter.height(32),
                    margin: EdgeInsets.symmetric(
                        vertical: ScreenAdapter.height(16)),
                    // color: Colors.red,
                    child: Row(
                      children: [
                        //搜索框
                        Expanded(
                          child: Container(
                            height: ScreenAdapter.height(36),
                            padding:
                                EdgeInsets.only(left: ScreenAdapter.width(5)),
                            decoration: BoxDecoration(
                                border: Border.all(
                                  color: !widget.controller.searchBool.value
                                      ? AppColors.searchBdColor
                                      : AppColors.lightBlue, // 边框颜色，使用16进制表示
                                  width: ScreenAdapter.width(1), // 边框宽度
                                  style: BorderStyle.solid, // 边框样式
                                ),
                                borderRadius: BorderRadius.circular(
                                    ScreenAdapter.width(26))),
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  Assets.images.search,
                                  semanticsLabel: 'My Icon',
                                  width: ScreenAdapter.width(18),
                                  height: ScreenAdapter.height(18),
                                ),
                                SizedBox(
                                  width: ScreenAdapter.width(3),
                                ),
                                Text(
                                  T.commonTimeValue.tr,
                                  style: TextStyle(
                                      fontSize: ScreenAdapter.fontSize(12),
                                      fontWeight: FontWeight.w400,
                                      height: ScreenAdapter.fontSize(16.8 / 12),
                                      color: AppColors.Color999),
                                  textAlign: TextAlign.start,
                                )
                              ],
                            ),
                          ),
                        ),
                        SizedBox(
                          width: ScreenAdapter.width(4),
                        ),
                        //筛选 btn
                        SizedBox(
                          child: SvgPicture.asset(
                            Assets.images.filter,
                            semanticsLabel: 'My Icon',
                            width: ScreenAdapter.width(24),
                            height: ScreenAdapter.height(24),
                            color: widget.controller.searchBool.value
                                ? AppColors.lightBlue
                                : null,
                          ),
                        )
                      ],
                    ),
                  )),
              widget.controller.searchBool.value
                  ? SizedBox(
                      width: double.infinity,
                      height: ScreenAdapter.height(278),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _SearchNormalItem(
                            key: ValueKey('severity_search'),
                            title: 
                                T.commonNumericalRange.tr,
                            list: widget.controller.severityList.value,
                            selectFunction: 0,
                            padding: EdgeInsets.symmetric(
                                horizontal: ScreenAdapter.width(10),
                                vertical: ScreenAdapter.height(4)),
                            controller: widget.controller,
                          ),
                          _SearchNormalItem(
                            key: ValueKey('date_search'),
                            title:
                                T.commonScreeningTime.tr,
                            list: widget.controller.dateList.value,
                            selectFunction: 1,
                            padding: EdgeInsets.symmetric(
                                horizontal: ScreenAdapter.width(8),
                                vertical: ScreenAdapter.height(4)),
                            controller: widget.controller,
                          ),
                          _SearchDateItem(
                            key: ValueKey('date_picker'),
                            controller: widget.controller,
                          ),
                          Expanded(child: SizedBox()),
                          _SearchButton(controller: widget.controller,),
                        ],
                      ),
                    )
                  : SizedBox()
            ],
          ),
        ));
  }
}

class _SearchNormalItem extends StatelessWidget {
  final String title;
  final List<String> list;
  final EdgeInsetsGeometry padding;
  RecordSearchController controller;

  final int selectFunction;

  _SearchNormalItem(
      {super.key,
      required this.title,
      required this.list,
      required this.padding,
      required this.selectFunction,
      required this.controller});

  @override
  Widget build(BuildContext context) {
    return  Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: ScreenAdapter.fontSize(12),
              height: ScreenAdapter.fontSize(16.8 / 12),
              color: AppColors.Color333),
        ),
        SizedBox(
          height: ScreenAdapter.height(12),
        ),
        Wrap(
          spacing: ScreenAdapter.width(8),
          runSpacing: ScreenAdapter.height(8),
          children: list.asMap().entries.map((entry) {
            int index = entry.key; // 获取索引
            String item = entry.value; // 获取元素
            return item == ""? SizedBox():InkWell(
                onTap: () {
                  if (selectFunction == 0) {
                    controller.doSelectSeverity(index);
                  } else {
                    controller.doSelectDate(index);
                  }
                },
                child: Obx(
                  () => Container(
                    height: ScreenAdapter.height(25),
                    padding: padding,
                    decoration: BoxDecoration(
                      color: selectFunction == 0
                          ? controller.selectSeverity.value == index
                              ? AppColors.lightBlue
                              : AppColors.searchButton
                          : controller.selectFuzzyTime.value == index
                              ? AppColors.lightBlue
                              : AppColors.searchButton,
                      borderRadius:
                          BorderRadius.circular(ScreenAdapter.width(24)),
                    ),
                    child: Text(
                      item,
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: ScreenAdapter.fontSize(12),
                        height: ScreenAdapter.fontSize(16.8 / 12),
                        color: selectFunction == 0
                            ? controller.selectSeverity.value == index
                                ? AppColors.colorWhite
                                : AppColors.Color333
                            : controller.selectFuzzyTime.value == index
                                ? AppColors.colorWhite
                                : AppColors.Color333,
                      ),
                    ),
                  ),
                ));
          
          }).toList(),
        ),
        SizedBox(
          height: ScreenAdapter.height(24),
        )
      ],
    );
  }
}

class _SearchDateItem extends StatelessWidget {
  RecordSearchController controller;
  _SearchDateItem({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: () {
          controller.selectDateRange(context);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              T.commonTimeInterval.tr,
              style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: ScreenAdapter.fontSize(12),
                  height: ScreenAdapter.fontSize(16.8 / 12),
                  color: AppColors.Color333),
            ),
            SizedBox(
              height: ScreenAdapter.height(12),
            ),
            Row(
              children: [
                Expanded(
                    child: Container(
                        height: ScreenAdapter.height(32),
                        padding: EdgeInsets.symmetric(
                            vertical: ScreenAdapter.height(7),
                            horizontal: ScreenAdapter.width(22)),
                        decoration: BoxDecoration(
                          color: AppColors.searchButton,
                          borderRadius:
                              BorderRadius.circular(ScreenAdapter.width(24)),
                        ),
                        child: Obx(
                          () => Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                controller.firstData.value[0],
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: ScreenAdapter.fontSize(12),
                                    height: ScreenAdapter.fontSize(16.8 / 12),
                                    color: controller.selectExactShow.value
                                        ? AppColors.Color333
                                        : AppColors.Color999),
                              ),
                              Container(
                                margin: EdgeInsets.symmetric(
                                    horizontal: ScreenAdapter.width(4)),
                                child: Text("/", style: normalF12H17C999),
                              ),
                              Text(
                                controller.firstData.value[1],
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: ScreenAdapter.fontSize(12),
                                    height: ScreenAdapter.fontSize(16.8 / 12),
                                    color: controller.selectExactShow.value
                                        ? AppColors.Color333
                                        : AppColors.Color999),
                              ),
                              Container(
                                margin: EdgeInsets.symmetric(
                                    horizontal: ScreenAdapter.width(4)),
                                child: Text("/", style: normalF12H17C999),
                              ),
                              Text(
                                controller.firstData.value[2],
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: ScreenAdapter.fontSize(12),
                                    height: ScreenAdapter.fontSize(16.8 / 12),
                                    color: controller.selectExactShow.value
                                        ? AppColors.Color333
                                        : AppColors.Color999),
                              ),
                            ],
                          ),
                        ))),
                Container(
                  width: ScreenAdapter.width(13),
                  height: ScreenAdapter.height(1),
                  color: AppColors.searchBgColor,
                  margin:
                      EdgeInsets.symmetric(horizontal: ScreenAdapter.width(3)),
                ),
                Expanded(
                    child: Container(
                        height: ScreenAdapter.height(32),
                        padding: EdgeInsets.symmetric(
                            vertical: ScreenAdapter.height(7),
                            horizontal: ScreenAdapter.width(22)),
                        decoration: BoxDecoration(
                          color: AppColors.searchButton,
                          borderRadius:
                              BorderRadius.circular(ScreenAdapter.width(24)),
                        ),
                        child: Obx(
                          () => Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                controller.lateData.value[0],
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: ScreenAdapter.fontSize(12),
                                    height: ScreenAdapter.fontSize(16.8 / 12),
                                    color: controller.selectExactShow.value
                                        ? AppColors.Color333
                                        : AppColors.Color999),
                              ),
                              Container(
                                margin: EdgeInsets.symmetric(
                                    horizontal: ScreenAdapter.width(4)),
                                child: Text("/", style: normalF12H17C999),
                              ),
                              Text(
                                controller.lateData.value[1],
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: ScreenAdapter.fontSize(12),
                                    height: ScreenAdapter.fontSize(16.8 / 12),
                                    color: controller.selectExactShow.value
                                        ? AppColors.Color333
                                        : AppColors.Color999),
                              ),
                              Container(
                                margin: EdgeInsets.symmetric(
                                    horizontal: ScreenAdapter.width(4)),
                                child: Text("/", style: normalF12H17C999),
                              ),
                              Text(
                                controller.lateData.value[2],
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: ScreenAdapter.fontSize(12),
                                    height: ScreenAdapter.fontSize(16.8 / 12),
                                    color: controller.selectExactShow.value
                                        ? AppColors.Color333
                                        : AppColors.Color999),
                              ),
                            ],
                          ),
                        ))),
              ],
            ),
          ],
        ));
  }
}

class _SearchButton extends StatelessWidget {
  final RecordSearchController controller;
   _SearchButton({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
        return Row(
      children: [
        InkWell(
          onTap: controller.doClose,
          child: Container(
              width: ScreenAdapter.width(138),
              height: ScreenAdapter.height(29),
              decoration: BoxDecoration(
                  color: AppColors.searchButton,
                  border: Border.all(
                    color: AppColors.lightBlue, // 边框颜色，使用16进制表示
                    width: ScreenAdapter.width(1), // 边框宽度
                    style: BorderStyle.solid, // 边框样式
                  ),
                  borderRadius: BorderRadius.circular(ScreenAdapter.width(24))),
              // padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(52),vertical: ScreenAdapter.height(6)),
              child: Center(
                child: Text(
                  T.wordsCancel.tr,
                  style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(12),
                      height: ScreenAdapter.fontSize(16.8 / 12),
                      color: AppColors.lightBlue),
                ),
              )),
        ),
        Expanded(child: SizedBox()),
        InkWell(
          onTap: controller.doFinish,
          child: Container(
              width: ScreenAdapter.width(196),
              height: ScreenAdapter.height(29),
              decoration: BoxDecoration(
                  color: AppColors.lightBlue,
                  border: Border.all(
                    color: AppColors.lightBlue, // 边框颜色，使用16进制表示
                    width: ScreenAdapter.width(1), // 边框宽度
                    style: BorderStyle.solid, // 边框样式
                  ),
                  borderRadius: BorderRadius.circular(ScreenAdapter.width(24))),
              // padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(52),vertical: ScreenAdapter.height(6)),
              child: Center(
                child: Text(
                  T.wordsSave.tr,
                  style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(12),
                      height: ScreenAdapter.fontSize(16.8 / 12),
                      color: AppColors.colorWhite),
                ),
              )),
        )
      ],
    );
  
  }
}