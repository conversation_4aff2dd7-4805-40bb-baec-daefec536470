import 'dart:async';
import 'dart:math' as math;

import 'package:aiCare/app/core/values/app_colors.dart';
import 'package:aiCare/app/core/widget/innner_outer_container.dart';
import 'package:aiCare/app/services/screenAdapter.dart';
import 'package:aiCare/gen/assets.gen.dart';
import 'package:fl_chart/fl_chart.dart';

import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

class CustomLiquid extends StatefulWidget {
  final double amplitude; // 中心百分比数据
  final double parentWidth; // 外层宽高
  final double childWidth; // 内层宽高
  final TextStyle dataStyle; // 中心百分比样式
  final double parentBdWidth; // 外层边框宽度

  CustomLiquid({
    super.key,
    required this.amplitude,
    required this.parentWidth,
    required this.childWidth,
    required this.dataStyle,
    required this.parentBdWidth,
  });

  final Color bdColor = AppColors.lightBlue;

  @override
  State<CustomLiquid> createState() => _CustomLiquidState();
}

class _CustomLiquidState extends State<CustomLiquid> with SingleTickerProviderStateMixin {
  final limitCount = 100;
  final sinPoints = <FlSpot>[];
  double xValue = 0;
  double step = 0.25;
  Timer? timer; // 将 timer 设置为 nullable 类型

  List<List<Color>> waveList = [
    [Color(0xFF30FF1E), Color(0xFF1ED90D)],
    [Color(0xFFFFB775), Color(0xFFFEBF1E)],
    [Color(0xFFB174FF), Color(0xFFE5D0FF)],
    [Color(0xFFFF7575), Color(0xFFFE473B)],
  ];

  @override
  void initState() {
    super.initState();
    _initializeWaveData();
  }

  @override
  void didUpdateWidget(CustomLiquid oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 检测 amplitude 是否发生变化，如果变化了，重新初始化波浪数据
    if (widget.amplitude != oldWidget.amplitude) {
      _initializeWaveData();
    }
  }

  void _initializeWaveData() {
    // 清空之前的波浪数据
    sinPoints.clear();
    xValue = 0;

    // 生成新的波浪数据
    for (int i = 0; i < limitCount; i++) {
      // sinPoints.add(FlSpot(xValue, widget.amplitude / 100 * math.sin(xValue)));
      // sinPoints.add(FlSpot(xValue, widget.amplitude / 100 + 0.05 * math.sin(xValue)));
      sinPoints.add(FlSpot(xValue, widget.amplitude / 100 + 0.05 * math.sin(xValue)));
      xValue += step;
    }

    // 取消旧的定时器（如果存在）
    timer?.cancel();
    // 设置新的定时器
    timer = Timer.periodic(const Duration(milliseconds: 40), (timer) {
      setState(() {
        // 保证波浪点的数量不会超过 limitCount
        while (sinPoints.length > limitCount) {
          sinPoints.removeAt(0);  // 移除最旧的点
        }

        // 添加新的波浪数据点
        // sinPoints.add(FlSpot(xValue, widget.amplitude / 100 * math.sin(xValue)));
        sinPoints.add(FlSpot(xValue, widget.amplitude / 100 + 0.05 * math.sin(xValue)));
        xValue += step;  // 让 x 值不断增大，形成波浪效果
      });
    });
  }

  @override
  void dispose() {
    timer?.cancel(); // 在销毁时取消定时器
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Stack(
        alignment: Alignment.center, // 中心对齐
        children: [
          Container(
            width: ScreenAdapter.width(widget.parentWidth),
            height: ScreenAdapter.width(widget.parentWidth),
            decoration: const BoxDecoration(
              color: Colors.transparent,
              shape: BoxShape.circle,
            ),
            child: widget.amplitude != 0
                ? CustomPaint(
                    painter: GradientBorderPainter(
                      paintColor: waveList[switchIndex(widget.amplitude / 100)],
                      width: widget.parentBdWidth,
                    ),
                    child: Center(
                      child: Container(
                        width: ScreenAdapter.width(widget.childWidth),
                        height: ScreenAdapter.width(widget.childWidth),
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: ClipOval(
                          child: LineChart(
                            LineChartData(
                              minY: -1,
                              maxY: 1,
                              minX: sinPoints.first.x + 0.2,
                              maxX: sinPoints.last.x,
                              lineTouchData: const LineTouchData(enabled: false),
                              clipData: const FlClipData.all(),
                              gridData: const FlGridData(show: true, drawVerticalLine: false,drawHorizontalLine: false),
                              borderData: FlBorderData(show: false),
                              lineBarsData: [
                                sinLine(sinPoints),
                              ],
                              titlesData: const FlTitlesData(show: false),
                            ),
                          ),
                        ),
                      ),
                    ),
                  )
                : Image.asset(Assets.images.noneCircle151.path),
          ),
          Text(
            '${(widget.amplitude == 0 ? "--" : widget.amplitude.toInt().toString() + "%")}', // 动态显示百分比
            style: widget.amplitude == 0
                ? widget.dataStyle.copyWith(color: AppColors.Color999)
                : widget.dataStyle,
          ),
        ],
      ),
    );
  }

  LineChartBarData sinLine(List<FlSpot> points) {
    return LineChartBarData(
      spots: points,
      dotData: const FlDotData(show: false),
      color: Colors.transparent,
      barWidth: ScreenAdapter.width(1),
      isCurved: false,
      isStrokeCapRound: true,
      belowBarData: BarAreaData(
        show: true,
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: waveList[switchIndex(widget.amplitude / 100)],
        ),
      ),
    );
  }

  int switchIndex(double data) {
    if (data >= 0.95) return 0;
    else if (data >= 0.90) return 1;
    else if (data >= 0.80) return 2;
    else return 3;
  }
}
