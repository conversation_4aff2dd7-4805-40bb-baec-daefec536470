import 'package:flutter/material.dart';

class CustomShadowContainer extends StatelessWidget {
  final double width;
  final double height;
  final EdgeInsets padding;
  final Color color;
  final BorderRadius borderRadius;
  final List<BoxShadow> boxShadow;
  final Widget child;

  CustomShadowContainer({
    required this.width,
    required this.height,
    required this.padding,
    required this.color,
    required this.borderRadius,
    required this.boxShadow,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      decoration: BoxDecoration(
        color: color,
        borderRadius: borderRadius,
        boxShadow: boxShadow,
      ),
      child: Stack(
        children: [
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: borderRadius,
                boxShadow: [
                  BoxShadow(
                    color: Color(0x73FFFFFF),
                    offset: Offset(0, 2),
                    blurRadius: 4,
                    spreadRadius: 0,
                    blurStyle: BlurStyle.inner, // 使用 blurStyle 来创建内嵌阴影
                  ),
                  BoxShadow(
                    color: Color(0x73FFFFFF),
                    offset: Offset(0, -2),
                    blurRadius: 4,
                    spreadRadius: 0,
                    blurStyle: BlurStyle.inner, // 使用 blurStyle 来创建内嵌阴影
                  ),
                ],
              ),
            ),
          ),
          Center(child: child),
        ],
      ),
    );
  }
}