import 'package:aiCare/app/services/screenAdapter.dart';

import 'package:flutter/material.dart';
 
class TestOne extends StatefulWidget {
  const TestOne({Key? key}) : super(key: key);
 
  @override
  State<TestOne> createState() => _TestOneState();
}
 
class _TestOneState extends State<TestOne> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          height: ScreenAdapter.height(300),
          width: ScreenAdapter.height(600),
          padding: EdgeInsets.only(top: 30, left: 10, right: 10),
          child: Container(
            padding: EdgeInsets.all(10),
            child: Container(
              decoration: BoxDecoration(
                // color: Colors.grey,
                color: Color(0x759A4105),
                borderRadius: BorderRadius.all(
                  Radius.circular(12),
                ),
              ),
              padding: EdgeInsets.all(10),
              child: Container(
                padding: EdgeInsets.zero,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white,
                      blurRadius: 10,
                      spreadRadius: 10,
                    ),
                  ],
                ),
                width: double.infinity,
                height: 272,
                child: Center(
                  child: Text("内部阴影效果"),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}