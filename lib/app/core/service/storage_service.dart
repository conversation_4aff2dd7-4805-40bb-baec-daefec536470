/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-03-10 14:38:57
 * @FilePath: /rpmappmaster/lib/app/services/sercureStorageService.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:convert';
import 'dart:typed_data';

import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/core/enum/region_unit.dart';
import 'package:aiCare/app/core/enum/temperature_unit.dart';
import 'package:aiCare/app/core/utils/logger_singleton.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/data/model/user_model.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:mmkv/mmkv.dart';

class SecureStorageService {
  /// 单例实例
  static final SecureStorageService instance = SecureStorageService._internal();

  /// 工厂构造函数，返回单例实例
  factory SecureStorageService() => instance;

  /// MMKV 实例
  late MMKV _mmkv;

  final logger = LoggerSingleton.getInstance();

  /// 私有构造函数，初始化 MMKV
  SecureStorageService._internal() {
    MMKV.initialize(); // 确保 MMKV 已初始化
    _mmkv = MMKV.defaultMMKV();
  }

  //------------------------------- 数据类型封装 -------------------------------

  /// 存储布尔值
  ///
  /// [key] 存储键
  /// [value] 要存储的布尔值
  void setBool(String key, bool value) {
    _mmkv.encodeBool(key, value);
  }

  /// 获取布尔值
  ///
  /// [key] 存储键
  /// 返回存储的布尔值，如果不存在则返回 null
  bool? getBool(String key) {
    return _mmkv.decodeBool(key);
  }

  /// 存储32位整数
  ///
  /// [key] 存储键
  /// [value] 要存储的32位整数
  void setInt32(String key, int value) {
    _mmkv.encodeInt32(key, value);
  }

  /// 获取32位整数
  ///
  /// [key] 存储键
  /// 返回存储的32位整数，如果不存在则返回 null
  int? getInt32(String key) {
    return _mmkv.decodeInt32(key);
  }

  /// 存储64位整数
  ///
  /// [key] 存储键
  /// [value] 要存储的64位整数
  void setInt(String key, int value) {
    _mmkv.encodeInt(key, value);
  }

  /// 获取64位整数
  ///
  /// [key] 存储键
  /// 返回存储的64位整数，如果不存在则返回 null
  int? getInt(String key) {
    return _mmkv.decodeInt(key);
  }

  /// 存储字符串
  ///
  /// [key] 存储键
  /// [value] 要存储的字符串
  void setString(String key, String value) {
    _mmkv.encodeString(key, value);
  }

  /// 获取字符串
  ///
  /// [key] 存储键
  /// 返回存储的字符串，如果不存在则返回 null
  String? getString(String key) {
    return _mmkv.decodeString(key);
  }

  /// 存储二进制数据
  ///
  /// [key] 存储键
  /// [bytes] 要存储的二进制数据
  ///
  /// 注意：使用完后会自动销毁内存缓冲区
  void setBytes(String key, Uint8List bytes) {
    final buffer = MMBuffer.fromList(bytes)!;
    _mmkv.encodeBytes(key, buffer);
    buffer.destroy(); // 必须手动销毁内存
  }

  /// 获取二进制数据
  ///
  /// [key] 存储键
  /// 返回存储的二进制数据，如果不存在则返回 null
  ///
  /// 注意：使用完后会自动销毁内存缓冲区
  Uint8List? getBytes(String key) {
    final buffer = _mmkv.decodeBytes(key);
    if (buffer == null) return null;
    final bytes = buffer.asList()!;
    buffer.destroy(); // 必须手动销毁内存
    return Uint8List.fromList(bytes);
  }

  //------------------------------- 通用操作 -------------------------------

  /// 删除指定键的数据
  ///
  /// [key] 要删除的存储键
  void delete(String key) {
    _mmkv.removeValue(key);
  }

  /// 删除所有存储的数据
  ///
  /// 注意：此操作会清空所有存储的数据，请谨慎使用
  void deleteAll() {
    final allKeys = _mmkv.allKeys;
    for (var key in allKeys) {
      _mmkv.removeValue(key);
    }
  }

  bool bluetoothIsConnecting() {
    return getBool(AppValues.bluetoothConnect) == true;
  }

  TemperatureUnit getTemperatureUnit() {
    final unitString = getString(AppValues.temperatureUnit);
    if (unitString == null) {
      setString(AppValues.temperatureUnit, TemperatureUnit.celsius.name);
      return TemperatureUnit.celsius;
    }

    // 将字符串转换为枚举类型
    try {
      return TemperatureUnit.values.firstWhere(
        (unit) => unit.name == unitString,
        orElse: () => TemperatureUnit.celsius,
      );
    } catch (e) {
      // 如果转换失败，返回默认值
      return TemperatureUnit.celsius;
    }
  }

  void setTemperatureUnit(TemperatureUnit value) {
    setString(AppValues.temperatureUnit, value.name);
  }

  bool haveHeartLineDays(String dateTime) {
    // 从存储中读取心率数据日期
    String? storedDate = getString(AppValues.heartRateLineDaysDate);
    return storedDate == dateTime;
  }

  bool haveHeartLineWeeks(String dateTime) {
    // 从存储中读取心率数据日期
    String? storedDate = getString(AppValues.heartRateLineWeeksDate);
    return storedDate == dateTime;
  }

  bool haveHeartLineMonths(String dateTime) {
    // 从存储中读取心率数据日期
    String? storedDate = getString(AppValues.heartRateLineMonthsDate);
    return storedDate == dateTime;
  }

  // 血氧
bool haveBloodOxygenLineDays(String dateTime) {
  String? storedDate = getString(AppValues.bloodOxygenLineDaysRefresh);
  return storedDate == dateTime;
}

bool haveBloodOxygenLineWeeks(String dateTime) {
  String? storedDate = getString(AppValues.bloodOxygenLineWeeksRefresh );
  return storedDate == dateTime;
}

bool haveBloodOxygenLineMonths(String dateTime) {
  String? storedDate = getString(AppValues.bloodOxygenLineMonthsRefresh);
  return storedDate == dateTime;
}

// 体温
bool haveTemperatureLineDays(String dateTime) {
  String? storedDate = getString(AppValues.bodyTemperatureLineDaysRefresh );
  return storedDate == dateTime;
}

bool haveTemperatureLineWeeks(String dateTime) {
  String? storedDate = getString(AppValues.bodyTemperatureLineWeeksRefresh);
  return storedDate == dateTime;
}

bool haveTemperatureLineMonths(String dateTime) {
  String? storedDate = getString(AppValues.bodyTemperatureLineMonthsRefresh);
  return storedDate == dateTime;
}

  UserModel? getUserInfo() {
    // 从存储中读取用户数据
    String? userData = getString(AppValues.userInfo);
    if (userData == null || userData.isEmpty) {
      return null;
    }
    return UserModel.fromJson(jsonDecode(userData));
  }

  void setUserInfo(UserModel user) {
    setString(AppValues.userInfo, jsonEncode(user.toJson()));
  }

  void setRegion(RegionUnit region) {
    setString(AppValues.apiRegionKey, region.name);
  }

  RegionUnit getRegion() {
    try {
      String? region = getString(AppValues.apiRegionKey);
      if (region == null || region.isEmpty) {
        logger.d("Region not found in storage, using default region");
        return RegionUnit.en; // 默认使用中文
      }

      // 尝试将存储的字符串转换为 RegionUnit
      for (var unit in RegionUnit.values) {
        if (unit.name == region) {
          return unit;
        }
      }

      // 如果找不到匹配的区域，记录错误并返回默认值
      logger.e("Invalid region value in storage: $region");
      return RegionUnit.en;
    } catch (e) {
      logger.e("Error getting region: $e");
      return RegionUnit.en; // 发生错误时返回默认值
    }
  }

  String? getIDToken() {
    return getString(AppValues.idToken);
  }

  void setIDToken(String token) {
    setString(AppValues.idToken, token);
  }

  String? getAccessToken() {
    return getString(AppValues.accessToken);
  }

  void setAccessToken(String token) {
    setString(AppValues.accessToken, token);
  }

  String? getID() {
    return getString(AppValues.id);
  }

  void setID(String id) {
    setString(AppValues.id, id);
  }

  bool getAizoConnectionStatus() {
    return getBool(AppValues.aizoConnectionStatus) ?? false;
  }

  void setAizoConnectionStatus(bool status) {
    setBool(AppValues.aizoConnectionStatus, status);
  }

// 在 lib/app/core/service/storage_service.dart 中
  MyBluetoothDevice? getAizoDevicesLast() {
    String? deviceData = getString(AppValues.aizoDevicesLast);
    if (deviceData == null || deviceData.isEmpty) {
      return null;
    }
    Map<String, dynamic> data = jsonDecode(deviceData);
    return MyBluetoothDevice(
      DeviceIdentifier(data['remoteId']),
      data['advName'] ?? '',
    );
  }

  void setAizoDevicesLast(MyBluetoothDevice device) {
    Map<String, dynamic> deviceData = {
      'remoteId': device.remoteId.str,
    };
    setString(AppValues.aizoDevicesLast, jsonEncode(deviceData));
  }

  void deleteAizoDevicesLast() {
    delete(AppValues.aizoDevicesLast);
  }

  setBluetoothDataUpdate(bool value) {
    setBool(AppValues.bluetoothDataUpdate, value);
  }

  bool getBluetoothDataUpdate() {
    return getBool(AppValues.bluetoothDataUpdate) ?? false;
  }
}
