/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2025-06-18 16:19:34
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-05 15:59:02
 * @FilePath: /RPM-APP/lib/app/network/request_headers.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/app/core/utils/logger_singleton.dart';
import 'package:dio/dio.dart';

class RequestHeaderInterceptor extends InterceptorsWrapper {
  final storage = SecureStorageService.instance;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    getCustomHeaders().then((customHeaders) {
      options.headers.addAll(customHeaders);
      super.onRequest(options, handler);
    });
  }

  Future<Map<String, String>> getCustomHeaders() async {
    var bearer = await storage.getIDToken();
    // LoggerSingleton.getInstance().d("Token: $bearer");
    
    var customHeaders = {
      'Accept': '*/*',
    };

    if (bearer != null) {
      customHeaders['Authorization'] = 'Bearer $bearer';
    }

    return customHeaders;
  }
}
