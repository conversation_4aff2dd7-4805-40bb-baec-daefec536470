/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-02-25 15:10:37
 * @FilePath: /rpmappmaster/lib/app/network/dio_provider.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:aiCare/app/core/enum/region_unit.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart' as TranslationKeys;
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/app/network/dio_request_retrier.dart';
import 'package:aiCare/main.dart';
import 'package:dio/dio.dart';
import 'package:aiCare/app/network/pretty_dio_logger.dart';
import 'package:aiCare/app/network/request_headers.dart';
import 'package:aiCare/flavors/environment.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import '/app/network/exceptions/api_exception.dart';
import 'package:get/get.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/flavors/build_config.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';

class DioProvider {
  static String get baseUrl =>
      SecureStorageService.instance.getRegion() == RegionUnit.zh
          ? BuildConfig.instance.config.baseUrlCN
          : BuildConfig.instance.config.baseUrlOUT;

  static final String authUrl = BuildConfig.instance.config.auth0Domain;
  static final String clientID = BuildConfig.instance.config.clientID;
  static final String clientSercet = BuildConfig.instance.config.clientSercet;

  static Dio? _instance;

  // static const int _maxLineWidth = 1000;
  /// 日志拦截器
  static final _prettyDioLogger = PrettyDioLogger(
  );

  /// 基础配置选项
  static final BaseOptions _options = BaseOptions(
    baseUrl: baseUrl,
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
    contentType: 'application/json',
    responseType: ResponseType.json,
  );

  /// 获取基础 Dio 实例（带日志拦截器）
  static Dio get httpDio {
    if (_instance == null) {
      _instance = Dio(_options);
      _instance!.interceptors.add(_prettyDioLogger);
    } else {
      _instance!.interceptors.clear();
      _instance!.interceptors.add(_prettyDioLogger);
    }
    return _instance!;
  }

  /// 获取带认证令牌的 Dio 实例
  /// 包含请求头拦截器和日志拦截器
  static Dio get tokenClient {
    _addInterceptors();
    return _instance!;
  }

  /// 添加拦截器
  /// 包括：请求头拦截器、日志拦截器、错误处理拦截器
  static void _addInterceptors() {
    _instance ??= httpDio;
    _instance!.interceptors.clear();

    // 添加请求头拦截器
    _instance!.interceptors.add(RequestHeaderInterceptor());

    // 添加日志拦截器
    _instance!.interceptors.add(_prettyDioLogger);

    // 添加错误拦截器
    _instance!.interceptors.add(InterceptorsWrapper(
      onError: (DioException e, handler) async {
        // 检查是否是 getinfo 请求
        bool isGetInfoRequest = e.requestOptions.path.toLowerCase().contains('/v2/profiles/info');

        print("查看错误拦截器的错误代码");
        print(e.response);
        print(e.response?.statusCode);


        
        if (e.response?.statusCode == 401) {
          // Token 过期，使用 DioRequestRetrier 重试请求
          try {
            final retrier = DioRequestRetrier(requestOptions: e.requestOptions);
            final response = await retrier.retry();
            return handler.resolve(response);
          } catch (retryError) {
            print("Token 刷新失败: $retryError");
            return handler.reject(e);
          }
        } else if (e.type == DioExceptionType.unknown) {
          // 网络层面的错误
          ToastUtil.showError(Get.context!, TranslationKeys.T.errorNetwork.tr);
          return handler.reject(e);
        } else if (e.response != null) {
          // 服务器返回了错误状态码
          String errorMessage = "服务器错误";
          if (e.response?.data is Map<String, dynamic>) {
            final data = e.response?.data as Map<String, dynamic>;
            errorMessage = data['message'] ?? "服务器错误";
          }
          
          // 根据状态码显示不同的错误信息
          switch (e.response?.statusCode) {
            case 500:
              // 如果是 getinfo 请求，直接传递给下一个处理器，保持原始错误
              if (isGetInfoRequest) {
                return handler.reject(e);
              }
              ToastUtil.showError(Get.context!, TranslationKeys.T.errorServer.tr);
              return handler.reject(DioException(
                requestOptions: e.requestOptions,
                response: e.response,
                error: ApiException(
                  httpCode: 500,
                  status: "Server Error",
                  message: TranslationKeys.T.errorServer.tr,
                ),
              ));
            case 404:
              ToastUtil.showError(Get.context!, TranslationKeys.T.errorNotFound.tr);
              return handler.reject(DioException(
                requestOptions: e.requestOptions,
                response: e.response,
                error: ApiException(
                  httpCode: 404,
                  status: "Not Found",
                  message: TranslationKeys.T.errorNotFound.tr,
                ),
              ));
            case 403:
              ToastUtil.showError(Get.context!, TranslationKeys.T.errorForbidden.tr);
              return handler.reject(DioException(
                requestOptions: e.requestOptions,
                response: e.response,
                error: ApiException(
                  httpCode: 403,
                  status: "Forbidden",
                  message: TranslationKeys.T.errorForbidden.tr,
                ),
              ));
            default:
              ToastUtil.showError(Get.context!, TranslationKeys.T.errorServer.tr);
              return handler.reject(DioException(
                requestOptions: e.requestOptions,
                response: e.response,
                error: ApiException(
                  httpCode: e.response?.statusCode ?? -1,
                  status: "Error",
                  message: TranslationKeys.T.errorServer.tr,
                ),
              ));
          }
        } else {
          // 其他类型的错误
          return handler.next(e);
        }
      },
    ));
  }

  /// 设置自定义内容类型
  static void setContentType(String version) {
    _instance?.options.contentType = "user_defined_content_type+$version";
  }

  /// 设置 JSON 内容类型
  static void setContentTypeApplicationJson() {
    _instance?.options.contentType = "application/json";
  }
}
