/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-07-16 17:27:07
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-04-25 16:37:59
 * @FilePath: /rpmappmaster/lib/app/network/api/Api.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class Api {

  //用户信息
  static String infoApi = "/v2/profiles/info?test=1";

  //上传图片
  static String uploadImageApi = "/v2/profiles/image?test=1";



  //auth的Api
  //登录
  static String signUpApi = "/dbconnections/signup";

  //邮箱密码登录
  static String loginEmailPassApi = "/oauth/token";

  //第三方登录
  //苹果登录
  static String signAppaleApi = "/oauth/token-type/apple-authz-code";

  //FaceBook登录
  static String signFacebookApi = "/oauth/token-type/facebook-info-session-access-token";

  //Google登录
  static String signGoogleApi = "/oauth/token-type/facebook-info-session-access-token";

  //忘记密码
  static String forgetPasswordApi = "/dbconnections/change_password";

  //获取用户信息
  static String getUserDetailsApi = "/userinfo";

  //更新用户信息
  static String updateUserApi = "/api/v2/users/";

  //获得血氧
  static String bloodOxygen = "/v2/profiles/vital_signs/blood_oxygen";

    //获得血氧列表页
  static String bloodOxygenPage = "/profiles/vital_signs/page/blood_oxygen";

  //获得血氧
  static String bloodOxygenLast = "/v3/profiles/vital_signs/blood_oxygen?test=1";

  static String bodyTemperaturePage = "/profiles/vital_signs/page/body_temperature";
  //获得体温
  static String bodyTemperatureLast = "/v3/profiles/vital_signs/body_temperature?test=1";
  //获得心率
  static String heartRateLast = "/v3/profiles/vital_signs/heart_rate?test=1";

   //获得血氧列表页
  static String heartRatePage = "/profiles/vital_signs/page/heart_rate";

  //获得体温
  static String bodyTemperature = "/v2/profiles/vital_signs/body_temperature";

  //获得心率
  static String heartRateV2 = "/v2/profiles/vital_signs/heart_rate";

  //获得心率
  static String heartRateV3 = "/v3/profiles/vital_signs/heart_rate";

  //获得心率
  static String heartRateLineDays = "/profiles/vital_signs/health/day/heart_rate";

  //获得心率
  static String heartRateLineWeek = "/profiles/vital_signs/health/weekly/heart_rate";

  //获得心率
  static String heartRateLineMonth = "/profiles/vital_signs/health/monthly/heart_rate";

    //获得心率
  static String bloodOxygenLineDays = "/profiles/vital_signs/health/day/blood_oxygen";

  //获得心率
  static String bloodOxygenLineWeek = "/profiles/vital_signs/health/weekly/blood_oxygen";

  //获得心率
  static String bloodOxygenLineMonth = "/profiles/vital_signs/health/monthly/blood_oxygen";

      //获得心率
  static String bodyTemperatureLineDays = "/profiles/vital_signs/health/day/body_temperature";

  //获得心率
  static String bodyTemperatureLineWeek = "/profiles/vital_signs/health/weekly/body_temperature";

  //获得心率
  static String bodyTemperatureLineMonth = "/profiles/vital_signs/health/monthly/body_temperature";

  //aizo自动上传接口
  static String aizoAutoUpload = "/v3/profiles/vital_signs/data?test=1";


  //删除账号
  static String deleteAccount = "/v2/profiles/info?test=1";

  static String goalSetting = "/profiles/add/user?test=1";

  //睡眠数据
  static String sleepData = "/v2/profiles/vital_signs/body_sleep?test=1";

  static String sleepDataLast = "/v3/profiles/vital_signs/body_sleep?test=1";

  //身体活动数据
  static String bodyActivityData = "/v3/profiles/vital_signs/body_fitness?test=1";





}