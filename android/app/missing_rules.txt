# Please add these rules to your existing keep rules in order to suppress warnings.
# This is generated automatically by the Android Gradle plugin.
-dontwarn com.google.errorprone.annotations.CanIgnoreReturnValue
-dontwarn com.google.errorprone.annotations.CheckReturnValue
-dontwarn com.google.errorprone.annotations.Immutable
-dontwarn com.google.errorprone.annotations.RestrictedApi
-dontwarn com.jieli.bmp_convert.BmpConvert
-dontwarn com.jieli.bmp_convert.OnConvertListener
-dontwarn com.jieli.jl_fatfs.interfaces.OnFatFileProgressListener
-dontwarn com.jieli.jl_fatfs.model.FatFile
-dontwarn com.jieli.jl_fatfs.utils.FatUtil
-dontwarn com.jieli.jl_rcsp.impl.RcspOpImpl
-dontwarn com.jieli.jl_rcsp.impl.WatchOpImpl
-dontwarn com.jieli.jl_rcsp.interfaces.watch.OnWatchCallback
-dontwarn com.jieli.jl_rcsp.interfaces.watch.OnWatchOpCallback
-dontwarn com.jieli.jl_rcsp.model.NotificationMsg
-dontwarn com.jieli.jl_rcsp.model.base.BaseError
-dontwarn com.jieli.jl_rcsp.task.SimpleTaskListener
-dontwarn com.jieli.jl_rcsp.task.TaskListener
-dontwarn com.jieli.jl_rcsp.task.contacts.DeviceContacts
-dontwarn com.jieli.jl_rcsp.task.contacts.UpdateContactsTask
-dontwarn com.realsil.sdk.bbpro.core.transportlayer.SppTransportLayer
-dontwarn com.realsil.sdk.bbpro.core.transportlayer.TransportLayerCallback
-dontwarn com.realsil.sdk.core.usb.GlobalUsbGatt
-dontwarn com.realsil.sdk.core.usb.UsbGatt
-dontwarn com.realsil.sdk.core.usb.UsbGattCallback
-dontwarn com.realsil.sdk.core.usb.UsbGattCharacteristic
-dontwarn com.realsil.sdk.core.usb.UsbGattImpl
-dontwarn kotlinx.parcelize.Parcelize

# Keep specific classes to prevent them from being obfuscated
# 保留 AAR 和 JAR 文件中的类，防止它们被混淆或移除
-keep class com.aizo.** { *; }
-keep class com.rtk.** { *; }
-dontwarn com.aizo.**
-dontwarn com.rtk.**

# 保留 Google ErrorProne 注解类
-keep class com.google.errorprone.annotations.** { *; }
-dontwarn com.google.errorprone.annotations.**

# 保留 realsil 库中的 USB 类
-keep class com.realsil.sdk.core.usb.** { *; }
-dontwarn com.realsil.sdk.core.usb.**

# 保留 kotlinx.parcelize 包中的类
-keep class kotlinx.parcelize.** { *; }
-dontwarn kotlinx.parcelize.**
