
plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}


def flutterVersionCode = localProperties.getProperty("flutter.versionCode") ?: "1"
def flutterVersionName = localProperties.getProperty("flutter.versionName") ?: "1.0"




android {
    namespace = "com.aihhnet.aicare"
    compileSdk 35

    // ndkVersion = flutter.ndkVersion
    // 修改点2: 指定NDK版本 - 使用已安装的完整版本
    ndkVersion "27.0.11902837"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.aihhnet.aicare"
//        applicationIdSuffix ='com.aihnet.aicare'

        // 修改点4: 升级targetSdkVersion到35
        minSdkVersion 23
        targetSdkVersion 35
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        manifestPlaceholders += [
            auth0Domain: "ineck.auth0.com",
            auth0Scheme: "com.aihhnet.aicare",
            JPUSH_PKGNAME : applicationId,
            JPUSH_APPKEY : "3e3e1930538d23b7dbdec74b", // NOTE: JPush 上注册的包名对应的 Appkey.
            JPUSH_CHANNEL : "developer-default", //暂时填写默认值即可.
        ]
        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a"
        }

    }

    buildTypes {
        release {
           minifyEnabled false // 启用混淆
           shrinkResources false
//            proguardFiles getDefaultProguardFile('proguard-android.txt')
            signingConfig = signingConfigs.debug

        }
    }
}

tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
    kotlinOptions {
        jvmTarget = "17"
    }
}


flutter {
    source = "../.."
}

dependencies {







    // 修改点5: 添加Desugaring依赖
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    
    implementation('com.github.Alexxiaopang:KotlinKtx:1.2.0') {
        exclude module: 'rxlife-coroutine'
    }
//    implementation 'com.github.Alexxiaopang:KotlinKtx:1.0.5'

    implementation 'androidx.core:core-ktx:1.8.0'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.2'
    implementation 'com.alibaba:fastjson:1.1.72.android'
    implementation 'com.blankj:utilcodex:1.30.6'


    api 'com.github.getActivity:XXPermissions:16.0'
    api files('libs\\aizo_sdk_debug_v1.1.2.aar')
    api files('libs\\aizo_be_lib_release_v1.1.1.aar')
    api files('libs\\rtk-dfu-3.3.11.jar')
    api files('libs\\rtk-core-1.2.4.jar')
    api files('libs\\aizo_serversdk_release_v2_1.0.3.aar')
    api 'com.gitee.wjiaqiao:product-kotlin:1.0.35'
    api 'com.github.liangjingkanji:Serialize:1.3.1'

        modules {
            module("com.tencent:mmkv-static") {
                replacedBy("com.tencent:mmkv", "Using mmkv for flutter")
            }
            module("com.tencent:mmkv-shared") {
                replacedBy("com.tencent:mmkv", "Using mmkv for flutter")
            }
        }

    implementation files('libs/aoj-device-plugin-1.0.0-beta18.jar')


}



