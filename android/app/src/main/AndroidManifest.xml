<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">



    <!-- 权限声明 -->
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM"/>
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <!-- 声明不使用广告 ID -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove" />

    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> -->
    <uses-permission android:name="android.permission.BODY_SENSORS" />
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" tools:node="remove" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" tools:node="remove" />
    
    <!-- Android 12及以上需要的权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" 
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    
    <!-- 位置权限（蓝牙扫描需要） -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    
    <!-- 如果目标SDK版本是31或更高，还需要声明 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />




    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />


    <application
        android:requestLegacyExternalStorage="true"
        android:hardwareAccelerated="true"
        android:label="aiCare"
        android:name="${applicationName}"
        android:icon="@mipmap/launcher_icon">

        <service
            android:name="com.realsil.sdk.dfu.DfuService"
            android:enabled="true"
            android:exported="false" />
        
<!--        <service -->
<!--            android:name="com.transistorsoft.flutter.backgroundfetch.HeadlessTask"-->
<!--            android:exported="false" />-->

        <!-- <service 
            android:name="com.transistorsoft.flutter.backgroundfetch.BackgroundFetchService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:exported="true" /> -->

        <!-- 开机启动接收器
        <receiver android:name="com.transistorsoft.flutter.backgroundfetch.BackgroundFetchBootReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.REBOOT" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
            </intent-filter>
        </receiver> -->

        <!-- <receiver
            android:name="com.transistorsoft.flutter.backgroundfetch.HeadlessBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.transistorsoft.flutter.backgroundfetch.ACTION_RECEIVE" />
            </intent-filter>
        </receiver> -->

        <!-- <receiver android:name="com.transistorsoft.flutter.backgroundfetch.BackgroundFetchReceiver"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE">
            <intent-filter>
                <action android:name="com.transistorsoft.backgroundfetch.event" />
            </intent-filter>
        </receiver> -->

        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"

            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="com.aihhnet.aicare"
                    android:host="ineck.auth0.com"
                    android:pathPrefix="/android/com.aihhnet.aicare/callback" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>

        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>
