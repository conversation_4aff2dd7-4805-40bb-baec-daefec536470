package com.aihhnet.aicare

//import io.flutter.plugin.common.MethodCall
//import io.flutter.plugin.common.MethodChannel

import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.aojmedical.plugin.ble.AHDevicePlugin
import com.aojmedical.plugin.ble.OnSearchingListener
import com.aojmedical.plugin.ble.OnSettingListener
import com.aojmedical.plugin.ble.OnSyncingListener
import com.aojmedical.plugin.ble.data.BTConnectState
import com.aojmedical.plugin.ble.data.BTDeviceInfo
import com.aojmedical.plugin.ble.data.BTDeviceType
import com.aojmedical.plugin.ble.data.BTErrorCode
import com.aojmedical.plugin.ble.data.BTManagerStatus
import com.aojmedical.plugin.ble.data.BTScanFilter
import com.aojmedical.plugin.ble.data.IDeviceData
import com.aojmedical.plugin.ble.data.bpm.AHBpmConfig
import com.aojmedical.plugin.ble.data.bpm.AHBpmConfigSetting
import com.aojmedical.plugin.ble.data.bpm.AHBpmData
import com.aojmedical.plugin.ble.data.bpm.AHBpmStatus
import com.aojmedical.plugin.ble.data.po.AHPlethysmogram
import com.aojmedical.plugin.ble.data.po.AHSpO2
import com.aojmedical.plugin.ble.data.temp.AHTempCmd
import com.aojmedical.plugin.ble.data.temp.AHTempData
import com.aojmedical.plugin.ble.data.temp.AHTempMode
import com.aojmedical.plugin.ble.data.temp.AHTempSetting
import com.aojmedical.plugin.ble.data.temp.AHTempStatus
import com.blankj.utilcode.util.ToastUtils
import com.eiot.aizo.ext.otherwise
import com.eiot.aizo.ext.toBoolean
import com.eiot.aizo.ext.yes
import com.eiot.aizo.sdk.callback.AizoDeviceConnectCallback
import com.eiot.ringsdk.ServiceSdkCommandV2
import com.eiot.ringsdk.battery.PowerState
import com.eiot.ringsdk.be.DeviceConfig
import com.eiot.ringsdk.be.DeviceManager
import com.eiot.ringsdk.bean.HealthDataBean
import com.eiot.ringsdk.bean.SleepRecord
import com.eiot.ringsdk.bean.SportStatus
import com.eiot.ringsdk.callback.BCallback
import com.eiot.ringsdk.callback.DeviceConfigCallback
import com.eiot.ringsdk.callback.HealthDataCallback
import com.eiot.ringsdk.callback.PowerStateCallback
import com.eiot.ringsdk.callback.SleepDataCallback
import com.eiot.ringsdk.callback.SportStatusCallback
import com.eiot.ringsdk.ext.logIx
import com.eiot.ringsdk.heartrate.MeasureTimeCallback
import com.eiot.ringsdk.heartrate.MeasureTimeData
import com.eiot.ringsdk.measure.MeasureResult
import com.eiot.ringsdk.measure.MeasureResultCallback
import com.eiot.ringsdk.score.ActivityGoals
import com.eiot.ringsdk.score.ActivityGoalsCallback
import com.eiot.ringsdk.userinfo.UserInfo
import com.eiot.ringsdk.userinfo.UserInfoCallback
import com.eiot.ringsdk.util.TimeUtil
import com.google.gson.Gson
import com.jiaqiao.product.ext.toFastJson
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.Result as MethodResult


class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.aihnet.aicare_single_time"
    private val BATTERY_EVENT_CHANNEL = "com.aihnet.aicare_manyTimesBatteryStatus"
    private val STATUS_EVENT_CHANNEL = "com.aihnet.aicare_manyTimesRingStatus"
    private var eventSink: EventChannel.EventSink? = null

    private val AOJ_CHANNEL = "com.aihnet.aicare_aoj"
    private val AOJ_SCAN_EVENT_CHANNEL = "com.aihnet.aicare_aoj_scan"
    private val AOJ_SYNC_EVENT_CHANNEL = "com.aihnet.aicare_aoj_sync"
    private val AOJ_SETTING_EVENT_CHANNEL = "com.aihnet.aicare_aoj_setting"

    private val pushedDevices = mutableSetOf<String>()

    object AojListenerManager {
        var scanListener: OnSearchingListener? = null
        var syncListener: OnSyncingListener? = null
        var settingListener: OnSettingListener? =object : OnSettingListener() {
            override fun onSuccess(macAddress: String) {
                super.onSuccess(macAddress)
                println("成功:"+String)
            }

            override fun onFailure(errorCode: Int) {
                super.onFailure(errorCode)
                println("失败:"+String)
                val test = BTErrorCode.DeviceUnsupported.code;
                println(errorCode)
            }
        }
    }




    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化蓝牙 SDK
        val result =  AHDevicePlugin.getInstance().initPlugin(this);
        Log.d("AOJ", "initPlugin called, initFlag: ${AHDevicePlugin.getInstance()}")
        Log.d("AOJ", "Current API Level: ${Build.VERSION.SDK_INT}")


        flutterEngine?.dartExecutor?.binaryMessenger?.let {
            // 设置 EventChannel
            EventChannel(it, BATTERY_EVENT_CHANNEL).setStreamHandler(object : EventChannel.StreamHandler {
                private var handler: Handler? = null

                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    // 开始监听设备电池状态
                    handler = Handler(Looper.getMainLooper())

                    // 这里是电池状态的回调
                    ServiceSdkCommandV2.getDevicePowerState(object : PowerStateCallback {
                        override fun PowerState(bean: PowerState) {
                            // 将电池状态转换为列表格式
                            val batteryLevel = bean.electricity.toString()  // 电量
                            val chargingState = when (bean.workingMode) {
                                0 -> "NOT_CHARGING"    // 未充电
                                1 -> "CHARGING"        // 充电中
                                else -> "UNKNOWN"      // 未知状态
                            }

                            // 创建一个包含电量和充电状态的列表
                            val batteryStatus = listOf(batteryLevel, chargingState)

                            // 将列表发送给 Flutter 端
                            events?.success(batteryStatus)
                        }
                    })
                }

                override fun onCancel(arguments: Any?) {
                    // 停止监听
                    handler?.removeCallbacksAndMessages(null)
                    handler = null
                }
            })

            EventChannel(it, STATUS_EVENT_CHANNEL).setStreamHandler(object : EventChannel.StreamHandler {
                private var handler: Handler? = null



                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    // 开始监听设备连接状态
                    handler = Handler(Looper.getMainLooper())

                    // 这里是电池状态的回调
                    ServiceSdkCommandV2.addCallback(object : AizoDeviceConnectCallback {
                        override fun connect() {
                            // 设备连接成功时的逻辑
//                            println("设备已连接asdasd：$deviceMac")
                            events?.success("CONNECTED")
                        }

                        override fun disconnect() {
                            // 设备断开连接时的逻辑
                            events?.success("DISCONNECTED")
                        }

                        override fun connectError(throwable: Throwable, state: Int) {
                            // 设备连接时出错的逻辑
                            events?.success("$state")
                        }

                    })
                }

                override fun onCancel(arguments: Any?) {
                    // 停止监听
                    handler?.removeCallbacksAndMessages(null)
                    handler = null
                }
            })

            MethodChannel(it, CHANNEL).setMethodCallHandler { call, result ->
                when (call.method) {
                    "init" -> {
                        try {
                            // aizo设备初始化
                            ServiceSdkCommandV2.init(
                                application,
                                region = 1,
                                version = "1.0.0",
                                name = "SDK_DEMO_APP",
                                id = "com.eiot.sdk.demoapp",
                                country = "CN",
                                language = "ZH",
                                debugging = true)
                            result.success(true) // 返回 true 表示成功
                        } catch (e: Exception) {
                            println(e);
                            result.success(false) // 发生异常时返回 false
                        }
                    }


                    "aizoConnect" -> {
                        val params = call.arguments as Map<String, Any> // 获取传入的参数
                        val deviceMac = params["deviceMac"] as String // 获取蓝牙设备的 MAC 地址
                        val deviceName = params["deviceName"] as String // 获取蓝牙设备的名称
                        println("获得的参数 $deviceMac")
                        try {
                            // aizo设备连接
                            ServiceSdkCommandV2.connect(deviceMac)
                            // 调用 notifyBoundDevice 方法，传入设备名称、MAC 地址和 BCallback
                            ServiceSdkCommandV2.notifyBoundDevice(deviceName = deviceName,
                                deviceMac = DeviceManager?.mac!!,
                                callback = object : BCallback {
                                    override fun result(r: Boolean) {
                                        r.yes {
                                            println("notifyBoundDevice true")
                                        }
                                    }
                                })

                            result.success(true) // 返回 true 表示成功
                        } catch (e: Exception) {
                            println(e)
                            result.success(false) // 发生异常时返回 false
                        }
                    }

                    "aizoConfigurationList" -> {
                        try {
                            ServiceSdkCommandV2.addDeviceConfigCallback(object : DeviceConfigCallback {
                                override fun deviceConfig(bean: DeviceConfig) {
                                    println("开始获取配置清单")

                                    // 创建配置响应对象
                                    val configResponse = DeviceConfigResponse(
                                        bloodOxygenMonitoring = bean.bloodOxygenMonitoring.toBoolean(),
                                        bloodPressureMonitoring = bean.bloodPressureMonitoring.toBoolean(),
                                        bloodSugarMonitoring = bean.bloodSugarMonitoring.toBoolean(),
                                        breatheMonitoring = bean.breatheMonitoring.toBoolean(),
                                        ecgMonitoring = bean.ecgMonitoring.toBoolean(),
                                        heartRateMonitoring = bean.heartRateMonitoring.toBoolean(),
                                        isHeartRateSupport = bean.isHeartRateSupport,
                                        isTouchSet = bean.isTouchSet.toBoolean(),
                                        pressureMonitoring = bean.pressureMonitoring.toBoolean(),
                                        sleepMonitoring = bean.sleepMonitoring.toBoolean(),
                                        sosTriggerMode = bean.sosTriggerMode,
                                        supportSos = bean.supportSos.toBoolean(),
                                        supportWakeupByGesture = bean.supportWakeupByGesture.toBoolean(),
                                        temperatureMonitoring = bean.temperatureMonitoring.toBoolean()
                                    )

                                    // 将对象转换为 JSON 字符串
                                    val gson = Gson()
                                    val jsonString = gson.toJson(configResponse)

                                    // 返回 JSON 字符串
                                    result.success(jsonString)
                                }
                            })
                        } catch (e: Exception) {
                            println(e);
                            result.success("false") // 发生异常时返回 false
                        }
                    }

                    "aizoDestory" -> {
                        try {
                            // aizo设备销毁
                            ServiceSdkCommandV2.onDestory()
                            result.success(true)

                        } catch (e: Exception) {
                            println(e);
                            result.success(false) // 发生异常时返回 false
                        }
                    }

                    "aizoGetCurActGoal" -> {
                        try {
                            // aizo设备销毁
                            ServiceSdkCommandV2.getCurrentActivityGoals(object : ActivityGoalsCallback {
                                override fun ActivityGoals(bean: ActivityGoals) {
                                    println("android获取当前活动目标")
                                    println("目标卡路里消耗：（默认300）${bean.caloriesGoals}")
                                    println("目标距离消耗：（默认5000）${bean.distanceGoals}")
                                    println("目标步数消耗：（默认8000）${bean.stepGoals}")

                                    // 创建包含活动目标的列表
                                    val activityGoals = listOf(
                                        bean.caloriesGoals,  // 卡路里目标
                                        bean.distanceGoals,  // 距离目标
                                        bean.stepGoals       // 步数目标
                                    )

                                    // 返回列表
                                    result.success(activityGoals)
                                }
                            })
                        } catch (e: Exception) {
                            println(e)
                            result.success(null)  // 发生异常时返回 null
                        }
                    }

                    "aizoGetUserInfo" -> {
                        try {
                            // aizo设备销毁
                            ServiceSdkCommandV2.getUserInfo(object : UserInfoCallback {
                                override fun userInfo(bean: UserInfo) {
                                    result.success(convertUserInfoToMap(bean))
                                }
                            })

                        } catch (e: Exception) {
                            println(e);
                            result.success(convertUserInfoToMap(UserInfo())) // 发生异常时返回 false
                        }
                    }

                    "aizoSetUserInfo" -> {
                        try {
                            val params = call.arguments as Map<String, Any> // 获取传入的参数
                            val height = params["height"] as Int // 获取蓝牙设备的 MAC 地址
                            val weightDouble = params["weight"] as Number // 获取蓝牙设备的名称
                            val weight = weightDouble.toFloat()
                            val gender = params["gender"] as Int // 获取蓝牙设备的名称
                            val birth = params["birth"] as String // 获取蓝牙设备的名称


                            // aizo设备销毁
                            ServiceSdkCommandV2.setUserInfo(UserInfo().apply {
                                this.height = height
                                this.weight = weight
                                this.gender = gender
                                this.birth = birth
                            }, object : BCallback {
                                override fun result(r: Boolean) {
                                    r.yes {
                                        ServiceSdkCommandV2.getUserInfo(object : UserInfoCallback {
                                            override fun userInfo(bean: UserInfo) {
                                                return result.success(convertUserInfoToMap(bean))
                                                "改变用户信息成功".logIx()
                                            }
                                        })
                                    }
                                }
                            })
                        } catch (e: Exception) {
                            println(e);
                            result.success(convertUserInfoToMap(UserInfo())) // 发生异常时返回 false
                        }
                    }

                    "aizoGetMeasureInterval" -> {
                        try {
                            ServiceSdkCommandV2.getDeviceMeasureTime(object : MeasureTimeCallback {
                                override fun measureTime(bean: MeasureTimeData) {
                                    // 转换为与 Flutter 端匹配的格式
                                    val response = convertMeasureTimeData(bean)
                                    result.success(response)
                                }
                            })
                        } catch (e: Exception) {
                            println(e)
                            // 发生异常时返回默认值
                            result.success(mapOf(
                                "currentInterval" to 0,
                                "defaultInterval" to 0,
                                "intervalList" to emptyList<Int>()
                            ))
                        }
                    }

                    "aizoSetMeasureInterval" -> {
                        try {
                            val params = call.arguments as Map<String, Any>
                            val time = params["time"] as Int

                            ServiceSdkCommandV2.setDeviceMeasureTime(time, object : BCallback {
                                override fun result(r: Boolean) {
                                    if (r) {
                                        result.success(true)
                                    } else {
                                        result.error("201", "Failed to set interval", null)
                                    }
                                }
                            })
                        } catch (e: Exception) {
                            println(e)
                            result.error("201", "error", null)
                        }
                    }

                    "aizoInstantMeasurement" -> {
                        try {
                            val params = call.arguments as Map<String, Any>
                            val type = params["type"] as Int
                            val operation = params["operation"] as Int

                            ServiceSdkCommandV2.instantMeasurement(type, operation = operation, object : MeasureResultCallback {
                                override fun measureResult(bean: MeasureResult) {
                                    // 转换为与 Flutter 端匹配的格式
                                    val templateResult = mapOf(
                                        "result" to bean.result,
                                        "type" to bean.type,
                                        "time" to bean.time,
                                        "heartrate" to bean.heartrate,
                                        "bloodoxygen" to bean.bloodoxygen,
                                        "bodytemp" to bean.bodytemp,
                                        "envtemp" to bean.envtemp
                                    )

                                    result.success(templateResult)
                                }
                            })
                        } catch (e: Exception) {
                            println(e)
                            // 发生异常时返回包含错误信息的 Map
                            result.success(mapOf(
                                "result" to false,
                                "type" to 0,
                                "time" to 0,
                                "heartrate" to 0,
                                "bloodoxygen" to 0,
                                "bodytemp" to 0,
                                "envtemp" to 0
                            ))
                        }
                    }

//                    "aizoGetHealthData" -> {
//                        try {
//                            val params = call.arguments as Map<String, Any> // 获取传入的参数
//                            val timeInt = params["time"] as Long // 获取蓝牙设备的 MAC 地址
//                            val time = timeInt.toLong()
//                            // 初始化 healthDataAda 为一个空列表
//                            val healthDataAda: MutableList<HealthDataBean> = mutableListOf()
//
////                            if(!DeviceManager.isConnect()){
////                                println("未连接") ;
////                                result.error("201","未连接",{})
////                                return@setMethodCallHandler
////                            }
////                            val healthList = new List<dynamic>();
//                            ServiceSdkCommandV2.getHealthData(time,object : HealthDataCallback {
//                                override fun onFinish(time: Long) {
//                                    println("数据结束 时间:${TimeUtil.getTimeStr2(time)}")
////                                    mViewBind.btHealthData.isEnabled = true
//                                    // 将 healthDataAda 转换为可序列化的 Map 列表
//                                    val resultData = healthDataAda.map { toMap(it) }
//
//                                    // 返回结果
//                                    result.success(resultData)
//                                }
//                                override fun onReceive(data: MutableList<HealthDataBean>) {
////                                    healthDataAda.add(data)
//                                    healthDataAda.addAll(data)
////                                    data:${data.size},data.toFastJson():${data.toFastJson()}".logIx()
//                                    println("收到的")
//                                }
//                            })
//
//
//
//                        } catch (e: Exception) {
//                            println(e);
//                            result.success(false) // 发生异常时返回 false
//                        }
//                    }

                    "aizoGetHealthData" -> {
                        try {
                            // 检查设备是否连接
                            if(!DeviceManager.isConnect()){
                                println("设备未连接")
                                result.error("201", "设备未连接", null)
                                return@setMethodCallHandler
                            }

                            val params = call.arguments as Map<String, Any> // 获取传入的参数
                            val timeInt = params["time"] as Long // 获取蓝牙设备的 MAC 地址
                            val time = timeInt.toLong()
                            // 初始化 healthDataAda 为一个空列表
                            val healthDataAda: MutableList<HealthDataBean> = mutableListOf()

                            ServiceSdkCommandV2.getHealthData(time,object : HealthDataCallback {
                                override fun onFinish(time: Long) {
                                    println("数据结束 时间:${TimeUtil.getTimeStr2(time)}")
                                    // 将 healthDataAda 转换为可序列化的 Map 列表
                                    val resultData = healthDataAda.map { toMap(it) }

                                    // 返回结果
                                    result.success(resultData)
                                }
                                override fun onReceive(data: MutableList<HealthDataBean>) {
                                    healthDataAda.addAll(data)
                                    println("收到的")
                                }
                            })

                        } catch (e: Exception) {
                            println(e);
                            result.success(false) // 发生异常时返回 false
                        }
                    }
                    "aizoGetSleepData" -> {
                        try {
                            val params = call.arguments as Map<String, Any> // 获取传入的参数
                            val timeInt = params["time"] as Long // 获取蓝牙设备的 MAC 地址
                            val time = timeInt.toLong()

                            ServiceSdkCommandV2.getSleepData(time,object : SleepDataCallback {
                                override fun sleepData(bean: MutableList<SleepRecord>) {
                                    println(
                                        bean.toFastJson()
                                    )
//                                    result.success("返回数据成功")
                                    result.success(bean.toFastJson())
                                }


                            })



                        } catch (e: Exception) {
                            e.printStackTrace()  // 建议使用.printStackTrace() 打印完整堆栈信息
                            // 发生异常时返回空数组 []
                            result.success(emptyList<SleepRecord>())
                        }
                    }


                    "aizoGetHardwareData" -> {
                        try {
                            DeviceManager.isConnect().yes {
                                val test = ServiceSdkCommandV2.getFirmwareParams().toString()
                                val text = "你好"
//                                mViewBind.txFirmwareInfoDetail.text = ServiceSdkCommandV2.getFirmwareParams().toString()
                            }.otherwise {
                                ToastUtils.showLong("请连接戒指！")
                            }

                            val test = ServiceSdkCommandV2.getFirmwareParams().toString()
                            result.success(ServiceSdkCommandV2.getFirmwareParams().toString())
                        } catch (e: Exception) {
                            println(e);
                            result.success("失败") // 发生异常时返回 false
                        }
                    }

                    "aizoGetSportStatus" -> {
                        try {
                            if(!DeviceManager.isConnect()){
                                println("未连接") ;
                                result.error("201","未连接",{})
                                return@setMethodCallHandler
                            }
                            ServiceSdkCommandV2.getSportStatus(object : SportStatusCallback{
                                override fun status(bean: SportStatus) {
                                    when(bean.sportStatus){

                                    }
                                }
                            })
                            val test = ServiceSdkCommandV2.getFirmwareParams().toString();
                            result.success(ServiceSdkCommandV2.getFirmwareParams().toString())
                        } catch (e: Exception) {
                            println(e);
                            result.success("失败") // 发生异常时返回 false
                        }
                    }

                    "aizoUnbind" -> {
                        try {
//                            DeviceManager.isConnect().yes {
                                ServiceSdkCommandV2.handleDevice(2, object : BCallback {
                                    override fun result(r: Boolean) {
//                                        ToastUtils.showLong("操作戒指成功！")
                                        print("进行解绑操作");
                                        print(r)
                                        result.success(r)
                                    }
                                })
//                            }.otherwise {
////                                ToastUtils.showLong("请连接戒指")
//                                print("请连接戒指先")
//                                result.error("201","请链接戒指",{})
//                            }

                        } catch (e: Exception) {
                            println(e);
                            result.success(false) // 发生异常时返回 false
                        }
                    }

                    else -> {
                        result.notImplemented()
                    }
                }
            }


            // ================== 添加 AOJ 相关通道 ==================
            // AOJ 方法通道
            MethodChannel(it, AOJ_CHANNEL).setMethodCallHandler { call, result ->
                handleAojMethodCall(call, result)
            }



            // AOJ 扫描事件通道
            EventChannel(it, AOJ_SCAN_EVENT_CHANNEL).setStreamHandler(
                object : EventChannel.StreamHandler {
                    private var scanListener: OnSearchingListener? = null


                    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                        scanListener = object : OnSearchingListener() {
                            override fun onSearchResults(device: BTDeviceInfo) {
                                if (device.deviceName != null) {
                                    if(device.deviceName.contains("AOJ")){
                                        print("nihao")
                                        print(device.deviceName)
                                        val mac = device.macAddress
                                        if (pushedDevices.add(mac)) { // 只有第一次 add 返回 true
                                            val deviceMap = mapOf(
                                                "name" to device.deviceName,
                                                "macAddress" to device.macAddress,
                                                "broadcastID" to device.broadcastID,
                                                "deviceType" to device.deviceType,
                                                "rssi" to device.rssi
                                            )
                                            runOnUiThread {
                                                events?.success(deviceMap)
                                            }
                                        }
                                    }

                                }
                            }
                        }
                        // 注册到全局管理器
                        AojListenerManager.scanListener = scanListener
                    }

                    override fun onCancel(arguments: Any?) {
                        // 停止扫描并清理监听器
                        AHDevicePlugin.getInstance().stopSearch()
                        scanListener = null
                        AojListenerManager.scanListener = null
                    }
                }
            )

            // AOJ 同步事件通道
            EventChannel(it, AOJ_SYNC_EVENT_CHANNEL).setStreamHandler(
                object : EventChannel.StreamHandler {
                    private var syncListener: OnSyncingListener? = null

                    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                        syncListener = object : OnSyncingListener() {
                            override fun onStateChanged(broadcastId: String, state: BTConnectState) {
                                super.onStateChanged(broadcastId, state)
                                println("设备当前连接状态为："+state.name);
                                events?.success(mapOf(
                                    "eventType" to "stateChanged",
                                    "broadcastId" to broadcastId,
                                    "state" to state.name
                                ))

                            }

                            override fun onDeviceDataUpdate(broadcastId: String, data: IDeviceData) {
                                super.onDeviceDataUpdate(broadcastId,data)
                                // 根据具体数据类型处理
                                val dataMap = when (data) {
                                    is AHTempData -> convertTempData(broadcastId, data)
                                    is AHSpO2 -> convertSpO2Data(broadcastId, data)
                                    is AHBpmData -> convertBpmData(broadcastId, data)
                                    is AHPlethysmogram -> convertPlethData(broadcastId, data)
                                    is AHTempStatus -> convertTempStatus(broadcastId, data)
                                    is AHBpmStatus -> convertBpmStatus(broadcastId, data)
                                    else -> mapOf(
                                        "eventType" to "unknownData",
                                        "broadcastId" to broadcastId,
                                        "cmd" to data.cmd,
                                        "utc" to data.utc,
                                        "measureTime" to data.measureTime
                                    )
                                }
                                events?.success(dataMap)
                            }
                        }
                        // 将监听器设置到全局管理器
                        AojListenerManager.syncListener = syncListener
                    }

                    override fun onCancel(arguments: Any?) {
                        syncListener = null
                        AojListenerManager.syncListener = null
                    }

                    // ============== 数据转换方法 ==============
                    private fun convertTempData(broadcastId: String, data: AHTempData): Map<String, Any> {
                        return mapOf(
                            "eventType" to "tempData",
                            "broadcastId" to broadcastId,
                            "temperature" to data.temp,
//                            "unit" to data.unit,
                            "timestamp" to data.utc,
                            "measureTime" to data.measureTime,
                            "mode" to data.mode.name,
//                            "isError" to data.isError,
//                            "errorCode" to data.errorCode
                        )
                    }

                    private fun convertSpO2Data(broadcastId: String, data: AHSpO2): Map<String, Any> {
                        return mapOf(
                            "eventType" to "spo2Data",
                            "broadcastId" to broadcastId,
                            "spo2" to data.value,
                            "pulseRate" to data.pulseRate,
                            "timestamp" to data.utc,
                            "measureTime" to data.measureTime,
//                            "signalQuality" to data.signalQuality
                        )
                    }

                    private fun convertBpmData(broadcastId: String, data: AHBpmData): Map<String, Any> {
                        return mapOf(
                            "eventType" to "bpmData",
                            "broadcastId" to broadcastId,
                            "systolic" to data.systolic,
                            "diastolic" to data.diastolic,
                            "pulse" to data.pulse,
                            "timestamp" to data.utc,
                            "measureTime" to data.measureTime,
                            "user" to data.userNumber,
                            "irregular" to data.isIrregularPulse
                        )
                    }

                    private fun convertPlethData(broadcastId: String, data: AHPlethysmogram): Map<String, Any> {
                        return mapOf(
                            "eventType" to "plethData",
                            "broadcastId" to broadcastId,
                            "waveform" to data.battery, // 转换为List
                            "timestamp" to data.utc
                        )
                    }

                    private fun convertTempStatus(broadcastId: String, data: AHTempStatus): Map<String, Any> {
                        return mapOf(
                            "eventType" to "tempStatus",
                            "broadcastId" to broadcastId,
                            "batteryLevel" to data.battery,
//                            "memoryUsage" to data.memoryUsage,
//                            "firmwareVersion" to data.firmwareVersion,
                            "timestamp" to data.utc
                        )
                    }

                    private fun convertBpmStatus(broadcastId: String, data: AHBpmStatus): Map<String, Any> {
                        return mapOf(
                            "eventType" to "bpmStatus",
                            "broadcastId" to broadcastId,
//                            "batteryLevel" to data.batteryLevel,
//                            "serialNumber" to data.serialNumber,
//                            "firmwareVersion" to data.firmwareVersion,
                            "timestamp" to data.utc
                        )
                    }
                }
            )

            // AOJ 设置事件通道
            EventChannel(it, AOJ_SETTING_EVENT_CHANNEL).setStreamHandler(
                object : EventChannel.StreamHandler {
                    private var settingListener: OnSettingListener? = null

                    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                        settingListener = object : OnSettingListener() {
                            override fun onSuccess(macAddress: String) {
                                events?.success(mapOf(
                                    "eventType" to "success",
                                    "macAddress" to macAddress
                                ))
                            }

                            override fun onFailure(errorCode: Int) {
                                events?.success(mapOf(
                                    "eventType" to "failure",
                                    "errorCode" to errorCode
                                ))
                            }

                            override fun onDataUpdate(obj: Any) {
                                // 处理设置相关的数据更新
                                events?.success(mapOf(
                                    "eventType" to "dataUpdate",
                                    "data" to obj.toString()
                                ))
                            }
                        }
                    }

                    override fun onCancel(arguments: Any?) {
                        settingListener = null
                    }
                }
            )
        }





    }

    fun convertUserInfoToMap(userInfo: UserInfo): Map<String, Any> {
        val userInfoMap: MutableMap<String, Any> = HashMap()
        userInfoMap["birth"] = userInfo.birth
        userInfoMap["gender"] = userInfo.gender
        userInfoMap["height"] = userInfo.height
        userInfoMap["weight"] = userInfo.weight
        return userInfoMap
    }

    fun convertMeasureTimeData(data: MeasureTimeData): Map<String, Any> {
        val userInfoMap: MutableMap<String, Any> = HashMap()
        userInfoMap["intervalList"] = data.intervalList as List<Int>
        userInfoMap["currentInterval"] = data.currentInterval
        userInfoMap["defaultInterval"] = data.defaultInterval
        return userInfoMap
    }

    fun convertMeasureResult(data: MeasureResult): Map<String, Any> {
        val resultMap: MutableMap<String, Any> = HashMap()
        resultMap["result"] = data.result
        resultMap["type"] = data.type
        resultMap["time"] = data.time
        resultMap["heartrate"] = data.heartrate
        resultMap["bloodoxygen"] = data.bloodoxygen
        resultMap["bodytemp"] = data.bodytemp
        resultMap["envtemp"] = data.envtemp
        return resultMap
    }

    fun toMap(data:HealthDataBean): Map<String, Any> {
        return mapOf(
            "bo" to data.bo,
            "calorie" to data.calorie,
            "date" to data.date,
            "device" to data.device,
            "distance" to data.distance,
            "envtp" to data.envtp,
            "hr" to data.hr,
            "hrv" to data.hrv,
            "sos" to data.sos,
            "step" to data.step,
            "temp" to data.temp,
            "time" to data.time
        )
    }
    data class DeviceConfigResponse(
        val bloodOxygenMonitoring: Boolean,
        val bloodPressureMonitoring: Boolean,
        val bloodSugarMonitoring: Boolean,
        val breatheMonitoring: Boolean,
        val ecgMonitoring: Boolean,
        val heartRateMonitoring: Boolean,
        val isHeartRateSupport: Boolean,
        val isTouchSet: Boolean,
        val pressureMonitoring: Boolean,
        val sleepMonitoring: Boolean,
        val sosTriggerMode: Int,
        val supportSos: Boolean,
        val supportWakeupByGesture: Boolean,
        val temperatureMonitoring: Boolean
    )
    data class MeasureTimeResponse(
        val currentInterval: Int,
        val defaultInterval: Int,
        val intervalList: List<Int>
    )
    // 转换函数
    fun convertMeasureTimeData(bean: MeasureTimeResponse): Map<String, Any> {
        return mapOf(
            "currentInterval" to bean.currentInterval,
            "defaultInterval" to bean.defaultInterval,
            "intervalList" to (bean.intervalList ?: emptyList())
        )
    }



    private fun handleAojMethodCall(call: MethodCall, result: MethodResult) {
        println(call.method)
        println("call.method = ${call.method}")
        println("call.method = ${call.method == "startBpmMeasure"}")
        when (call.method) {

            "startScan" -> {
                try {
                    // 检查状态
                    val sdkStatus = AHDevicePlugin.getInstance().managerStatus

                    when (sdkStatus) {
                        BTManagerStatus.Scanning -> {
                            // 正在扫描中 - 先停止扫描
                            AHDevicePlugin.getInstance().stopSearch()

                            // 延迟后重新开始扫描
                            Handler(Looper.getMainLooper()).postDelayed({
                                startDeviceScan(result)
                            }, 1000)
                        }

                        BTManagerStatus.Syncing -> {
                            // 同步中 - 不进行扫描
                            result.error("AOJ_SYNCING", "Device is syncing data", null)
                        }

                        BTManagerStatus.Free -> {
                            // 空闲 - 开始扫描
                            startDeviceScan(result)
                        }

                        else -> {
                            // 其他状态处理
                            result.error("AOJ_BUSY", "SDK is busy with status: $sdkStatus", null)
                        }
                    }
                } catch (e: Exception) {
                    result.error("AOJ_ERROR", "Scan failed: ${e.message}", null)
                }
            }

            "stopScan" -> {
                try {
//                    val  test = AHDevicePlugin.getInstance().managerStatus

//                    if (AHDevicePlugin.getInstance().managerStatus == BTManagerStatus.Scanning) {
                        AHDevicePlugin.getInstance().stopSearch()
                        result.success(true)
//                    } else {
//                        result.success(false)
//                    }
                } catch (e: Exception) {
                    result.error("AOJ_ERROR", "Stop scan failed: ${e.message}", null)
                }
            }

            "addDevice" -> {
                try {
                    val params = call.arguments as Map<String, Any>
                    val macAddress = params["macAddress"] as String
                    val deviceTypeStr = params["deviceType"] as Int

                    var deviceType = 0;
                    if(deviceTypeStr == 0) deviceType = BTDeviceType.BloodPressureMeter.value;
                    else if(deviceTypeStr == 1) deviceType = BTDeviceType.DigitalThermometer.value;
                    else if(deviceTypeStr == 2) deviceType = BTDeviceType.Oximeter.value;
                    else if(deviceTypeStr == 3) deviceType = BTDeviceType.Thermometer.value;
                    else deviceType = BTDeviceType.Unknown.value;



                    // 创建设备信息
//                    val device = BTDeviceInfo().apply {
//                        broadcastID = macAddress.replace(":", "")
//                        this.macAddress = macAddress
//                        deviceType = BTDeviceType.BloodPressureMeter.value
//                    }
//                    val deviceMac = "FA:B2:CA:4A:9A:94"
                    val device = BTDeviceInfo()
                    device.broadcastID = macAddress.replace(":", "")
                    device.macAddress = macAddress
                    device.deviceType = deviceType
//                    AHDevicePlugin.getInstance().initPlugin()
                    Log.d("AOJ", "initFlag: ${AHDevicePlugin.getInstance()}")
                    Log.d("AOJ", "plugin instance: ${AHDevicePlugin.getInstance().hashCode()}")
                    val look = AHDevicePlugin.getInstance();
                    // 添加设备
                    val success = AHDevicePlugin.getInstance().addDevice(device)


                    result.success(success)
                } catch (e: Exception) {
                    result.error("AOJ_ERROR", "Add device failed: ${e.message}", null)
                }
            }

            "startSync" -> {
                try {
                    val sdkStatus = AHDevicePlugin.getInstance().managerStatus

                    when (sdkStatus) {
                        BTManagerStatus.Syncing -> {
                            result.error("AOJ_SYNCING", "Already syncing", null)
                        }
                        BTManagerStatus.Scanning -> {
                            // 停止扫描
                            AHDevicePlugin.getInstance().stopSearch()
                            startDataSync(result)
                        }
                        BTManagerStatus.Free -> {
                            println("开始async")
                            startDataSync(result)
                        }
                        else -> {
                            result.error("AOJ_BUSY", "SDK is busy with status: $sdkStatus", null)
                        }
                    }
                } catch (e: Exception) {
                    result.error("AOJ_ERROR", "Start sync failed: ${e.message}", null)
                }
            }

            "stopSync" -> {
                try {
                    if (AHDevicePlugin.getInstance().managerStatus == BTManagerStatus.Syncing) {
                        AHDevicePlugin.getInstance().stopAutoConnect()
                        result.success(true)
                    } else {
                        result.success(false)
                    }
                } catch (e: Exception) {
                    result.error("AOJ_ERROR", "Stop sync failed: ${e.message}", null)
                }
            }

            "pushSetting" -> {
                try {
                    val params = call.arguments as Map<String, Any>
                    val broadcastId = params["broadcastId"] as String
                    val settingType = params["settingType"] as String
                    val settingParams = params["params"] as Map<String, Any>?

                    // 根据设置类型创建相应的设置对象
                    val setting = when (settingType) {
                        "startBpmMeasure" -> AHBpmConfigSetting(AHBpmConfig.StartMeasuring)
//                        "TempSyncTime" -> createTempSyncTimeSetting()
//                        "TempStartMeasure" -> createTempStartMeasureSetting()
//                        "BpmUserSwitch" -> createBpmUserSwitchSetting(settingParams)
//                        "BpmVoiceControl" -> createBpmVoiceControlSetting(settingParams)
                        // 添加其他设置类型...
                        else -> throw IllegalArgumentException("Unknown setting type: $settingType")
                    }

                    // 推送设置
                    AHDevicePlugin.getInstance().pushSetting(
                        broadcastId,
                        setting,
                        AojListenerManager.settingListener // 使用全局设置监听器
                    )

                    result.success(true)
                } catch (e: Exception) {
                    result.error("AOJ_ERROR", "Push setting failed: ${e.message}", null)
                }
            }

            "startBpmMeasure" -> {
                try {
                    println("开始测量血压")
                    val params = call.arguments as Map<String, Any>
                    val macAddress = params["macAddress"] as String

                    val setting = AHBpmConfigSetting(AHBpmConfig.StartMeasuring)
                    AHDevicePlugin.getInstance().pushSetting(
                        macAddress,
                        setting,
                        AojListenerManager.settingListener
                    )
                    result.success(true)
                } catch (e: Exception) {
                    result.error("AOJ_ERROR", "Start BPM measure failed: ${e.message}", null)
                }
            }

            "stopBpmMeasure" -> {
                try {
                    val params = call.arguments as Map<String, Any>
                    val macAddress = params["macAddress"] as String

                    val setting = AHBpmConfigSetting(AHBpmConfig.StopMeasuring)
                    AHDevicePlugin.getInstance().pushSetting(
                        macAddress,
                        setting,
                        AojListenerManager.settingListener
                    )
                    result.success(true)
                } catch (e: Exception) {
                    result.error("AOJ_ERROR", "Stop BPM measure failed: ${e.message}", null)
                }
            }

            else -> {
                result.notImplemented()
            }
        }
    }

    // 设备扫描实现
    private fun startDeviceScan(result: MethodResult) {
        try {
            pushedDevices.clear()
            val types: MutableList<BTDeviceType> = ArrayList()
            types.add(BTDeviceType.BloodPressureMeter)
            val filter = BTScanFilter(types);
            // 开始扫描所有设备
            AHDevicePlugin.getInstance().searchDevice(filter,AojListenerManager.scanListener) // 使用全局扫描监听器
            val test = AHDevicePlugin.getInstance().managerStatus
            result.success(true)
        } catch (e: Exception) {
            result.error("AOJ_ERROR", "Scan failed: ${e.message}", null)
        }
    }

    // 数据同步实现
    private fun startDataSync(result: MethodResult) {
        try {
            println("startDataSync开始行动");
            AHDevicePlugin.getInstance().startAutoConnect(AojListenerManager.syncListener) // 使用全局同步监听器
            result.success(true)
        } catch (e: Exception) {
            result.error("AOJ_ERROR", "Start sync failed: ${e.message}", null)
        }
    }

    // ================== 设备设置创建方法 ==================
    private fun createTempModeSetting(params: Map<String, Any>?): AHTempSetting {
        //新旧固件设备？
        val setting = AHTempSetting(AHTempCmd.NewSyncTime)

        params?.let {
            // 设置测量模式
            val modeValue = it["mode"] as Int
            setting.mode = when (modeValue) {
                1 -> AHTempMode.Adult
                2 -> AHTempMode.Children
                3 -> AHTempMode.Ear
                4 -> AHTempMode.Material
                else -> AHTempMode.Adult
            }

            // 设置温度单位
            val unitValue = it["unit"] as Int
            setting.unit = unitValue
        }

        return setting
    }

    private fun createTempSyncTimeSetting(): AHTempSetting {
        // 根据文档，新固件设备使用 NewSyncTime
        return AHTempSetting(AHTempCmd.NewSyncTime)
    }

    private fun createTempStartMeasureSetting(): AHTempSetting {
        return AHTempSetting(AHTempCmd.NewStartMeasuring)
    }

    private fun createBpmUserSwitchSetting(params: Map<String, Any>?): AHBpmConfigSetting {
        val user = params?.get("user") as? Int ?: 1
        return AHBpmConfigSetting(AHBpmConfig.SwitchUser, user)
    }

    private fun createBpmVoiceControlSetting(params: Map<String, Any>?): AHBpmConfigSetting {
        val enabled = params?.get("enabled") as? Boolean ?: true
        return AHBpmConfigSetting(AHBpmConfig.VoiceControl, enabled)
    }




}
